#!/usr/bin/env node

/**
 * Security Enhancements Validation Script
 * 
 * This script validates that all security enhancements are working correctly
 * and demonstrates the integrated security features.
 */

import { 
  ConstantTimeOps,
  CryptoValidation,
  SecurityValidationError,
  SecureMemory,
  SecureMemoryPool,
  ProtocolErrorRecovery,
  ERROR_TYPES,
  globalErrorRecovery
} from '../src/core/crypto/index.js';

import { BigInteger } from 'jsbn';

console.log('🔒 WebOTR Security Enhancements Validation');
console.log('==========================================\n');

let allTestsPassed = true;

function runTest(testName, testFn) {
  try {
    console.log(`Testing: ${testName}`);
    testFn();
    console.log('✅ PASSED\n');
  } catch (error) {
    console.log(`❌ FAILED: ${error.message}\n`);
    allTestsPassed = false;
  }
}

// Test 1: Constant-Time Operations
runTest('Constant-Time Operations', () => {
  // Test basic equality
  const data1 = new Uint8Array([1, 2, 3, 4]);
  const data2 = new Uint8Array([1, 2, 3, 4]);
  const data3 = new Uint8Array([1, 2, 3, 5]);

  if (!ConstantTimeOps.constantTimeEqual(data1, data2)) {
    throw new Error('Equal arrays should return true');
  }

  if (ConstantTimeOps.constantTimeEqual(data1, data3)) {
    throw new Error('Different arrays should return false');
  }

  // Test conditional selection
  if (ConstantTimeOps.conditionalSelect(true, 0xFF, 0x00) !== 0xFF) {
    throw new Error('Conditional select failed for true condition');
  }

  if (ConstantTimeOps.conditionalSelect(false, 0xFF, 0x00) !== 0x00) {
    throw new Error('Conditional select failed for false condition');
  }

  // Test self-test
  if (!ConstantTimeOps.selfTest()) {
    throw new Error('Self-test failed');
  }

  console.log('  - Basic equality: ✓');
  console.log('  - Conditional selection: ✓');
  console.log('  - Self-test: ✓');
});

// Test 2: Input Validation
runTest('Input Validation Framework', () => {
  // Test valid DH key
  const validDHKey = new BigInteger('12345678901234567890ABCDEF', 16);
  CryptoValidation.validateDHPublicKey(validDHKey);

  // Test invalid DH key (should throw)
  const invalidDHKey = new BigInteger('1');
  try {
    CryptoValidation.validateDHPublicKey(invalidDHKey);
    throw new Error('Should have thrown for invalid DH key');
  } catch (error) {
    if (!(error instanceof SecurityValidationError)) {
      throw new Error('Should throw SecurityValidationError');
    }
  }

  // Test valid SMP element
  const validSMPElement = new BigInteger('123456789ABCDEF', 16);
  CryptoValidation.validateSMPGroupElement(validSMPElement);

  // Test invalid SMP element (should throw)
  const invalidSMPElement = new BigInteger('1');
  try {
    CryptoValidation.validateSMPGroupElement(invalidSMPElement);
    throw new Error('Should have thrown for invalid SMP element');
  } catch (error) {
    if (!(error instanceof SecurityValidationError)) {
      throw new Error('Should throw SecurityValidationError');
    }
  }

  // Test protocol message validation
  const validMessage = {
    type: 'SMP1',
    instanceFrom: 0x12345678,
    instanceTo: 0x87654321
  };
  CryptoValidation.validateProtocolMessage(validMessage, 'SMP1');

  // Test message counter validation
  CryptoValidation.validateMessageCounter(2, 1); // Valid progression

  try {
    CryptoValidation.validateMessageCounter(1, 2); // Invalid regression
    throw new Error('Should have thrown for counter regression');
  } catch (error) {
    if (!(error instanceof SecurityValidationError)) {
      throw new Error('Should throw SecurityValidationError');
    }
  }

  console.log('  - DH key validation: ✓');
  console.log('  - SMP element validation: ✓');
  console.log('  - Protocol message validation: ✓');
  console.log('  - Counter validation: ✓');
});

// Test 3: Secure Memory Management
runTest('Secure Memory Management', () => {
  // Test basic secure memory
  const memory = new SecureMemory(64);
  const testData = new Uint8Array([1, 2, 3, 4, 5]);
  
  memory.write(testData);
  const readData = memory.read(5);
  
  if (!ConstantTimeOps.constantTimeEqual(testData, readData)) {
    throw new Error('Read data does not match written data');
  }

  // Test secure wiping
  memory.secureWipe();
  const wipedData = memory.getView();
  for (let i = 0; i < wipedData.length; i++) {
    if (wipedData[i] !== 0) {
      throw new Error('Memory not properly wiped');
    }
  }

  memory.destroy();

  // Test memory pool
  const pool = new SecureMemoryPool({ maxPoolSize: 5 });
  const poolMemory1 = pool.allocate(32);
  const poolMemory2 = pool.allocate(32);
  
  pool.deallocate(poolMemory1);
  const poolMemory3 = pool.allocate(32); // Should reuse from pool
  
  const stats = pool.getStats();
  if (stats.poolHits === 0) {
    throw new Error('Memory pool not working correctly');
  }

  poolMemory2.destroy();
  poolMemory3.destroy();
  pool.destroy();

  console.log('  - Basic allocation/deallocation: ✓');
  console.log('  - Secure wiping: ✓');
  console.log('  - Memory pool: ✓');
  console.log('  - Lifecycle management: ✓');
});

// Test 4: Error Recovery System
runTest('Enhanced Error Recovery', () => {
  const recovery = new ProtocolErrorRecovery({ enableLogging: false });
  const mockContext = {
    id: 'test-context',
    auth: {
      state: 'AWAITING_DHKEY',
      hashgx: new Uint8Array([1, 2, 3, 4]),
      privateKey: new Uint8Array([5, 6, 7, 8])
    }
  };

  // Test signature error recovery
  const signatureError = new Error('Invalid signature');
  signatureError.type = ERROR_TYPES.INVALID_SIGNATURE;
  
  const recoveryResult = recovery.handleAKEError(signatureError, mockContext);
  
  if (recoveryResult.strategy !== 'RESET_STATE') {
    throw new Error('Incorrect recovery strategy for signature error');
  }

  if (mockContext.auth.privateKey !== null) {
    throw new Error('Sensitive data not cleared during recovery');
  }

  // Test competing DH commit resolution
  const commitContext = {
    id: 'commit-context',
    auth: {
      state: 'AWAITING_DHKEY',
      hashgx: new Uint8Array([5, 6, 7, 8])
    }
  };

  const commitError = new Error('Competing DH commits');
  commitError.type = ERROR_TYPES.COMPETING_DH_COMMIT;
  commitError.data = {
    hashgx: new Uint8Array([1, 2, 3, 4]) // Smaller hash
  };

  const commitRecovery = recovery.handleAKEError(commitError, commitContext);
  
  if (commitRecovery.strategy !== 'IGNORE') {
    throw new Error('Incorrect recovery strategy for competing commits');
  }

  // Test statistics
  const stats = recovery.getStats();
  if (stats.totalErrors < 2) {
    throw new Error('Error statistics not tracking correctly');
  }

  console.log('  - Signature error recovery: ✓');
  console.log('  - Competing commit resolution: ✓');
  console.log('  - State clearing: ✓');
  console.log('  - Statistics tracking: ✓');
});

// Test 5: Integration Test
runTest('Security Integration', () => {
  // Test end-to-end security workflow
  const secureMemory = new SecureMemory(32);
  
  try {
    // Generate and store sensitive data
    const sensitiveKey = new Uint8Array(32);
    if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
      crypto.getRandomValues(sensitiveKey);
    } else {
      // Fallback for Node.js environment
      for (let i = 0; i < sensitiveKey.length; i++) {
        sensitiveKey[i] = Math.floor(Math.random() * 256);
      }
    }
    
    secureMemory.write(sensitiveKey);
    
    // Validate cryptographic parameters
    const dhKey = new BigInteger('123456789ABCDEF0123456789ABCDEF', 16);
    CryptoValidation.validateDHPublicKey(dhKey);
    
    // Test constant-time operations
    const key1 = secureMemory.read();
    const key2 = new Uint8Array(key1);
    
    if (!ConstantTimeOps.constantTimeEqual(key1, key2)) {
      throw new Error('Constant-time comparison failed');
    }
    
    // Test error recovery with secure cleanup
    const context = {
      id: 'integration-test',
      auth: {
        state: 'ENCRYPTED',
        sessionKey: key1
      }
    };
    
    const error = new Error('Security violation');
    error.type = ERROR_TYPES.PROTOCOL_VIOLATION;
    
    globalErrorRecovery.handleAKEError(error, context);
    
    console.log('  - Secure key storage: ✓');
    console.log('  - Parameter validation: ✓');
    console.log('  - Constant-time operations: ✓');
    console.log('  - Error recovery integration: ✓');
    
  } finally {
    secureMemory.destroy();
  }
});

// Test 6: Performance Validation
runTest('Performance Validation', () => {
  const iterations = 1000;
  
  // Test constant-time operations performance
  const data1 = new Uint8Array(256);
  const data2 = new Uint8Array(256);
  
  if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
    crypto.getRandomValues(data1);
    crypto.getRandomValues(data2);
  }
  
  const start = performance.now();
  for (let i = 0; i < iterations; i++) {
    ConstantTimeOps.constantTimeEqual(data1, data2);
  }
  const duration = performance.now() - start;
  
  if (duration > 1000) { // 1 second for 1000 iterations
    throw new Error(`Performance too slow: ${duration}ms for ${iterations} iterations`);
  }
  
  // Test validation performance
  const validationStart = performance.now();
  for (let i = 0; i < 100; i++) {
    const key = new BigInteger('123456789ABCDEF', 16);
    CryptoValidation.validateSMPGroupElement(key);
  }
  const validationDuration = performance.now() - validationStart;
  
  if (validationDuration > 500) { // 500ms for 100 validations
    throw new Error(`Validation too slow: ${validationDuration}ms for 100 validations`);
  }
  
  console.log(`  - Constant-time ops: ${duration.toFixed(2)}ms for ${iterations} iterations ✓`);
  console.log(`  - Validation: ${validationDuration.toFixed(2)}ms for 100 validations ✓`);
});

// Final Results
console.log('🔒 Security Validation Results');
console.log('==============================');

if (allTestsPassed) {
  console.log('✅ ALL TESTS PASSED');
  console.log('\n🎉 WebOTR Security Enhancements Successfully Validated!');
  console.log('\nSecurity Features Confirmed:');
  console.log('  ✓ Timing attack resistance');
  console.log('  ✓ Comprehensive input validation');
  console.log('  ✓ Secure memory management');
  console.log('  ✓ Robust error recovery');
  console.log('  ✓ Performance within acceptable limits');
  console.log('\n🚀 Ready for production deployment!');
  process.exit(0);
} else {
  console.log('❌ SOME TESTS FAILED');
  console.log('\n⚠️  Security validation incomplete. Please review failed tests.');
  process.exit(1);
}
