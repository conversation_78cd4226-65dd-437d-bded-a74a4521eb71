Version Negotiation
===================

The Version Negotiation module provides comprehensive OTR protocol version negotiation, supporting both OTR v2 and v3 with automatic version selection and security validation.

Overview
--------

Version negotiation is the first step in establishing an OTR session, where both parties determine the optimal protocol version to use based on their capabilities and security policies.

.. mermaid::

   graph LR
       subgraph "Version Negotiation Process"
           A[Client A] --> Q1[Send Query ?OTRv23?]
           Q1 --> B[Client B]
           B --> Q2[Parse Query]
           Q2 --> N[Negotiate Version]
           N --> R[Return v3]
           R --> A
           A --> S[Start AKE with v3]
       end

Supported Versions
-----------------

WebOTR supports the following OTR protocol versions:

* **OTR v2**: Basic OTR protocol with essential security features
* **OTR v3**: Enhanced protocol with instance tags and additional security features

Version Capabilities
~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TB
       subgraph "OTR v2 Capabilities"
           V2_1[Basic AKE Protocol]
           V2_2[SMP v1]
           V2_3[Message Fragmentation]
           V2_4[Basic Error Handling]
       end
       
       subgraph "OTR v3 Capabilities"
           V3_1[Enhanced AKE Protocol]
           V3_2[Instance Tags]
           V3_3[Extra Symmetric Key]
           V3_4[SMP v2]
           V3_5[Advanced Error Handling]
       end
       
       subgraph "Shared Features"
           S1[End-to-End Encryption]
           S2[Perfect Forward Secrecy]
           S3[Deniable Authentication]
           S4[Message Authentication]
       end
       
       V2_1 -.-> S1
       V2_2 -.-> S2
       V2_3 -.-> S3
       V2_4 -.-> S4
       
       V3_1 -.-> S1
       V3_2 -.-> S2
       V3_3 -.-> S3
       V3_4 -.-> S4
       V3_5 -.-> S4

Negotiation Process
------------------

The version negotiation process follows a specific sequence to ensure optimal version selection:

.. mermaid::

   sequenceDiagram
       participant Alice as Alice
       participant VN_A as Version Negotiation A
       participant Network as Network
       participant VN_B as Version Negotiation B
       participant Bob as Bob
       
       Note over Alice,Bob: Initial Query
       Alice->>VN_A: createQueryMessage("<EMAIL>")
       VN_A->>VN_A: Check local policies
       VN_A-->>Alice: "?OTRv23? <EMAIL>"
       Alice->>Network: Send query message
       
       Note over Alice,Bob: Query Processing
       Network->>Bob: Deliver query message
       Bob->>VN_B: parseVersions("?OTRv23? <EMAIL>")
       VN_B->>VN_B: Extract versions [2, 3]
       VN_B-->>Bob: {versions: [2, 3], account: "<EMAIL>"}
       
       Note over Alice,Bob: Version Negotiation
       Bob->>VN_B: negotiateVersion([2, 3], localPolicy)
       VN_B->>VN_B: Check compatibility
       VN_B->>VN_B: Select optimal version
       VN_B-->>Bob: {version: 3, capabilities: {...}}
       
       Note over Alice,Bob: Security Validation
       Bob->>VN_B: checkVersionDowngrade(3, [2, 3], policy)
       VN_B->>VN_B: Validate no downgrade attack
       VN_B-->>Bob: {isDowngrade: false, isAllowed: true}
       
       Note over Alice,Bob: Protocol Initialization
       Bob->>Bob: Initialize OTR v3 protocol
       Bob-->>Alice: Start AKE with version 3

Query Message Format
-------------------

Query messages follow the OTR specification format:

.. mermaid::

   graph TB
       subgraph "Query Message Types"
           Q1["?OTRv2? - OTR v2 only"]
           Q2["?OTRv3? - OTR v3 only"]
           Q3["?OTRv23? - Both v2 and v3"]
           Q4["?OTR? - Generic query"]
       end
       
       subgraph "Message Structure"
           PREFIX[Query Prefix]
           VERSION[Version Indicator]
           ACCOUNT[Account Information]
       end
       
       Q1 --> PREFIX
       Q2 --> PREFIX
       Q3 --> PREFIX
       Q4 --> PREFIX
       
       PREFIX --> VERSION
       VERSION --> ACCOUNT

API Reference
------------

VersionNegotiation Class
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   import { VersionNegotiation } from 'webotter/protocol';

   // Negotiate version with remote party
   const result = VersionNegotiation.negotiateVersion(
     [2, 3],  // Their supported versions
     {        // Our policy
       allowV2: true,
       allowV3: true,
       preferV3: true
     }
   );

   // Create query message
   const query = VersionNegotiation.createQueryMessage(
     '<EMAIL>',
     { allowV2: true, allowV3: true }
   );

   // Parse incoming query
   const parsed = VersionNegotiation.parseVersions(
     '?OTRv23? <EMAIL>'
   );

   // Validate version compatibility
   const compatible = VersionNegotiation.validateVersionCompatibility(
     3,  // Version to check
     ['instanceTags', 'extraSymmetricKey']  // Required features
   );

Method Details
~~~~~~~~~~~~~

**negotiateVersion(theirVersions, ourPolicy)**

Negotiates the optimal OTR version between two parties.

*Parameters:*
- ``theirVersions`` (Array<number>): Versions supported by remote party
- ``ourPolicy`` (Object): Local version policy configuration

*Returns:* Object with negotiation result

.. code-block:: javascript

   {
     version: 3,                    // Selected version
     capabilities: {...},           // Version capabilities
     commonVersions: [2, 3],       // Mutually supported versions
     negotiationSuccess: true,      // Success flag
     securityLevel: 'high'         // Security assessment
   }

**createQueryMessage(account, policy)**

Creates an OTR query message for version negotiation.

*Parameters:*
- ``account`` (string): Account identifier
- ``policy`` (Object): Version policy configuration

*Returns:* Object with query message and metadata

.. code-block:: javascript

   {
     message: '?OTRv23? <EMAIL>',  // Query message
     versions: [2, 3],                      // Supported versions
     account: '<EMAIL>',          // Account info
     timestamp: *************               // Creation timestamp
   }

**parseVersions(message)**

Parses an OTR query message to extract version information.

*Parameters:*
- ``message`` (string): OTR query message

*Returns:* Object with parsed version information

.. code-block:: javascript

   {
     versions: [2, 3],                    // Extracted versions
     account: '<EMAIL>',        // Account information
     originalMessage: '?OTRv23? <EMAIL>',
     parseSuccess: true                   // Parse success flag
   }

Security Considerations
----------------------

Version Downgrade Protection
~~~~~~~~~~~~~~~~~~~~~~~~~~~

The version negotiation system includes protection against version downgrade attacks:

.. mermaid::

   graph TB
       subgraph "Downgrade Attack Prevention"
           A[Receive Version List]
           B{Check Available Versions}
           C[Detect Potential Downgrade]
           D{Policy Allows Downgrade?}
           E[Accept Lower Version]
           F[Reject Downgrade Attack]
           G[Use Highest Available]
       end
       
       A --> B
       B --> C
       C --> D
       D -->|Yes| E
       D -->|No| F
       B -->|No Downgrade| G

Policy Enforcement
~~~~~~~~~~~~~~~~~

Version selection respects security policies:

.. code-block:: javascript

   const policy = {
     requireEncryption: true,    // Require encrypted protocols
     allowV2: true,            // Allow OTR v2
     allowV3: true,            // Allow OTR v3
     preferV3: true,           // Prefer v3 when available
     whitelistStartAKE: true,  // Allow whitespace tag AKE start
     errorStartAKE: true       // Allow error message AKE start
   };

Error Handling
~~~~~~~~~~~~~

The system handles various error conditions gracefully:

.. mermaid::

   graph TB
       subgraph "Error Scenarios"
           E1[Invalid Query Format]
           E2[Unsupported Versions]
           E3[Policy Violations]
           E4[Network Errors]
       end
       
       subgraph "Error Responses"
           R1[Parse Error Response]
           R2[No Compatible Versions]
           R3[Policy Rejection]
           R4[Retry with Fallback]
       end
       
       E1 --> R1
       E2 --> R2
       E3 --> R3
       E4 --> R4

Configuration Options
--------------------

Default Configuration
~~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   const defaultConfig = {
     supportedVersions: [2, 3],
     defaultVersion: 3,
     minimumVersion: 2,
     maximumVersion: 3,
     
     policies: {
       requireEncryption: true,
       allowV2: true,
       allowV3: true,
       preferV3: true,
       whitelistStartAKE: true,
       errorStartAKE: true
     }
   };

Custom Configuration
~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   // Restrict to OTR v3 only
   const strictConfig = {
     policies: {
       allowV2: false,
       allowV3: true,
       requireEncryption: true
     }
   };

   // Development/testing configuration
   const devConfig = {
     policies: {
       allowV2: true,
       allowV3: true,
       preferV3: false  // Allow v2 for testing
     }
   };

Integration Examples
-------------------

Basic Integration
~~~~~~~~~~~~~~~~

.. code-block:: javascript

   import { VersionNegotiation } from 'webotter/protocol';

   class OTRSession {
     async startSession(remoteAccount) {
       // Create and send query
       const query = VersionNegotiation.createQueryMessage(
         this.localAccount,
         this.versionPolicy
       );
       
       await this.sendMessage(query.message);
     }
     
     async handleQuery(queryMessage) {
       // Parse incoming query
       const parsed = VersionNegotiation.parseVersions(queryMessage);
       
       if (parsed.parseSuccess) {
         // Negotiate version
         const result = VersionNegotiation.negotiateVersion(
           parsed.versions,
           this.versionPolicy
         );
         
         if (result.negotiationSuccess) {
           // Initialize protocol with selected version
           await this.initializeProtocol(result.version);
         }
       }
     }
   }

Advanced Integration
~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   class EnhancedOTRSession extends OTRSession {
     async negotiateWithFallback(remoteVersions) {
       let policy = this.primaryPolicy;
       
       // Try primary policy first
       let result = VersionNegotiation.negotiateVersion(
         remoteVersions, 
         policy
       );
       
       // Fallback to secondary policy if needed
       if (!result.negotiationSuccess && this.fallbackPolicy) {
         result = VersionNegotiation.negotiateVersion(
           remoteVersions,
           this.fallbackPolicy
         );
       }
       
       // Validate security implications
       if (result.negotiationSuccess) {
         const downgradeCheck = VersionNegotiation.checkVersionDowngrade(
           result.version,
           remoteVersions,
           policy
         );
         
         if (!downgradeCheck.isAllowed) {
           throw new Error('Version downgrade attack detected');
         }
       }
       
       return result;
     }
   }

Testing and Validation
---------------------

The version negotiation system includes comprehensive testing:

.. code-block:: javascript

   // Test version negotiation
   describe('Version Negotiation', () => {
     test('should negotiate highest common version', () => {
       const result = VersionNegotiation.negotiateVersion([2, 3]);
       expect(result.version).toBe(3);
       expect(result.negotiationSuccess).toBe(true);
     });
     
     test('should detect downgrade attacks', () => {
       expect(() => {
         VersionNegotiation.checkVersionDowngrade(2, [2, 3], {
           allowV2: false
         });
       }).toThrow('Version downgrade attack detected');
     });
   });

.. note::
   Version negotiation is automatic and transparent to end users, but provides extensive configuration options for administrators and developers who need fine-grained control over protocol selection.
