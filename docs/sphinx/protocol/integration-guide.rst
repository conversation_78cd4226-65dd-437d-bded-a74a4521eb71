Integration Guide
================

This guide provides comprehensive instructions for integrating Phase 3 protocol features into existing WebOTR applications and new deployments.

Integration Overview
-------------------

Phase 3 features are designed to integrate seamlessly with existing WebOTR implementations while providing enhanced functionality for new deployments.

.. code-block:: mermaid

   graph TB
       subgraph "Integration Layers"
           subgraph "Application Layer"
               APP[Your Application]
               UI[User Interface]
               API[Application API]
           end
           
           subgraph "WebOTR Integration Layer"
               WRAPPER[WebOTR Wrapper]
               CONFIG[Configuration Manager]
               EVENT[Event Handlers]
           end
           
           subgraph "Phase 3 Protocol Layer"
               VN[Version Negotiation]
               MO[Message Ordering]
               ESMP[Enhanced SMP]
               PM[Policy Manager]
           end
           
           subgraph "Core WebOTR"
               CORE[Core Protocol]
               CRYPTO[Cryptography]
               TRANSPORT[Transport]
           end
       end
       
       APP --> WRAPPER
       UI --> CONFIG
       API --> EVENT
       
       WRAPPER --> VN
       CONFIG --> MO
       EVENT --> ESMP
       CONFIG --> PM
       
       VN --> CORE
       MO --> CORE
       ESMP --> CORE
       PM --> CRYPTO

Quick Start
----------

Basic Integration
~~~~~~~~~~~~~~~~

For basic integration with minimal configuration:

.. code-block:: javascript

   import { WebOTR } from 'webotter';
   import { PolicyManager, MessageOrdering, EnhancedSMP } from 'webotter/protocol';

   // Initialize WebOTR with Phase 3 features
   const otr = new WebOTR({
     // Enable Phase 3 features
     enableVersionNegotiation: true,
     enableMessageOrdering: true,
     enableEnhancedSMP: true,
     enablePolicyManager: true,
     
     // Basic configuration
     account: '<EMAIL>',
     policies: {
       'security.requireEncryption': true,
       'security.allowV3': true,
       'protocol.maxBufferSize': 100
     }
   });

   // Start OTR session with automatic version negotiation
   await otr.startSession('<EMAIL>');

Advanced Integration
~~~~~~~~~~~~~~~~~~~

For advanced integration with custom configuration:

.. code-block:: javascript

   import { 
     WebOTR, 
     VersionNegotiation, 
     MessageOrdering, 
     EnhancedSMP, 
     PolicyManager,
     VALIDATION_LEVEL,
     ACCESS_LEVEL
   } from 'webotter/protocol';

   class AdvancedWebOTRClient {
     constructor(options = {}) {
       // Initialize policy manager first
       this.policyManager = new PolicyManager({
         currentUser: options.userId,
         userRoles: options.userRoles || ['user'],
         validationLevel: VALIDATION_LEVEL.STRICT,
         enableAccessControl: true,
         enableAuditLogging: true
       });
       
       // Configure policies
       this.configurePolicies(options.environment);
       
       // Initialize message ordering
       this.messageOrdering = new MessageOrdering({
         maxBufferSize: this.policyManager.getPolicy('protocol.maxBufferSize'),
         replayWindowSize: this.policyManager.getPolicy('protocol.replayWindowSize'),
         gapTimeoutMs: this.policyManager.getPolicy('protocol.gapTimeoutMs')
       });
       
       // Initialize enhanced SMP
       this.enhancedSMP = new EnhancedSMP({
         enableStatePersistence: this.policyManager.getPolicy('protocol.enableStatePersistence'),
         sessionTimeoutMs: this.policyManager.getPolicy('security.sessionTimeoutMs'),
         enableDetailedLogging: this.policyManager.getPolicy('logging.enableDetailedLogging')
       });
       
       // Initialize core WebOTR
       this.otr = new WebOTR({
         account: options.account,
         versionNegotiation: VersionNegotiation,
         messageOrdering: this.messageOrdering,
         enhancedSMP: this.enhancedSMP,
         policyManager: this.policyManager
       });
       
       this.setupEventHandlers();
     }
     
     configurePolicies(environment) {
       const context = { environment: environment };
       
       // Set environment-specific policies
       if (environment === 'production') {
         this.policyManager.setPolicy('security.sessionTimeoutMs', 1800000, context);
         this.policyManager.setPolicy('logging.logLevel', 'warn', context);
         this.policyManager.setPolicy('performance.enableOptimizations', true, context);
       } else if (environment === 'development') {
         this.policyManager.setPolicy('security.sessionTimeoutMs', 300000, context);
         this.policyManager.setPolicy('logging.logLevel', 'debug', context);
         this.policyManager.setPolicy('logging.enableDetailedLogging', true, context);
       }
     }
     
     setupEventHandlers() {
       // Handle version negotiation events
       this.otr.on('versionNegotiated', (event) => {
         console.log('OTR version negotiated:', event.version);
         this.updateUIForVersion(event.version);
       });
       
       // Handle message ordering events
       this.otr.on('messageReordered', (event) => {
         console.log('Messages reordered:', event.count);
         this.updateMessageDisplay(event.messages);
       });
       
       // Handle enhanced SMP events
       this.otr.on('smpStateChanged', (event) => {
         console.log('SMP state changed:', event.state);
         this.updateSMPUI(event);
       });
       
       // Handle policy changes
       this.policyManager.addChangeListener((event) => {
         console.log('Policy changed:', event.key, event.newValue);
         this.applyPolicyChange(event);
       });
     }
   }

Migration from Existing WebOTR
------------------------------

Migrating from existing WebOTR implementations:

.. code-block:: mermaid

   graph TB
       subgraph "Migration Process"
           ASSESS[Assess Current Implementation]
           PLAN[Create Migration Plan]
           BACKUP[Backup Current Configuration]
           INSTALL[Install Phase 3 Features]
           CONFIG[Configure New Features]
           TEST[Test Integration]
           DEPLOY[Deploy to Production]
       end
       
       subgraph "Migration Considerations"
           COMPAT[Backward Compatibility]
           CONFIG_MIGRATE[Configuration Migration]
           DATA_MIGRATE[Data Migration]
           PERF[Performance Impact]
           SECURITY[Security Enhancements]
       end
       
       ASSESS --> PLAN
       PLAN --> BACKUP
       BACKUP --> INSTALL
       INSTALL --> CONFIG
       CONFIG --> TEST
       TEST --> DEPLOY
       
       ASSESS -.-> COMPAT
       PLAN -.-> CONFIG_MIGRATE
       BACKUP -.-> DATA_MIGRATE
       CONFIG -.-> PERF
       TEST -.-> SECURITY

Step-by-Step Migration
~~~~~~~~~~~~~~~~~~~~~

**Step 1: Assessment**

.. code-block:: javascript

   // Assess current WebOTR usage
   const currentConfig = {
     version: otr.getVersion(),
     features: otr.getSupportedFeatures(),
     configuration: otr.getConfiguration(),
     sessions: otr.getActiveSessions()
   };
   
   console.log('Current WebOTR configuration:', currentConfig);

**Step 2: Install Phase 3 Features**

.. code-block:: bash

   # Update WebOTR to include Phase 3 features
   npm install webotter@latest
   
   # Or if using yarn
   yarn add webotter@latest

**Step 3: Gradual Feature Enablement**

.. code-block:: javascript

   // Enable features gradually
   const migrationConfig = {
     // Start with version negotiation only
     enableVersionNegotiation: true,
     enableMessageOrdering: false,  // Enable later
     enableEnhancedSMP: false,      // Enable later
     enablePolicyManager: false,    // Enable later
     
     // Maintain existing configuration
     ...existingConfig
   };
   
   const otr = new WebOTR(migrationConfig);

**Step 4: Configuration Migration**

.. code-block:: javascript

   // Migrate existing configuration to policy manager
   function migrateConfiguration(existingConfig) {
     const policyManager = new PolicyManager();
     
     // Map existing config to policies
     const configMapping = {
       'requireEncryption': 'security.requireEncryption',
       'sessionTimeout': 'security.sessionTimeoutMs',
       'maxBufferSize': 'protocol.maxBufferSize',
       'enableLogging': 'logging.enableDetailedLogging'
     };
     
     for (const [oldKey, newKey] of Object.entries(configMapping)) {
       if (existingConfig[oldKey] !== undefined) {
         policyManager.setPolicy(newKey, existingConfig[oldKey], {
           source: 'migration',
           timestamp: Date.now()
         });
       }
     }
     
     return policyManager;
   }

**Step 5: Testing and Validation**

.. code-block:: javascript

   // Comprehensive testing after migration
   async function validateMigration() {
     const tests = [
       testVersionNegotiation,
       testMessageOrdering,
       testEnhancedSMP,
       testPolicyManager,
       testBackwardCompatibility
     ];
     
     const results = [];
     for (const test of tests) {
       try {
         const result = await test();
         results.push({ test: test.name, status: 'PASS', result });
       } catch (error) {
         results.push({ test: test.name, status: 'FAIL', error: error.message });
       }
     }
     
     return results;
   }

Configuration Patterns
----------------------

Common configuration patterns for different use cases:

Enterprise Deployment
~~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   const enterpriseConfig = {
     policyManager: {
       validationLevel: 'enterprise',
       enableAccessControl: true,
       enableAuditLogging: true,
       userRoles: ['user', 'admin', 'security_officer']
     },
     
     security: {
       'security.requireEncryption': true,
       'security.allowV2': false,  // Only OTR v3
       'security.sessionTimeoutMs': 1800000,  // 30 minutes
       'security.enableSecurityValidation': true
     },
     
     protocol: {
       'protocol.maxBufferSize': 200,
       'protocol.replayWindowSize': 128,
       'protocol.enableStatePersistence': true
     },
     
     logging: {
       'logging.enableSecurityAudit': true,
       'logging.logLevel': 'warn',
       'logging.maxLogSize': 104857600  // 100MB
     }
   };

Development Environment
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   const developmentConfig = {
     policyManager: {
       validationLevel: 'basic',
       enableAccessControl: false,
       enableAuditLogging: true
     },
     
     security: {
       'security.requireEncryption': true,
       'security.allowV2': true,  // Allow both versions
       'security.sessionTimeoutMs': 300000,  // 5 minutes
       'security.enableSecurityValidation': true
     },
     
     protocol: {
       'protocol.maxBufferSize': 50,
       'protocol.replayWindowSize': 32
     },
     
     logging: {
       'logging.enableDetailedLogging': true,
       'logging.logLevel': 'debug'
     }
   };

Mobile Application
~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   const mobileConfig = {
     policyManager: {
       validationLevel: 'strict',
       enableAccessControl: true
     },
     
     security: {
       'security.sessionTimeoutMs': 600000,  // 10 minutes
       'security.enableSecurityValidation': true
     },
     
     protocol: {
       'protocol.maxBufferSize': 25,  // Smaller buffer for memory
       'protocol.replayWindowSize': 32,
       'protocol.gapTimeoutMs': 15000  // Shorter timeout
     },
     
     performance: {
       'performance.enableOptimizations': true,
       'performance.memoryPoolSize': 524288,  // 512KB
       'performance.enableCaching': false  // Save memory
     }
   };

Event Handling
--------------

Comprehensive event handling for Phase 3 features:

.. code-block:: mermaid

   graph TB
       subgraph "Event Categories"
           VN_EVENTS[Version Negotiation Events]
           MO_EVENTS[Message Ordering Events]
           SMP_EVENTS[Enhanced SMP Events]
           POLICY_EVENTS[Policy Manager Events]
       end
       
       subgraph "Event Handlers"
           VN_HANDLER[Version Handler]
           MO_HANDLER[Ordering Handler]
           SMP_HANDLER[SMP Handler]
           POLICY_HANDLER[Policy Handler]
       end
       
       subgraph "Application Actions"
           UI_UPDATE[Update UI]
           LOG_EVENT[Log Event]
           NOTIFY_USER[Notify User]
           UPDATE_CONFIG[Update Configuration]
       end
       
       VN_EVENTS --> VN_HANDLER
       MO_EVENTS --> MO_HANDLER
       SMP_EVENTS --> SMP_HANDLER
       POLICY_EVENTS --> POLICY_HANDLER
       
       VN_HANDLER --> UI_UPDATE
       MO_HANDLER --> LOG_EVENT
       SMP_HANDLER --> NOTIFY_USER
       POLICY_HANDLER --> UPDATE_CONFIG

Event Handler Implementation
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   class WebOTREventHandler {
     constructor(otr, ui) {
       this.otr = otr;
       this.ui = ui;
       this.setupEventHandlers();
     }
     
     setupEventHandlers() {
       // Version negotiation events
       this.otr.on('versionNegotiationStarted', (event) => {
         this.ui.showStatus('Negotiating OTR version...');
       });
       
       this.otr.on('versionNegotiated', (event) => {
         this.ui.showStatus(`Using OTR v${event.version}`);
         this.ui.updateCapabilities(event.capabilities);
       });
       
       this.otr.on('versionNegotiationFailed', (event) => {
         this.ui.showError(`Version negotiation failed: ${event.reason}`);
       });
       
       // Message ordering events
       this.otr.on('messageBuffered', (event) => {
         this.ui.showStatus(`Message ${event.sequence} buffered (waiting for ${event.expected})`);
       });
       
       this.otr.on('messagesReordered', (event) => {
         this.ui.showStatus(`${event.count} messages reordered`);
         event.messages.forEach(msg => this.ui.displayMessage(msg));
       });
       
       this.otr.on('replayDetected', (event) => {
         this.ui.showWarning(`Replay attack detected: message ${event.sequence}`);
       });
       
       // Enhanced SMP events
       this.otr.on('smpSessionStarted', (event) => {
         this.ui.showSMPDialog(event.sessionId, event.question);
       });
       
       this.otr.on('smpSessionPaused', (event) => {
         this.ui.showStatus(`SMP session paused: ${event.reason}`);
       });
       
       this.otr.on('smpSessionCompleted', (event) => {
         this.ui.showSMPResult(event.result, event.sessionId);
       });
       
       this.otr.on('smpSessionAborted', (event) => {
         this.ui.showError(`SMP aborted: ${event.reason}`);
       });
       
       // Policy manager events
       this.otr.policyManager.addChangeListener((event) => {
         this.handlePolicyChange(event);
       });
     }
     
     handlePolicyChange(event) {
       // Handle security policy changes
       if (event.key.startsWith('security.')) {
         this.ui.showSecurityNotification(`Security policy updated: ${event.key}`);
         
         // Apply immediate changes if needed
         if (event.key === 'security.sessionTimeoutMs') {
           this.otr.updateSessionTimeout(event.newValue);
         }
       }
       
       // Handle protocol policy changes
       if (event.key.startsWith('protocol.')) {
         this.ui.showStatus(`Protocol configuration updated: ${event.key}`);
       }
       
       // Log all policy changes
       console.log('Policy changed:', {
         key: event.key,
         oldValue: event.oldValue,
         newValue: event.newValue,
         user: event.context.user,
         timestamp: event.timestamp
       });
     }
   }

Performance Optimization
-----------------------

Optimizing Phase 3 features for different environments:

.. code-block:: javascript

   // Performance optimization configuration
   const performanceConfig = {
     // Message ordering optimization
     messageOrdering: {
       maxBufferSize: 50,        // Smaller buffer for memory-constrained environments
       cleanupIntervalMs: 30000, // More frequent cleanup
       gapTimeoutMs: 15000      // Shorter gap timeout
     },
     
     // Enhanced SMP optimization
     enhancedSMP: {
       enableStatePersistence: false,  // Disable for performance
       sessionTimeoutMs: 180000,       // Shorter timeout
       maxRetryAttempts: 2             // Fewer retries
     },
     
     // Policy manager optimization
     policyManager: {
       validationLevel: 'basic',       // Less validation overhead
       enableAuditLogging: false,      // Disable for performance
       maxPolicySize: 524288           // Smaller policy size limit
     }
   };

Troubleshooting
--------------

Common integration issues and solutions:

**Version Negotiation Issues**

.. code-block:: javascript

   // Debug version negotiation
   if (!otr.isVersionNegotiated()) {
     const debug = otr.getVersionNegotiationDebug();
     console.log('Version negotiation debug:', debug);
     
     // Check policy restrictions
     const policies = otr.policyManager.getEffectivePolicy();
     console.log('Version policies:', {
       allowV2: policies.security.allowV2,
       allowV3: policies.security.allowV3,
       preferV3: policies.security.preferV3
     });
   }

**Message Ordering Issues**

.. code-block:: javascript

   // Debug message ordering
   const orderingStats = otr.messageOrdering.getStats();
   console.log('Message ordering stats:', orderingStats);
   
   if (orderingStats.duplicatesDetected > 0) {
     console.warn('Replay attacks detected:', orderingStats.duplicatesDetected);
   }
   
   if (orderingStats.gapsDetected > 0) {
     console.warn('Message gaps detected:', orderingStats.gapsDetected);
   }

**Policy Manager Issues**

.. code-block:: javascript

   // Debug policy issues
   const policyStats = otr.policyManager.getStats();
   console.log('Policy manager stats:', policyStats);
   
   if (policyStats.accessDenials > 0) {
     console.warn('Policy access denials:', policyStats.accessDenials);
     
     // Check user roles and permissions
     console.log('Current user:', otr.policyManager.currentUser);
     console.log('User roles:', Array.from(otr.policyManager.userRoles));
   }

.. note::
   This integration guide provides comprehensive instructions for implementing Phase 3 features. For specific use cases or advanced configurations, refer to the individual component documentation and API references.
