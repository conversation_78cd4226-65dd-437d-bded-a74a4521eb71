Message Ordering
===============

The Message Ordering module provides robust handling of out-of-order messages, replay protection, and sequence validation to ensure reliable and secure message delivery in OTR sessions.

Overview
--------

Message ordering is critical for maintaining the integrity and security of OTR communications, especially in unreliable network conditions where messages may arrive out of order or be duplicated.

.. mermaid::

   graph TB
       subgraph "Message Ordering System"
           IN[Incoming Message]
           SV[Sequence Validation]
           RP[Replay Protection]
           OO[Out-of-Order Handling]
           BUF[Message Buffer]
           PROC[Process Message]
           OUT[Ordered Output]
       end
       
       IN --> SV
       SV --> RP
       RP --> OO
       OO --> BUF
       BUF --> PROC
       PROC --> OUT

Core Features
------------

Sequence Number Validation
~~~~~~~~~~~~~~~~~~~~~~~~~

Every message includes a sequence number that must be validated:

.. mermaid::

   graph LR
       subgraph "Sequence Validation"
           MSG[Message with Seq N]
           EXP[Expected Seq M]
           CMP{N vs M}
           IN_ORDER[N = M: In Order]
           FUTURE[N > M: Future Message]
           PAST[N < M: Past Message]
       end
       
       MSG --> CMP
       EXP --> CMP
       CMP --> IN_ORDER
       CMP --> FUTURE
       CMP --> PAST

Replay Protection
~~~~~~~~~~~~~~~~

The system uses a sliding window to detect and prevent replay attacks:

.. mermaid::

   graph TB
       subgraph "Sliding Window Replay Protection"
           subgraph "Window State"
               BASE[Window Base: 100]
               SIZE[Window Size: 64]
               END[Window End: 164]
           end
           
           subgraph "Message Validation"
               MSG[Message Seq: 150]
               CHECK{In Window?}
               SEEN{Already Seen?}
               ACCEPT[Accept Message]
               REJECT[Reject Replay]
           end
       end
       
       MSG --> CHECK
       CHECK -->|Yes| SEEN
       CHECK -->|No| REJECT
       SEEN -->|No| ACCEPT
       SEEN -->|Yes| REJECT

Out-of-Order Message Handling
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Messages arriving out of order are buffered and reordered:

.. mermaid::

   sequenceDiagram
       participant Network as Network
       participant MO as Message Ordering
       participant Buffer as Message Buffer
       participant App as Application
       
       Note over Network,App: Messages Arrive Out of Order
       Network->>MO: Message Seq 5
       MO->>Buffer: Buffer message 5
       MO-->>Network: Waiting for 3, 4
       
       Network->>MO: Message Seq 3
       MO->>MO: Process message 3
       MO->>App: Deliver message 3
       
       Network->>MO: Message Seq 4
       MO->>MO: Process message 4
       MO->>App: Deliver message 4
       MO->>Buffer: Check for ready messages
       Buffer-->>MO: Message 5 ready
       MO->>App: Deliver message 5

Message Flow States
------------------

Messages can be in various states during processing:

.. mermaid::

   stateDiagram-v2
       [*] --> Received
       Received --> Validating: Check sequence
       Validating --> InOrder: Seq = Expected
       Validating --> OutOfOrder: Seq > Expected
       Validating --> Duplicate: Seq < Expected
       
       InOrder --> Processing: Immediate process
       OutOfOrder --> Buffered: Store for later
       Duplicate --> Rejected: Replay detected
       
       Buffered --> Ready: Gap filled
       Ready --> Processing: Process in order
       Processing --> Delivered: Send to application
       
       Rejected --> [*]
       Delivered --> [*]

API Reference
------------

MessageOrdering Class
~~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   import { MessageOrdering } from 'webotter/protocol';

   // Create message ordering instance
   const ordering = new MessageOrdering({
     maxBufferSize: 100,      // Maximum buffered messages
     maxGapSize: 10,          // Maximum sequence gap
     replayWindowSize: 64,    // Replay protection window
     gapTimeoutMs: 30000,     // Gap timeout
     cleanupIntervalMs: 60000 // Cleanup interval
   });

   // Validate incoming message
   const result = ordering.validateIncomingMessage({
     sequence: 5,
     data: 'message content'
   });

   // Handle out-of-order message
   const bufferResult = ordering.handleOutOfOrder(message, currentSequence);

   // Get next sequence for outgoing message
   const nextSeq = ordering.getNextSendSequence();

Method Details
~~~~~~~~~~~~~

**validateIncomingMessage(message, expectedSequence)**

Validates an incoming message and determines processing action.

*Parameters:*
- ``message`` (Object): Message with sequence number
- ``expectedSequence`` (number): Expected sequence number (optional)

*Returns:* Validation result object

.. code-block:: javascript

   {
     action: 'PROCESS',           // Action to take
     reason: 'In order message',  // Reason for action
     state: 'PROCESSED',          // Message state
     sequence: 5,                 // Message sequence
     readyMessages: []            // Additional ready messages
   }

**handleOutOfOrder(message, currentSequence)**

Handles out-of-order message buffering and reordering.

*Parameters:*
- ``message`` (Object): Out-of-order message
- ``currentSequence`` (number): Current sequence number

*Returns:* Buffer operation result

.. code-block:: javascript

   {
     action: 'BUFFERED',          // Buffer action taken
     reason: 'Out of order message buffered',
     readyMessages: [],           // Messages now ready
     pendingCount: 3              // Total pending messages
   }

**detectMessageGaps(receivedSequence, expectedSequence)**

Detects gaps in message sequence.

*Parameters:*
- ``receivedSequence`` (number): Received sequence number
- ``expectedSequence`` (number): Expected sequence number

*Returns:* Gap detection result

.. code-block:: javascript

   {
     hasGap: true,                // Gap detected
     gapSize: 3,                  // Size of gap
     missingSequences: [2, 3, 4]  // Missing sequence numbers
   }

Configuration Options
--------------------

Default Configuration
~~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   const defaultConfig = {
     maxBufferSize: 100,        // Maximum out-of-order messages to buffer
     maxGapSize: 10,           // Maximum gap in sequence numbers
     replayWindowSize: 64,     // Size of replay protection window
     maxSequenceNumber: 0xFFFFFFFF, // Maximum sequence number (32-bit)
     gapTimeoutMs: 30000,      // Timeout for waiting for missing messages
     cleanupIntervalMs: 60000  // Interval for cleanup operations
   };

Performance Tuning
~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   // High-throughput configuration
   const highThroughputConfig = {
     maxBufferSize: 200,
     replayWindowSize: 128,
     cleanupIntervalMs: 30000
   };

   // Low-latency configuration
   const lowLatencyConfig = {
     maxBufferSize: 50,
     maxGapSize: 5,
     gapTimeoutMs: 10000
   };

   // Memory-constrained configuration
   const memoryConstrainedConfig = {
     maxBufferSize: 25,
     replayWindowSize: 32,
     cleanupIntervalMs: 120000
   };

Message Processing Flow
----------------------

The complete message processing flow:

.. mermaid::

   flowchart TD
       START[Receive Message] --> VALIDATE[Validate Structure]
       VALIDATE --> SEQ_CHECK[Check Sequence Number]
       
       SEQ_CHECK --> IN_ORDER{In Order?}
       IN_ORDER -->|Yes| REPLAY_CHECK[Check Replay Protection]
       IN_ORDER -->|No| OUT_OF_ORDER{Future Message?}
       
       OUT_OF_ORDER -->|Yes| BUFFER[Buffer Message]
       OUT_OF_ORDER -->|No| DUPLICATE[Handle Duplicate]
       
       REPLAY_CHECK --> VALID{Valid?}
       VALID -->|Yes| PROCESS[Process Message]
       VALID -->|No| REJECT[Reject Message]
       
       BUFFER --> CHECK_READY[Check Ready Messages]
       CHECK_READY --> READY{Messages Ready?}
       READY -->|Yes| PROCESS_READY[Process Ready Messages]
       READY -->|No| WAIT[Wait for Gap Fill]
       
       PROCESS --> UPDATE_STATE[Update Sequence State]
       PROCESS_READY --> UPDATE_STATE
       UPDATE_STATE --> DELIVER[Deliver to Application]
       
       DUPLICATE --> LOG[Log Duplicate]
       REJECT --> LOG
       WAIT --> TIMEOUT{Timeout?}
       TIMEOUT -->|Yes| CLEANUP[Cleanup Expired]
       TIMEOUT -->|No| WAIT
       
       DELIVER --> END[Complete]
       LOG --> END
       CLEANUP --> END

Error Handling
--------------

The system handles various error conditions:

.. mermaid::

   graph TB
       subgraph "Error Types"
           E1[Invalid Message Structure]
           E2[Sequence Out of Range]
           E3[Buffer Overflow]
           E4[Gap Too Large]
           E5[Replay Attack]
       end
       
       subgraph "Error Responses"
           R1[Validation Error]
           R2[Range Error]
           R3[Buffer Management]
           R4[Gap Rejection]
           R5[Security Alert]
       end
       
       subgraph "Recovery Actions"
           A1[Request Retransmission]
           A2[Reset Sequence State]
           A3[Evict Oldest Messages]
           A4[Skip Missing Messages]
           A5[Log Security Event]
       end
       
       E1 --> R1 --> A1
       E2 --> R2 --> A2
       E3 --> R3 --> A3
       E4 --> R4 --> A4
       E5 --> R5 --> A5

Performance Characteristics
--------------------------

The message ordering system is optimized for performance:

.. mermaid::

   graph LR
       subgraph "Performance Metrics"
           subgraph "Latency"
               L1[In-order: < 1ms]
               L2[Out-of-order: < 10ms]
               L3[Gap detection: < 5ms]
           end
           
           subgraph "Throughput"
               T1[10,000+ messages/sec]
               T2[1,000+ out-of-order/sec]
               T3[100+ gaps/sec]
           end
           
           subgraph "Memory"
               M1[< 10KB buffer typical]
               M2[< 1KB per message]
               M3[O(1) lookup time]
           end
       end

Security Considerations
----------------------

Replay Attack Prevention
~~~~~~~~~~~~~~~~~~~~~~

The sliding window mechanism prevents replay attacks:

.. code-block:: javascript

   // Replay protection validation
   const isValidMessage = ordering.validateReplayProtection(message, windowSize);
   
   if (!isValidMessage) {
     // Message is a replay - reject and log
     securityLogger.logReplayAttempt(message);
     return false;
   }

Sequence Number Security
~~~~~~~~~~~~~~~~~~~~~~

Sequence numbers are validated to prevent manipulation:

.. code-block:: javascript

   // Validate sequence number range
   if (sequence < 0 || sequence > MAX_SEQUENCE) {
     throw new SecurityValidationError('Sequence number out of range');
   }
   
   // Check for sequence number rollover
   if (sequence < lastSequence && (lastSequence - sequence) > ROLLOVER_THRESHOLD) {
     // Handle sequence number rollover
     handleSequenceRollover();
   }

Buffer Security
~~~~~~~~~~~~~~

Message buffers are protected against overflow attacks:

.. code-block:: javascript

   // Enforce buffer size limits
   if (pendingMessages.size >= maxBufferSize) {
     // Evict oldest message to make room
     const oldestSequence = Math.min(...pendingMessages.keys());
     pendingMessages.delete(oldestSequence);
     stats.timeouts++;
   }

Integration Examples
-------------------

Basic Integration
~~~~~~~~~~~~~~~~

.. code-block:: javascript

   import { MessageOrdering } from 'webotter/protocol';

   class OTRMessaging {
     constructor() {
       this.ordering = new MessageOrdering({
         maxBufferSize: 100,
         replayWindowSize: 64
       });
     }
     
     async handleIncomingMessage(message) {
       const result = this.ordering.validateIncomingMessage(message);
       
       switch (result.action) {
         case 'PROCESS':
           await this.processMessage(message);
           // Process any ready buffered messages
           for (const ready of result.readyMessages) {
             await this.processMessage(ready.message);
           }
           break;
           
         case 'BUFFER':
           // Message buffered, waiting for earlier messages
           this.logInfo(`Message ${message.sequence} buffered`);
           break;
           
         case 'REJECT':
           // Replay or invalid message
           this.logWarning(`Message ${message.sequence} rejected: ${result.reason}`);
           break;
       }
     }
     
     async sendMessage(content) {
       const sequence = this.ordering.getNextSendSequence();
       const message = {
         sequence: sequence,
         content: content,
         timestamp: Date.now()
       };
       
       await this.transmitMessage(message);
       this.ordering.updateSequenceTracking(sequence);
     }
   }

Advanced Integration
~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   class EnhancedOTRMessaging extends OTRMessaging {
     constructor(options = {}) {
       super();
       this.gapRecovery = options.enableGapRecovery || true;
       this.metrics = new MessageMetrics();
     }
     
     async handleIncomingMessage(message) {
       const startTime = performance.now();
       
       try {
         const result = await super.handleIncomingMessage(message);
         
         // Handle gap recovery
         if (result.action === 'BUFFER' && this.gapRecovery) {
           await this.requestMissingMessages(result.gap);
         }
         
         // Update metrics
         this.metrics.recordMessage(result.action, performance.now() - startTime);
         
         return result;
         
       } catch (error) {
         this.metrics.recordError(error);
         throw error;
       }
     }
     
     async requestMissingMessages(gap) {
       if (gap && gap.missingSequences) {
         for (const seq of gap.missingSequences) {
           await this.sendRetransmissionRequest(seq);
         }
       }
     }
     
     getOrderingStatistics() {
       return {
         ...this.ordering.getStats(),
         ...this.metrics.getStats()
       };
     }
   }

Testing and Validation
---------------------

Comprehensive testing ensures reliability:

.. code-block:: javascript

   describe('Message Ordering', () => {
     test('should handle out-of-order messages', () => {
       const ordering = new MessageOrdering();
       
       // Send messages out of order: 0, 2, 1
       const result1 = ordering.validateIncomingMessage({ sequence: 0 });
       expect(result1.action).toBe('PROCESS');
       
       const result2 = ordering.validateIncomingMessage({ sequence: 2 });
       expect(result2.action).toBe('BUFFER');
       
       const result3 = ordering.validateIncomingMessage({ sequence: 1 });
       expect(result3.action).toBe('PROCESS');
       expect(result3.readyMessages).toHaveLength(1);
       expect(result3.readyMessages[0].sequence).toBe(2);
     });
     
     test('should detect replay attacks', () => {
       const ordering = new MessageOrdering();
       
       // Process message
       ordering.validateIncomingMessage({ sequence: 0 });
       
       // Try to replay same message
       const result = ordering.validateIncomingMessage({ sequence: 0 });
       expect(result.action).toBe('REJECT');
       expect(result.reason).toBe('REPLAY_DETECTED');
     });
   });

.. note::
   Message ordering is transparent to applications but provides extensive monitoring and configuration options for performance tuning and security analysis.
