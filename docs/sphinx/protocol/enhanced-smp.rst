Enhanced SMP
============

The Enhanced Socialist Millionaire Protocol (SMP) extends the base SMP implementation with advanced features including state persistence, session management, comprehensive abort handling, and detailed diagnostics.

Overview
--------

Enhanced SMP builds upon the standard SMP protocol to provide enterprise-grade features for production deployments, including session resumption, detailed monitoring, and robust error recovery.

.. mermaid::

   graph TB
       subgraph "Enhanced SMP Features"
           subgraph "Core SMP"
               SMP1[SMP1 Message]
               SMP2[SMP2 Message]
               SMP3[SMP3 Message]
               SMP4[SMP4 Message]
           end
           
           subgraph "Enhanced Features"
               SP[State Persistence]
               SM[Session Management]
               AH[Advanced Abort Handling]
               DIAG[Comprehensive Diagnostics]
           end
           
           subgraph "Security Enhancements"
               SEC_MEM[Secure Memory]
               SEC_STATE[Secure State Storage]
               SEC_AUDIT[Security Audit Logging]
           end
       end
       
       SMP1 --> SP
       SMP2 --> SM
       SMP3 --> AH
       SMP4 --> DIAG
       
       SP --> SEC_MEM
       SM --> SEC_STATE
       AH --> SEC_AUDIT

Enhanced SMP States
-------------------

Enhanced SMP introduces additional states for better session management:

.. mermaid::

   stateDiagram-v2
       [*] --> IDLE
       IDLE --> INITIATING: initiateSMP()
       IDLE --> RESPONDING: respondToSMP()
       
       INITIATING --> IN_PROGRESS: SMP1 sent
       RESPONDING --> IN_PROGRESS: SMP2 sent
       
       IN_PROGRESS --> PAUSED: pauseSession()
       PAUSED --> IN_PROGRESS: resumeSession()
       
       IN_PROGRESS --> COMPLETING: Final validation
       COMPLETING --> COMPLETED: Success
       COMPLETING --> FAILED: Validation failed
       
       IN_PROGRESS --> ABORTED: handleAbort()
       PAUSED --> ABORTED: handleAbort()
       
       COMPLETED --> IDLE: reset()
       FAILED --> IDLE: reset()
       ABORTED --> IDLE: reset()

SMP Session Lifecycle
---------------------

The complete lifecycle of an Enhanced SMP session:

.. mermaid::

   sequenceDiagram
       participant Alice as Alice
       participant ESMP_A as Enhanced SMP A
       participant Storage as Secure Storage
       participant ESMP_B as Enhanced SMP B
       participant Bob as Bob
       
       Note over Alice,Bob: Session Initialization
       Alice->>ESMP_A: initiateEnhancedSMP("shared-secret")
       ESMP_A->>ESMP_A: Generate session ID
       ESMP_A->>Storage: Store secret securely
       ESMP_A->>ESMP_A: Set session timeout
       ESMP_A-->>Alice: SMP1 message + session info
       
       Note over Alice,Bob: State Persistence
       ESMP_A->>Storage: persistState()
       Storage-->>ESMP_A: State encrypted and stored
       
       Note over Alice,Bob: Message Exchange
       Alice->>Bob: Send SMP1 message
       Bob->>ESMP_B: Process SMP1
       ESMP_B->>ESMP_B: Validate and respond
       ESMP_B-->>Bob: SMP2 message
       
       Note over Alice,Bob: Session Management
       Bob->>ESMP_B: pauseSession("user_request")
       ESMP_B->>ESMP_B: Clear timeout, save state
       ESMP_B-->>Bob: Session paused
       
       Bob->>ESMP_B: resumeSession()
       ESMP_B->>ESMP_B: Restore timeout, validate state
       ESMP_B-->>Bob: Session resumed
       
       Note over Alice,Bob: Completion or Abort
       alt Successful completion
           ESMP_A->>ESMP_A: Verify shared secret
           ESMP_A-->>Alice: SMP SUCCESS
       else Abort scenario
           Alice->>ESMP_A: handleAbort("user_abort")
           ESMP_A->>Storage: Secure cleanup
           ESMP_A-->>Alice: Abort message with reason
       end

State Persistence
----------------

Enhanced SMP provides secure state persistence for session resumption:

.. mermaid::

   graph TB
       subgraph "State Persistence Flow"
           STATE[Current SMP State]
           SNAPSHOT[Create State Snapshot]
           ENCRYPT[Encrypt Sensitive Data]
           STORE[Store to Storage]
           
           RETRIEVE[Retrieve from Storage]
           DECRYPT[Decrypt State Data]
           VALIDATE[Validate State Integrity]
           RESTORE[Restore SMP State]
       end
       
       subgraph "State Components"
           SESSION[Session ID]
           STAGE[SMP Stage]
           SECRETS[Cryptographic Secrets]
           METADATA[Session Metadata]
           TIMESTAMP[State Timestamp]
       end
       
       STATE --> SNAPSHOT
       SNAPSHOT --> ENCRYPT
       ENCRYPT --> STORE
       
       RETRIEVE --> DECRYPT
       DECRYPT --> VALIDATE
       VALIDATE --> RESTORE
       
       SNAPSHOT -.-> SESSION
       SNAPSHOT -.-> STAGE
       SNAPSHOT -.-> SECRETS
       SNAPSHOT -.-> METADATA
       SNAPSHOT -.-> TIMESTAMP

API Reference
------------

EnhancedSMP Class
~~~~~~~~~~~~~~~~

.. code-block:: javascript

   import { EnhancedSMP, SMP_ABORT_REASON, ENHANCED_SMP_STATE } from 'webotter/protocol';

   // Create enhanced SMP instance
   const smp = new EnhancedSMP({
     sessionTimeoutMs: 300000,      // 5 minutes
     maxRetryAttempts: 3,           // Maximum retries
     enableStatePersistence: true,  // Enable state persistence
     enableDetailedLogging: false,  // Detailed logging
     enableSecurityValidation: true // Security validation
   });

   // Initiate enhanced SMP
   const smp1 = await smp.initiateEnhancedSMP('shared-secret', {
     question: 'What is our shared secret?',
     timeout: 600000  // 10 minutes
   });

   // Respond to enhanced SMP
   const smp2 = await smp.respondToEnhancedSMP('shared-secret');

   // Handle abort with reason
   const abortMsg = smp.handleAbort(SMP_ABORT_REASON.USER_ABORT, {
     reason: 'User cancelled operation'
   });

Method Details
~~~~~~~~~~~~~

**initiateEnhancedSMP(secret, options)**

Initiates an enhanced SMP session with comprehensive options.

*Parameters:*
- ``secret`` (string): Shared secret for verification
- ``options`` (Object): Enhanced SMP options

*Returns:* Promise resolving to enhanced SMP1 message

.. code-block:: javascript

   {
     type: 'SMP1',
     sessionId: 'smp_abc123_def456',
     timestamp: 1640995200000,
     version: '1.0',
     capabilities: {
       statePersistence: true,
       sessionResumption: true,
       enhancedAbort: true
     }
   }

**respondToEnhancedSMP(secret, options)**

Responds to an enhanced SMP initiation.

*Parameters:*
- ``secret`` (string): Shared secret for verification
- ``options`` (Object): Response options

*Returns:* Promise resolving to enhanced SMP2 message

.. code-block:: javascript

   {
     type: 'SMP2',
     sessionId: 'smp_abc123_def456',
     timestamp: 1640995200000,
     responseTime: 1500  // Time to respond in ms
   }

**handleAbort(reason, context)**

Handles SMP abort with detailed reason and context.

*Parameters:*
- ``reason`` (string): Abort reason code
- ``context`` (Object): Additional context information

*Returns:* Enhanced abort message

.. code-block:: javascript

   {
     type: 'SMP_ABORT',
     sessionId: 'smp_abc123_def456',
     reason: 'user_abort',
     timestamp: 1640995200000,
     context: {
       userAction: 'cancel',
       sessionDuration: 45000
     }
   }

**persistState(storage)**

Persists current SMP state securely.

*Parameters:*
- ``storage`` (Object): Optional storage interface

*Returns:* Promise resolving to encrypted state data

.. code-block:: javascript

   {
     encrypted: 'base64-encoded-encrypted-state',
     timestamp: 1640995200000,
     version: '1.0'
   }

**resumeFromState(state, validation)**

Resumes SMP session from persisted state.

*Parameters:*
- ``state`` (Object): Persisted state data
- ``validation`` (Object): Validation parameters

*Returns:* Promise resolving to boolean success indicator

Session Management
-----------------

Enhanced SMP provides comprehensive session management capabilities:

.. mermaid::

   graph TB
       subgraph "Session Operations"
           START[Start Session]
           PAUSE[Pause Session]
           RESUME[Resume Session]
           ABORT[Abort Session]
           COMPLETE[Complete Session]
       end
       
       subgraph "Session State"
           ACTIVE[Active Session]
           PAUSED_STATE[Paused State]
           TIMEOUT[Timeout Management]
           CLEANUP[Secure Cleanup]
       end
       
       subgraph "Session Monitoring"
           METRICS[Session Metrics]
           DIAGNOSTICS[Diagnostics]
           AUDIT[Audit Logging]
       end
       
       START --> ACTIVE
       ACTIVE --> PAUSE
       PAUSE --> PAUSED_STATE
       PAUSED_STATE --> RESUME
       RESUME --> ACTIVE
       
       ACTIVE --> TIMEOUT
       TIMEOUT --> ABORT
       ABORT --> CLEANUP
       COMPLETE --> CLEANUP
       
       ACTIVE -.-> METRICS
       PAUSED_STATE -.-> DIAGNOSTICS
       CLEANUP -.-> AUDIT

Session Management API
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   // Pause active session
   const pauseResult = smp.pauseSession('user_request');
   if (pauseResult.success) {
     console.log('Session paused:', pauseResult.sessionId);
   }

   // Resume paused session
   const resumeResult = smp.resumeSession({
     validateTimeout: true,
     maxPauseDuration: 300000  // 5 minutes
   });
   if (resumeResult.success) {
     console.log('Session resumed:', resumeResult.sessionId);
   }

   // Get detailed session state
   const state = smp.getDetailedState();
   console.log('Session state:', {
     enhancedState: state.enhancedState,
     sessionDuration: state.sessionDuration,
     canPause: state.canPause,
     canResume: state.canResume,
     canAbort: state.canAbort
   });

Abort Handling
--------------

Enhanced abort handling provides detailed reason codes and context:

.. mermaid::

   graph TB
       subgraph "Abort Reasons"
           USER[User Abort]
           PROTOCOL[Protocol Error]
           TIMEOUT[Session Timeout]
           SECURITY[Security Error]
           NETWORK[Network Error]
           RESOURCE[Resource Error]
       end
       
       subgraph "Abort Processing"
           REASON[Determine Reason]
           CONTEXT[Gather Context]
           CLEANUP[Secure Cleanup]
           LOG[Audit Logging]
           NOTIFY[Notify Parties]
       end
       
       subgraph "Recovery Actions"
           RETRY[Retry Logic]
           FALLBACK[Fallback Options]
           RESET[State Reset]
       end
       
       USER --> REASON
       PROTOCOL --> REASON
       TIMEOUT --> REASON
       SECURITY --> REASON
       NETWORK --> REASON
       RESOURCE --> REASON
       
       REASON --> CONTEXT
       CONTEXT --> CLEANUP
       CLEANUP --> LOG
       LOG --> NOTIFY
       
       NOTIFY -.-> RETRY
       NOTIFY -.-> FALLBACK
       NOTIFY -.-> RESET

Abort Reason Codes
~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   import { SMP_ABORT_REASON } from 'webotter/protocol';

   // Available abort reasons
   const abortReasons = {
     USER_ABORT: 'user_abort',           // User manually aborted
     PROTOCOL_ERROR: 'protocol_error',   // Protocol violation
     TIMEOUT: 'timeout',                 // Session timed out
     INVALID_MESSAGE: 'invalid_message', // Invalid message received
     STATE_ERROR: 'state_error',         // Invalid state transition
     SECURITY_ERROR: 'security_error',   // Security validation failed
     NETWORK_ERROR: 'network_error',     // Network communication failed
     RESOURCE_ERROR: 'resource_error'    // Resource exhaustion
   };

   // Handle different abort scenarios
   switch (errorType) {
     case 'user_cancelled':
       smp.handleAbort(SMP_ABORT_REASON.USER_ABORT, {
         userAction: 'cancel_button',
         sessionDuration: Date.now() - sessionStart
       });
       break;
       
     case 'network_failure':
       smp.handleAbort(SMP_ABORT_REASON.NETWORK_ERROR, {
         errorCode: 'CONNECTION_LOST',
         retryAttempts: 3
       });
       break;
       
     case 'security_violation':
       smp.handleAbort(SMP_ABORT_REASON.SECURITY_ERROR, {
         violationType: 'INVALID_SIGNATURE',
         securityLevel: 'HIGH'
       });
       break;
   }

Diagnostics and Monitoring
-------------------------

Enhanced SMP provides comprehensive diagnostics:

.. mermaid::

   graph TB
       subgraph "Diagnostic Categories"
           PERF[Performance Metrics]
           SEC[Security Metrics]
           RES[Resource Usage]
           STATE[State Information]
       end
       
       subgraph "Performance Metrics"
           SESS_TIME[Session Duration]
           RESP_TIME[Response Times]
           SUCCESS_RATE[Success Rate]
           RETRY_RATE[Retry Rate]
       end
       
       subgraph "Security Metrics"
           SEC_VIOL[Security Violations]
           ABORT_RATE[Abort Rate]
           TIMEOUT_RATE[Timeout Rate]
           STATE_ERRORS[State Errors]
       end
       
       subgraph "Resource Metrics"
           MEM_USAGE[Memory Usage]
           STORAGE_SIZE[Storage Size]
           SESSION_COUNT[Active Sessions]
       end
       
       PERF --> SESS_TIME
       PERF --> RESP_TIME
       PERF --> SUCCESS_RATE
       PERF --> RETRY_RATE
       
       SEC --> SEC_VIOL
       SEC --> ABORT_RATE
       SEC --> TIMEOUT_RATE
       SEC --> STATE_ERRORS
       
       RES --> MEM_USAGE
       RES --> STORAGE_SIZE
       RES --> SESSION_COUNT

Diagnostics API
~~~~~~~~~~~~~~

.. code-block:: javascript

   // Generate comprehensive diagnostics
   const diagnostics = smp.generateDiagnostics();

   console.log('Enhanced SMP Diagnostics:', {
     // Session information
     sessionId: diagnostics.sessionId,
     enhancedState: diagnostics.enhancedState,
     sessionDuration: diagnostics.sessionDuration,
     
     // Performance metrics
     performance: {
       averageSessionTime: diagnostics.performance.averageSessionTime,
       successRate: diagnostics.performance.successRate,
       retryRate: diagnostics.performance.retryRate
     },
     
     // Security metrics
     security: {
       securityViolations: diagnostics.security.securityViolations,
       validationEnabled: diagnostics.security.validationEnabled,
       persistenceEnabled: diagnostics.security.persistenceEnabled
     },
     
     // Resource usage
     resources: {
       secureStorageSize: diagnostics.resources.secureStorageSize,
       memoryUsage: diagnostics.resources.memoryUsage
     },
     
     // Recommendations
     recommendations: diagnostics.recommendations
   });

Security Considerations
----------------------

Enhanced SMP implements multiple security layers:

.. mermaid::

   graph TB
       subgraph "Security Layers"
           subgraph "Memory Security"
               SEC_MEM[Secure Memory Storage]
               MEM_WIPE[Multi-pass Wiping]
               MEM_ENCRYPT[Memory Encryption]
           end
           
           subgraph "State Security"
               STATE_ENCRYPT[State Encryption]
               STATE_INTEGRITY[Integrity Validation]
               STATE_TIMEOUT[State Expiration]
           end
           
           subgraph "Session Security"
               SESS_TIMEOUT[Session Timeouts]
               SESS_VALIDATION[Session Validation]
               SESS_AUDIT[Session Audit Logging]
           end
           
           subgraph "Communication Security"
               MSG_VALIDATION[Message Validation]
               REPLAY_PROTECTION[Replay Protection]
               TIMING_RESISTANCE[Timing Attack Resistance]
           end
       end

Secure Memory Management
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   // Enhanced SMP automatically manages secure memory
   class EnhancedSMP {
     _storeSecretSecurely(secret) {
       if (this.sessionId) {
         // Create secure memory for the secret
         const secureMemory = new SecureMemory(secret.length * 2);
         secureMemory.write(new TextEncoder().encode(secret));
         
         // Store with session ID as key
         this.secureStorage.set(this.sessionId, secureMemory);
       }
     }
     
     _secureCleanup() {
       // Securely wipe all stored secrets
       for (const [sessionId, secureMemory] of this.secureStorage) {
         secureMemory.destroy();  // Multi-pass wipe
       }
       this.secureStorage.clear();
       
       // Clear persisted state
       this.persistedState = null;
     }
   }

Integration Examples
-------------------

Basic Enhanced SMP Usage
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   import { EnhancedSMP, SMP_ABORT_REASON } from 'webotter/protocol';

   class SecureChat {
     constructor() {
       this.smp = new EnhancedSMP({
         enableStatePersistence: true,
         sessionTimeoutMs: 300000,  // 5 minutes
         enableDetailedLogging: true
       });
     }
     
     async verifyContact(contactId, sharedSecret) {
       try {
         // Initiate enhanced SMP
         const smp1 = await this.smp.initiateEnhancedSMP(sharedSecret, {
           question: `Verify identity with ${contactId}`,
           timeout: 600000  // 10 minutes for user interaction
         });
         
         // Send SMP1 message
         await this.sendMessage(contactId, smp1);
         
         // Persist state for resumption
         const state = await this.smp.persistState();
         await this.saveSessionState(smp1.sessionId, state);
         
         return smp1.sessionId;
         
       } catch (error) {
         console.error('SMP initiation failed:', error);
         throw error;
       }
     }
     
     async handleSMPMessage(message) {
       try {
         switch (message.type) {
           case 'SMP1':
             return await this.handleSMP1(message);
           case 'SMP2':
             return await this.handleSMP2(message);
           case 'SMP3':
             return await this.handleSMP3(message);
           case 'SMP4':
             return await this.handleSMP4(message);
           case 'SMP_ABORT':
             return await this.handleSMPAbort(message);
         }
       } catch (error) {
         // Handle error and abort if necessary
         this.smp.handleAbort(SMP_ABORT_REASON.PROTOCOL_ERROR, {
           error: error.message,
           messageType: message.type
         });
         throw error;
       }
     }
   }

Advanced Session Management
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   class EnterpriseSecureChat extends SecureChat {
     constructor(options = {}) {
       super();
       this.sessionManager = new SMPSessionManager();
       this.auditLogger = new SecurityAuditLogger();
     }
     
     async pauseSMPSession(sessionId, reason) {
       const session = this.sessionManager.getSession(sessionId);
       if (session) {
         const result = session.smp.pauseSession(reason);
         
         if (result.success) {
           // Log pause event
           this.auditLogger.logSessionEvent('SMP_PAUSED', {
             sessionId: sessionId,
             reason: reason,
             timestamp: Date.now()
           });
           
           // Persist paused state
           const state = await session.smp.persistState();
           await this.saveSessionState(sessionId, state);
         }
         
         return result;
       }
     }
     
     async resumeSMPSession(sessionId) {
       const session = this.sessionManager.getSession(sessionId);
       if (session) {
         const result = session.smp.resumeSession({
           validateTimeout: true,
           maxPauseDuration: 1800000  // 30 minutes
         });
         
         if (result.success) {
           this.auditLogger.logSessionEvent('SMP_RESUMED', {
             sessionId: sessionId,
             timestamp: Date.now()
           });
         }
         
         return result;
       }
     }
     
     async generateSessionReport(sessionId) {
       const session = this.sessionManager.getSession(sessionId);
       if (session) {
         const diagnostics = session.smp.generateDiagnostics();
         
         return {
           sessionId: sessionId,
           status: diagnostics.enhancedState,
           duration: diagnostics.sessionDuration,
           performance: diagnostics.performance,
           security: diagnostics.security,
           recommendations: diagnostics.recommendations,
           generatedAt: Date.now()
         };
       }
     }
   }

.. note::
   Enhanced SMP is designed to be backward compatible with standard SMP while providing advanced features for enterprise deployments that require session persistence, detailed monitoring, and robust error handling.
