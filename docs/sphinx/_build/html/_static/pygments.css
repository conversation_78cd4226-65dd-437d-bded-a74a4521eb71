.highlight pre { line-height: 125%; }
.highlight td.linenos .normal { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }
.highlight span.linenos { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }
.highlight td.linenos .special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }
.highlight span.linenos.special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }
.highlight .hll { background-color: #ffffcc }
.highlight { background: #eeffcc; }
.highlight .c { color: #408090; font-style: italic } /* Comment */
.highlight .err { border: 1px solid #FF0000 } /* Error */
.highlight .k { color: #007020; font-weight: bold } /* Keyword */
.highlight .o { color: #666666 } /* Operator */
.highlight .ch { color: #408090; font-style: italic } /* Comment.Hashbang */
.highlight .cm { color: #408090; font-style: italic } /* Comment.Multiline */
.highlight .cp { color: #007020 } /* Comment.Preproc */
.highlight .cpf { color: #408090; font-style: italic } /* Comment.PreprocFile */
.highlight .c1 { color: #408090; font-style: italic } /* Comment.Single */
.highlight .cs { color: #408090; background-color: #fff0f0 } /* Comment.Special */
.highlight .gd { color: #A00000 } /* Generic.Deleted */
.highlight .ge { font-style: italic } /* Generic.Emph */
.highlight .ges { font-weight: bold; font-style: italic } /* Generic.EmphStrong */
.highlight .gr { color: #FF0000 } /* Generic.Error */
.highlight .gh { color: #000080; font-weight: bold } /* Generic.Heading */
.highlight .gi { color: #00A000 } /* Generic.Inserted */
.highlight .go { color: #333333 } /* Generic.Output */
.highlight .gp { color: #c65d09; font-weight: bold } /* Generic.Prompt */
.highlight .gs { font-weight: bold } /* Generic.Strong */
.highlight .gu { color: #800080; font-weight: bold } /* Generic.Subheading */
.highlight .gt { color: #0044DD } /* Generic.Traceback */
.highlight .kc { color: #007020; font-weight: bold } /* Keyword.Constant */
.highlight .kd { color: #007020; font-weight: bold } /* Keyword.Declaration */
.highlight .kn { color: #007020; font-weight: bold } /* Keyword.Namespace */
.highlight .kp { color: #007020 } /* Keyword.Pseudo */
.highlight .kr { color: #007020; font-weight: bold } /* Keyword.Reserved */
.highlight .kt { color: #902000 } /* Keyword.Type */
.highlight .m { color: #208050 } /* Literal.Number */
.highlight .s { color: #4070a0 } /* Literal.String */
.highlight .na { color: #4070a0 } /* Name.Attribute */
.highlight .nb { color: #007020 } /* Name.Builtin */
.highlight .nc { color: #0e84b5; font-weight: bold } /* Name.Class */
.highlight .no { color: #60add5 } /* Name.Constant */
.highlight .nd { color: #555555; font-weight: bold } /* Name.Decorator */
.highlight .ni { color: #d55537; font-weight: bold } /* Name.Entity */
.highlight .ne { color: #007020 } /* Name.Exception */
.highlight .nf { color: #06287e } /* Name.Function */
.highlight .nl { color: #002070; font-weight: bold } /* Name.Label */
.highlight .nn { color: #0e84b5; font-weight: bold } /* Name.Namespace */
.highlight .nt { color: #062873; font-weight: bold } /* Name.Tag */
.highlight .nv { color: #bb60d5 } /* Name.Variable */
.highlight .ow { color: #007020; font-weight: bold } /* Operator.Word */
.highlight .w { color: #bbbbbb } /* Text.Whitespace */
.highlight .mb { color: #208050 } /* Literal.Number.Bin */
.highlight .mf { color: #208050 } /* Literal.Number.Float */
.highlight .mh { color: #208050 } /* Literal.Number.Hex */
.highlight .mi { color: #208050 } /* Literal.Number.Integer */
.highlight .mo { color: #208050 } /* Literal.Number.Oct */
.highlight .sa { color: #4070a0 } /* Literal.String.Affix */
.highlight .sb { color: #4070a0 } /* Literal.String.Backtick */
.highlight .sc { color: #4070a0 } /* Literal.String.Char */
.highlight .dl { color: #4070a0 } /* Literal.String.Delimiter */
.highlight .sd { color: #4070a0; font-style: italic } /* Literal.String.Doc */
.highlight .s2 { color: #4070a0 } /* Literal.String.Double */
.highlight .se { color: #4070a0; font-weight: bold } /* Literal.String.Escape */
.highlight .sh { color: #4070a0 } /* Literal.String.Heredoc */
.highlight .si { color: #70a0d0; font-style: italic } /* Literal.String.Interpol */
.highlight .sx { color: #c65d09 } /* Literal.String.Other */
.highlight .sr { color: #235388 } /* Literal.String.Regex */
.highlight .s1 { color: #4070a0 } /* Literal.String.Single */
.highlight .ss { color: #517918 } /* Literal.String.Symbol */
.highlight .bp { color: #007020 } /* Name.Builtin.Pseudo */
.highlight .fm { color: #06287e } /* Name.Function.Magic */
.highlight .vc { color: #bb60d5 } /* Name.Variable.Class */
.highlight .vg { color: #bb60d5 } /* Name.Variable.Global */
.highlight .vi { color: #bb60d5 } /* Name.Variable.Instance */
.highlight .vm { color: #bb60d5 } /* Name.Variable.Magic */
.highlight .il { color: #208050 } /* Literal.Number.Integer.Long */
@media not print {
body[data-theme="dark"] .highlight pre { line-height: 125%; }
body[data-theme="dark"] .highlight td.linenos .normal { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }
body[data-theme="dark"] .highlight span.linenos { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }
body[data-theme="dark"] .highlight td.linenos .special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }
body[data-theme="dark"] .highlight span.linenos.special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }
body[data-theme="dark"] .highlight .hll { background-color: #49483e }
body[data-theme="dark"] .highlight { background: #272822; color: #f8f8f2 }
body[data-theme="dark"] .highlight .c { color: #959077 } /* Comment */
body[data-theme="dark"] .highlight .err { color: #ed007e; background-color: #1e0010 } /* Error */
body[data-theme="dark"] .highlight .esc { color: #f8f8f2 } /* Escape */
body[data-theme="dark"] .highlight .g { color: #f8f8f2 } /* Generic */
body[data-theme="dark"] .highlight .k { color: #66d9ef } /* Keyword */
body[data-theme="dark"] .highlight .l { color: #ae81ff } /* Literal */
body[data-theme="dark"] .highlight .n { color: #f8f8f2 } /* Name */
body[data-theme="dark"] .highlight .o { color: #ff4689 } /* Operator */
body[data-theme="dark"] .highlight .x { color: #f8f8f2 } /* Other */
body[data-theme="dark"] .highlight .p { color: #f8f8f2 } /* Punctuation */
body[data-theme="dark"] .highlight .ch { color: #959077 } /* Comment.Hashbang */
body[data-theme="dark"] .highlight .cm { color: #959077 } /* Comment.Multiline */
body[data-theme="dark"] .highlight .cp { color: #959077 } /* Comment.Preproc */
body[data-theme="dark"] .highlight .cpf { color: #959077 } /* Comment.PreprocFile */
body[data-theme="dark"] .highlight .c1 { color: #959077 } /* Comment.Single */
body[data-theme="dark"] .highlight .cs { color: #959077 } /* Comment.Special */
body[data-theme="dark"] .highlight .gd { color: #ff4689 } /* Generic.Deleted */
body[data-theme="dark"] .highlight .ge { color: #f8f8f2; font-style: italic } /* Generic.Emph */
body[data-theme="dark"] .highlight .ges { color: #f8f8f2; font-weight: bold; font-style: italic } /* Generic.EmphStrong */
body[data-theme="dark"] .highlight .gr { color: #f8f8f2 } /* Generic.Error */
body[data-theme="dark"] .highlight .gh { color: #f8f8f2 } /* Generic.Heading */
body[data-theme="dark"] .highlight .gi { color: #a6e22e } /* Generic.Inserted */
body[data-theme="dark"] .highlight .go { color: #66d9ef } /* Generic.Output */
body[data-theme="dark"] .highlight .gp { color: #ff4689; font-weight: bold } /* Generic.Prompt */
body[data-theme="dark"] .highlight .gs { color: #f8f8f2; font-weight: bold } /* Generic.Strong */
body[data-theme="dark"] .highlight .gu { color: #959077 } /* Generic.Subheading */
body[data-theme="dark"] .highlight .gt { color: #f8f8f2 } /* Generic.Traceback */
body[data-theme="dark"] .highlight .kc { color: #66d9ef } /* Keyword.Constant */
body[data-theme="dark"] .highlight .kd { color: #66d9ef } /* Keyword.Declaration */
body[data-theme="dark"] .highlight .kn { color: #ff4689 } /* Keyword.Namespace */
body[data-theme="dark"] .highlight .kp { color: #66d9ef } /* Keyword.Pseudo */
body[data-theme="dark"] .highlight .kr { color: #66d9ef } /* Keyword.Reserved */
body[data-theme="dark"] .highlight .kt { color: #66d9ef } /* Keyword.Type */
body[data-theme="dark"] .highlight .ld { color: #e6db74 } /* Literal.Date */
body[data-theme="dark"] .highlight .m { color: #ae81ff } /* Literal.Number */
body[data-theme="dark"] .highlight .s { color: #e6db74 } /* Literal.String */
body[data-theme="dark"] .highlight .na { color: #a6e22e } /* Name.Attribute */
body[data-theme="dark"] .highlight .nb { color: #f8f8f2 } /* Name.Builtin */
body[data-theme="dark"] .highlight .nc { color: #a6e22e } /* Name.Class */
body[data-theme="dark"] .highlight .no { color: #66d9ef } /* Name.Constant */
body[data-theme="dark"] .highlight .nd { color: #a6e22e } /* Name.Decorator */
body[data-theme="dark"] .highlight .ni { color: #f8f8f2 } /* Name.Entity */
body[data-theme="dark"] .highlight .ne { color: #a6e22e } /* Name.Exception */
body[data-theme="dark"] .highlight .nf { color: #a6e22e } /* Name.Function */
body[data-theme="dark"] .highlight .nl { color: #f8f8f2 } /* Name.Label */
body[data-theme="dark"] .highlight .nn { color: #f8f8f2 } /* Name.Namespace */
body[data-theme="dark"] .highlight .nx { color: #a6e22e } /* Name.Other */
body[data-theme="dark"] .highlight .py { color: #f8f8f2 } /* Name.Property */
body[data-theme="dark"] .highlight .nt { color: #ff4689 } /* Name.Tag */
body[data-theme="dark"] .highlight .nv { color: #f8f8f2 } /* Name.Variable */
body[data-theme="dark"] .highlight .ow { color: #ff4689 } /* Operator.Word */
body[data-theme="dark"] .highlight .pm { color: #f8f8f2 } /* Punctuation.Marker */
body[data-theme="dark"] .highlight .w { color: #f8f8f2 } /* Text.Whitespace */
body[data-theme="dark"] .highlight .mb { color: #ae81ff } /* Literal.Number.Bin */
body[data-theme="dark"] .highlight .mf { color: #ae81ff } /* Literal.Number.Float */
body[data-theme="dark"] .highlight .mh { color: #ae81ff } /* Literal.Number.Hex */
body[data-theme="dark"] .highlight .mi { color: #ae81ff } /* Literal.Number.Integer */
body[data-theme="dark"] .highlight .mo { color: #ae81ff } /* Literal.Number.Oct */
body[data-theme="dark"] .highlight .sa { color: #e6db74 } /* Literal.String.Affix */
body[data-theme="dark"] .highlight .sb { color: #e6db74 } /* Literal.String.Backtick */
body[data-theme="dark"] .highlight .sc { color: #e6db74 } /* Literal.String.Char */
body[data-theme="dark"] .highlight .dl { color: #e6db74 } /* Literal.String.Delimiter */
body[data-theme="dark"] .highlight .sd { color: #e6db74 } /* Literal.String.Doc */
body[data-theme="dark"] .highlight .s2 { color: #e6db74 } /* Literal.String.Double */
body[data-theme="dark"] .highlight .se { color: #ae81ff } /* Literal.String.Escape */
body[data-theme="dark"] .highlight .sh { color: #e6db74 } /* Literal.String.Heredoc */
body[data-theme="dark"] .highlight .si { color: #e6db74 } /* Literal.String.Interpol */
body[data-theme="dark"] .highlight .sx { color: #e6db74 } /* Literal.String.Other */
body[data-theme="dark"] .highlight .sr { color: #e6db74 } /* Literal.String.Regex */
body[data-theme="dark"] .highlight .s1 { color: #e6db74 } /* Literal.String.Single */
body[data-theme="dark"] .highlight .ss { color: #e6db74 } /* Literal.String.Symbol */
body[data-theme="dark"] .highlight .bp { color: #f8f8f2 } /* Name.Builtin.Pseudo */
body[data-theme="dark"] .highlight .fm { color: #a6e22e } /* Name.Function.Magic */
body[data-theme="dark"] .highlight .vc { color: #f8f8f2 } /* Name.Variable.Class */
body[data-theme="dark"] .highlight .vg { color: #f8f8f2 } /* Name.Variable.Global */
body[data-theme="dark"] .highlight .vi { color: #f8f8f2 } /* Name.Variable.Instance */
body[data-theme="dark"] .highlight .vm { color: #f8f8f2 } /* Name.Variable.Magic */
body[data-theme="dark"] .highlight .il { color: #ae81ff } /* Literal.Number.Integer.Long */
@media (prefers-color-scheme: dark) {
body:not([data-theme="light"]) .highlight pre { line-height: 125%; }
body:not([data-theme="light"]) .highlight td.linenos .normal { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }
body:not([data-theme="light"]) .highlight span.linenos { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }
body:not([data-theme="light"]) .highlight td.linenos .special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }
body:not([data-theme="light"]) .highlight span.linenos.special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }
body:not([data-theme="light"]) .highlight .hll { background-color: #49483e }
body:not([data-theme="light"]) .highlight { background: #272822; color: #f8f8f2 }
body:not([data-theme="light"]) .highlight .c { color: #959077 } /* Comment */
body:not([data-theme="light"]) .highlight .err { color: #ed007e; background-color: #1e0010 } /* Error */
body:not([data-theme="light"]) .highlight .esc { color: #f8f8f2 } /* Escape */
body:not([data-theme="light"]) .highlight .g { color: #f8f8f2 } /* Generic */
body:not([data-theme="light"]) .highlight .k { color: #66d9ef } /* Keyword */
body:not([data-theme="light"]) .highlight .l { color: #ae81ff } /* Literal */
body:not([data-theme="light"]) .highlight .n { color: #f8f8f2 } /* Name */
body:not([data-theme="light"]) .highlight .o { color: #ff4689 } /* Operator */
body:not([data-theme="light"]) .highlight .x { color: #f8f8f2 } /* Other */
body:not([data-theme="light"]) .highlight .p { color: #f8f8f2 } /* Punctuation */
body:not([data-theme="light"]) .highlight .ch { color: #959077 } /* Comment.Hashbang */
body:not([data-theme="light"]) .highlight .cm { color: #959077 } /* Comment.Multiline */
body:not([data-theme="light"]) .highlight .cp { color: #959077 } /* Comment.Preproc */
body:not([data-theme="light"]) .highlight .cpf { color: #959077 } /* Comment.PreprocFile */
body:not([data-theme="light"]) .highlight .c1 { color: #959077 } /* Comment.Single */
body:not([data-theme="light"]) .highlight .cs { color: #959077 } /* Comment.Special */
body:not([data-theme="light"]) .highlight .gd { color: #ff4689 } /* Generic.Deleted */
body:not([data-theme="light"]) .highlight .ge { color: #f8f8f2; font-style: italic } /* Generic.Emph */
body:not([data-theme="light"]) .highlight .ges { color: #f8f8f2; font-weight: bold; font-style: italic } /* Generic.EmphStrong */
body:not([data-theme="light"]) .highlight .gr { color: #f8f8f2 } /* Generic.Error */
body:not([data-theme="light"]) .highlight .gh { color: #f8f8f2 } /* Generic.Heading */
body:not([data-theme="light"]) .highlight .gi { color: #a6e22e } /* Generic.Inserted */
body:not([data-theme="light"]) .highlight .go { color: #66d9ef } /* Generic.Output */
body:not([data-theme="light"]) .highlight .gp { color: #ff4689; font-weight: bold } /* Generic.Prompt */
body:not([data-theme="light"]) .highlight .gs { color: #f8f8f2; font-weight: bold } /* Generic.Strong */
body:not([data-theme="light"]) .highlight .gu { color: #959077 } /* Generic.Subheading */
body:not([data-theme="light"]) .highlight .gt { color: #f8f8f2 } /* Generic.Traceback */
body:not([data-theme="light"]) .highlight .kc { color: #66d9ef } /* Keyword.Constant */
body:not([data-theme="light"]) .highlight .kd { color: #66d9ef } /* Keyword.Declaration */
body:not([data-theme="light"]) .highlight .kn { color: #ff4689 } /* Keyword.Namespace */
body:not([data-theme="light"]) .highlight .kp { color: #66d9ef } /* Keyword.Pseudo */
body:not([data-theme="light"]) .highlight .kr { color: #66d9ef } /* Keyword.Reserved */
body:not([data-theme="light"]) .highlight .kt { color: #66d9ef } /* Keyword.Type */
body:not([data-theme="light"]) .highlight .ld { color: #e6db74 } /* Literal.Date */
body:not([data-theme="light"]) .highlight .m { color: #ae81ff } /* Literal.Number */
body:not([data-theme="light"]) .highlight .s { color: #e6db74 } /* Literal.String */
body:not([data-theme="light"]) .highlight .na { color: #a6e22e } /* Name.Attribute */
body:not([data-theme="light"]) .highlight .nb { color: #f8f8f2 } /* Name.Builtin */
body:not([data-theme="light"]) .highlight .nc { color: #a6e22e } /* Name.Class */
body:not([data-theme="light"]) .highlight .no { color: #66d9ef } /* Name.Constant */
body:not([data-theme="light"]) .highlight .nd { color: #a6e22e } /* Name.Decorator */
body:not([data-theme="light"]) .highlight .ni { color: #f8f8f2 } /* Name.Entity */
body:not([data-theme="light"]) .highlight .ne { color: #a6e22e } /* Name.Exception */
body:not([data-theme="light"]) .highlight .nf { color: #a6e22e } /* Name.Function */
body:not([data-theme="light"]) .highlight .nl { color: #f8f8f2 } /* Name.Label */
body:not([data-theme="light"]) .highlight .nn { color: #f8f8f2 } /* Name.Namespace */
body:not([data-theme="light"]) .highlight .nx { color: #a6e22e } /* Name.Other */
body:not([data-theme="light"]) .highlight .py { color: #f8f8f2 } /* Name.Property */
body:not([data-theme="light"]) .highlight .nt { color: #ff4689 } /* Name.Tag */
body:not([data-theme="light"]) .highlight .nv { color: #f8f8f2 } /* Name.Variable */
body:not([data-theme="light"]) .highlight .ow { color: #ff4689 } /* Operator.Word */
body:not([data-theme="light"]) .highlight .pm { color: #f8f8f2 } /* Punctuation.Marker */
body:not([data-theme="light"]) .highlight .w { color: #f8f8f2 } /* Text.Whitespace */
body:not([data-theme="light"]) .highlight .mb { color: #ae81ff } /* Literal.Number.Bin */
body:not([data-theme="light"]) .highlight .mf { color: #ae81ff } /* Literal.Number.Float */
body:not([data-theme="light"]) .highlight .mh { color: #ae81ff } /* Literal.Number.Hex */
body:not([data-theme="light"]) .highlight .mi { color: #ae81ff } /* Literal.Number.Integer */
body:not([data-theme="light"]) .highlight .mo { color: #ae81ff } /* Literal.Number.Oct */
body:not([data-theme="light"]) .highlight .sa { color: #e6db74 } /* Literal.String.Affix */
body:not([data-theme="light"]) .highlight .sb { color: #e6db74 } /* Literal.String.Backtick */
body:not([data-theme="light"]) .highlight .sc { color: #e6db74 } /* Literal.String.Char */
body:not([data-theme="light"]) .highlight .dl { color: #e6db74 } /* Literal.String.Delimiter */
body:not([data-theme="light"]) .highlight .sd { color: #e6db74 } /* Literal.String.Doc */
body:not([data-theme="light"]) .highlight .s2 { color: #e6db74 } /* Literal.String.Double */
body:not([data-theme="light"]) .highlight .se { color: #ae81ff } /* Literal.String.Escape */
body:not([data-theme="light"]) .highlight .sh { color: #e6db74 } /* Literal.String.Heredoc */
body:not([data-theme="light"]) .highlight .si { color: #e6db74 } /* Literal.String.Interpol */
body:not([data-theme="light"]) .highlight .sx { color: #e6db74 } /* Literal.String.Other */
body:not([data-theme="light"]) .highlight .sr { color: #e6db74 } /* Literal.String.Regex */
body:not([data-theme="light"]) .highlight .s1 { color: #e6db74 } /* Literal.String.Single */
body:not([data-theme="light"]) .highlight .ss { color: #e6db74 } /* Literal.String.Symbol */
body:not([data-theme="light"]) .highlight .bp { color: #f8f8f2 } /* Name.Builtin.Pseudo */
body:not([data-theme="light"]) .highlight .fm { color: #a6e22e } /* Name.Function.Magic */
body:not([data-theme="light"]) .highlight .vc { color: #f8f8f2 } /* Name.Variable.Class */
body:not([data-theme="light"]) .highlight .vg { color: #f8f8f2 } /* Name.Variable.Global */
body:not([data-theme="light"]) .highlight .vi { color: #f8f8f2 } /* Name.Variable.Instance */
body:not([data-theme="light"]) .highlight .vm { color: #f8f8f2 } /* Name.Variable.Magic */
body:not([data-theme="light"]) .highlight .il { color: #ae81ff } /* Literal.Number.Integer.Long */
}
}