/**
 * Mermaid Diagram Renderer for WebOTR Documentation
 * 
 * This script provides client-side Mermaid diagram rendering for the WebOTR
 * documentation when the sphinxcontrib.mermaid extension is not available.
 */

// Load Mermaid from CDN
(function() {
    'use strict';
    
    // Configuration
    const MERMAID_VERSION = '10.6.1';
    const MERMAID_CDN = `https://cdn.jsdelivr.net/npm/mermaid@${MERMAID_VERSION}/dist/mermaid.min.js`;
    
    // Mermaid configuration
    const mermaidConfig = {
        startOnLoad: false,
        theme: 'default',
        themeVariables: {
            primaryColor: '#6366f1',
            primaryTextColor: '#1f2937',
            primaryBorderColor: '#6366f1',
            lineColor: '#6b7280',
            secondaryColor: '#f3f4f6',
            tertiaryColor: '#ffffff'
        },
        flowchart: {
            curve: 'basis',
            padding: 20,
            nodeSpacing: 50,
            rankSpacing: 50
        },
        sequence: {
            diagramMarginX: 50,
            diagramMarginY: 10,
            actorMargin: 50,
            width: 150,
            height: 65,
            boxMargin: 10,
            boxTextMargin: 5,
            noteMargin: 10,
            messageMargin: 35,
            mirrorActors: true,
            bottomMarginAdj: 1
        },
        gantt: {
            titleTopMargin: 25,
            barHeight: 20,
            fontFamily: '"Inter", sans-serif',
            fontSize: 11,
            gridLineStartPadding: 35,
            bottomPadding: 25,
            leftPadding: 75,
            rightPadding: 35
        },
        state: {
            dividerMargin: 10,
            sizeUnit: 5,
            fontSize: 12
        },
        pie: {
            textPosition: 0.75
        },
        requirement: {
            rect_fill: '#f9f9f9',
            text_color: '#333',
            rect_border_size: '0.5px',
            rect_border_color: '#bbb'
        },
        gitgraph: {
            mainBranchName: 'main',
            showBranches: true,
            showCommitLabel: true,
            rotateCommitLabel: true
        }
    };
    
    // Load Mermaid library
    function loadMermaid() {
        return new Promise((resolve, reject) => {
            if (window.mermaid) {
                resolve(window.mermaid);
                return;
            }
            
            const script = document.createElement('script');
            script.src = MERMAID_CDN;
            script.onload = () => {
                if (window.mermaid) {
                    resolve(window.mermaid);
                } else {
                    reject(new Error('Mermaid failed to load'));
                }
            };
            script.onerror = () => reject(new Error('Failed to load Mermaid from CDN'));
            document.head.appendChild(script);
        });
    }
    
    // Find and process Mermaid diagrams
    function findMermaidDiagrams() {
        const diagrams = [];
        
        // Look for code blocks with mermaid class
        const codeBlocks = document.querySelectorAll('pre code.language-mermaid, .highlight-mermaid pre, .mermaid');
        
        codeBlocks.forEach((block, index) => {
            const content = block.textContent || block.innerText;
            if (content && content.trim()) {
                diagrams.push({
                    element: block,
                    content: content.trim(),
                    id: `mermaid-diagram-${index}`
                });
            }
        });
        
        return diagrams;
    }
    
    // Render a single Mermaid diagram
    async function renderDiagram(diagram, mermaid) {
        try {
            // Create container for the diagram
            const container = document.createElement('div');
            container.className = 'mermaid-container';
            container.style.cssText = `
                margin: 20px 0;
                text-align: center;
                background: #ffffff;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 20px;
                overflow-x: auto;
            `;
            
            // Create diagram element
            const diagramElement = document.createElement('div');
            diagramElement.className = 'mermaid-diagram';
            diagramElement.id = diagram.id;
            
            // Render the diagram
            const { svg } = await mermaid.render(diagram.id + '-svg', diagram.content);
            diagramElement.innerHTML = svg;
            
            // Add diagram to container
            container.appendChild(diagramElement);
            
            // Add error handling
            const errorHandler = document.createElement('div');
            errorHandler.className = 'mermaid-error';
            errorHandler.style.display = 'none';
            errorHandler.style.cssText = `
                color: #dc2626;
                background: #fef2f2;
                border: 1px solid #fecaca;
                border-radius: 4px;
                padding: 12px;
                margin: 10px 0;
                font-family: monospace;
                font-size: 14px;
            `;
            container.appendChild(errorHandler);
            
            // Replace original element
            const parent = diagram.element.parentNode;
            if (parent.tagName === 'PRE') {
                parent.parentNode.replaceChild(container, parent);
            } else {
                diagram.element.parentNode.replaceChild(container, diagram.element);
            }
            
            console.log(`✅ Rendered Mermaid diagram: ${diagram.id}`);
            
        } catch (error) {
            console.error(`❌ Failed to render Mermaid diagram: ${diagram.id}`, error);
            
            // Show error message
            const errorContainer = document.createElement('div');
            errorContainer.className = 'mermaid-error';
            errorContainer.style.cssText = `
                color: #dc2626;
                background: #fef2f2;
                border: 1px solid #fecaca;
                border-radius: 4px;
                padding: 12px;
                margin: 10px 0;
                font-family: monospace;
                font-size: 14px;
            `;
            errorContainer.innerHTML = `
                <strong>Mermaid Diagram Error:</strong><br>
                <code>${error.message}</code><br>
                <details style="margin-top: 8px;">
                    <summary>Diagram Source</summary>
                    <pre style="margin: 8px 0; padding: 8px; background: #f9fafb; border-radius: 4px; overflow-x: auto;">${diagram.content}</pre>
                </details>
            `;
            
            // Replace original element with error
            const parent = diagram.element.parentNode;
            if (parent.tagName === 'PRE') {
                parent.parentNode.replaceChild(errorContainer, parent);
            } else {
                diagram.element.parentNode.replaceChild(errorContainer, diagram.element);
            }
        }
    }
    
    // Initialize Mermaid rendering
    async function initializeMermaid() {
        try {
            console.log('🎨 Initializing Mermaid diagram rendering...');
            
            // Load Mermaid library
            const mermaid = await loadMermaid();
            
            // Initialize Mermaid with configuration
            mermaid.initialize(mermaidConfig);
            
            // Find all Mermaid diagrams
            const diagrams = findMermaidDiagrams();
            console.log(`📊 Found ${diagrams.length} Mermaid diagrams to render`);
            
            if (diagrams.length === 0) {
                console.log('ℹ️ No Mermaid diagrams found in document');
                return;
            }
            
            // Render each diagram
            const renderPromises = diagrams.map(diagram => renderDiagram(diagram, mermaid));
            await Promise.allSettled(renderPromises);
            
            console.log('✅ Mermaid diagram rendering complete');
            
        } catch (error) {
            console.error('❌ Failed to initialize Mermaid:', error);
        }
    }
    
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeMermaid);
    } else {
        // DOM is already ready
        initializeMermaid();
    }
    
    // Also handle dynamic content loading
    if (window.MutationObserver) {
        const observer = new MutationObserver((mutations) => {
            let hasNewContent = false;
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    hasNewContent = true;
                }
            });
            
            if (hasNewContent) {
                // Debounce the rendering
                clearTimeout(window.mermaidRenderTimeout);
                window.mermaidRenderTimeout = setTimeout(() => {
                    const newDiagrams = findMermaidDiagrams();
                    if (newDiagrams.length > 0) {
                        console.log(`📊 Found ${newDiagrams.length} new Mermaid diagrams`);
                        initializeMermaid();
                    }
                }, 500);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
})();

// Export for manual use if needed
window.WebOTRMermaid = {
    render: function(selector, content) {
        if (window.mermaid) {
            const element = document.querySelector(selector);
            if (element) {
                const id = 'manual-mermaid-' + Date.now();
                window.mermaid.render(id, content).then(({ svg }) => {
                    element.innerHTML = svg;
                });
            }
        }
    }
};
