/* Custom CSS for WebOTR Sphinx Documentation */

:root {
  --webotr-primary: #6366f1;
  --webotr-secondary: #8b5cf6;
  --webotr-accent: #06b6d4;
  --webotr-success: #10b981;
}

/* Logo styling */
.sidebar-brand img {
  max-height: 60px;
  width: auto;
}

/* Custom admonitions */
.admonition.note {
  border-left: 4px solid var(--webotr-primary);
}

.admonition.warning {
  border-left: 4px solid #f59e0b;
}

.admonition.tip {
  border-left: 4px solid var(--webotr-success);
}

/* Code blocks */
.highlight {
  border-radius: 8px;
  overflow: hidden;
}

/* Tables */
table.docutils {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Navigation improvements */
.sidebar-tree .toctree-l1 > a {
  font-weight: 600;
  color: var(--color-foreground-primary);
}

.sidebar-tree .toctree-l1 > a:hover {
  color: var(--webotr-primary);
}

/* Dark mode specific adjustments */
[data-theme="dark"] {
  --webotr-primary: #8b5cf6;
  --webotr-secondary: #a855f7;
}
