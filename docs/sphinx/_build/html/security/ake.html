<!doctype html>
<html class="no-js" lang="en" data-content_root="../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><meta name="viewport" content="width=device-width, initial-scale=1" />
<link rel="index" title="Index" href="../genindex.html" /><link rel="search" title="Search" href="../search.html" /><link rel="next" title="AKE Architecture" href="ake-architecture.html" /><link rel="prev" title="AKE Documentation Summary" href="ake-summary.html" />

    <link rel="shortcut icon" href="../_static/favicon.svg"/><!-- Generated with Sphinx 7.4.7 and Furo 2024.08.06 -->
        <title>Authenticated Key Exchange (AKE) - WebOTR Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../_static/styles/furo.css?v=354aac6f" />
    <link rel="stylesheet" type="text/css" href="../_static/styles/furo-extensions.css?v=302659d7" />
    <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=21a7f706" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  --color-brand-primary: #6366f1;
  --color-brand-content: #6366f1;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8b5cf6;
  --color-brand-content: #8b5cf6;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8b5cf6;
  --color-brand-content: #8b5cf6;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-with-moon" viewBox="0 0 24 24">
    <title>Auto light/dark, in light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
      class="icon-custom-derived-from-feather-sun-and-tabler-moon">
      <path style="opacity: 50%" d="M 5.411 14.504 C 5.471 14.504 5.532 14.504 5.591 14.504 C 3.639 16.319 4.383 19.569 6.931 20.352 C 7.693 20.586 8.512 20.551 9.25 20.252 C 8.023 23.207 4.056 23.725 2.11 21.184 C 0.166 18.642 1.702 14.949 4.874 14.536 C 5.051 14.512 5.231 14.5 5.411 14.5 L 5.411 14.504 Z"/>
      <line x1="14.5" y1="3.25" x2="14.5" y2="1.25"/>
      <line x1="14.5" y1="15.85" x2="14.5" y2="17.85"/>
      <line x1="10.044" y1="5.094" x2="8.63" y2="3.68"/>
      <line x1="19" y1="14.05" x2="20.414" y2="15.464"/>
      <line x1="8.2" y1="9.55" x2="6.2" y2="9.55"/>
      <line x1="20.8" y1="9.55" x2="22.8" y2="9.55"/>
      <line x1="10.044" y1="14.006" x2="8.63" y2="15.42"/>
      <line x1="19" y1="5.05" x2="20.414" y2="3.636"/>
      <circle cx="14.5" cy="9.55" r="3.6"/>
    </svg>
  </symbol>
  <symbol id="svg-moon-with-sun" viewBox="0 0 24 24">
    <title>Auto light/dark, in dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
      class="icon-custom-derived-from-feather-sun-and-tabler-moon">
      <path d="M 8.282 7.007 C 8.385 7.007 8.494 7.007 8.595 7.007 C 5.18 10.184 6.481 15.869 10.942 17.24 C 12.275 17.648 13.706 17.589 15 17.066 C 12.851 22.236 5.91 23.143 2.505 18.696 C -0.897 14.249 1.791 7.786 7.342 7.063 C 7.652 7.021 7.965 7 8.282 7 L 8.282 7.007 Z"/>
      <line style="opacity: 50%" x1="18" y1="3.705" x2="18" y2="2.5"/>
      <line style="opacity: 50%" x1="18" y1="11.295" x2="18" y2="12.5"/>
      <line style="opacity: 50%" x1="15.316" y1="4.816" x2="14.464" y2="3.964"/>
      <line style="opacity: 50%" x1="20.711" y1="10.212" x2="21.563" y2="11.063"/>
      <line style="opacity: 50%" x1="14.205" y1="7.5" x2="13.001" y2="7.5"/>
      <line style="opacity: 50%" x1="21.795" y1="7.5" x2="23" y2="7.5"/>
      <line style="opacity: 50%" x1="15.316" y1="10.184" x2="14.464" y2="11.036"/>
      <line style="opacity: 50%" x1="20.711" y1="4.789" x2="21.563" y2="3.937"/>
      <circle style="opacity: 50%" cx="18" cy="7.5" r="2.169"/>
    </svg>
  </symbol>
  <symbol id="svg-pencil" viewBox="0 0 24 24">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-pencil-code">
      <path d="M4 20h4l10.5 -10.5a2.828 2.828 0 1 0 -4 -4l-10.5 10.5v4" />
      <path d="M13.5 6.5l4 4" />
      <path d="M20 21l2 -2l-2 -2" />
      <path d="M17 17l-2 2l2 2" />
    </svg>
  </symbol>
  <symbol id="svg-eye" viewBox="0 0 24 24">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-eye-code">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0" />
      <path
        d="M11.11 17.958c-3.209 -.307 -5.91 -2.293 -8.11 -5.958c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6c-.21 .352 -.427 .688 -.647 1.008" />
      <path d="M20 21l2 -2l-2 -2" />
      <path d="M17 17l-2 2l2 2" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>

<a class="skip-to-content muted-link" href="#furo-main-content">Skip to content</a>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../index.html"><div class="brand">WebOTR Documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto-light"><use href="#svg-sun-with-moon"></use></svg>
          <svg class="theme-icon-when-auto-dark"><use href="#svg-moon-with-sun"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><a class="sidebar-brand" href="../index.html">
  
  <div class="sidebar-logo-container">
    <img class="sidebar-logo" src="../_static/logo.svg" alt="Logo"/>
  </div>
  
  <span class="sidebar-brand-text">WebOTR Documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-scroll"><div class="sidebar-tree">
  <p class="caption" role="heading"><span class="caption-text">User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user-guide/installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../user-guide/getting-started.html">Getting Started</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../developer-guide/architecture.html">Architecture Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../developer-guide/contributing.html">Contributing to WebOTR</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Protocol Compliance &amp; Advanced Features</span></p>
<ul>
<li class="toctree-l1 has-children"><a class="reference internal" href="../protocol/index.html">Protocol Compliance &amp; Advanced Features</a><input class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of Protocol Compliance &amp; Advanced Features</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul>
<li class="toctree-l2"><a class="reference internal" href="../protocol/overview.html">Protocol Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/version-negotiation.html">Version Negotiation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/message-ordering.html">Message Ordering</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/enhanced-smp.html">Enhanced SMP</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/policy-manager.html">Policy Manager</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/integration-guide.html">Integration Guide</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/troubleshooting.html">Troubleshooting Guide</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Security</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="overview.html">Security Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements-summary.html">libOTR Security Enhancements Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements.html">libOTR Security Enhancements</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements-implementation.html">libOTR Enhancements Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements-api.html">libOTR Enhancements API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements-testing.html">libOTR Enhancements Testing and Validation</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy-summary.html">Forward Secrecy Documentation Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy.html">Forward Secrecy Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy-architecture.html">Forward Secrecy Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy-implementation.html">Forward Secrecy Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy-api.html">Forward Secrecy API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake-summary.html">AKE Documentation Summary</a></li>
<li class="toctree-l1 current current-page"><a class="current reference internal" href="#">Authenticated Key Exchange (AKE)</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake-architecture.html">AKE Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake-implementation.html">AKE Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake-api.html">AKE API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography-summary.html">Steganography Documentation Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography.html">Steganography Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography-architecture.html">Steganography Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography-implementation.html">Steganography Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography-api.html">Steganography API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../reference/faq.html">Frequently Asked Questions</a></li>
<li class="toctree-l1"><a class="reference internal" href="../reference/changelog.html">Changelog</a></li>
</ul>

</div>
</div>

      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          <div class="view-this-page">
  <a class="muted-link" href="https://github.com/forkrul/webOTteR/blob/main/docs/sphinx/security/ake.rst?plain=true" title="View this page">
    <svg><use href="#svg-eye"></use></svg>
    <span class="visually-hidden">View this page</span>
  </a>
</div><div class="edit-this-page">
  <a class="muted-link" href="https://github.com/forkrul/webOTteR/edit/main/docs/sphinx/security/ake.rst" title="Edit this page">
    <svg><use href="#svg-pencil"></use></svg>
    <span class="visually-hidden">Edit this page</span>
  </a>
</div><div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto-light"><use href="#svg-sun-with-moon"></use></svg>
              <svg class="theme-icon-when-auto-dark"><use href="#svg-moon-with-sun"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main" id="furo-main-content">
          <section id="authenticated-key-exchange-ake">
<h1>Authenticated Key Exchange (AKE)<a class="headerlink" href="#authenticated-key-exchange-ake" title="Link to this heading">¶</a></h1>
<p>WebOTR’s Authenticated Key Exchange (AKE) implementation provides secure, authenticated establishment of cryptographic keys between two parties. This implementation follows the OTR protocol specification with enhanced security features and performance optimizations.</p>
<nav class="contents local" id="table-of-contents">
<p class="topic-title">Table of Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#overview" id="id1">Overview</a></p></li>
<li><p><a class="reference internal" href="#protocol-architecture" id="id2">Protocol Architecture</a></p>
<ul>
<li><p><a class="reference internal" href="#core-components" id="id3">Core Components</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#ake-protocol-flow" id="id4">AKE Protocol Flow</a></p>
<ul>
<li><p><a class="reference internal" href="#four-message-handshake" id="id5">Four-Message Handshake</a></p></li>
<li><p><a class="reference internal" href="#message-structure" id="id6">Message Structure</a></p></li>
<li><p><a class="reference internal" href="#state-machine" id="id7">State Machine</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#cryptographic-implementation" id="id8">Cryptographic Implementation</a></p>
<ul>
<li><p><a class="reference internal" href="#diffie-hellman-key-exchange" id="id9">Diffie-Hellman Key Exchange</a></p></li>
<li><p><a class="reference internal" href="#digital-signatures" id="id10">Digital Signatures</a></p></li>
<li><p><a class="reference internal" href="#key-derivation" id="id11">Key Derivation</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#implementation-details" id="id12">Implementation Details</a></p>
<ul>
<li><p><a class="reference internal" href="#ake-protocol-engine" id="id13">AKE Protocol Engine</a></p></li>
<li><p><a class="reference internal" href="#message-creation-functions" id="id14">Message Creation Functions</a></p></li>
<li><p><a class="reference internal" href="#message-processing-functions" id="id15">Message Processing Functions</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#security-features" id="id16">Security Features</a></p>
<ul>
<li><p><a class="reference internal" href="#perfect-forward-secrecy" id="id17">Perfect Forward Secrecy</a></p></li>
<li><p><a class="reference internal" href="#mutual-authentication" id="id18">Mutual Authentication</a></p></li>
<li><p><a class="reference internal" href="#deniable-authentication" id="id19">Deniable Authentication</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#performance-optimizations" id="id20">Performance Optimizations</a></p>
<ul>
<li><p><a class="reference internal" href="#cryptographic-optimizations" id="id21">Cryptographic Optimizations</a></p></li>
<li><p><a class="reference internal" href="#protocol-optimizations" id="id22">Protocol Optimizations</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#error-handling" id="id23">Error Handling</a></p></li>
<li><p><a class="reference internal" href="#testing-and-validation" id="id24">Testing and Validation</a></p></li>
</ul>
</nav>
<section id="overview">
<h2><a class="toc-backref" href="#id1" role="doc-backlink">Overview</a><a class="headerlink" href="#overview" title="Link to this heading">¶</a></h2>
<p>The AKE protocol enables two parties to establish a secure communication channel with mutual authentication and perfect forward secrecy. WebOTR’s implementation combines Diffie-Hellman key exchange with digital signatures to provide both confidentiality and authenticity.</p>
</section>
<section id="protocol-architecture">
<h2><a class="toc-backref" href="#id2" role="doc-backlink">Protocol Architecture</a><a class="headerlink" href="#protocol-architecture" title="Link to this heading">¶</a></h2>
<section id="core-components">
<h3><a class="toc-backref" href="#id3" role="doc-backlink">Core Components</a><a class="headerlink" href="#core-components" title="Link to this heading">¶</a></h3>
<dl class="simple">
<dt><strong>AKE Protocol Engine</strong></dt><dd><p>Manages the four-message AKE handshake with state transitions and error handling.</p>
</dd>
<dt><strong>Diffie-Hellman Key Exchange</strong></dt><dd><p>Provides ephemeral key generation and shared secret computation using MODP groups.</p>
</dd>
<dt><strong>Digital Signature Authentication</strong></dt><dd><p>Uses DSA/ECDSA signatures for mutual authentication and non-repudiation.</p>
</dd>
<dt><strong>Key Derivation Functions</strong></dt><dd><p>Derives session keys from shared secrets using HKDF with SHA-256.</p>
</dd>
<dt><strong>State Management</strong></dt><dd><p>Tracks protocol state transitions and maintains security context.</p>
</dd>
</dl>
</section>
</section>
<section id="ake-protocol-flow">
<h2><a class="toc-backref" href="#id4" role="doc-backlink">AKE Protocol Flow</a><a class="headerlink" href="#ake-protocol-flow" title="Link to this heading">¶</a></h2>
<section id="four-message-handshake">
<h3><a class="toc-backref" href="#id5" role="doc-backlink">Four-Message Handshake</a><a class="headerlink" href="#four-message-handshake" title="Link to this heading">¶</a></h3>
<p>The AKE protocol consists of four messages that establish authenticated encryption:</p>
</section>
<section id="message-structure">
<h3><a class="toc-backref" href="#id6" role="doc-backlink">Message Structure</a><a class="headerlink" href="#message-structure" title="Link to this heading">¶</a></h3>
<p><strong>1. DH Commit Message</strong></p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">protocolVersion</span><span class="o">:</span><span class="w"> </span><span class="mf">3</span><span class="p">,</span>
<span class="w">  </span><span class="nx">messageType</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;DH_COMMIT&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">senderInstanceTag</span><span class="o">:</span><span class="w"> </span><span class="mh">0x12345678</span><span class="p">,</span>
<span class="w">  </span><span class="nx">receiverInstanceTag</span><span class="o">:</span><span class="w"> </span><span class="mh">0x00000000</span><span class="p">,</span>
<span class="w">  </span><span class="nx">encryptedGx</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="p">,</span><span class="w"> </span><span class="c1">// AES-encrypted g^x</span>
<span class="w">  </span><span class="nx">hashOfEncryptedGx</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="w"> </span><span class="c1">// SHA-256 hash</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>2. DH Key Message</strong></p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">protocolVersion</span><span class="o">:</span><span class="w"> </span><span class="mf">3</span><span class="p">,</span>
<span class="w">  </span><span class="nx">messageType</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;DH_KEY&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">senderInstanceTag</span><span class="o">:</span><span class="w"> </span><span class="mh">0x87654321</span><span class="p">,</span>
<span class="w">  </span><span class="nx">receiverInstanceTag</span><span class="o">:</span><span class="w"> </span><span class="mh">0x12345678</span><span class="p">,</span>
<span class="w">  </span><span class="nx">publicKey</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="w"> </span><span class="c1">// g^y</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>3. Reveal Signature Message</strong></p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">protocolVersion</span><span class="o">:</span><span class="w"> </span><span class="mf">3</span><span class="p">,</span>
<span class="w">  </span><span class="nx">messageType</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;REVEAL_SIGNATURE&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">senderInstanceTag</span><span class="o">:</span><span class="w"> </span><span class="mh">0x12345678</span><span class="p">,</span>
<span class="w">  </span><span class="nx">receiverInstanceTag</span><span class="o">:</span><span class="w"> </span><span class="mh">0x87654321</span><span class="p">,</span>
<span class="w">  </span><span class="nx">revealedKey</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="p">,</span><span class="w"> </span><span class="c1">// AES key from step 1</span>
<span class="w">  </span><span class="nx">encryptedSignature</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="p">,</span><span class="w"> </span><span class="c1">// Encrypted DSA signature</span>
<span class="w">  </span><span class="nx">macKey</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="w"> </span><span class="c1">// HMAC for integrity</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>4. Signature Message</strong></p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">protocolVersion</span><span class="o">:</span><span class="w"> </span><span class="mf">3</span><span class="p">,</span>
<span class="w">  </span><span class="nx">messageType</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;SIGNATURE&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">senderInstanceTag</span><span class="o">:</span><span class="w"> </span><span class="mh">0x87654321</span><span class="p">,</span>
<span class="w">  </span><span class="nx">receiverInstanceTag</span><span class="o">:</span><span class="w"> </span><span class="mh">0x12345678</span><span class="p">,</span>
<span class="w">  </span><span class="nx">encryptedSignature</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="p">,</span><span class="w"> </span><span class="c1">// Encrypted DSA signature</span>
<span class="w">  </span><span class="nx">macKey</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="w"> </span><span class="c1">// HMAC for integrity</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="state-machine">
<h3><a class="toc-backref" href="#id7" role="doc-backlink">State Machine</a><a class="headerlink" href="#state-machine" title="Link to this heading">¶</a></h3>
<p>The AKE protocol uses a state machine to track progress:</p>
</section>
</section>
<section id="cryptographic-implementation">
<h2><a class="toc-backref" href="#id8" role="doc-backlink">Cryptographic Implementation</a><a class="headerlink" href="#cryptographic-implementation" title="Link to this heading">¶</a></h2>
<section id="diffie-hellman-key-exchange">
<h3><a class="toc-backref" href="#id9" role="doc-backlink">Diffie-Hellman Key Exchange</a><a class="headerlink" href="#diffie-hellman-key-exchange" title="Link to this heading">¶</a></h3>
<p>WebOTR uses the MODP (Modular Exponentiation) group for DH key exchange:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="c1">// MODP Group 14 (2048-bit)</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">MODP_GROUP</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">P</span><span class="o">:</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="p">([...]),</span><span class="w"> </span><span class="c1">// 2048-bit prime</span>
<span class="w">  </span><span class="nx">G</span><span class="o">:</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="p">([</span><span class="mh">0x02</span><span class="p">])</span><span class="w"> </span><span class="c1">// Generator = 2</span>
<span class="p">};</span>

<span class="c1">// Generate DH key pair</span>
<span class="k">async</span><span class="w"> </span><span class="kd">function</span><span class="w"> </span><span class="nx">generateDHKeyPair</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">privateKey</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">generateRandomBytes</span><span class="p">(</span><span class="mf">256</span><span class="p">);</span><span class="w"> </span><span class="c1">// 2048-bit private key</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">publicKey</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">modularExponentiation</span><span class="p">(</span><span class="nx">G</span><span class="p">,</span><span class="w"> </span><span class="nx">privateKey</span><span class="p">,</span><span class="w"> </span><span class="nx">P</span><span class="p">);</span>

<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">privateKey</span><span class="p">,</span>
<span class="w">    </span><span class="nx">publicKey</span>
<span class="w">  </span><span class="p">};</span>
<span class="p">}</span>

<span class="c1">// Compute shared secret</span>
<span class="k">async</span><span class="w"> </span><span class="kd">function</span><span class="w"> </span><span class="nx">dhExchange</span><span class="p">(</span><span class="nx">privateKey</span><span class="p">,</span><span class="w"> </span><span class="nx">theirPublicKey</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="nx">modularExponentiation</span><span class="p">(</span><span class="nx">theirPublicKey</span><span class="p">,</span><span class="w"> </span><span class="nx">privateKey</span><span class="p">,</span><span class="w"> </span><span class="nx">P</span><span class="p">);</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="digital-signatures">
<h3><a class="toc-backref" href="#id10" role="doc-backlink">Digital Signatures</a><a class="headerlink" href="#digital-signatures" title="Link to this heading">¶</a></h3>
<p>Authentication uses DSA or ECDSA signatures:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="c1">// Create signature for authentication</span>
<span class="k">async</span><span class="w"> </span><span class="kd">function</span><span class="w"> </span><span class="nx">createSignature</span><span class="p">(</span><span class="nx">dhKeyPair</span><span class="p">,</span><span class="w"> </span><span class="nx">dsaKeyPair</span><span class="p">,</span><span class="w"> </span><span class="nx">sharedSecret</span><span class="p">,</span><span class="w"> </span><span class="nx">instanceTag</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">signatureData</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">publicKey</span><span class="o">:</span><span class="w"> </span><span class="nx">dhKeyPair</span><span class="p">.</span><span class="nx">publicKey</span><span class="p">,</span>
<span class="w">    </span><span class="nx">dhSharedSecret</span><span class="o">:</span><span class="w"> </span><span class="nx">sharedSecret</span><span class="p">,</span>
<span class="w">    </span><span class="nx">instanceTag</span><span class="o">:</span><span class="w"> </span><span class="nx">instanceTag</span>
<span class="w">  </span><span class="p">};</span>

<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">signature</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">sign</span><span class="p">(</span>
<span class="w">    </span><span class="nb">JSON</span><span class="p">.</span><span class="nx">stringify</span><span class="p">(</span><span class="nx">signatureData</span><span class="p">),</span>
<span class="w">    </span><span class="nx">dsaKeyPair</span><span class="p">.</span><span class="nx">privateKey</span>
<span class="w">  </span><span class="p">);</span>

<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="nx">signature</span><span class="p">;</span>
<span class="p">}</span>

<span class="c1">// Verify signature for authentication</span>
<span class="k">async</span><span class="w"> </span><span class="kd">function</span><span class="w"> </span><span class="nx">verifySignature</span><span class="p">(</span><span class="nx">signature</span><span class="p">,</span><span class="w"> </span><span class="nx">publicKey</span><span class="p">,</span><span class="w"> </span><span class="nx">data</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">verify</span><span class="p">(</span><span class="nx">data</span><span class="p">,</span><span class="w"> </span><span class="nx">signature</span><span class="p">,</span><span class="w"> </span><span class="nx">publicKey</span><span class="p">);</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="key-derivation">
<h3><a class="toc-backref" href="#id11" role="doc-backlink">Key Derivation</a><a class="headerlink" href="#key-derivation" title="Link to this heading">¶</a></h3>
<p>Session keys are derived using HKDF (HMAC-based Key Derivation Function):</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="kd">function</span><span class="w"> </span><span class="nx">deriveKeys</span><span class="p">(</span><span class="nx">sharedSecret</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="c1">// Use HKDF to derive multiple keys from shared secret</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">salt</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="p">(</span><span class="mf">32</span><span class="p">);</span><span class="w"> </span><span class="c1">// Zero salt</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">info</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">TextEncoder</span><span class="p">().</span><span class="nx">encode</span><span class="p">(</span><span class="s1">&#39;WebOTR-v3-key-derivation&#39;</span><span class="p">);</span>

<span class="w">  </span><span class="c1">// Derive 128 bytes of key material</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">keyMaterial</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">hkdf</span><span class="p">(</span><span class="nx">sharedSecret</span><span class="p">,</span><span class="w"> </span><span class="nx">salt</span><span class="p">,</span><span class="w"> </span><span class="nx">info</span><span class="p">,</span><span class="w"> </span><span class="mf">128</span><span class="p">);</span>

<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">sendingAESKey</span><span class="o">:</span><span class="w"> </span><span class="nx">keyMaterial</span><span class="p">.</span><span class="nx">slice</span><span class="p">(</span><span class="mf">0</span><span class="p">,</span><span class="w"> </span><span class="mf">32</span><span class="p">),</span><span class="w">    </span><span class="c1">// 256-bit AES key</span>
<span class="w">    </span><span class="nx">receivingAESKey</span><span class="o">:</span><span class="w"> </span><span class="nx">keyMaterial</span><span class="p">.</span><span class="nx">slice</span><span class="p">(</span><span class="mf">32</span><span class="p">,</span><span class="w"> </span><span class="mf">64</span><span class="p">),</span><span class="w"> </span><span class="c1">// 256-bit AES key</span>
<span class="w">    </span><span class="nx">sendingMACKey</span><span class="o">:</span><span class="w"> </span><span class="nx">keyMaterial</span><span class="p">.</span><span class="nx">slice</span><span class="p">(</span><span class="mf">64</span><span class="p">,</span><span class="w"> </span><span class="mf">96</span><span class="p">),</span><span class="w">   </span><span class="c1">// 256-bit HMAC key</span>
<span class="w">    </span><span class="nx">receivingMACKey</span><span class="o">:</span><span class="w"> </span><span class="nx">keyMaterial</span><span class="p">.</span><span class="nx">slice</span><span class="p">(</span><span class="mf">96</span><span class="p">,</span><span class="w"> </span><span class="mf">128</span><span class="p">),</span><span class="w"> </span><span class="c1">// 256-bit HMAC key</span>
<span class="w">    </span><span class="nx">ssid</span><span class="o">:</span><span class="w"> </span><span class="nx">keyMaterial</span><span class="p">.</span><span class="nx">slice</span><span class="p">(</span><span class="mf">0</span><span class="p">,</span><span class="w"> </span><span class="mf">8</span><span class="p">)</span><span class="w"> </span><span class="c1">// 64-bit session ID</span>
<span class="w">  </span><span class="p">};</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="implementation-details">
<h2><a class="toc-backref" href="#id12" role="doc-backlink">Implementation Details</a><a class="headerlink" href="#implementation-details" title="Link to this heading">¶</a></h2>
<section id="ake-protocol-engine">
<h3><a class="toc-backref" href="#id13" role="doc-backlink">AKE Protocol Engine</a><a class="headerlink" href="#ake-protocol-engine" title="Link to this heading">¶</a></h3>
<p>The main AKE implementation provides a complete protocol engine:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">import</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">createDHCommit</span><span class="p">,</span>
<span class="w">  </span><span class="nx">createDHKey</span><span class="p">,</span>
<span class="w">  </span><span class="nx">createRevealSignature</span><span class="p">,</span>
<span class="w">  </span><span class="nx">createSignature</span><span class="p">,</span>
<span class="w">  </span><span class="nx">processDHCommit</span><span class="p">,</span>
<span class="w">  </span><span class="nx">processDHKey</span><span class="p">,</span>
<span class="w">  </span><span class="nx">processRevealSignature</span><span class="p">,</span>
<span class="w">  </span><span class="nx">processSignature</span><span class="p">,</span>
<span class="w">  </span><span class="nx">startAKE</span>
<span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;webOTR/core/protocol/ake&#39;</span><span class="p">;</span>
</pre></div>
</div>
</section>
<section id="message-creation-functions">
<h3><a class="toc-backref" href="#id14" role="doc-backlink">Message Creation Functions</a><a class="headerlink" href="#message-creation-functions" title="Link to this heading">¶</a></h3>
<p><strong>Create DH Commit Message</strong></p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="kd">function</span><span class="w"> </span><span class="nx">createDHCommit</span><span class="p">(</span><span class="nx">dhKeyPair</span><span class="p">,</span><span class="w"> </span><span class="nx">protocolVersion</span><span class="p">,</span><span class="w"> </span><span class="nx">instanceTag</span><span class="p">,</span><span class="w"> </span><span class="nx">receiverInstanceTag</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="c1">// Generate random AES key and IV for encryption</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">aesKey</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">generateRandomBytes</span><span class="p">(</span><span class="mf">32</span><span class="p">);</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">iv</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">generateRandomBytes</span><span class="p">(</span><span class="mf">16</span><span class="p">);</span>

<span class="w">  </span><span class="c1">// Encrypt our DH public key</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">encryptedGx</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">encrypt</span><span class="p">(</span><span class="nx">dhKeyPair</span><span class="p">.</span><span class="nx">publicKey</span><span class="p">,</span><span class="w"> </span><span class="nx">aesKey</span><span class="p">,</span><span class="w"> </span><span class="nx">iv</span><span class="p">);</span>

<span class="w">  </span><span class="c1">// Create hash of encrypted public key</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">hashOfEncryptedGx</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">sha256</span><span class="p">(</span><span class="nx">encryptedGx</span><span class="p">);</span>

<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">protocolVersion</span><span class="p">,</span>
<span class="w">    </span><span class="nx">messageType</span><span class="o">:</span><span class="w"> </span><span class="nx">MESSAGE_TYPE</span><span class="p">.</span><span class="nx">DH_COMMIT</span><span class="p">,</span>
<span class="w">    </span><span class="nx">senderInstanceTag</span><span class="o">:</span><span class="w"> </span><span class="nx">instanceTag</span><span class="p">,</span>
<span class="w">    </span><span class="nx">receiverInstanceTag</span><span class="p">,</span>
<span class="w">    </span><span class="nx">encryptedGx</span><span class="p">,</span>
<span class="w">    </span><span class="nx">hashOfEncryptedGx</span><span class="p">,</span>
<span class="w">    </span><span class="nx">aesKey</span><span class="p">,</span><span class="w"> </span><span class="c1">// Stored for later reveal</span>
<span class="w">    </span><span class="nx">iv</span><span class="w">      </span><span class="c1">// Stored for later reveal</span>
<span class="w">  </span><span class="p">};</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Create DH Key Message</strong></p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="kd">function</span><span class="w"> </span><span class="nx">createDHKey</span><span class="p">(</span><span class="nx">dhKeyPair</span><span class="p">,</span><span class="w"> </span><span class="nx">protocolVersion</span><span class="p">,</span><span class="w"> </span><span class="nx">instanceTag</span><span class="p">,</span><span class="w"> </span><span class="nx">receiverInstanceTag</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">protocolVersion</span><span class="p">,</span>
<span class="w">    </span><span class="nx">messageType</span><span class="o">:</span><span class="w"> </span><span class="nx">MESSAGE_TYPE</span><span class="p">.</span><span class="nx">DH_KEY</span><span class="p">,</span>
<span class="w">    </span><span class="nx">senderInstanceTag</span><span class="o">:</span><span class="w"> </span><span class="nx">instanceTag</span><span class="p">,</span>
<span class="w">    </span><span class="nx">receiverInstanceTag</span><span class="p">,</span>
<span class="w">    </span><span class="nx">publicKey</span><span class="o">:</span><span class="w"> </span><span class="nx">dhKeyPair</span><span class="p">.</span><span class="nx">publicKey</span>
<span class="w">  </span><span class="p">};</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="message-processing-functions">
<h3><a class="toc-backref" href="#id15" role="doc-backlink">Message Processing Functions</a><a class="headerlink" href="#message-processing-functions" title="Link to this heading">¶</a></h3>
<p><strong>Process DH Commit</strong></p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="kd">function</span><span class="w"> </span><span class="nx">processDHCommit</span><span class="p">(</span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="nx">state</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="c1">// Store the DH commit for later verification</span>
<span class="w">  </span><span class="nx">state</span><span class="p">.</span><span class="nx">dhCommitMessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">message</span><span class="p">;</span>

<span class="w">  </span><span class="c1">// Generate our DH key pair</span>
<span class="w">  </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="nx">state</span><span class="p">.</span><span class="nx">dhKeyPair</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">state</span><span class="p">.</span><span class="nx">dhKeyPair</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">generateDHKeyPair</span><span class="p">();</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="c1">// Create DH key response</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">dhKeyMessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">createDHKey</span><span class="p">(</span>
<span class="w">    </span><span class="nx">state</span><span class="p">.</span><span class="nx">dhKeyPair</span><span class="p">,</span>
<span class="w">    </span><span class="nx">state</span><span class="p">.</span><span class="nx">protocolVersion</span><span class="p">,</span>
<span class="w">    </span><span class="nx">state</span><span class="p">.</span><span class="nx">instanceTag</span><span class="p">,</span>
<span class="w">    </span><span class="nx">message</span><span class="p">.</span><span class="nx">senderInstanceTag</span>
<span class="w">  </span><span class="p">);</span>

<span class="w">  </span><span class="c1">// Update state</span>
<span class="w">  </span><span class="nx">state</span><span class="p">.</span><span class="nx">handleDHCommit</span><span class="p">();</span>

<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">state</span><span class="p">,</span>
<span class="w">    </span><span class="nx">message</span><span class="o">:</span><span class="w"> </span><span class="nx">dhKeyMessage</span>
<span class="w">  </span><span class="p">};</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Process DH Key</strong></p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="kd">function</span><span class="w"> </span><span class="nx">processDHKey</span><span class="p">(</span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="nx">state</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="c1">// Store their public key</span>
<span class="w">  </span><span class="nx">state</span><span class="p">.</span><span class="nx">theirPublicKey</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">message</span><span class="p">.</span><span class="nx">publicKey</span><span class="p">;</span>

<span class="w">  </span><span class="c1">// Compute shared secret</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">sharedSecret</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">dhExchange</span><span class="p">(</span>
<span class="w">    </span><span class="nx">state</span><span class="p">.</span><span class="nx">dhKeyPair</span><span class="p">.</span><span class="nx">privateKey</span><span class="p">,</span>
<span class="w">    </span><span class="nx">message</span><span class="p">.</span><span class="nx">publicKey</span>
<span class="w">  </span><span class="p">);</span>

<span class="w">  </span><span class="c1">// Derive session keys</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">keys</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">deriveKeys</span><span class="p">(</span><span class="nx">sharedSecret</span><span class="p">);</span>

<span class="w">  </span><span class="c1">// Create reveal signature message</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">revealSignatureMessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">createRevealSignature</span><span class="p">(</span>
<span class="w">    </span><span class="nx">state</span><span class="p">.</span><span class="nx">dhKeyPair</span><span class="p">,</span>
<span class="w">    </span><span class="nx">state</span><span class="p">.</span><span class="nx">dsaKeyPair</span><span class="p">,</span>
<span class="w">    </span><span class="nx">sharedSecret</span><span class="p">,</span>
<span class="w">    </span><span class="nx">state</span><span class="p">.</span><span class="nx">protocolVersion</span><span class="p">,</span>
<span class="w">    </span><span class="nx">state</span><span class="p">.</span><span class="nx">instanceTag</span><span class="p">,</span>
<span class="w">    </span><span class="nx">message</span><span class="p">.</span><span class="nx">senderInstanceTag</span><span class="p">,</span>
<span class="w">    </span><span class="nx">state</span><span class="p">.</span><span class="nx">dhCommitMessage</span><span class="p">.</span><span class="nx">aesKey</span><span class="p">,</span>
<span class="w">    </span><span class="nx">state</span><span class="p">.</span><span class="nx">dhCommitMessage</span><span class="p">.</span><span class="nx">iv</span>
<span class="w">  </span><span class="p">);</span>

<span class="w">  </span><span class="c1">// Store keys and update state</span>
<span class="w">  </span><span class="nx">state</span><span class="p">.</span><span class="nx">setSessionKeys</span><span class="p">(</span><span class="nx">keys</span><span class="p">);</span>
<span class="w">  </span><span class="nx">state</span><span class="p">.</span><span class="nx">handleDHKey</span><span class="p">();</span>

<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">state</span><span class="p">,</span>
<span class="w">    </span><span class="nx">message</span><span class="o">:</span><span class="w"> </span><span class="nx">revealSignatureMessage</span>
<span class="w">  </span><span class="p">};</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="security-features">
<h2><a class="toc-backref" href="#id16" role="doc-backlink">Security Features</a><a class="headerlink" href="#security-features" title="Link to this heading">¶</a></h2>
<section id="perfect-forward-secrecy">
<h3><a class="toc-backref" href="#id17" role="doc-backlink">Perfect Forward Secrecy</a><a class="headerlink" href="#perfect-forward-secrecy" title="Link to this heading">¶</a></h3>
<p>The AKE protocol provides perfect forward secrecy through:</p>
<ul class="simple">
<li><p><strong>Ephemeral DH Keys</strong>: New key pairs generated for each session</p></li>
<li><p><strong>Secure Key Deletion</strong>: Old keys are securely erased after use</p></li>
<li><p><strong>No Long-term Key Reuse</strong>: Session keys are never reused</p></li>
</ul>
</section>
<section id="mutual-authentication">
<h3><a class="toc-backref" href="#id18" role="doc-backlink">Mutual Authentication</a><a class="headerlink" href="#mutual-authentication" title="Link to this heading">¶</a></h3>
<p>Both parties authenticate each other through:</p>
<ul class="simple">
<li><p><strong>Digital Signatures</strong>: DSA/ECDSA signatures prove identity</p></li>
<li><p><strong>Key Binding</strong>: Signatures include DH public keys and shared secret</p></li>
<li><p><strong>Replay Protection</strong>: Instance tags prevent replay attacks</p></li>
</ul>
</section>
<section id="deniable-authentication">
<h3><a class="toc-backref" href="#id19" role="doc-backlink">Deniable Authentication</a><a class="headerlink" href="#deniable-authentication" title="Link to this heading">¶</a></h3>
<p>The protocol provides deniable authentication:</p>
<ul class="simple">
<li><p><strong>Malleable Signatures</strong>: Signatures can be forged after the fact</p></li>
<li><p><strong>No Non-repudiation</strong>: Participants cannot prove messages to third parties</p></li>
<li><p><strong>Plausible Deniability</strong>: Messages could have been sent by either party</p></li>
</ul>
</section>
</section>
<section id="performance-optimizations">
<h2><a class="toc-backref" href="#id20" role="doc-backlink">Performance Optimizations</a><a class="headerlink" href="#performance-optimizations" title="Link to this heading">¶</a></h2>
<section id="cryptographic-optimizations">
<h3><a class="toc-backref" href="#id21" role="doc-backlink">Cryptographic Optimizations</a><a class="headerlink" href="#cryptographic-optimizations" title="Link to this heading">¶</a></h3>
<ul class="simple">
<li><p><strong>Web Crypto API</strong>: Uses browser’s native crypto when available</p></li>
<li><p><strong>Fallback Implementation</strong>: Pure JavaScript for compatibility</p></li>
<li><p><strong>Efficient Modular Arithmetic</strong>: Optimized big integer operations</p></li>
<li><p><strong>Key Caching</strong>: Reuses expensive computations when possible</p></li>
</ul>
</section>
<section id="protocol-optimizations">
<h3><a class="toc-backref" href="#id22" role="doc-backlink">Protocol Optimizations</a><a class="headerlink" href="#protocol-optimizations" title="Link to this heading">¶</a></h3>
<ul class="simple">
<li><p><strong>Parallel Processing</strong>: Concurrent signature verification and key derivation</p></li>
<li><p><strong>Early Validation</strong>: Input validation before expensive operations</p></li>
<li><p><strong>State Caching</strong>: Efficient state management and transitions</p></li>
<li><p><strong>Memory Management</strong>: Secure cleanup of sensitive data</p></li>
</ul>
</section>
</section>
<section id="error-handling">
<h2><a class="toc-backref" href="#id23" role="doc-backlink">Error Handling</a><a class="headerlink" href="#error-handling" title="Link to this heading">¶</a></h2>
<p>The AKE implementation includes comprehensive error handling:</p>
<p><strong>Protocol Errors</strong></p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">processDHCommit</span><span class="p">(</span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="nx">state</span><span class="p">);</span>
<span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="w"> </span><span class="ow">instanceof</span><span class="w"> </span><span class="nx">AKEProtocolError</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Handle protocol-specific errors</span>
<span class="w">    </span><span class="nx">console</span><span class="p">.</span><span class="nx">error</span><span class="p">(</span><span class="s1">&#39;AKE Protocol Error:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">.</span><span class="nx">code</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">.</span><span class="nx">message</span><span class="p">);</span>
<span class="w">    </span><span class="nx">state</span><span class="p">.</span><span class="nx">goPlaintext</span><span class="p">();</span><span class="w"> </span><span class="c1">// Reset to plaintext state</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Cryptographic Errors</strong></p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">sharedSecret</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">dhExchange</span><span class="p">(</span><span class="nx">privateKey</span><span class="p">,</span><span class="w"> </span><span class="nx">publicKey</span><span class="p">);</span>
<span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="w"> </span><span class="ow">instanceof</span><span class="w"> </span><span class="nx">CryptographicError</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Handle crypto failures</span>
<span class="w">    </span><span class="nx">console</span><span class="p">.</span><span class="nx">error</span><span class="p">(</span><span class="s1">&#39;Cryptographic Error:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">.</span><span class="nx">message</span><span class="p">);</span>
<span class="w">    </span><span class="k">throw</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">AKEError</span><span class="p">(</span><span class="s1">&#39;Key exchange failed&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;CRYPTO_ERROR&#39;</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>State Validation</strong></p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">function</span><span class="w"> </span><span class="nx">validateState</span><span class="p">(</span><span class="nx">state</span><span class="p">,</span><span class="w"> </span><span class="nx">expectedState</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">state</span><span class="p">.</span><span class="nx">getState</span><span class="p">()</span><span class="w"> </span><span class="o">!==</span><span class="w"> </span><span class="nx">expectedState</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">throw</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">AKEError</span><span class="p">(</span>
<span class="w">      </span><span class="sb">`Invalid state: expected </span><span class="si">${</span><span class="nx">expectedState</span><span class="si">}</span><span class="sb">, got </span><span class="si">${</span><span class="nx">state</span><span class="p">.</span><span class="nx">getState</span><span class="p">()</span><span class="si">}</span><span class="sb">`</span><span class="p">,</span>
<span class="w">      </span><span class="s1">&#39;INVALID_STATE&#39;</span>
<span class="w">    </span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="testing-and-validation">
<h2><a class="toc-backref" href="#id24" role="doc-backlink">Testing and Validation</a><a class="headerlink" href="#testing-and-validation" title="Link to this heading">¶</a></h2>
<p>The AKE implementation includes comprehensive test suites:</p>
<p><strong>Unit Tests</strong></p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="nx">describe</span><span class="p">(</span><span class="s1">&#39;AKE Protocol&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">test</span><span class="p">(</span><span class="s1">&#39;should complete full AKE handshake&#39;</span><span class="p">,</span><span class="w"> </span><span class="k">async</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">alice</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">OtrState</span><span class="p">();</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">bob</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">OtrState</span><span class="p">();</span>

<span class="w">    </span><span class="c1">// Alice starts AKE</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">akeStart</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">startAKE</span><span class="p">(</span><span class="nx">alice</span><span class="p">);</span>

<span class="w">    </span><span class="c1">// Bob processes DH commit</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">dhKeyResponse</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">processDHCommit</span><span class="p">(</span><span class="nx">akeStart</span><span class="p">.</span><span class="nx">dhCommit</span><span class="p">,</span><span class="w"> </span><span class="nx">bob</span><span class="p">);</span>

<span class="w">    </span><span class="c1">// Alice processes DH key</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">revealSigResponse</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">processDHKey</span><span class="p">(</span><span class="nx">dhKeyResponse</span><span class="p">.</span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="nx">alice</span><span class="p">);</span>

<span class="w">    </span><span class="c1">// Bob processes reveal signature</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">sigResponse</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">processRevealSignature</span><span class="p">(</span><span class="nx">revealSigResponse</span><span class="p">.</span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="nx">bob</span><span class="p">);</span>

<span class="w">    </span><span class="c1">// Alice processes signature</span>
<span class="w">    </span><span class="k">await</span><span class="w"> </span><span class="nx">processSignature</span><span class="p">(</span><span class="nx">sigResponse</span><span class="p">.</span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="nx">alice</span><span class="p">);</span>

<span class="w">    </span><span class="c1">// Both parties should be in encrypted state</span>
<span class="w">    </span><span class="nx">expect</span><span class="p">(</span><span class="nx">alice</span><span class="p">.</span><span class="nx">getState</span><span class="p">()).</span><span class="nx">toBe</span><span class="p">(</span><span class="nx">STATE</span><span class="p">.</span><span class="nx">ENCRYPTED</span><span class="p">);</span>
<span class="w">    </span><span class="nx">expect</span><span class="p">(</span><span class="nx">bob</span><span class="p">.</span><span class="nx">getState</span><span class="p">()).</span><span class="nx">toBe</span><span class="p">(</span><span class="nx">STATE</span><span class="p">.</span><span class="nx">ENCRYPTED</span><span class="p">);</span>
<span class="w">  </span><span class="p">});</span>
<span class="p">});</span>
</pre></div>
</div>
<p><strong>Integration Tests</strong></p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="nx">describe</span><span class="p">(</span><span class="s1">&#39;AKE Integration&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">test</span><span class="p">(</span><span class="s1">&#39;should establish secure communication&#39;</span><span class="p">,</span><span class="w"> </span><span class="k">async</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">session1</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">OTRSession</span><span class="p">();</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">session2</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">OTRSession</span><span class="p">();</span>

<span class="w">    </span><span class="c1">// Establish AKE</span>
<span class="w">    </span><span class="k">await</span><span class="w"> </span><span class="nx">establishAKE</span><span class="p">(</span><span class="nx">session1</span><span class="p">,</span><span class="w"> </span><span class="nx">session2</span><span class="p">);</span>

<span class="w">    </span><span class="c1">// Test encrypted communication</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">plaintext</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;Hello, secure world!&#39;</span><span class="p">;</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">encrypted</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">session1</span><span class="p">.</span><span class="nx">encrypt</span><span class="p">(</span><span class="nx">plaintext</span><span class="p">);</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">decrypted</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">session2</span><span class="p">.</span><span class="nx">decrypt</span><span class="p">(</span><span class="nx">encrypted</span><span class="p">);</span>

<span class="w">    </span><span class="nx">expect</span><span class="p">(</span><span class="nx">decrypted</span><span class="p">).</span><span class="nx">toBe</span><span class="p">(</span><span class="nx">plaintext</span><span class="p">);</span>
<span class="w">  </span><span class="p">});</span>
<span class="p">});</span>
</pre></div>
</div>
<p>This comprehensive AKE implementation provides secure, authenticated key exchange with perfect forward secrecy, mutual authentication, and deniable authentication properties essential for private communication.</p>
</section>
</section>

        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          <a class="next-page" href="ake-architecture.html">
              <div class="page-info">
                <div class="context">
                  <span>Next</span>
                </div>
                <div class="title">AKE Architecture</div>
              </div>
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
            </a>
          <a class="prev-page" href="ake-summary.html">
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
              <div class="page-info">
                <div class="context">
                  <span>Previous</span>
                </div>
                
                <div class="title">AKE Documentation Summary</div>
                
              </div>
            </a>
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 1980, WebOTR Team
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer">
      
      
      <div class="toc-sticky toc-scroll">
        <div class="toc-title-container">
          <span class="toc-title">
            On this page
          </span>
        </div>
        <div class="toc-tree-container">
          <div class="toc-tree">
            <ul>
<li><a class="reference internal" href="#">Authenticated Key Exchange (AKE)</a><ul>
<li><a class="reference internal" href="#overview">Overview</a></li>
<li><a class="reference internal" href="#protocol-architecture">Protocol Architecture</a><ul>
<li><a class="reference internal" href="#core-components">Core Components</a></li>
</ul>
</li>
<li><a class="reference internal" href="#ake-protocol-flow">AKE Protocol Flow</a><ul>
<li><a class="reference internal" href="#four-message-handshake">Four-Message Handshake</a></li>
<li><a class="reference internal" href="#message-structure">Message Structure</a></li>
<li><a class="reference internal" href="#state-machine">State Machine</a></li>
</ul>
</li>
<li><a class="reference internal" href="#cryptographic-implementation">Cryptographic Implementation</a><ul>
<li><a class="reference internal" href="#diffie-hellman-key-exchange">Diffie-Hellman Key Exchange</a></li>
<li><a class="reference internal" href="#digital-signatures">Digital Signatures</a></li>
<li><a class="reference internal" href="#key-derivation">Key Derivation</a></li>
</ul>
</li>
<li><a class="reference internal" href="#implementation-details">Implementation Details</a><ul>
<li><a class="reference internal" href="#ake-protocol-engine">AKE Protocol Engine</a></li>
<li><a class="reference internal" href="#message-creation-functions">Message Creation Functions</a></li>
<li><a class="reference internal" href="#message-processing-functions">Message Processing Functions</a></li>
</ul>
</li>
<li><a class="reference internal" href="#security-features">Security Features</a><ul>
<li><a class="reference internal" href="#perfect-forward-secrecy">Perfect Forward Secrecy</a></li>
<li><a class="reference internal" href="#mutual-authentication">Mutual Authentication</a></li>
<li><a class="reference internal" href="#deniable-authentication">Deniable Authentication</a></li>
</ul>
</li>
<li><a class="reference internal" href="#performance-optimizations">Performance Optimizations</a><ul>
<li><a class="reference internal" href="#cryptographic-optimizations">Cryptographic Optimizations</a></li>
<li><a class="reference internal" href="#protocol-optimizations">Protocol Optimizations</a></li>
</ul>
</li>
<li><a class="reference internal" href="#error-handling">Error Handling</a></li>
<li><a class="reference internal" href="#testing-and-validation">Testing and Validation</a></li>
</ul>
</li>
</ul>

          </div>
        </div>
      </div>
      
      
    </aside>
  </div>
</div><script src="../_static/documentation_options.js?v=01f34227"></script>
    <script src="../_static/doctools.js?v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/scripts/furo.js?v=5fa4622c"></script>
    <script src="../_static/mermaid-renderer.js?v=b683124c"></script>
    </body>
</html>