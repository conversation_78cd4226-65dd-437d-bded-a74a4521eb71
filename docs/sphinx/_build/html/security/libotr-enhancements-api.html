<!doctype html>
<html class="no-js" lang="en" data-content_root="../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><meta name="viewport" content="width=device-width, initial-scale=1" />
<link rel="index" title="Index" href="../genindex.html" /><link rel="search" title="Search" href="../search.html" /><link rel="next" title="libOTR Enhancements Testing and Validation" href="libotr-enhancements-testing.html" /><link rel="prev" title="libOTR Enhancements Implementation" href="libotr-enhancements-implementation.html" />

    <link rel="shortcut icon" href="../_static/favicon.svg"/><!-- Generated with Sphinx 7.4.7 and Furo 2024.08.06 -->
        <title>libOTR Enhancements API Reference - WebOTR Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../_static/styles/furo.css?v=354aac6f" />
    <link rel="stylesheet" type="text/css" href="../_static/styles/furo-extensions.css?v=302659d7" />
    <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=21a7f706" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  --color-brand-primary: #6366f1;
  --color-brand-content: #6366f1;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8b5cf6;
  --color-brand-content: #8b5cf6;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8b5cf6;
  --color-brand-content: #8b5cf6;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-with-moon" viewBox="0 0 24 24">
    <title>Auto light/dark, in light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
      class="icon-custom-derived-from-feather-sun-and-tabler-moon">
      <path style="opacity: 50%" d="M 5.411 14.504 C 5.471 14.504 5.532 14.504 5.591 14.504 C 3.639 16.319 4.383 19.569 6.931 20.352 C 7.693 20.586 8.512 20.551 9.25 20.252 C 8.023 23.207 4.056 23.725 2.11 21.184 C 0.166 18.642 1.702 14.949 4.874 14.536 C 5.051 14.512 5.231 14.5 5.411 14.5 L 5.411 14.504 Z"/>
      <line x1="14.5" y1="3.25" x2="14.5" y2="1.25"/>
      <line x1="14.5" y1="15.85" x2="14.5" y2="17.85"/>
      <line x1="10.044" y1="5.094" x2="8.63" y2="3.68"/>
      <line x1="19" y1="14.05" x2="20.414" y2="15.464"/>
      <line x1="8.2" y1="9.55" x2="6.2" y2="9.55"/>
      <line x1="20.8" y1="9.55" x2="22.8" y2="9.55"/>
      <line x1="10.044" y1="14.006" x2="8.63" y2="15.42"/>
      <line x1="19" y1="5.05" x2="20.414" y2="3.636"/>
      <circle cx="14.5" cy="9.55" r="3.6"/>
    </svg>
  </symbol>
  <symbol id="svg-moon-with-sun" viewBox="0 0 24 24">
    <title>Auto light/dark, in dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
      class="icon-custom-derived-from-feather-sun-and-tabler-moon">
      <path d="M 8.282 7.007 C 8.385 7.007 8.494 7.007 8.595 7.007 C 5.18 10.184 6.481 15.869 10.942 17.24 C 12.275 17.648 13.706 17.589 15 17.066 C 12.851 22.236 5.91 23.143 2.505 18.696 C -0.897 14.249 1.791 7.786 7.342 7.063 C 7.652 7.021 7.965 7 8.282 7 L 8.282 7.007 Z"/>
      <line style="opacity: 50%" x1="18" y1="3.705" x2="18" y2="2.5"/>
      <line style="opacity: 50%" x1="18" y1="11.295" x2="18" y2="12.5"/>
      <line style="opacity: 50%" x1="15.316" y1="4.816" x2="14.464" y2="3.964"/>
      <line style="opacity: 50%" x1="20.711" y1="10.212" x2="21.563" y2="11.063"/>
      <line style="opacity: 50%" x1="14.205" y1="7.5" x2="13.001" y2="7.5"/>
      <line style="opacity: 50%" x1="21.795" y1="7.5" x2="23" y2="7.5"/>
      <line style="opacity: 50%" x1="15.316" y1="10.184" x2="14.464" y2="11.036"/>
      <line style="opacity: 50%" x1="20.711" y1="4.789" x2="21.563" y2="3.937"/>
      <circle style="opacity: 50%" cx="18" cy="7.5" r="2.169"/>
    </svg>
  </symbol>
  <symbol id="svg-pencil" viewBox="0 0 24 24">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-pencil-code">
      <path d="M4 20h4l10.5 -10.5a2.828 2.828 0 1 0 -4 -4l-10.5 10.5v4" />
      <path d="M13.5 6.5l4 4" />
      <path d="M20 21l2 -2l-2 -2" />
      <path d="M17 17l-2 2l2 2" />
    </svg>
  </symbol>
  <symbol id="svg-eye" viewBox="0 0 24 24">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-eye-code">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0" />
      <path
        d="M11.11 17.958c-3.209 -.307 -5.91 -2.293 -8.11 -5.958c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6c-.21 .352 -.427 .688 -.647 1.008" />
      <path d="M20 21l2 -2l-2 -2" />
      <path d="M17 17l-2 2l2 2" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>

<a class="skip-to-content muted-link" href="#furo-main-content">Skip to content</a>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../index.html"><div class="brand">WebOTR Documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto-light"><use href="#svg-sun-with-moon"></use></svg>
          <svg class="theme-icon-when-auto-dark"><use href="#svg-moon-with-sun"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><a class="sidebar-brand" href="../index.html">
  
  <div class="sidebar-logo-container">
    <img class="sidebar-logo" src="../_static/logo.svg" alt="Logo"/>
  </div>
  
  <span class="sidebar-brand-text">WebOTR Documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-scroll"><div class="sidebar-tree">
  <p class="caption" role="heading"><span class="caption-text">User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user-guide/installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../user-guide/getting-started.html">Getting Started</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../developer-guide/architecture.html">Architecture Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../developer-guide/contributing.html">Contributing to WebOTR</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Protocol Compliance &amp; Advanced Features</span></p>
<ul>
<li class="toctree-l1 has-children"><a class="reference internal" href="../protocol/index.html">Protocol Compliance &amp; Advanced Features</a><input class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of Protocol Compliance &amp; Advanced Features</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul>
<li class="toctree-l2"><a class="reference internal" href="../protocol/overview.html">Protocol Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/version-negotiation.html">Version Negotiation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/message-ordering.html">Message Ordering</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/enhanced-smp.html">Enhanced SMP</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/policy-manager.html">Policy Manager</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/integration-guide.html">Integration Guide</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/troubleshooting.html">Troubleshooting Guide</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Security</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="overview.html">Security Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements-summary.html">libOTR Security Enhancements Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements.html">libOTR Security Enhancements</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements-implementation.html">libOTR Enhancements Implementation</a></li>
<li class="toctree-l1 current current-page"><a class="current reference internal" href="#">libOTR Enhancements API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements-testing.html">libOTR Enhancements Testing and Validation</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy-summary.html">Forward Secrecy Documentation Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy.html">Forward Secrecy Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy-architecture.html">Forward Secrecy Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy-implementation.html">Forward Secrecy Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy-api.html">Forward Secrecy API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake-summary.html">AKE Documentation Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake.html">Authenticated Key Exchange (AKE)</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake-architecture.html">AKE Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake-implementation.html">AKE Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake-api.html">AKE API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography-summary.html">Steganography Documentation Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography.html">Steganography Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography-architecture.html">Steganography Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography-implementation.html">Steganography Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography-api.html">Steganography API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../reference/faq.html">Frequently Asked Questions</a></li>
<li class="toctree-l1"><a class="reference internal" href="../reference/changelog.html">Changelog</a></li>
</ul>

</div>
</div>

      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          <div class="view-this-page">
  <a class="muted-link" href="https://github.com/forkrul/webOTteR/blob/main/docs/sphinx/security/libotr-enhancements-api.rst?plain=true" title="View this page">
    <svg><use href="#svg-eye"></use></svg>
    <span class="visually-hidden">View this page</span>
  </a>
</div><div class="edit-this-page">
  <a class="muted-link" href="https://github.com/forkrul/webOTteR/edit/main/docs/sphinx/security/libotr-enhancements-api.rst" title="Edit this page">
    <svg><use href="#svg-pencil"></use></svg>
    <span class="visually-hidden">Edit this page</span>
  </a>
</div><div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto-light"><use href="#svg-sun-with-moon"></use></svg>
              <svg class="theme-icon-when-auto-dark"><use href="#svg-moon-with-sun"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main" id="furo-main-content">
          <section id="libotr-enhancements-api-reference">
<h1>libOTR Enhancements API Reference<a class="headerlink" href="#libotr-enhancements-api-reference" title="Link to this heading">¶</a></h1>
<p>This document provides comprehensive API reference for WebOTR’s libOTR security enhancements.</p>
<section id="constant-time-operations-api">
<h2>Constant-Time Operations API<a class="headerlink" href="#constant-time-operations-api" title="Link to this heading">¶</a></h2>
<dl class="js class">
<dt class="sig sig-object js" id="ConstantTimeOps">
<em class="property"><span class="k"><span class="pre">class</span></span><span class="w"> </span></em><span class="sig-name descname"><span class="n"><span class="pre">ConstantTimeOps</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ConstantTimeOps" title="Link to this definition">¶</a></dt>
<dd><p>Provides timing attack resistant cryptographic operations.</p>
</dd></dl>

</section>
<section id="input-validation-api">
<h2>Input Validation API<a class="headerlink" href="#input-validation-api" title="Link to this heading">¶</a></h2>
<dl class="js class">
<dt class="sig sig-object js" id="CryptoValidation">
<em class="property"><span class="k"><span class="pre">class</span></span><span class="w"> </span></em><span class="sig-name descname"><span class="n"><span class="pre">CryptoValidation</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#CryptoValidation" title="Link to this definition">¶</a></dt>
<dd><p>Comprehensive cryptographic parameter validation framework.</p>
</dd></dl>

<dl class="js class">
<dt class="sig sig-object js" id="SecurityValidationError">
<em class="property"><span class="k"><span class="pre">class</span></span><span class="w"> </span></em><span class="sig-name descname"><span class="n"><span class="pre">SecurityValidationError</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#SecurityValidationError" title="Link to this definition">¶</a></dt>
<dd><p>Custom error class for validation failures.</p>
<dl class="js attribute">
<dt class="sig sig-object js" id="SecurityValidationError.code">
<span class="sig-prename descclassname"><span class="n"><span class="pre">SecurityValidationError</span></span><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">code</span></span></span><a class="headerlink" href="#SecurityValidationError.code" title="Link to this definition">¶</a></dt>
<dd><p>Error code for programmatic handling.</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>string</p>
</dd>
</dl>
</dd></dl>

<dl class="js attribute">
<dt class="sig sig-object js" id="SecurityValidationError.message">
<span class="sig-prename descclassname"><span class="n"><span class="pre">SecurityValidationError</span></span><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">message</span></span></span><a class="headerlink" href="#SecurityValidationError.message" title="Link to this definition">¶</a></dt>
<dd><p>Human-readable error message.</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>string</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

</section>
<section id="secure-memory-management-api">
<h2>Secure Memory Management API<a class="headerlink" href="#secure-memory-management-api" title="Link to this heading">¶</a></h2>
<dl class="js class">
<dt class="sig sig-object js" id="SecureMemory">
<em class="property"><span class="k"><span class="pre">class</span></span><span class="w"> </span></em><span class="sig-name descname"><span class="n"><span class="pre">SecureMemory</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#SecureMemory" title="Link to this definition">¶</a></dt>
<dd><p>Secure memory allocation with automatic wiping and lifecycle management.</p>
<dl class="js method">
<dt class="sig sig-object js" id="SecureMemory.constructor">
<span class="sig-prename descclassname"><span class="n"><span class="pre">SecureMemory</span></span><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">constructor</span></span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">size</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">options</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#SecureMemory.constructor" title="Link to this definition">¶</a></dt>
<dd><p>Creates a new secure memory buffer.</p>
<dl class="field-list simple">
<dt class="field-odd">Arguments<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>size</strong> (<span><code class="xref js js-func docutils literal notranslate"><span class="pre">number()</span></code></span>) – Size of buffer in bytes</p></li>
<li><p><strong>options</strong> (<span><code class="xref js js-func docutils literal notranslate"><span class="pre">Object()</span></code></span>) – Configuration options</p></li>
</ul>
</dd>
</dl>
<p><strong>Options:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">autoWipe</span></code> (boolean): Automatic wiping on destroy (default: true)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">wipePatterns</span></code> (Array): Patterns for secure wiping (default: [0xFF, 0xAA, 0x55, 0x00])</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">trackAccess</span></code> (boolean): Enable access tracking (default: false)</p></li>
</ul>
</dd></dl>

<dl class="js method">
<dt class="sig sig-object js" id="SecureMemory.write">
<span class="sig-prename descclassname"><span class="n"><span class="pre">SecureMemory</span></span><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">write</span></span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">offset</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#SecureMemory.write" title="Link to this definition">¶</a></dt>
<dd><p>Writes data to secure memory.</p>
<dl class="field-list simple">
<dt class="field-odd">Arguments<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>data</strong> (<span><code class="xref js js-func docutils literal notranslate"><span class="pre">Uint8Array|Array()</span></code></span>) – Data to write</p></li>
<li><p><strong>offset</strong> (<span><code class="xref js js-func docutils literal notranslate"><span class="pre">number()</span></code></span>) – Offset to start writing (default: 0)</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="js method">
<dt class="sig sig-object js" id="SecureMemory.read">
<span class="sig-prename descclassname"><span class="n"><span class="pre">SecureMemory</span></span><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">read</span></span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">length</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">offset</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#SecureMemory.read" title="Link to this definition">¶</a></dt>
<dd><p>Reads data from secure memory.</p>
<dl class="field-list simple">
<dt class="field-odd">Arguments<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>length</strong> (<span><code class="xref js js-func docutils literal notranslate"><span class="pre">number()</span></code></span>) – Number of bytes to read (default: all)</p></li>
<li><p><strong>offset</strong> (<span><code class="xref js js-func docutils literal notranslate"><span class="pre">number()</span></code></span>) – Offset to start reading (default: 0)</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>Copy of the data</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>Uint8Array</p>
</dd>
</dl>
</dd></dl>

<dl class="js method">
<dt class="sig sig-object js" id="SecureMemory.getView">
<span class="sig-prename descclassname"><span class="n"><span class="pre">SecureMemory</span></span><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">getView</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#SecureMemory.getView" title="Link to this definition">¶</a></dt>
<dd><p>Gets a view of the secure memory buffer.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>View of the memory buffer</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p>Uint8Array</p>
</dd>
</dl>
</dd></dl>

<dl class="js method">
<dt class="sig sig-object js" id="SecureMemory.secureWipe">
<span class="sig-prename descclassname"><span class="n"><span class="pre">SecureMemory</span></span><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">secureWipe</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#SecureMemory.secureWipe" title="Link to this definition">¶</a></dt>
<dd><p>Securely wipes the memory buffer using multiple passes.</p>
</dd></dl>

<dl class="js method">
<dt class="sig sig-object js" id="SecureMemory.destroy">
<span class="sig-prename descclassname"><span class="n"><span class="pre">SecureMemory</span></span><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">destroy</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#SecureMemory.destroy" title="Link to this definition">¶</a></dt>
<dd><p>Destroys the secure memory buffer with secure wiping.</p>
</dd></dl>

<dl class="js method">
<dt class="sig sig-object js" id="SecureMemory.getStats">
<span class="sig-prename descclassname"><span class="n"><span class="pre">SecureMemory</span></span><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">getStats</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#SecureMemory.getStats" title="Link to this definition">¶</a></dt>
<dd><p>Gets memory usage statistics.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Statistics object</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p>Object</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="js class">
<dt class="sig sig-object js" id="SecureMemoryPool">
<em class="property"><span class="k"><span class="pre">class</span></span><span class="w"> </span></em><span class="sig-name descname"><span class="n"><span class="pre">SecureMemoryPool</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#SecureMemoryPool" title="Link to this definition">¶</a></dt>
<dd><p>Memory pool for efficient secure memory allocation and reuse.</p>
<dl class="js method">
<dt class="sig sig-object js" id="SecureMemoryPool.constructor">
<span class="sig-prename descclassname"><span class="n"><span class="pre">SecureMemoryPool</span></span><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">constructor</span></span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">options</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#SecureMemoryPool.constructor" title="Link to this definition">¶</a></dt>
<dd><p>Creates a new secure memory pool.</p>
<dl class="field-list simple">
<dt class="field-odd">Arguments<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>options</strong> (<span><code class="xref js js-func docutils literal notranslate"><span class="pre">Object()</span></code></span>) – Pool configuration options</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="js method">
<dt class="sig sig-object js" id="SecureMemoryPool.allocate">
<span class="sig-prename descclassname"><span class="n"><span class="pre">SecureMemoryPool</span></span><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">allocate</span></span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">size</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#SecureMemoryPool.allocate" title="Link to this definition">¶</a></dt>
<dd><p>Allocates secure memory from the pool.</p>
<dl class="field-list simple">
<dt class="field-odd">Arguments<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>size</strong> (<span><code class="xref js js-func docutils literal notranslate"><span class="pre">number()</span></code></span>) – Size of memory to allocate</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>Secure memory instance</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>SecureMemory</p>
</dd>
</dl>
</dd></dl>

<dl class="js method">
<dt class="sig sig-object js" id="SecureMemoryPool.deallocate">
<span class="sig-prename descclassname"><span class="n"><span class="pre">SecureMemoryPool</span></span><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">deallocate</span></span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">memory</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#SecureMemoryPool.deallocate" title="Link to this definition">¶</a></dt>
<dd><p>Returns secure memory to the pool.</p>
<dl class="field-list simple">
<dt class="field-odd">Arguments<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>memory</strong> (<span><a class="reference internal" href="#SecureMemory" title="SecureMemory"><code class="xref js js-func docutils literal notranslate"><span class="pre">SecureMemory()</span></code></a></span>) – Memory to return to pool</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="js method">
<dt class="sig sig-object js" id="SecureMemoryPool.getStats">
<span class="sig-prename descclassname"><span class="n"><span class="pre">SecureMemoryPool</span></span><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">getStats</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#SecureMemoryPool.getStats" title="Link to this definition">¶</a></dt>
<dd><p>Gets pool statistics.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Pool statistics object</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p>Object</p>
</dd>
</dl>
</dd></dl>

<dl class="js method">
<dt class="sig sig-object js" id="SecureMemoryPool.cleanup">
<span class="sig-prename descclassname"><span class="n"><span class="pre">SecureMemoryPool</span></span><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">cleanup</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#SecureMemoryPool.cleanup" title="Link to this definition">¶</a></dt>
<dd><p>Cleans up expired memory from pools.</p>
</dd></dl>

<dl class="js method">
<dt class="sig sig-object js" id="SecureMemoryPool.destroy">
<span class="sig-prename descclassname"><span class="n"><span class="pre">SecureMemoryPool</span></span><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">destroy</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#SecureMemoryPool.destroy" title="Link to this definition">¶</a></dt>
<dd><p>Destroys all pooled memory.</p>
</dd></dl>

</dd></dl>

</section>
<section id="enhanced-error-recovery-api">
<h2>Enhanced Error Recovery API<a class="headerlink" href="#enhanced-error-recovery-api" title="Link to this heading">¶</a></h2>
<dl class="js class">
<dt class="sig sig-object js" id="ProtocolErrorRecovery">
<em class="property"><span class="k"><span class="pre">class</span></span><span class="w"> </span></em><span class="sig-name descname"><span class="n"><span class="pre">ProtocolErrorRecovery</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ProtocolErrorRecovery" title="Link to this definition">¶</a></dt>
<dd><p>Robust protocol error handling and state recovery system.</p>
<dl class="js method">
<dt class="sig sig-object js" id="ProtocolErrorRecovery.constructor">
<span class="sig-prename descclassname"><span class="n"><span class="pre">ProtocolErrorRecovery</span></span><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">constructor</span></span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">options</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ProtocolErrorRecovery.constructor" title="Link to this definition">¶</a></dt>
<dd><p>Creates a new error recovery instance.</p>
<dl class="field-list simple">
<dt class="field-odd">Arguments<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>options</strong> (<span><code class="xref js js-func docutils literal notranslate"><span class="pre">Object()</span></code></span>) – Configuration options</p></li>
</ul>
</dd>
</dl>
<p><strong>Options:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">maxRetries</span></code> (number): Maximum retry attempts (default: 3)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">retryDelay</span></code> (number): Delay between retries in ms (default: 1000)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">enableLogging</span></code> (boolean): Enable console logging (default: true)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">securityEventHandler</span></code> (Function): Custom event handler</p></li>
</ul>
</dd></dl>

<dl class="js method">
<dt class="sig sig-object js" id="ProtocolErrorRecovery.handleAKEError">
<span class="sig-prename descclassname"><span class="n"><span class="pre">ProtocolErrorRecovery</span></span><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">handleAKEError</span></span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">error</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">context</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ProtocolErrorRecovery.handleAKEError" title="Link to this definition">¶</a></dt>
<dd><p>Handles AKE protocol errors with automatic recovery.</p>
<dl class="field-list simple">
<dt class="field-odd">Arguments<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>error</strong> (<span><code class="xref js js-func docutils literal notranslate"><span class="pre">Error()</span></code></span>) – The error that occurred</p></li>
<li><p><strong>context</strong> (<span><code class="xref js js-func docutils literal notranslate"><span class="pre">Object()</span></code></span>) – Protocol context</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>Recovery action object</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>Object</p>
</dd>
</dl>
<p><strong>Recovery Object:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">strategy</span></code> (string): Recovery strategy to apply</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">action</span></code> (string): Specific action to take</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">message</span></code> (string): Human-readable description</p></li>
</ul>
</dd></dl>

<dl class="js method">
<dt class="sig sig-object js" id="ProtocolErrorRecovery.getStats">
<span class="sig-prename descclassname"><span class="n"><span class="pre">ProtocolErrorRecovery</span></span><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">getStats</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ProtocolErrorRecovery.getStats" title="Link to this definition">¶</a></dt>
<dd><p>Gets recovery statistics.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Statistics object with recovery metrics</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p>Object</p>
</dd>
</dl>
</dd></dl>

<dl class="js method">
<dt class="sig sig-object js" id="ProtocolErrorRecovery.resetStats">
<span class="sig-prename descclassname"><span class="n"><span class="pre">ProtocolErrorRecovery</span></span><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">resetStats</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ProtocolErrorRecovery.resetStats" title="Link to this definition">¶</a></dt>
<dd><p>Resets recovery statistics.</p>
</dd></dl>

</dd></dl>

<dl class="js class">
<dt class="sig sig-object js" id="SecurityEvent">
<em class="property"><span class="k"><span class="pre">class</span></span><span class="w"> </span></em><span class="sig-name descname"><span class="n"><span class="pre">SecurityEvent</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#SecurityEvent" title="Link to this definition">¶</a></dt>
<dd><p>Security event for logging and monitoring.</p>
<dl class="js method">
<dt class="sig sig-object js" id="SecurityEvent.constructor">
<span class="sig-prename descclassname"><span class="n"><span class="pre">SecurityEvent</span></span><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">constructor</span></span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">severity</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">message</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">context</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#SecurityEvent.constructor" title="Link to this definition">¶</a></dt>
<dd><p>Creates a new security event.</p>
<dl class="field-list simple">
<dt class="field-odd">Arguments<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>type</strong> (<span><code class="xref js js-func docutils literal notranslate"><span class="pre">string()</span></code></span>) – Event type</p></li>
<li><p><strong>severity</strong> (<span><code class="xref js js-func docutils literal notranslate"><span class="pre">string()</span></code></span>) – Event severity (LOW, MEDIUM, HIGH, CRITICAL)</p></li>
<li><p><strong>message</strong> (<span><code class="xref js js-func docutils literal notranslate"><span class="pre">string()</span></code></span>) – Event message</p></li>
<li><p><strong>context</strong> (<span><code class="xref js js-func docutils literal notranslate"><span class="pre">Object()</span></code></span>) – Event context</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="js method">
<dt class="sig sig-object js" id="SecurityEvent.toJSON">
<span class="sig-prename descclassname"><span class="n"><span class="pre">SecurityEvent</span></span><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">toJSON</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#SecurityEvent.toJSON" title="Link to this definition">¶</a></dt>
<dd><p>Serializes event to JSON.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>JSON representation</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p>Object</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

</section>
<section id="constants-and-enums">
<h2>Constants and Enums<a class="headerlink" href="#constants-and-enums" title="Link to this heading">¶</a></h2>
<dl class="js data">
<dt class="sig sig-object js" id="ERROR_TYPES">
<span class="sig-name descname"><span class="n"><span class="pre">ERROR_TYPES</span></span></span><a class="headerlink" href="#ERROR_TYPES" title="Link to this definition">¶</a></dt>
<dd><p>Error type constants for classification.</p>
<p><strong>Protocol Errors:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">PROTOCOL_VIOLATION</span></code>: General protocol violation</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">INVALID_MESSAGE_TYPE</span></code>: Invalid message type</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">SEQUENCE_ERROR</span></code>: Message sequence error</p></li>
</ul>
<p><strong>Cryptographic Errors:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">INVALID_SIGNATURE</span></code>: Signature verification failure</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">INVALID_MAC</span></code>: MAC verification failure</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">INVALID_DH_KEY</span></code>: Invalid DH public key</p></li>
</ul>
<p><strong>Security Errors:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">REPLAY_ATTACK</span></code>: Potential replay attack</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">COMPETING_DH_COMMIT</span></code>: Competing DH commits</p></li>
</ul>
</dd></dl>

<dl class="js data">
<dt class="sig sig-object js" id="RECOVERY_STRATEGIES">
<span class="sig-name descname"><span class="n"><span class="pre">RECOVERY_STRATEGIES</span></span></span><a class="headerlink" href="#RECOVERY_STRATEGIES" title="Link to this definition">¶</a></dt>
<dd><p>Recovery strategy constants.</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">IGNORE</span></code>: Ignore the error and continue</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">RETRY</span></code>: Retry the operation</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">RESET_STATE</span></code>: Reset protocol state</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">GRACEFUL_DEGRADATION</span></code>: Degrade to plaintext</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">ABORT_SESSION</span></code>: Abort the session</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">RESTART_PROTOCOL</span></code>: Restart the protocol</p></li>
</ul>
</dd></dl>

</section>
<section id="global-instances">
<h2>Global Instances<a class="headerlink" href="#global-instances" title="Link to this heading">¶</a></h2>
<dl class="js data">
<dt class="sig sig-object js" id="globalErrorRecovery">
<span class="sig-name descname"><span class="n"><span class="pre">globalErrorRecovery</span></span></span><a class="headerlink" href="#globalErrorRecovery" title="Link to this definition">¶</a></dt>
<dd><p>Global error recovery instance for consistent error handling across the application.</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>ProtocolErrorRecovery</p>
</dd>
</dl>
</dd></dl>

<dl class="js data">
<dt class="sig sig-object js" id="globalSecureMemoryPool">
<span class="sig-name descname"><span class="n"><span class="pre">globalSecureMemoryPool</span></span></span><a class="headerlink" href="#globalSecureMemoryPool" title="Link to this definition">¶</a></dt>
<dd><p>Global secure memory pool for efficient memory management.</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>SecureMemoryPool</p>
</dd>
</dl>
</dd></dl>

</section>
<section id="usage-examples">
<h2>Usage Examples<a class="headerlink" href="#usage-examples" title="Link to this heading">¶</a></h2>
<p><strong>Complete Security Integration Example:</strong></p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">import</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">ConstantTimeOps</span><span class="p">,</span>
<span class="w">  </span><span class="nx">CryptoValidation</span><span class="p">,</span>
<span class="w">  </span><span class="nx">SecureMemory</span><span class="p">,</span>
<span class="w">  </span><span class="nx">ProtocolErrorRecovery</span><span class="p">,</span>
<span class="w">  </span><span class="nx">ERROR_TYPES</span>
<span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;webOTR&#39;</span><span class="p">;</span>

<span class="c1">// Secure key handling</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">keyMemory</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">SecureMemory</span><span class="p">(</span><span class="mf">32</span><span class="p">);</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">sessionKey</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="p">(</span><span class="mf">32</span><span class="p">);</span>
<span class="nx">crypto</span><span class="p">.</span><span class="nx">getRandomValues</span><span class="p">(</span><span class="nx">sessionKey</span><span class="p">);</span>
<span class="nx">keyMemory</span><span class="p">.</span><span class="nx">write</span><span class="p">(</span><span class="nx">sessionKey</span><span class="p">);</span>

<span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="c1">// Validate DH public key</span>
<span class="w">  </span><span class="nx">CryptoValidation</span><span class="p">.</span><span class="nx">validateDHPublicKey</span><span class="p">(</span><span class="nx">dhPublicKey</span><span class="p">);</span>

<span class="w">  </span><span class="c1">// Constant-time MAC verification</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">isValidMAC</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">ConstantTimeOps</span><span class="p">.</span><span class="nx">constantTimeEqual</span><span class="p">(</span>
<span class="w">    </span><span class="nx">computedMAC</span><span class="p">,</span>
<span class="w">    </span><span class="nx">receivedMAC</span>
<span class="w">  </span><span class="p">);</span>

<span class="w">  </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="nx">isValidMAC</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">throw</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="ne">Error</span><span class="p">(</span><span class="s1">&#39;MAC verification failed&#39;</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="c1">// Handle errors with recovery system</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">recovery</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">ProtocolErrorRecovery</span><span class="p">();</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">recovery</span><span class="p">.</span><span class="nx">handleAKEError</span><span class="p">(</span><span class="nx">error</span><span class="p">,</span><span class="w"> </span><span class="nx">protocolContext</span><span class="p">);</span>

<span class="w">  </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="sb">`Recovery strategy: </span><span class="si">${</span><span class="nx">result</span><span class="p">.</span><span class="nx">strategy</span><span class="si">}</span><span class="sb">`</span><span class="p">);</span>

<span class="p">}</span><span class="w"> </span><span class="k">finally</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="c1">// Secure cleanup</span>
<span class="w">  </span><span class="nx">keyMemory</span><span class="p">.</span><span class="nx">destroy</span><span class="p">();</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Performance Monitoring Example:</strong></p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">import</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">ConstantTimeOps</span><span class="p">,</span><span class="w"> </span><span class="nx">SecureMemory</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;webOTR&#39;</span><span class="p">;</span>

<span class="c1">// Monitor timing consistency</span>
<span class="kd">function</span><span class="w"> </span><span class="nx">benchmarkConstantTime</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">iterations</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">1000</span><span class="p">;</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">data1</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="p">(</span><span class="mf">256</span><span class="p">);</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">data2</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="p">(</span><span class="mf">256</span><span class="p">);</span>

<span class="w">  </span><span class="nx">crypto</span><span class="p">.</span><span class="nx">getRandomValues</span><span class="p">(</span><span class="nx">data1</span><span class="p">);</span>
<span class="w">  </span><span class="nx">crypto</span><span class="p">.</span><span class="nx">getRandomValues</span><span class="p">(</span><span class="nx">data2</span><span class="p">);</span>

<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">start</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">performance</span><span class="p">.</span><span class="nx">now</span><span class="p">();</span>
<span class="w">  </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="kd">let</span><span class="w"> </span><span class="nx">i</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0</span><span class="p">;</span><span class="w"> </span><span class="nx">i</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="nx">iterations</span><span class="p">;</span><span class="w"> </span><span class="nx">i</span><span class="o">++</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">ConstantTimeOps</span><span class="p">.</span><span class="nx">constantTimeEqual</span><span class="p">(</span><span class="nx">data1</span><span class="p">,</span><span class="w"> </span><span class="nx">data2</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">duration</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">performance</span><span class="p">.</span><span class="nx">now</span><span class="p">()</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="nx">start</span><span class="p">;</span>

<span class="w">  </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="sb">`Constant-time ops: </span><span class="si">${</span><span class="nx">duration</span><span class="p">.</span><span class="nx">toFixed</span><span class="p">(</span><span class="mf">2</span><span class="p">)</span><span class="si">}</span><span class="sb">ms for </span><span class="si">${</span><span class="nx">iterations</span><span class="si">}</span><span class="sb"> iterations`</span><span class="p">);</span>
<span class="p">}</span>

<span class="c1">// Monitor memory usage</span>
<span class="kd">function</span><span class="w"> </span><span class="nx">monitorMemoryUsage</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">stats</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">SecureMemory</span><span class="p">.</span><span class="nx">getGlobalStats</span><span class="p">();</span>
<span class="w">  </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;Memory Statistics:&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">totalInstances</span><span class="o">:</span><span class="w"> </span><span class="nx">stats</span><span class="p">.</span><span class="nx">totalInstances</span><span class="p">,</span>
<span class="w">    </span><span class="nx">activeInstances</span><span class="o">:</span><span class="w"> </span><span class="nx">stats</span><span class="p">.</span><span class="nx">activeInstances</span><span class="p">,</span>
<span class="w">    </span><span class="nx">totalSize</span><span class="o">:</span><span class="w"> </span><span class="nx">stats</span><span class="p">.</span><span class="nx">totalSize</span><span class="p">,</span>
<span class="w">    </span><span class="nx">averageSize</span><span class="o">:</span><span class="w"> </span><span class="nx">stats</span><span class="p">.</span><span class="nx">averageSize</span>
<span class="w">  </span><span class="p">});</span>
<span class="p">}</span>
</pre></div>
</div>
<p>For implementation details, see <a class="reference internal" href="libotr-enhancements-implementation.html"><span class="doc">libOTR Enhancements Implementation</span></a>.</p>
</section>
</section>

        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          <a class="next-page" href="libotr-enhancements-testing.html">
              <div class="page-info">
                <div class="context">
                  <span>Next</span>
                </div>
                <div class="title">libOTR Enhancements Testing and Validation</div>
              </div>
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
            </a>
          <a class="prev-page" href="libotr-enhancements-implementation.html">
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
              <div class="page-info">
                <div class="context">
                  <span>Previous</span>
                </div>
                
                <div class="title">libOTR Enhancements Implementation</div>
                
              </div>
            </a>
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 1980, WebOTR Team
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer">
      
      
      <div class="toc-sticky toc-scroll">
        <div class="toc-title-container">
          <span class="toc-title">
            On this page
          </span>
        </div>
        <div class="toc-tree-container">
          <div class="toc-tree">
            <ul>
<li><a class="reference internal" href="#">libOTR Enhancements API Reference</a><ul>
<li><a class="reference internal" href="#constant-time-operations-api">Constant-Time Operations API</a><ul>
<li><a class="reference internal" href="#ConstantTimeOps"><code class="docutils literal notranslate"><span class="pre">ConstantTimeOps</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#input-validation-api">Input Validation API</a><ul>
<li><a class="reference internal" href="#CryptoValidation"><code class="docutils literal notranslate"><span class="pre">CryptoValidation</span></code></a></li>
<li><a class="reference internal" href="#SecurityValidationError"><code class="docutils literal notranslate"><span class="pre">SecurityValidationError</span></code></a><ul>
<li><a class="reference internal" href="#SecurityValidationError.code"><code class="docutils literal notranslate"><span class="pre">SecurityValidationError.code</span></code></a></li>
<li><a class="reference internal" href="#SecurityValidationError.message"><code class="docutils literal notranslate"><span class="pre">SecurityValidationError.message</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#secure-memory-management-api">Secure Memory Management API</a><ul>
<li><a class="reference internal" href="#SecureMemory"><code class="docutils literal notranslate"><span class="pre">SecureMemory</span></code></a><ul>
<li><a class="reference internal" href="#SecureMemory.constructor"><code class="docutils literal notranslate"><span class="pre">SecureMemory.constructor()</span></code></a></li>
<li><a class="reference internal" href="#SecureMemory.write"><code class="docutils literal notranslate"><span class="pre">SecureMemory.write()</span></code></a></li>
<li><a class="reference internal" href="#SecureMemory.read"><code class="docutils literal notranslate"><span class="pre">SecureMemory.read()</span></code></a></li>
<li><a class="reference internal" href="#SecureMemory.getView"><code class="docutils literal notranslate"><span class="pre">SecureMemory.getView()</span></code></a></li>
<li><a class="reference internal" href="#SecureMemory.secureWipe"><code class="docutils literal notranslate"><span class="pre">SecureMemory.secureWipe()</span></code></a></li>
<li><a class="reference internal" href="#SecureMemory.destroy"><code class="docutils literal notranslate"><span class="pre">SecureMemory.destroy()</span></code></a></li>
<li><a class="reference internal" href="#SecureMemory.getStats"><code class="docutils literal notranslate"><span class="pre">SecureMemory.getStats()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#SecureMemoryPool"><code class="docutils literal notranslate"><span class="pre">SecureMemoryPool</span></code></a><ul>
<li><a class="reference internal" href="#SecureMemoryPool.constructor"><code class="docutils literal notranslate"><span class="pre">SecureMemoryPool.constructor()</span></code></a></li>
<li><a class="reference internal" href="#SecureMemoryPool.allocate"><code class="docutils literal notranslate"><span class="pre">SecureMemoryPool.allocate()</span></code></a></li>
<li><a class="reference internal" href="#SecureMemoryPool.deallocate"><code class="docutils literal notranslate"><span class="pre">SecureMemoryPool.deallocate()</span></code></a></li>
<li><a class="reference internal" href="#SecureMemoryPool.getStats"><code class="docutils literal notranslate"><span class="pre">SecureMemoryPool.getStats()</span></code></a></li>
<li><a class="reference internal" href="#SecureMemoryPool.cleanup"><code class="docutils literal notranslate"><span class="pre">SecureMemoryPool.cleanup()</span></code></a></li>
<li><a class="reference internal" href="#SecureMemoryPool.destroy"><code class="docutils literal notranslate"><span class="pre">SecureMemoryPool.destroy()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#enhanced-error-recovery-api">Enhanced Error Recovery API</a><ul>
<li><a class="reference internal" href="#ProtocolErrorRecovery"><code class="docutils literal notranslate"><span class="pre">ProtocolErrorRecovery</span></code></a><ul>
<li><a class="reference internal" href="#ProtocolErrorRecovery.constructor"><code class="docutils literal notranslate"><span class="pre">ProtocolErrorRecovery.constructor()</span></code></a></li>
<li><a class="reference internal" href="#ProtocolErrorRecovery.handleAKEError"><code class="docutils literal notranslate"><span class="pre">ProtocolErrorRecovery.handleAKEError()</span></code></a></li>
<li><a class="reference internal" href="#ProtocolErrorRecovery.getStats"><code class="docutils literal notranslate"><span class="pre">ProtocolErrorRecovery.getStats()</span></code></a></li>
<li><a class="reference internal" href="#ProtocolErrorRecovery.resetStats"><code class="docutils literal notranslate"><span class="pre">ProtocolErrorRecovery.resetStats()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#SecurityEvent"><code class="docutils literal notranslate"><span class="pre">SecurityEvent</span></code></a><ul>
<li><a class="reference internal" href="#SecurityEvent.constructor"><code class="docutils literal notranslate"><span class="pre">SecurityEvent.constructor()</span></code></a></li>
<li><a class="reference internal" href="#SecurityEvent.toJSON"><code class="docutils literal notranslate"><span class="pre">SecurityEvent.toJSON()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#constants-and-enums">Constants and Enums</a><ul>
<li><a class="reference internal" href="#ERROR_TYPES"><code class="docutils literal notranslate"><span class="pre">ERROR_TYPES</span></code></a></li>
<li><a class="reference internal" href="#RECOVERY_STRATEGIES"><code class="docutils literal notranslate"><span class="pre">RECOVERY_STRATEGIES</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#global-instances">Global Instances</a><ul>
<li><a class="reference internal" href="#globalErrorRecovery"><code class="docutils literal notranslate"><span class="pre">globalErrorRecovery</span></code></a></li>
<li><a class="reference internal" href="#globalSecureMemoryPool"><code class="docutils literal notranslate"><span class="pre">globalSecureMemoryPool</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#usage-examples">Usage Examples</a></li>
</ul>
</li>
</ul>

          </div>
        </div>
      </div>
      
      
    </aside>
  </div>
</div><script src="../_static/documentation_options.js?v=01f34227"></script>
    <script src="../_static/doctools.js?v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/scripts/furo.js?v=5fa4622c"></script>
    <script src="../_static/mermaid-renderer.js?v=b683124c"></script>
    </body>
</html>