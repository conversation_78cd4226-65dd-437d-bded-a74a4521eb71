<!doctype html>
<html class="no-js" lang="en" data-content_root="../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><meta name="viewport" content="width=device-width, initial-scale=1" />
<link rel="index" title="Index" href="../genindex.html" /><link rel="search" title="Search" href="../search.html" /><link rel="next" title="AKE Documentation Summary" href="ake-summary.html" /><link rel="prev" title="Forward Secrecy Implementation Guide" href="forward-secrecy-implementation.html" />

    <link rel="shortcut icon" href="../_static/favicon.svg"/><!-- Generated with Sphinx 7.4.7 and Furo 2024.08.06 -->
        <title>Forward Secrecy API Reference - WebOTR Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../_static/styles/furo.css?v=354aac6f" />
    <link rel="stylesheet" type="text/css" href="../_static/styles/furo-extensions.css?v=302659d7" />
    <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=21a7f706" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  --color-brand-primary: #6366f1;
  --color-brand-content: #6366f1;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8b5cf6;
  --color-brand-content: #8b5cf6;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8b5cf6;
  --color-brand-content: #8b5cf6;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-with-moon" viewBox="0 0 24 24">
    <title>Auto light/dark, in light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
      class="icon-custom-derived-from-feather-sun-and-tabler-moon">
      <path style="opacity: 50%" d="M 5.411 14.504 C 5.471 14.504 5.532 14.504 5.591 14.504 C 3.639 16.319 4.383 19.569 6.931 20.352 C 7.693 20.586 8.512 20.551 9.25 20.252 C 8.023 23.207 4.056 23.725 2.11 21.184 C 0.166 18.642 1.702 14.949 4.874 14.536 C 5.051 14.512 5.231 14.5 5.411 14.5 L 5.411 14.504 Z"/>
      <line x1="14.5" y1="3.25" x2="14.5" y2="1.25"/>
      <line x1="14.5" y1="15.85" x2="14.5" y2="17.85"/>
      <line x1="10.044" y1="5.094" x2="8.63" y2="3.68"/>
      <line x1="19" y1="14.05" x2="20.414" y2="15.464"/>
      <line x1="8.2" y1="9.55" x2="6.2" y2="9.55"/>
      <line x1="20.8" y1="9.55" x2="22.8" y2="9.55"/>
      <line x1="10.044" y1="14.006" x2="8.63" y2="15.42"/>
      <line x1="19" y1="5.05" x2="20.414" y2="3.636"/>
      <circle cx="14.5" cy="9.55" r="3.6"/>
    </svg>
  </symbol>
  <symbol id="svg-moon-with-sun" viewBox="0 0 24 24">
    <title>Auto light/dark, in dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
      class="icon-custom-derived-from-feather-sun-and-tabler-moon">
      <path d="M 8.282 7.007 C 8.385 7.007 8.494 7.007 8.595 7.007 C 5.18 10.184 6.481 15.869 10.942 17.24 C 12.275 17.648 13.706 17.589 15 17.066 C 12.851 22.236 5.91 23.143 2.505 18.696 C -0.897 14.249 1.791 7.786 7.342 7.063 C 7.652 7.021 7.965 7 8.282 7 L 8.282 7.007 Z"/>
      <line style="opacity: 50%" x1="18" y1="3.705" x2="18" y2="2.5"/>
      <line style="opacity: 50%" x1="18" y1="11.295" x2="18" y2="12.5"/>
      <line style="opacity: 50%" x1="15.316" y1="4.816" x2="14.464" y2="3.964"/>
      <line style="opacity: 50%" x1="20.711" y1="10.212" x2="21.563" y2="11.063"/>
      <line style="opacity: 50%" x1="14.205" y1="7.5" x2="13.001" y2="7.5"/>
      <line style="opacity: 50%" x1="21.795" y1="7.5" x2="23" y2="7.5"/>
      <line style="opacity: 50%" x1="15.316" y1="10.184" x2="14.464" y2="11.036"/>
      <line style="opacity: 50%" x1="20.711" y1="4.789" x2="21.563" y2="3.937"/>
      <circle style="opacity: 50%" cx="18" cy="7.5" r="2.169"/>
    </svg>
  </symbol>
  <symbol id="svg-pencil" viewBox="0 0 24 24">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-pencil-code">
      <path d="M4 20h4l10.5 -10.5a2.828 2.828 0 1 0 -4 -4l-10.5 10.5v4" />
      <path d="M13.5 6.5l4 4" />
      <path d="M20 21l2 -2l-2 -2" />
      <path d="M17 17l-2 2l2 2" />
    </svg>
  </symbol>
  <symbol id="svg-eye" viewBox="0 0 24 24">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-eye-code">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0" />
      <path
        d="M11.11 17.958c-3.209 -.307 -5.91 -2.293 -8.11 -5.958c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6c-.21 .352 -.427 .688 -.647 1.008" />
      <path d="M20 21l2 -2l-2 -2" />
      <path d="M17 17l-2 2l2 2" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>

<a class="skip-to-content muted-link" href="#furo-main-content">Skip to content</a>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../index.html"><div class="brand">WebOTR Documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto-light"><use href="#svg-sun-with-moon"></use></svg>
          <svg class="theme-icon-when-auto-dark"><use href="#svg-moon-with-sun"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><a class="sidebar-brand" href="../index.html">
  
  <div class="sidebar-logo-container">
    <img class="sidebar-logo" src="../_static/logo.svg" alt="Logo"/>
  </div>
  
  <span class="sidebar-brand-text">WebOTR Documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-scroll"><div class="sidebar-tree">
  <p class="caption" role="heading"><span class="caption-text">User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user-guide/installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../user-guide/getting-started.html">Getting Started</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../developer-guide/architecture.html">Architecture Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../developer-guide/contributing.html">Contributing to WebOTR</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Protocol Compliance &amp; Advanced Features</span></p>
<ul>
<li class="toctree-l1 has-children"><a class="reference internal" href="../protocol/index.html">Protocol Compliance &amp; Advanced Features</a><input class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of Protocol Compliance &amp; Advanced Features</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul>
<li class="toctree-l2"><a class="reference internal" href="../protocol/overview.html">Protocol Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/version-negotiation.html">Version Negotiation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/message-ordering.html">Message Ordering</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/enhanced-smp.html">Enhanced SMP</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/policy-manager.html">Policy Manager</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/integration-guide.html">Integration Guide</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/troubleshooting.html">Troubleshooting Guide</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Security</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="overview.html">Security Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements-summary.html">libOTR Security Enhancements Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements.html">libOTR Security Enhancements</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements-implementation.html">libOTR Enhancements Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements-api.html">libOTR Enhancements API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements-testing.html">libOTR Enhancements Testing and Validation</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy-summary.html">Forward Secrecy Documentation Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy.html">Forward Secrecy Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy-architecture.html">Forward Secrecy Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy-implementation.html">Forward Secrecy Implementation Guide</a></li>
<li class="toctree-l1 current current-page"><a class="current reference internal" href="#">Forward Secrecy API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake-summary.html">AKE Documentation Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake.html">Authenticated Key Exchange (AKE)</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake-architecture.html">AKE Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake-implementation.html">AKE Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake-api.html">AKE API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography-summary.html">Steganography Documentation Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography.html">Steganography Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography-architecture.html">Steganography Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography-implementation.html">Steganography Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography-api.html">Steganography API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../reference/faq.html">Frequently Asked Questions</a></li>
<li class="toctree-l1"><a class="reference internal" href="../reference/changelog.html">Changelog</a></li>
</ul>

</div>
</div>

      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          <div class="view-this-page">
  <a class="muted-link" href="https://github.com/forkrul/webOTteR/blob/main/docs/sphinx/security/forward-secrecy-api.rst?plain=true" title="View this page">
    <svg><use href="#svg-eye"></use></svg>
    <span class="visually-hidden">View this page</span>
  </a>
</div><div class="edit-this-page">
  <a class="muted-link" href="https://github.com/forkrul/webOTteR/edit/main/docs/sphinx/security/forward-secrecy-api.rst" title="Edit this page">
    <svg><use href="#svg-pencil"></use></svg>
    <span class="visually-hidden">Edit this page</span>
  </a>
</div><div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto-light"><use href="#svg-sun-with-moon"></use></svg>
              <svg class="theme-icon-when-auto-dark"><use href="#svg-moon-with-sun"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main" id="furo-main-content">
          <section id="forward-secrecy-api-reference">
<h1>Forward Secrecy API Reference<a class="headerlink" href="#forward-secrecy-api-reference" title="Link to this heading">¶</a></h1>
<p>Complete API documentation for WebOTR’s Forward Secrecy system components.</p>
<nav class="contents local" id="table-of-contents">
<p class="topic-title">Table of Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#forwardsecrecymanager" id="id13">ForwardSecrecyManager</a></p>
<ul>
<li><p><a class="reference internal" href="#constructor" id="id14">Constructor</a></p></li>
<li><p><a class="reference internal" href="#methods" id="id15">Methods</a></p>
<ul>
<li><p><a class="reference internal" href="#initialize" id="id16">initialize()</a></p></li>
<li><p><a class="reference internal" href="#rotatekeysmanually" id="id17">rotateKeysManually()</a></p></li>
<li><p><a class="reference internal" href="#emergencyrotation" id="id18">emergencyRotation()</a></p></li>
<li><p><a class="reference internal" href="#getsecuritystatus" id="id19">getSecurityStatus()</a></p></li>
<li><p><a class="reference internal" href="#generatecompliancereport" id="id20">generateComplianceReport()</a></p></li>
<li><p><a class="reference internal" href="#shutdown" id="id21">shutdown()</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#events" id="id22">Events</a></p>
<ul>
<li><p><a class="reference internal" href="#initialized" id="id23">initialized</a></p></li>
<li><p><a class="reference internal" href="#rotationtriggered" id="id24">rotationTriggered</a></p></li>
<li><p><a class="reference internal" href="#rotationcompleted" id="id25">rotationCompleted</a></p></li>
<li><p><a class="reference internal" href="#deletioncompleted" id="id26">deletionCompleted</a></p></li>
</ul>
</li>
</ul>
</li>
<li><p><a class="reference internal" href="#keyrotationengine" id="id27">KeyRotationEngine</a></p>
<ul>
<li><p><a class="reference internal" href="#id1" id="id28">Constructor</a></p></li>
<li><p><a class="reference internal" href="#id2" id="id29">Methods</a></p>
<ul>
<li><p><a class="reference internal" href="#id3" id="id30">initialize()</a></p></li>
<li><p><a class="reference internal" href="#generatekeyset" id="id31">generateKeySet()</a></p></li>
<li><p><a class="reference internal" href="#rotatekeys" id="id32">rotateKeys()</a></p></li>
</ul>
</li>
</ul>
</li>
<li><p><a class="reference internal" href="#securedeletionmanager" id="id33">SecureDeletionManager</a></p>
<ul>
<li><p><a class="reference internal" href="#id4" id="id34">Constructor</a></p></li>
<li><p><a class="reference internal" href="#id5" id="id35">Methods</a></p>
<ul>
<li><p><a class="reference internal" href="#performsecuredeletion" id="id36">performSecureDeletion()</a></p></li>
<li><p><a class="reference internal" href="#verifydeletion" id="id37">verifyDeletion()</a></p></li>
</ul>
</li>
</ul>
</li>
<li><p><a class="reference internal" href="#zeroknowledgeverifier" id="id38">ZeroKnowledgeVerifier</a></p>
<ul>
<li><p><a class="reference internal" href="#id6" id="id39">Constructor</a></p></li>
<li><p><a class="reference internal" href="#id7" id="id40">Methods</a></p>
<ul>
<li><p><a class="reference internal" href="#generaterotationproof" id="id41">generateRotationProof()</a></p></li>
<li><p><a class="reference internal" href="#generatedeletionproof" id="id42">generateDeletionProof()</a></p></li>
<li><p><a class="reference internal" href="#verifyproof" id="id43">verifyProof()</a></p></li>
<li><p><a class="reference internal" href="#verifybatchproofs" id="id44">verifyBatchProofs()</a></p></li>
</ul>
</li>
</ul>
</li>
<li><p><a class="reference internal" href="#audittrailsystem" id="id45">AuditTrailSystem</a></p>
<ul>
<li><p><a class="reference internal" href="#id8" id="id46">Constructor</a></p></li>
<li><p><a class="reference internal" href="#id9" id="id47">Methods</a></p>
<ul>
<li><p><a class="reference internal" href="#logevent" id="id48">logEvent()</a></p></li>
<li><p><a class="reference internal" href="#getauditlog" id="id49">getAuditLog()</a></p></li>
<li><p><a class="reference internal" href="#generateauditreport" id="id50">generateAuditReport()</a></p></li>
<li><p><a class="reference internal" href="#verifyauditintegrity" id="id51">verifyAuditIntegrity()</a></p></li>
</ul>
</li>
</ul>
</li>
<li><p><a class="reference internal" href="#enterprisepolicymanager" id="id52">EnterprisePolicyManager</a></p>
<ul>
<li><p><a class="reference internal" href="#id10" id="id53">Constructor</a></p></li>
<li><p><a class="reference internal" href="#id11" id="id54">Methods</a></p>
<ul>
<li><p><a class="reference internal" href="#validatecompliance" id="id55">validateCompliance()</a></p></li>
<li><p><a class="reference internal" href="#enforcepolicy" id="id56">enforcePolicy()</a></p></li>
<li><p><a class="reference internal" href="#id12" id="id57">generateComplianceReport()</a></p></li>
</ul>
</li>
</ul>
</li>
<li><p><a class="reference internal" href="#error-classes" id="id58">Error Classes</a></p>
<ul>
<li><p><a class="reference internal" href="#forwardsecrecyerror" id="id59">ForwardSecrecyError</a></p></li>
<li><p><a class="reference internal" href="#keyrotationerror" id="id60">KeyRotationError</a></p></li>
<li><p><a class="reference internal" href="#securedeletionerror" id="id61">SecureDeletionError</a></p></li>
<li><p><a class="reference internal" href="#zeroknowledgeerror" id="id62">ZeroKnowledgeError</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#constants" id="id63">Constants</a></p>
<ul>
<li><p><a class="reference internal" href="#event-types" id="id64">Event Types</a></p></li>
<li><p><a class="reference internal" href="#rotation-triggers" id="id65">Rotation Triggers</a></p></li>
<li><p><a class="reference internal" href="#compliance-standards" id="id66">Compliance Standards</a></p></li>
<li><p><a class="reference internal" href="#deletion-patterns" id="id67">Deletion Patterns</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#type-definitions" id="id68">Type Definitions</a></p>
<ul>
<li><p><a class="reference internal" href="#keyset" id="id69">KeySet</a></p></li>
<li><p><a class="reference internal" href="#rotationresult" id="id70">RotationResult</a></p></li>
<li><p><a class="reference internal" href="#deletionresult" id="id71">DeletionResult</a></p></li>
<li><p><a class="reference internal" href="#proofdata" id="id72">ProofData</a></p></li>
<li><p><a class="reference internal" href="#auditevent" id="id73">AuditEvent</a></p></li>
<li><p><a class="reference internal" href="#compliancereport" id="id74">ComplianceReport</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#usage-examples" id="id75">Usage Examples</a></p>
<ul>
<li><p><a class="reference internal" href="#basic-usage" id="id76">Basic Usage</a></p></li>
<li><p><a class="reference internal" href="#advanced-configuration" id="id77">Advanced Configuration</a></p></li>
<li><p><a class="reference internal" href="#manual-operations" id="id78">Manual Operations</a></p></li>
<li><p><a class="reference internal" href="#error-handling" id="id79">Error Handling</a></p></li>
</ul>
</li>
</ul>
</nav>
<section id="forwardsecrecymanager">
<h2><a class="toc-backref" href="#id13" role="doc-backlink">ForwardSecrecyManager</a><a class="headerlink" href="#forwardsecrecymanager" title="Link to this heading">¶</a></h2>
<p>The main coordinator class for all forward secrecy operations.</p>
<section id="constructor">
<h3><a class="toc-backref" href="#id14" role="doc-backlink">Constructor</a><a class="headerlink" href="#constructor" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="ow">new</span><span class="w"> </span><span class="nx">ForwardSecrecyManager</span><span class="p">(</span><span class="nx">options</span><span class="p">)</span>
</pre></div>
</div>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">options</span></code> (Object): Configuration options</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">autoRotation</span></code> (boolean): Enable automatic key rotation (default: true)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">rotationInterval</span></code> (number): Time between rotations in milliseconds (default: 3600000)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">messageCountThreshold</span></code> (number): Rotate after N messages (default: 1000)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">dataVolumeThreshold</span></code> (number): Rotate after N bytes (default: 10485760)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">secureMemory</span></code> (boolean): Enable secure memory management (default: true)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">cryptographicErasure</span></code> (boolean): Enable cryptographic erasure (default: true)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">zeroKnowledgeProofs</span></code> (boolean): Enable ZK proofs (default: true)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">auditTrails</span></code> (boolean): Enable audit logging (default: true)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">rotationTimeout</span></code> (number): Max rotation time in ms (default: 100)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">deletionTimeout</span></code> (number): Max deletion time in ms (default: 50)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">verificationTimeout</span></code> (number): Max verification time in ms (default: 100)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">fipsCompliance</span></code> (boolean): Enable FIPS compliance mode (default: true)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">auditRetention</span></code> (number): Audit log retention in ms (default: 7776000000)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">enterpriseIntegration</span></code> (boolean): Enable enterprise features (default: false)</p></li>
</ul>
</li>
</ul>
</section>
<section id="methods">
<h3><a class="toc-backref" href="#id15" role="doc-backlink">Methods</a><a class="headerlink" href="#methods" title="Link to this heading">¶</a></h3>
<section id="initialize">
<h4><a class="toc-backref" href="#id16" role="doc-backlink">initialize()</a><a class="headerlink" href="#initialize" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">initialize</span><span class="p">()</span>
</pre></div>
</div>
<p>Initialize the Forward Secrecy Manager and all its components.</p>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">success</span><span class="o">:</span><span class="w"> </span><span class="kr">boolean</span><span class="p">,</span>
<span class="w">  </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">components</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">keyRotation</span><span class="o">:</span><span class="w"> </span><span class="kr">boolean</span><span class="p">,</span>
<span class="w">    </span><span class="nx">secureDeletion</span><span class="o">:</span><span class="w"> </span><span class="kr">boolean</span><span class="p">,</span>
<span class="w">    </span><span class="nx">zeroKnowledge</span><span class="o">:</span><span class="w"> </span><span class="kr">boolean</span><span class="p">,</span>
<span class="w">    </span><span class="nx">auditTrail</span><span class="o">:</span><span class="w"> </span><span class="kr">boolean</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="rotatekeysmanually">
<h4><a class="toc-backref" href="#id17" role="doc-backlink">rotateKeysManually()</a><a class="headerlink" href="#rotatekeysmanually" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">rotateKeysManually</span><span class="p">(</span><span class="nx">reason</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;MANUAL_REQUEST&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p>Perform immediate manual key rotation.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">reason</span></code> (string): Reason for manual rotation</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">success</span><span class="o">:</span><span class="w"> </span><span class="kr">boolean</span><span class="p">,</span>
<span class="w">  </span><span class="nx">oldKeys</span><span class="o">:</span><span class="w"> </span><span class="nb">Object</span><span class="p">,</span>
<span class="w">  </span><span class="nx">newKeys</span><span class="o">:</span><span class="w"> </span><span class="nb">Object</span><span class="p">,</span>
<span class="w">  </span><span class="nx">rotationData</span><span class="o">:</span><span class="w"> </span><span class="nb">Object</span><span class="p">,</span>
<span class="w">  </span><span class="nx">rotationTime</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">trigger</span><span class="o">:</span><span class="w"> </span><span class="nx">string</span><span class="p">,</span>
<span class="w">  </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="emergencyrotation">
<h4><a class="toc-backref" href="#id18" role="doc-backlink">emergencyRotation()</a><a class="headerlink" href="#emergencyrotation" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">emergencyRotation</span><span class="p">(</span><span class="nx">threat</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;UNKNOWN&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p>Perform emergency key rotation with immediate secure deletion.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">threat</span></code> (string): Type of security threat detected</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">success</span><span class="o">:</span><span class="w"> </span><span class="kr">boolean</span><span class="p">,</span>
<span class="w">  </span><span class="nx">emergencyResponse</span><span class="o">:</span><span class="w"> </span><span class="nb">Object</span><span class="p">,</span>
<span class="w">  </span><span class="nx">rotationTime</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">deletionTime</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="getsecuritystatus">
<h4><a class="toc-backref" href="#id19" role="doc-backlink">getSecurityStatus()</a><a class="headerlink" href="#getsecuritystatus" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">getSecurityStatus</span><span class="p">()</span>
</pre></div>
</div>
<p>Get current security status and metrics.</p>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">initialized</span><span class="o">:</span><span class="w"> </span><span class="kr">boolean</span><span class="p">,</span>
<span class="w">  </span><span class="nx">currentGeneration</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">lastRotation</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">nextRotation</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">rotationCount</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">deletionCount</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">complianceStatus</span><span class="o">:</span><span class="w"> </span><span class="nb">Object</span><span class="p">,</span>
<span class="w">  </span><span class="nx">performanceMetrics</span><span class="o">:</span><span class="w"> </span><span class="nb">Object</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="generatecompliancereport">
<h4><a class="toc-backref" href="#id20" role="doc-backlink">generateComplianceReport()</a><a class="headerlink" href="#generatecompliancereport" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">generateComplianceReport</span><span class="p">(</span><span class="nx">options</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{})</span>
</pre></div>
</div>
<p>Generate compliance report for audit purposes.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">options</span></code> (Object): Report configuration</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">startDate</span></code> (number): Report start timestamp</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">endDate</span></code> (number): Report end timestamp</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">standards</span></code> (Array): Compliance standards to include</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">format</span></code> (string): Output format (‘json’, ‘pdf’, ‘csv’)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">includeMetrics</span></code> (boolean): Include performance metrics</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">includeRecommendations</span></code> (boolean): Include recommendations</p></li>
</ul>
</li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
</section>
<section id="shutdown">
<h4><a class="toc-backref" href="#id21" role="doc-backlink">shutdown()</a><a class="headerlink" href="#shutdown" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">shutdown</span><span class="p">()</span>
</pre></div>
</div>
<p>Gracefully shutdown the Forward Secrecy Manager.</p>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
</section>
</section>
<section id="events">
<h3><a class="toc-backref" href="#id22" role="doc-backlink">Events</a><a class="headerlink" href="#events" title="Link to this heading">¶</a></h3>
<p>The ForwardSecrecyManager extends EventEmitter and emits the following events:</p>
<section id="initialized">
<h4><a class="toc-backref" href="#id23" role="doc-backlink">initialized</a><a class="headerlink" href="#initialized" title="Link to this heading">¶</a></h4>
<p>Emitted when the system is fully initialized.</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="nx">forwardSecrecy</span><span class="p">.</span><span class="nx">on</span><span class="p">(</span><span class="s1">&#39;initialized&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">event</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="c1">// event.timestamp: number</span>
<span class="w">  </span><span class="c1">// event.configuration: Object</span>
<span class="p">});</span>
</pre></div>
</div>
</section>
<section id="rotationtriggered">
<h4><a class="toc-backref" href="#id24" role="doc-backlink">rotationTriggered</a><a class="headerlink" href="#rotationtriggered" title="Link to this heading">¶</a></h4>
<p>Emitted when key rotation is initiated.</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="nx">forwardSecrecy</span><span class="p">.</span><span class="nx">on</span><span class="p">(</span><span class="s1">&#39;rotationTriggered&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">event</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="c1">// event.trigger: string</span>
<span class="w">  </span><span class="c1">// event.currentGeneration: number</span>
<span class="w">  </span><span class="c1">// event.timestamp: number</span>
<span class="p">});</span>
</pre></div>
</div>
</section>
<section id="rotationcompleted">
<h4><a class="toc-backref" href="#id25" role="doc-backlink">rotationCompleted</a><a class="headerlink" href="#rotationcompleted" title="Link to this heading">¶</a></h4>
<p>Emitted when key rotation completes successfully.</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="nx">forwardSecrecy</span><span class="p">.</span><span class="nx">on</span><span class="p">(</span><span class="s1">&#39;rotationCompleted&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">event</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="c1">// event.success: boolean</span>
<span class="w">  </span><span class="c1">// event.oldKeys: Object</span>
<span class="w">  </span><span class="c1">// event.newKeys: Object</span>
<span class="w">  </span><span class="c1">// event.rotationTime: number</span>
<span class="w">  </span><span class="c1">// event.trigger: string</span>
<span class="w">  </span><span class="c1">// event.timestamp: number</span>
<span class="p">});</span>
</pre></div>
</div>
</section>
<section id="deletioncompleted">
<h4><a class="toc-backref" href="#id26" role="doc-backlink">deletionCompleted</a><a class="headerlink" href="#deletioncompleted" title="Link to this heading">¶</a></h4>
<p>Emitted when secure deletion is verified.</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="nx">forwardSecrecy</span><span class="p">.</span><span class="nx">on</span><span class="p">(</span><span class="s1">&#39;deletionCompleted&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">event</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="c1">// event.keyGeneration: number</span>
<span class="w">  </span><span class="c1">// event.deletionTime: number</span>
<span class="w">  </span><span class="c1">// event.verified: boolean</span>
<span class="w">  </span><span class="c1">// event.passes: number</span>
<span class="w">  </span><span class="c1">// event.timestamp: number</span>
<span class="p">});</span>
</pre></div>
</div>
</section>
</section>
</section>
<section id="keyrotationengine">
<h2><a class="toc-backref" href="#id27" role="doc-backlink">KeyRotationEngine</a><a class="headerlink" href="#keyrotationengine" title="Link to this heading">¶</a></h2>
<p>Handles automatic and manual key rotation operations.</p>
<section id="id1">
<h3><a class="toc-backref" href="#id28" role="doc-backlink">Constructor</a><a class="headerlink" href="#id1" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="ow">new</span><span class="w"> </span><span class="nx">KeyRotationEngine</span><span class="p">(</span><span class="nx">options</span><span class="p">)</span>
</pre></div>
</div>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">options</span></code> (Object): Configuration options</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">autoRotation</span></code> (boolean): Enable automatic rotation</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">rotationInterval</span></code> (number): Time between rotations</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">messageCountThreshold</span></code> (number): Message count trigger</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">dataVolumeThreshold</span></code> (number): Data volume trigger</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">emergencyRotation</span></code> (boolean): Enable emergency rotation</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">maxRotationTime</span></code> (number): Maximum rotation time</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">keySize</span></code> (number): Key size in bytes (default: 32)</p></li>
</ul>
</li>
</ul>
</section>
<section id="id2">
<h3><a class="toc-backref" href="#id29" role="doc-backlink">Methods</a><a class="headerlink" href="#id2" title="Link to this heading">¶</a></h3>
<section id="id3">
<h4><a class="toc-backref" href="#id30" role="doc-backlink">initialize()</a><a class="headerlink" href="#id3" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">initialize</span><span class="p">()</span>
</pre></div>
</div>
<p>Initialize the key rotation engine.</p>
</section>
<section id="generatekeyset">
<h4><a class="toc-backref" href="#id31" role="doc-backlink">generateKeySet()</a><a class="headerlink" href="#generatekeyset" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">generateKeySet</span><span class="p">(</span><span class="nx">generation</span><span class="p">)</span>
</pre></div>
</div>
<p>Generate a new set of cryptographic keys.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">generation</span></code> (number): Key generation number</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">generation</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">masterKey</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="p">,</span>
<span class="w">  </span><span class="nx">encryptionKey</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="p">,</span>
<span class="w">  </span><span class="nx">macKey</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="p">,</span>
<span class="w">  </span><span class="nx">nextKeyMaterial</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="p">,</span>
<span class="w">  </span><span class="nx">keyFingerprint</span><span class="o">:</span><span class="w"> </span><span class="nx">string</span><span class="p">,</span>
<span class="w">  </span><span class="nx">createdAt</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">expiresAt</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="rotatekeys">
<h4><a class="toc-backref" href="#id32" role="doc-backlink">rotateKeys()</a><a class="headerlink" href="#rotatekeys" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">rotateKeys</span><span class="p">(</span><span class="nx">rotationRequest</span><span class="p">)</span>
</pre></div>
</div>
<p>Perform key rotation operation.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">rotationRequest</span></code> (Object): Rotation request details</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">trigger</span></code> (string): Rotation trigger type</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">currentGeneration</span></code> (number): Current key generation</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">reason</span></code> (string): Rotation reason</p></li>
</ul>
</li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
</section>
</section>
</section>
<section id="securedeletionmanager">
<h2><a class="toc-backref" href="#id33" role="doc-backlink">SecureDeletionManager</a><a class="headerlink" href="#securedeletionmanager" title="Link to this heading">¶</a></h2>
<p>Provides DoD 5220.22-M compliant secure deletion of cryptographic material.</p>
<section id="id4">
<h3><a class="toc-backref" href="#id34" role="doc-backlink">Constructor</a><a class="headerlink" href="#id4" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="ow">new</span><span class="w"> </span><span class="nx">SecureDeletionManager</span><span class="p">(</span><span class="nx">options</span><span class="p">)</span>
</pre></div>
</div>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">options</span></code> (Object): Configuration options</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">cryptographicErasure</span></code> (boolean): Enable cryptographic erasure</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">secureMemory</span></code> (boolean): Enable secure memory management</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">deletionTimeout</span></code> (number): Maximum deletion time</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">overwritePasses</span></code> (number): Number of overwrite passes (default: 7)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">verificationEnabled</span></code> (boolean): Enable deletion verification</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">fipsCompliance</span></code> (boolean): Enable FIPS compliance</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">enhancedErasure</span></code> (boolean): Enable enhanced erasure patterns</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">memoryForensicsResistance</span></code> (boolean): Enable anti-forensics measures</p></li>
</ul>
</li>
</ul>
</section>
<section id="id5">
<h3><a class="toc-backref" href="#id35" role="doc-backlink">Methods</a><a class="headerlink" href="#id5" title="Link to this heading">¶</a></h3>
<section id="performsecuredeletion">
<h4><a class="toc-backref" href="#id36" role="doc-backlink">performSecureDeletion()</a><a class="headerlink" href="#performsecuredeletion" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">performSecureDeletion</span><span class="p">(</span><span class="nx">keyMaterial</span><span class="p">)</span>
</pre></div>
</div>
<p>Perform secure deletion of cryptographic material.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">keyMaterial</span></code> (Object): Key material to delete</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">data</span></code> (Uint8Array): Key data</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">type</span></code> (string): Key type</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">generation</span></code> (number): Key generation</p></li>
</ul>
</li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">success</span><span class="o">:</span><span class="w"> </span><span class="kr">boolean</span><span class="p">,</span>
<span class="w">  </span><span class="nx">deletionTime</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">passes</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">verified</span><span class="o">:</span><span class="w"> </span><span class="kr">boolean</span><span class="p">,</span>
<span class="w">  </span><span class="nx">entropy</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="verifydeletion">
<h4><a class="toc-backref" href="#id37" role="doc-backlink">verifyDeletion()</a><a class="headerlink" href="#verifydeletion" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">verifyDeletion</span><span class="p">(</span><span class="nx">keyMaterial</span><span class="p">)</span>
</pre></div>
</div>
<p>Verify that secure deletion was successful.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">keyMaterial</span></code> (Object): Key material to verify</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">success</span><span class="o">:</span><span class="w"> </span><span class="kr">boolean</span><span class="p">,</span>
<span class="w">  </span><span class="nx">verified</span><span class="o">:</span><span class="w"> </span><span class="kr">boolean</span><span class="p">,</span>
<span class="w">  </span><span class="nx">entropy</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">residualData</span><span class="o">:</span><span class="w"> </span><span class="kr">boolean</span><span class="p">,</span>
<span class="w">  </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
</section>
<section id="zeroknowledgeverifier">
<h2><a class="toc-backref" href="#id38" role="doc-backlink">ZeroKnowledgeVerifier</a><a class="headerlink" href="#zeroknowledgeverifier" title="Link to this heading">¶</a></h2>
<p>Generates and verifies cryptographic proofs without revealing sensitive data.</p>
<section id="id6">
<h3><a class="toc-backref" href="#id39" role="doc-backlink">Constructor</a><a class="headerlink" href="#id6" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="ow">new</span><span class="w"> </span><span class="nx">ZeroKnowledgeVerifier</span><span class="p">(</span><span class="nx">options</span><span class="p">)</span>
</pre></div>
</div>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">options</span></code> (Object): Configuration options</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">enabled</span></code> (boolean): Enable zero-knowledge verification</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">verificationTimeout</span></code> (number): Maximum verification time</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">fipsCompliance</span></code> (boolean): Enable FIPS compliance</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">proofRetention</span></code> (number): Proof retention period</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">advancedProofs</span></code> (boolean): Enable advanced proof protocols</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">enterpriseFeatures</span></code> (boolean): Enable enterprise features</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">batchVerification</span></code> (boolean): Enable batch verification</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">proofCompression</span></code> (boolean): Enable proof compression</p></li>
</ul>
</li>
</ul>
</section>
<section id="id7">
<h3><a class="toc-backref" href="#id40" role="doc-backlink">Methods</a><a class="headerlink" href="#id7" title="Link to this heading">¶</a></h3>
<section id="generaterotationproof">
<h4><a class="toc-backref" href="#id41" role="doc-backlink">generateRotationProof()</a><a class="headerlink" href="#generaterotationproof" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">generateRotationProof</span><span class="p">(</span><span class="nx">rotationData</span><span class="p">)</span>
</pre></div>
</div>
<p>Generate zero-knowledge proof of key rotation.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">rotationData</span></code> (Object): Rotation data</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">oldKeys</span></code> (Object): Previous key set</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">newKeys</span></code> (Object): New key set</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">rotationTime</span></code> (number): Rotation time</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">trigger</span></code> (string): Rotation trigger</p></li>
</ul>
</li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">proof</span><span class="o">:</span><span class="w"> </span><span class="nb">Object</span><span class="p">,</span>
<span class="w">  </span><span class="nx">metadata</span><span class="o">:</span><span class="w"> </span><span class="nb">Object</span><span class="p">,</span>
<span class="w">  </span><span class="nx">commitment</span><span class="o">:</span><span class="w"> </span><span class="nb">Object</span><span class="p">,</span>
<span class="w">  </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="generatedeletionproof">
<h4><a class="toc-backref" href="#id42" role="doc-backlink">generateDeletionProof()</a><a class="headerlink" href="#generatedeletionproof" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">generateDeletionProof</span><span class="p">(</span><span class="nx">deletionData</span><span class="p">)</span>
</pre></div>
</div>
<p>Generate zero-knowledge proof of secure deletion.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">deletionData</span></code> (Object): Deletion data</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
</section>
<section id="verifyproof">
<h4><a class="toc-backref" href="#id43" role="doc-backlink">verifyProof()</a><a class="headerlink" href="#verifyproof" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">verifyProof</span><span class="p">(</span><span class="nx">proofData</span><span class="p">)</span>
</pre></div>
</div>
<p>Verify a zero-knowledge proof.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">proofData</span></code> (Object): Proof to verify</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">valid</span><span class="o">:</span><span class="w"> </span><span class="kr">boolean</span><span class="p">,</span>
<span class="w">  </span><span class="nx">proofType</span><span class="o">:</span><span class="w"> </span><span class="nx">string</span><span class="p">,</span>
<span class="w">  </span><span class="nx">verificationTime</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">metadata</span><span class="o">:</span><span class="w"> </span><span class="nb">Object</span><span class="p">,</span>
<span class="w">  </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="verifybatchproofs">
<h4><a class="toc-backref" href="#id44" role="doc-backlink">verifyBatchProofs()</a><a class="headerlink" href="#verifybatchproofs" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">verifyBatchProofs</span><span class="p">(</span><span class="nx">proofBatch</span><span class="p">)</span>
</pre></div>
</div>
<p>Verify multiple proofs in batch for performance.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">proofBatch</span></code> (Array): Array of proofs to verify</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">totalProofs</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">successfulVerifications</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">failedVerifications</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">batchVerificationTime</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">averageVerificationTime</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">results</span><span class="o">:</span><span class="w"> </span><span class="nb">Array</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
</section>
<section id="audittrailsystem">
<h2><a class="toc-backref" href="#id45" role="doc-backlink">AuditTrailSystem</a><a class="headerlink" href="#audittrailsystem" title="Link to this heading">¶</a></h2>
<p>Maintains tamper-evident logs for compliance and security monitoring.</p>
<section id="id8">
<h3><a class="toc-backref" href="#id46" role="doc-backlink">Constructor</a><a class="headerlink" href="#id8" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="ow">new</span><span class="w"> </span><span class="nx">AuditTrailSystem</span><span class="p">(</span><span class="nx">options</span><span class="p">)</span>
</pre></div>
</div>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">options</span></code> (Object): Configuration options</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">enabled</span></code> (boolean): Enable audit trails</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">retentionPeriod</span></code> (number): Log retention period</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">fipsCompliance</span></code> (boolean): Enable FIPS compliance</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">maxLogSize</span></code> (number): Maximum log entries</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">compressionEnabled</span></code> (boolean): Enable log compression</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">encryptionEnabled</span></code> (boolean): Enable log encryption</p></li>
</ul>
</li>
</ul>
</section>
<section id="id9">
<h3><a class="toc-backref" href="#id47" role="doc-backlink">Methods</a><a class="headerlink" href="#id9" title="Link to this heading">¶</a></h3>
<section id="logevent">
<h4><a class="toc-backref" href="#id48" role="doc-backlink">logEvent()</a><a class="headerlink" href="#logevent" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">logEvent</span><span class="p">(</span><span class="nx">eventData</span><span class="p">)</span>
</pre></div>
</div>
<p>Log an audit event.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">eventData</span></code> (Object): Event data</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">type</span></code> (string): Event type</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">timestamp</span></code> (number): Event timestamp</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">details</span></code> (Object): Event details</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">metadata</span></code> (Object): Additional metadata</p></li>
</ul>
</li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="nx">string</span><span class="p">,</span>
<span class="w">  </span><span class="nx">type</span><span class="o">:</span><span class="w"> </span><span class="nx">string</span><span class="p">,</span>
<span class="w">  </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">details</span><span class="o">:</span><span class="w"> </span><span class="nb">Object</span><span class="p">,</span>
<span class="w">  </span><span class="nx">metadata</span><span class="o">:</span><span class="w"> </span><span class="nb">Object</span><span class="p">,</span>
<span class="w">  </span><span class="nx">signature</span><span class="o">:</span><span class="w"> </span><span class="nx">string</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="getauditlog">
<h4><a class="toc-backref" href="#id49" role="doc-backlink">getAuditLog()</a><a class="headerlink" href="#getauditlog" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">getAuditLog</span><span class="p">(</span><span class="nx">options</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{})</span>
</pre></div>
</div>
<p>Retrieve audit log entries.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">options</span></code> (Object): Query options</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">startDate</span></code> (number): Start timestamp</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">endDate</span></code> (number): End timestamp</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">eventTypes</span></code> (Array): Event types to include</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">limit</span></code> (number): Maximum entries to return</p></li>
</ul>
</li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Array&gt;</p>
</section>
<section id="generateauditreport">
<h4><a class="toc-backref" href="#id50" role="doc-backlink">generateAuditReport()</a><a class="headerlink" href="#generateauditreport" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">generateAuditReport</span><span class="p">(</span><span class="nx">options</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{})</span>
</pre></div>
</div>
<p>Generate comprehensive audit report.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">options</span></code> (Object): Report options</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
</section>
<section id="verifyauditintegrity">
<h4><a class="toc-backref" href="#id51" role="doc-backlink">verifyAuditIntegrity()</a><a class="headerlink" href="#verifyauditintegrity" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">verifyAuditIntegrity</span><span class="p">()</span>
</pre></div>
</div>
<p>Verify the integrity of the audit log chain.</p>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">valid</span><span class="o">:</span><span class="w"> </span><span class="kr">boolean</span><span class="p">,</span>
<span class="w">  </span><span class="nx">totalEvents</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">verifiedEvents</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">integrityViolations</span><span class="o">:</span><span class="w"> </span><span class="nb">Array</span><span class="p">,</span>
<span class="w">  </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
</section>
<section id="enterprisepolicymanager">
<h2><a class="toc-backref" href="#id52" role="doc-backlink">EnterprisePolicyManager</a><a class="headerlink" href="#enterprisepolicymanager" title="Link to this heading">¶</a></h2>
<p>Manages enterprise policies and compliance requirements.</p>
<section id="id10">
<h3><a class="toc-backref" href="#id53" role="doc-backlink">Constructor</a><a class="headerlink" href="#id10" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="ow">new</span><span class="w"> </span><span class="nx">EnterprisePolicyManager</span><span class="p">(</span><span class="nx">options</span><span class="p">)</span>
</pre></div>
</div>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">options</span></code> (Object): Configuration options</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">policyEnforcement</span></code> (boolean): Enable policy enforcement</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">complianceStandards</span></code> (Array): Compliance standards to enforce</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">auditLevel</span></code> (string): Audit level (‘basic’, ‘comprehensive’)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">retentionPolicies</span></code> (Object): Data retention policies</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">alerting</span></code> (Object): Alerting configuration</p></li>
</ul>
</li>
</ul>
</section>
<section id="id11">
<h3><a class="toc-backref" href="#id54" role="doc-backlink">Methods</a><a class="headerlink" href="#id11" title="Link to this heading">¶</a></h3>
<section id="validatecompliance">
<h4><a class="toc-backref" href="#id55" role="doc-backlink">validateCompliance()</a><a class="headerlink" href="#validatecompliance" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">validateCompliance</span><span class="p">(</span><span class="nx">operation</span><span class="p">)</span>
</pre></div>
</div>
<p>Validate operation against enterprise policies.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">operation</span></code> (Object): Operation to validate</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">compliant</span><span class="o">:</span><span class="w"> </span><span class="kr">boolean</span><span class="p">,</span>
<span class="w">  </span><span class="nx">violations</span><span class="o">:</span><span class="w"> </span><span class="nb">Array</span><span class="p">,</span>
<span class="w">  </span><span class="nx">recommendations</span><span class="o">:</span><span class="w"> </span><span class="nb">Array</span><span class="p">,</span>
<span class="w">  </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="enforcepolicy">
<h4><a class="toc-backref" href="#id56" role="doc-backlink">enforcePolicy()</a><a class="headerlink" href="#enforcepolicy" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">enforcePolicy</span><span class="p">(</span><span class="nx">policyName</span><span class="p">,</span><span class="w"> </span><span class="nx">context</span><span class="p">)</span>
</pre></div>
</div>
<p>Enforce a specific enterprise policy.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">policyName</span></code> (string): Name of policy to enforce</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">context</span></code> (Object): Operation context</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
</section>
<section id="id12">
<h4><a class="toc-backref" href="#id57" role="doc-backlink">generateComplianceReport()</a><a class="headerlink" href="#id12" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">generateComplianceReport</span><span class="p">(</span><span class="nx">options</span><span class="p">)</span>
</pre></div>
</div>
<p>Generate enterprise compliance report.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">options</span></code> (Object): Report configuration</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
</section>
</section>
</section>
<section id="error-classes">
<h2><a class="toc-backref" href="#id58" role="doc-backlink">Error Classes</a><a class="headerlink" href="#error-classes" title="Link to this heading">¶</a></h2>
<section id="forwardsecrecyerror">
<h3><a class="toc-backref" href="#id59" role="doc-backlink">ForwardSecrecyError</a><a class="headerlink" href="#forwardsecrecyerror" title="Link to this heading">¶</a></h3>
<p>Base error class for Forward Secrecy operations.</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">class</span><span class="w"> </span><span class="nx">ForwardSecrecyError</span><span class="w"> </span><span class="k">extends</span><span class="w"> </span><span class="ne">Error</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="nx">code</span><span class="p">,</span><span class="w"> </span><span class="nx">details</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{})</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Properties:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">message</span></code> (string): Error message</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">code</span></code> (string): Error code</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">details</span></code> (Object): Additional error details</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">timestamp</span></code> (number): Error timestamp</p></li>
</ul>
</section>
<section id="keyrotationerror">
<h3><a class="toc-backref" href="#id60" role="doc-backlink">KeyRotationError</a><a class="headerlink" href="#keyrotationerror" title="Link to this heading">¶</a></h3>
<p>Error class for key rotation failures.</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">class</span><span class="w"> </span><span class="nx">KeyRotationError</span><span class="w"> </span><span class="k">extends</span><span class="w"> </span><span class="nx">ForwardSecrecyError</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="nx">rotationContext</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{})</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Additional Properties:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">rotationContext</span></code> (Object): Rotation operation context</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">currentGeneration</span></code> (number): Current key generation</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">trigger</span></code> (string): Rotation trigger</p></li>
</ul>
</section>
<section id="securedeletionerror">
<h3><a class="toc-backref" href="#id61" role="doc-backlink">SecureDeletionError</a><a class="headerlink" href="#securedeletionerror" title="Link to this heading">¶</a></h3>
<p>Error class for secure deletion failures.</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">class</span><span class="w"> </span><span class="nx">SecureDeletionError</span><span class="w"> </span><span class="k">extends</span><span class="w"> </span><span class="nx">ForwardSecrecyError</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="nx">deletionContext</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{})</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Additional Properties:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">deletionContext</span></code> (Object): Deletion operation context</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">keyGeneration</span></code> (number): Key generation being deleted</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">passes</span></code> (number): Completed overwrite passes</p></li>
</ul>
</section>
<section id="zeroknowledgeerror">
<h3><a class="toc-backref" href="#id62" role="doc-backlink">ZeroKnowledgeError</a><a class="headerlink" href="#zeroknowledgeerror" title="Link to this heading">¶</a></h3>
<p>Error class for zero-knowledge proof failures.</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">class</span><span class="w"> </span><span class="nx">ZeroKnowledgeError</span><span class="w"> </span><span class="k">extends</span><span class="w"> </span><span class="nx">ForwardSecrecyError</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="nx">proofContext</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{})</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Additional Properties:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">proofContext</span></code> (Object): Proof operation context</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">proofType</span></code> (string): Type of proof</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">verificationStage</span></code> (string): Stage where error occurred</p></li>
</ul>
</section>
</section>
<section id="constants">
<h2><a class="toc-backref" href="#id63" role="doc-backlink">Constants</a><a class="headerlink" href="#constants" title="Link to this heading">¶</a></h2>
<section id="event-types">
<h3><a class="toc-backref" href="#id64" role="doc-backlink">Event Types</a><a class="headerlink" href="#event-types" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">const</span><span class="w"> </span><span class="nx">EVENT_TYPES</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="c1">// Initialization events</span>
<span class="w">  </span><span class="nx">SYSTEM_INITIALIZED</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;SYSTEM_INITIALIZED&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">COMPONENT_INITIALIZED</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;COMPONENT_INITIALIZED&#39;</span><span class="p">,</span>

<span class="w">  </span><span class="c1">// Rotation events</span>
<span class="w">  </span><span class="nx">ROTATION_TRIGGERED</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;ROTATION_TRIGGERED&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">ROTATION_STARTED</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;ROTATION_STARTED&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">ROTATION_COMPLETED</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;ROTATION_COMPLETED&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">ROTATION_FAILED</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;ROTATION_FAILED&#39;</span><span class="p">,</span>

<span class="w">  </span><span class="c1">// Deletion events</span>
<span class="w">  </span><span class="nx">DELETION_STARTED</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;DELETION_STARTED&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">DELETION_COMPLETED</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;DELETION_COMPLETED&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">DELETION_FAILED</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;DELETION_FAILED&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">DELETION_VERIFIED</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;DELETION_VERIFIED&#39;</span><span class="p">,</span>

<span class="w">  </span><span class="c1">// Proof events</span>
<span class="w">  </span><span class="nx">PROOF_GENERATED</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;PROOF_GENERATED&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">PROOF_VERIFIED</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;PROOF_VERIFIED&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">PROOF_FAILED</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;PROOF_FAILED&#39;</span><span class="p">,</span>

<span class="w">  </span><span class="c1">// Compliance events</span>
<span class="w">  </span><span class="nx">COMPLIANCE_CHECK</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;COMPLIANCE_CHECK&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">COMPLIANCE_VIOLATION</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;COMPLIANCE_VIOLATION&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">AUDIT_EVENT</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;AUDIT_EVENT&#39;</span><span class="p">,</span>

<span class="w">  </span><span class="c1">// Performance events</span>
<span class="w">  </span><span class="nx">PERFORMANCE_METRIC</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;PERFORMANCE_METRIC&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">PERFORMANCE_WARNING</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;PERFORMANCE_WARNING&#39;</span><span class="p">,</span>

<span class="w">  </span><span class="c1">// Security events</span>
<span class="w">  </span><span class="nx">SECURITY_ALERT</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;SECURITY_ALERT&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">EMERGENCY_ROTATION</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;EMERGENCY_ROTATION&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">THREAT_DETECTED</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;THREAT_DETECTED&#39;</span>
<span class="p">};</span>
</pre></div>
</div>
</section>
<section id="rotation-triggers">
<h3><a class="toc-backref" href="#id65" role="doc-backlink">Rotation Triggers</a><a class="headerlink" href="#rotation-triggers" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">const</span><span class="w"> </span><span class="nx">ROTATION_TRIGGERS</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">TIME_BASED</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;TIME_BASED&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">MESSAGE_COUNT</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;MESSAGE_COUNT&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">DATA_VOLUME</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;DATA_VOLUME&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">MANUAL_REQUEST</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;MANUAL_REQUEST&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">EMERGENCY</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;EMERGENCY&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">POLICY_ENFORCEMENT</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;POLICY_ENFORCEMENT&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">SECURITY_EVENT</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;SECURITY_EVENT&#39;</span>
<span class="p">};</span>
</pre></div>
</div>
</section>
<section id="compliance-standards">
<h3><a class="toc-backref" href="#id66" role="doc-backlink">Compliance Standards</a><a class="headerlink" href="#compliance-standards" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">const</span><span class="w"> </span><span class="nx">COMPLIANCE_STANDARDS</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">FIPS_140_2</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;FIPS-140-2&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">DOD_5220_22_M</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;DoD-5220.22-M&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">SOX</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;SOX&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">HIPAA</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;HIPAA&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">GDPR</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;GDPR&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">ISO_27001</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;ISO-27001&#39;</span>
<span class="p">};</span>
</pre></div>
</div>
</section>
<section id="deletion-patterns">
<h3><a class="toc-backref" href="#id67" role="doc-backlink">Deletion Patterns</a><a class="headerlink" href="#deletion-patterns" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">const</span><span class="w"> </span><span class="nx">DELETION_PATTERNS</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">RANDOM</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;random&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">ZEROS</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;zeros&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">ONES</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;ones&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">ALTERNATING</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;alternating&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">INVERSE_ALTERNATING</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;inverse_alternating&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">HASH_BASED</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;hash_based&#39;</span>
<span class="p">};</span>
</pre></div>
</div>
</section>
</section>
<section id="type-definitions">
<h2><a class="toc-backref" href="#id68" role="doc-backlink">Type Definitions</a><a class="headerlink" href="#type-definitions" title="Link to this heading">¶</a></h2>
<section id="keyset">
<h3><a class="toc-backref" href="#id69" role="doc-backlink">KeySet</a><a class="headerlink" href="#keyset" title="Link to this heading">¶</a></h3>
<div class="highlight-typescript notranslate"><div class="highlight"><pre><span></span><span class="kd">interface</span><span class="w"> </span><span class="nx">KeySet</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">generation</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">  </span><span class="nx">masterKey</span><span class="o">:</span><span class="w"> </span><span class="kt">Uint8Array</span><span class="p">;</span>
<span class="w">  </span><span class="nx">encryptionKey</span><span class="o">:</span><span class="w"> </span><span class="kt">Uint8Array</span><span class="p">;</span>
<span class="w">  </span><span class="nx">macKey</span><span class="o">:</span><span class="w"> </span><span class="kt">Uint8Array</span><span class="p">;</span>
<span class="w">  </span><span class="nx">nextKeyMaterial</span><span class="o">:</span><span class="w"> </span><span class="kt">Uint8Array</span><span class="p">;</span>
<span class="w">  </span><span class="nx">keyFingerprint</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="nx">createdAt</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">  </span><span class="nx">expiresAt</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="rotationresult">
<h3><a class="toc-backref" href="#id70" role="doc-backlink">RotationResult</a><a class="headerlink" href="#rotationresult" title="Link to this heading">¶</a></h3>
<div class="highlight-typescript notranslate"><div class="highlight"><pre><span></span><span class="kd">interface</span><span class="w"> </span><span class="nx">RotationResult</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">success</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="p">;</span>
<span class="w">  </span><span class="nx">oldKeys</span><span class="o">:</span><span class="w"> </span><span class="kt">KeySet</span><span class="p">;</span>
<span class="w">  </span><span class="nx">newKeys</span><span class="o">:</span><span class="w"> </span><span class="kt">KeySet</span><span class="p">;</span>
<span class="w">  </span><span class="nx">rotationData</span><span class="o">:</span><span class="w"> </span><span class="kt">Object</span><span class="p">;</span>
<span class="w">  </span><span class="nx">rotationTime</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">  </span><span class="nx">trigger</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="deletionresult">
<h3><a class="toc-backref" href="#id71" role="doc-backlink">DeletionResult</a><a class="headerlink" href="#deletionresult" title="Link to this heading">¶</a></h3>
<div class="highlight-typescript notranslate"><div class="highlight"><pre><span></span><span class="kd">interface</span><span class="w"> </span><span class="nx">DeletionResult</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">success</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="p">;</span>
<span class="w">  </span><span class="nx">deletionTime</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">  </span><span class="nx">passes</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">  </span><span class="nx">verified</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="p">;</span>
<span class="w">  </span><span class="nx">entropy</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">  </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="proofdata">
<h3><a class="toc-backref" href="#id72" role="doc-backlink">ProofData</a><a class="headerlink" href="#proofdata" title="Link to this heading">¶</a></h3>
<div class="highlight-typescript notranslate"><div class="highlight"><pre><span></span><span class="kd">interface</span><span class="w"> </span><span class="nx">ProofData</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">proof</span><span class="o">:</span><span class="w"> </span><span class="kt">Object</span><span class="p">;</span>
<span class="w">  </span><span class="nx">metadata</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">proofId</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">    </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">    </span><span class="nx">generation</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">    </span><span class="nx">verificationTime</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">  </span><span class="p">};</span>
<span class="w">  </span><span class="nx">commitment</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">old</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">    </span><span class="nx">new</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="p">};</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="auditevent">
<h3><a class="toc-backref" href="#id73" role="doc-backlink">AuditEvent</a><a class="headerlink" href="#auditevent" title="Link to this heading">¶</a></h3>
<div class="highlight-typescript notranslate"><div class="highlight"><pre><span></span><span class="kd">interface</span><span class="w"> </span><span class="nx">AuditEvent</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="kr">type</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">  </span><span class="nx">details</span><span class="o">:</span><span class="w"> </span><span class="kt">Object</span><span class="p">;</span>
<span class="w">  </span><span class="nx">metadata</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">source</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">    </span><span class="nx">version</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">    </span><span class="nx">sequenceNumber</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">    </span><span class="nx">chainHash?</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="p">};</span>
<span class="w">  </span><span class="nx">signature</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="nx">compliance</span><span class="o">?:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">fipsCompliant</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="p">;</span>
<span class="w">    </span><span class="nx">dodCompliant</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="p">;</span>
<span class="w">    </span><span class="nx">retentionPeriod</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">  </span><span class="p">};</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="compliancereport">
<h3><a class="toc-backref" href="#id74" role="doc-backlink">ComplianceReport</a><a class="headerlink" href="#compliancereport" title="Link to this heading">¶</a></h3>
<div class="highlight-typescript notranslate"><div class="highlight"><pre><span></span><span class="kd">interface</span><span class="w"> </span><span class="nx">ComplianceReport</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">reportId</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="nx">generatedAt</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">  </span><span class="nx">period</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">startDate</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">    </span><span class="nx">endDate</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">  </span><span class="p">};</span>
<span class="w">  </span><span class="nx">standards</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">metrics</span><span class="o">:</span><span class="w"> </span><span class="kt">Object</span><span class="p">;</span>
<span class="w">  </span><span class="nx">events</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">  </span><span class="nx">compliance</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">overallScore</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">    </span><span class="nx">standardsCompliance</span><span class="o">:</span><span class="w"> </span><span class="kt">Object</span><span class="p">;</span>
<span class="w">    </span><span class="nx">recommendations</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">[];</span>
<span class="w">  </span><span class="p">};</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="usage-examples">
<h2><a class="toc-backref" href="#id75" role="doc-backlink">Usage Examples</a><a class="headerlink" href="#usage-examples" title="Link to this heading">¶</a></h2>
<section id="basic-usage">
<h3><a class="toc-backref" href="#id76" role="doc-backlink">Basic Usage</a><a class="headerlink" href="#basic-usage" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">import</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">ForwardSecrecyManager</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;webOTR/core/forward-secrecy&#39;</span><span class="p">;</span>

<span class="c1">// Initialize with default configuration</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">forwardSecrecy</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">ForwardSecrecyManager</span><span class="p">();</span>

<span class="c1">// Set up event listeners</span>
<span class="nx">forwardSecrecy</span><span class="p">.</span><span class="nx">on</span><span class="p">(</span><span class="s1">&#39;rotationCompleted&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">event</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="sb">`Key rotation completed in </span><span class="si">${</span><span class="nx">event</span><span class="p">.</span><span class="nx">rotationTime</span><span class="si">}</span><span class="sb">ms`</span><span class="p">);</span>
<span class="p">});</span>

<span class="c1">// Initialize and start</span>
<span class="k">await</span><span class="w"> </span><span class="nx">forwardSecrecy</span><span class="p">.</span><span class="nx">initialize</span><span class="p">();</span>
</pre></div>
</div>
</section>
<section id="advanced-configuration">
<h3><a class="toc-backref" href="#id77" role="doc-backlink">Advanced Configuration</a><a class="headerlink" href="#advanced-configuration" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">const</span><span class="w"> </span><span class="nx">forwardSecrecy</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">ForwardSecrecyManager</span><span class="p">({</span>
<span class="w">  </span><span class="nx">autoRotation</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nx">rotationInterval</span><span class="o">:</span><span class="w"> </span><span class="mf">1800000</span><span class="p">,</span><span class="w">        </span><span class="c1">// 30 minutes</span>
<span class="w">  </span><span class="nx">messageCountThreshold</span><span class="o">:</span><span class="w"> </span><span class="mf">500</span><span class="p">,</span>
<span class="w">  </span><span class="nx">dataVolumeThreshold</span><span class="o">:</span><span class="w"> </span><span class="mf">5242880</span><span class="p">,</span><span class="w">     </span><span class="c1">// 5MB</span>
<span class="w">  </span><span class="nx">fipsCompliance</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nx">enterpriseIntegration</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nx">auditTrails</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span>
<span class="p">});</span>

<span class="k">await</span><span class="w"> </span><span class="nx">forwardSecrecy</span><span class="p">.</span><span class="nx">initialize</span><span class="p">();</span>
</pre></div>
</div>
</section>
<section id="manual-operations">
<h3><a class="toc-backref" href="#id78" role="doc-backlink">Manual Operations</a><a class="headerlink" href="#manual-operations" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="c1">// Manual key rotation</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">rotationResult</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">forwardSecrecy</span><span class="p">.</span><span class="nx">rotateKeysManually</span><span class="p">(</span><span class="s1">&#39;Security audit&#39;</span><span class="p">);</span>

<span class="c1">// Emergency rotation</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">emergencyResult</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">forwardSecrecy</span><span class="p">.</span><span class="nx">emergencyRotation</span><span class="p">(</span><span class="s1">&#39;Suspected compromise&#39;</span><span class="p">);</span>

<span class="c1">// Get security status</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">status</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">forwardSecrecy</span><span class="p">.</span><span class="nx">getSecurityStatus</span><span class="p">();</span>

<span class="c1">// Generate compliance report</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">report</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">forwardSecrecy</span><span class="p">.</span><span class="nx">generateComplianceReport</span><span class="p">({</span>
<span class="w">  </span><span class="nx">standards</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;FIPS-140-2&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;DoD-5220.22-M&#39;</span><span class="p">],</span>
<span class="w">  </span><span class="nx">format</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;json&#39;</span>
<span class="p">});</span>
</pre></div>
</div>
</section>
<section id="error-handling">
<h3><a class="toc-backref" href="#id79" role="doc-backlink">Error Handling</a><a class="headerlink" href="#error-handling" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">await</span><span class="w"> </span><span class="nx">forwardSecrecy</span><span class="p">.</span><span class="nx">rotateKeysManually</span><span class="p">();</span>
<span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="w"> </span><span class="ow">instanceof</span><span class="w"> </span><span class="nx">KeyRotationError</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">console</span><span class="p">.</span><span class="nx">error</span><span class="p">(</span><span class="s1">&#39;Key rotation failed:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">.</span><span class="nx">message</span><span class="p">);</span>
<span class="w">    </span><span class="nx">console</span><span class="p">.</span><span class="nx">error</span><span class="p">(</span><span class="s1">&#39;Context:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">.</span><span class="nx">rotationContext</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="w"> </span><span class="ow">instanceof</span><span class="w"> </span><span class="nx">ForwardSecrecyError</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">console</span><span class="p">.</span><span class="nx">error</span><span class="p">(</span><span class="s1">&#39;Forward secrecy error:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">.</span><span class="nx">code</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">.</span><span class="nx">details</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">console</span><span class="p">.</span><span class="nx">error</span><span class="p">(</span><span class="s1">&#39;Unexpected error:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p>This API reference provides complete documentation for all Forward Secrecy system components, methods, events, and data structures.</p>
</section>
</section>
</section>

        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          <a class="next-page" href="ake-summary.html">
              <div class="page-info">
                <div class="context">
                  <span>Next</span>
                </div>
                <div class="title">AKE Documentation Summary</div>
              </div>
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
            </a>
          <a class="prev-page" href="forward-secrecy-implementation.html">
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
              <div class="page-info">
                <div class="context">
                  <span>Previous</span>
                </div>
                
                <div class="title">Forward Secrecy Implementation Guide</div>
                
              </div>
            </a>
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 1980, WebOTR Team
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer">
      
      
      <div class="toc-sticky toc-scroll">
        <div class="toc-title-container">
          <span class="toc-title">
            On this page
          </span>
        </div>
        <div class="toc-tree-container">
          <div class="toc-tree">
            <ul>
<li><a class="reference internal" href="#">Forward Secrecy API Reference</a><ul>
<li><a class="reference internal" href="#forwardsecrecymanager">ForwardSecrecyManager</a><ul>
<li><a class="reference internal" href="#constructor">Constructor</a></li>
<li><a class="reference internal" href="#methods">Methods</a><ul>
<li><a class="reference internal" href="#initialize">initialize()</a></li>
<li><a class="reference internal" href="#rotatekeysmanually">rotateKeysManually()</a></li>
<li><a class="reference internal" href="#emergencyrotation">emergencyRotation()</a></li>
<li><a class="reference internal" href="#getsecuritystatus">getSecurityStatus()</a></li>
<li><a class="reference internal" href="#generatecompliancereport">generateComplianceReport()</a></li>
<li><a class="reference internal" href="#shutdown">shutdown()</a></li>
</ul>
</li>
<li><a class="reference internal" href="#events">Events</a><ul>
<li><a class="reference internal" href="#initialized">initialized</a></li>
<li><a class="reference internal" href="#rotationtriggered">rotationTriggered</a></li>
<li><a class="reference internal" href="#rotationcompleted">rotationCompleted</a></li>
<li><a class="reference internal" href="#deletioncompleted">deletionCompleted</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#keyrotationengine">KeyRotationEngine</a><ul>
<li><a class="reference internal" href="#id1">Constructor</a></li>
<li><a class="reference internal" href="#id2">Methods</a><ul>
<li><a class="reference internal" href="#id3">initialize()</a></li>
<li><a class="reference internal" href="#generatekeyset">generateKeySet()</a></li>
<li><a class="reference internal" href="#rotatekeys">rotateKeys()</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#securedeletionmanager">SecureDeletionManager</a><ul>
<li><a class="reference internal" href="#id4">Constructor</a></li>
<li><a class="reference internal" href="#id5">Methods</a><ul>
<li><a class="reference internal" href="#performsecuredeletion">performSecureDeletion()</a></li>
<li><a class="reference internal" href="#verifydeletion">verifyDeletion()</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#zeroknowledgeverifier">ZeroKnowledgeVerifier</a><ul>
<li><a class="reference internal" href="#id6">Constructor</a></li>
<li><a class="reference internal" href="#id7">Methods</a><ul>
<li><a class="reference internal" href="#generaterotationproof">generateRotationProof()</a></li>
<li><a class="reference internal" href="#generatedeletionproof">generateDeletionProof()</a></li>
<li><a class="reference internal" href="#verifyproof">verifyProof()</a></li>
<li><a class="reference internal" href="#verifybatchproofs">verifyBatchProofs()</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#audittrailsystem">AuditTrailSystem</a><ul>
<li><a class="reference internal" href="#id8">Constructor</a></li>
<li><a class="reference internal" href="#id9">Methods</a><ul>
<li><a class="reference internal" href="#logevent">logEvent()</a></li>
<li><a class="reference internal" href="#getauditlog">getAuditLog()</a></li>
<li><a class="reference internal" href="#generateauditreport">generateAuditReport()</a></li>
<li><a class="reference internal" href="#verifyauditintegrity">verifyAuditIntegrity()</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#enterprisepolicymanager">EnterprisePolicyManager</a><ul>
<li><a class="reference internal" href="#id10">Constructor</a></li>
<li><a class="reference internal" href="#id11">Methods</a><ul>
<li><a class="reference internal" href="#validatecompliance">validateCompliance()</a></li>
<li><a class="reference internal" href="#enforcepolicy">enforcePolicy()</a></li>
<li><a class="reference internal" href="#id12">generateComplianceReport()</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#error-classes">Error Classes</a><ul>
<li><a class="reference internal" href="#forwardsecrecyerror">ForwardSecrecyError</a></li>
<li><a class="reference internal" href="#keyrotationerror">KeyRotationError</a></li>
<li><a class="reference internal" href="#securedeletionerror">SecureDeletionError</a></li>
<li><a class="reference internal" href="#zeroknowledgeerror">ZeroKnowledgeError</a></li>
</ul>
</li>
<li><a class="reference internal" href="#constants">Constants</a><ul>
<li><a class="reference internal" href="#event-types">Event Types</a></li>
<li><a class="reference internal" href="#rotation-triggers">Rotation Triggers</a></li>
<li><a class="reference internal" href="#compliance-standards">Compliance Standards</a></li>
<li><a class="reference internal" href="#deletion-patterns">Deletion Patterns</a></li>
</ul>
</li>
<li><a class="reference internal" href="#type-definitions">Type Definitions</a><ul>
<li><a class="reference internal" href="#keyset">KeySet</a></li>
<li><a class="reference internal" href="#rotationresult">RotationResult</a></li>
<li><a class="reference internal" href="#deletionresult">DeletionResult</a></li>
<li><a class="reference internal" href="#proofdata">ProofData</a></li>
<li><a class="reference internal" href="#auditevent">AuditEvent</a></li>
<li><a class="reference internal" href="#compliancereport">ComplianceReport</a></li>
</ul>
</li>
<li><a class="reference internal" href="#usage-examples">Usage Examples</a><ul>
<li><a class="reference internal" href="#basic-usage">Basic Usage</a></li>
<li><a class="reference internal" href="#advanced-configuration">Advanced Configuration</a></li>
<li><a class="reference internal" href="#manual-operations">Manual Operations</a></li>
<li><a class="reference internal" href="#error-handling">Error Handling</a></li>
</ul>
</li>
</ul>
</li>
</ul>

          </div>
        </div>
      </div>
      
      
    </aside>
  </div>
</div><script src="../_static/documentation_options.js?v=01f34227"></script>
    <script src="../_static/doctools.js?v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/scripts/furo.js?v=5fa4622c"></script>
    <script src="../_static/mermaid-renderer.js?v=b683124c"></script>
    </body>
</html>