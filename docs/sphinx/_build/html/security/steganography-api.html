<!doctype html>
<html class="no-js" lang="en" data-content_root="../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><meta name="viewport" content="width=device-width, initial-scale=1" />
<link rel="index" title="Index" href="../genindex.html" /><link rel="search" title="Search" href="../search.html" /><link rel="next" title="Frequently Asked Questions" href="../reference/faq.html" /><link rel="prev" title="Steganography Implementation Guide" href="steganography-implementation.html" />

    <link rel="shortcut icon" href="../_static/favicon.svg"/><!-- Generated with Sphinx 7.4.7 and Furo 2024.08.06 -->
        <title>Steganography API Reference - WebOTR Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../_static/styles/furo.css?v=354aac6f" />
    <link rel="stylesheet" type="text/css" href="../_static/styles/furo-extensions.css?v=302659d7" />
    <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=21a7f706" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  --color-brand-primary: #6366f1;
  --color-brand-content: #6366f1;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8b5cf6;
  --color-brand-content: #8b5cf6;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8b5cf6;
  --color-brand-content: #8b5cf6;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-with-moon" viewBox="0 0 24 24">
    <title>Auto light/dark, in light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
      class="icon-custom-derived-from-feather-sun-and-tabler-moon">
      <path style="opacity: 50%" d="M 5.411 14.504 C 5.471 14.504 5.532 14.504 5.591 14.504 C 3.639 16.319 4.383 19.569 6.931 20.352 C 7.693 20.586 8.512 20.551 9.25 20.252 C 8.023 23.207 4.056 23.725 2.11 21.184 C 0.166 18.642 1.702 14.949 4.874 14.536 C 5.051 14.512 5.231 14.5 5.411 14.5 L 5.411 14.504 Z"/>
      <line x1="14.5" y1="3.25" x2="14.5" y2="1.25"/>
      <line x1="14.5" y1="15.85" x2="14.5" y2="17.85"/>
      <line x1="10.044" y1="5.094" x2="8.63" y2="3.68"/>
      <line x1="19" y1="14.05" x2="20.414" y2="15.464"/>
      <line x1="8.2" y1="9.55" x2="6.2" y2="9.55"/>
      <line x1="20.8" y1="9.55" x2="22.8" y2="9.55"/>
      <line x1="10.044" y1="14.006" x2="8.63" y2="15.42"/>
      <line x1="19" y1="5.05" x2="20.414" y2="3.636"/>
      <circle cx="14.5" cy="9.55" r="3.6"/>
    </svg>
  </symbol>
  <symbol id="svg-moon-with-sun" viewBox="0 0 24 24">
    <title>Auto light/dark, in dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
      class="icon-custom-derived-from-feather-sun-and-tabler-moon">
      <path d="M 8.282 7.007 C 8.385 7.007 8.494 7.007 8.595 7.007 C 5.18 10.184 6.481 15.869 10.942 17.24 C 12.275 17.648 13.706 17.589 15 17.066 C 12.851 22.236 5.91 23.143 2.505 18.696 C -0.897 14.249 1.791 7.786 7.342 7.063 C 7.652 7.021 7.965 7 8.282 7 L 8.282 7.007 Z"/>
      <line style="opacity: 50%" x1="18" y1="3.705" x2="18" y2="2.5"/>
      <line style="opacity: 50%" x1="18" y1="11.295" x2="18" y2="12.5"/>
      <line style="opacity: 50%" x1="15.316" y1="4.816" x2="14.464" y2="3.964"/>
      <line style="opacity: 50%" x1="20.711" y1="10.212" x2="21.563" y2="11.063"/>
      <line style="opacity: 50%" x1="14.205" y1="7.5" x2="13.001" y2="7.5"/>
      <line style="opacity: 50%" x1="21.795" y1="7.5" x2="23" y2="7.5"/>
      <line style="opacity: 50%" x1="15.316" y1="10.184" x2="14.464" y2="11.036"/>
      <line style="opacity: 50%" x1="20.711" y1="4.789" x2="21.563" y2="3.937"/>
      <circle style="opacity: 50%" cx="18" cy="7.5" r="2.169"/>
    </svg>
  </symbol>
  <symbol id="svg-pencil" viewBox="0 0 24 24">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-pencil-code">
      <path d="M4 20h4l10.5 -10.5a2.828 2.828 0 1 0 -4 -4l-10.5 10.5v4" />
      <path d="M13.5 6.5l4 4" />
      <path d="M20 21l2 -2l-2 -2" />
      <path d="M17 17l-2 2l2 2" />
    </svg>
  </symbol>
  <symbol id="svg-eye" viewBox="0 0 24 24">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-eye-code">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0" />
      <path
        d="M11.11 17.958c-3.209 -.307 -5.91 -2.293 -8.11 -5.958c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6c-.21 .352 -.427 .688 -.647 1.008" />
      <path d="M20 21l2 -2l-2 -2" />
      <path d="M17 17l-2 2l2 2" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>

<a class="skip-to-content muted-link" href="#furo-main-content">Skip to content</a>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../index.html"><div class="brand">WebOTR Documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto-light"><use href="#svg-sun-with-moon"></use></svg>
          <svg class="theme-icon-when-auto-dark"><use href="#svg-moon-with-sun"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><a class="sidebar-brand" href="../index.html">
  
  <div class="sidebar-logo-container">
    <img class="sidebar-logo" src="../_static/logo.svg" alt="Logo"/>
  </div>
  
  <span class="sidebar-brand-text">WebOTR Documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-scroll"><div class="sidebar-tree">
  <p class="caption" role="heading"><span class="caption-text">User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user-guide/installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../user-guide/getting-started.html">Getting Started</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../developer-guide/architecture.html">Architecture Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../developer-guide/contributing.html">Contributing to WebOTR</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Protocol Compliance &amp; Advanced Features</span></p>
<ul>
<li class="toctree-l1 has-children"><a class="reference internal" href="../protocol/index.html">Protocol Compliance &amp; Advanced Features</a><input class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of Protocol Compliance &amp; Advanced Features</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul>
<li class="toctree-l2"><a class="reference internal" href="../protocol/overview.html">Protocol Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/version-negotiation.html">Version Negotiation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/message-ordering.html">Message Ordering</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/enhanced-smp.html">Enhanced SMP</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/policy-manager.html">Policy Manager</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/integration-guide.html">Integration Guide</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/troubleshooting.html">Troubleshooting Guide</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Security</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="overview.html">Security Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements-summary.html">libOTR Security Enhancements Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements.html">libOTR Security Enhancements</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements-implementation.html">libOTR Enhancements Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements-api.html">libOTR Enhancements API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements-testing.html">libOTR Enhancements Testing and Validation</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy-summary.html">Forward Secrecy Documentation Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy.html">Forward Secrecy Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy-architecture.html">Forward Secrecy Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy-implementation.html">Forward Secrecy Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy-api.html">Forward Secrecy API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake-summary.html">AKE Documentation Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake.html">Authenticated Key Exchange (AKE)</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake-architecture.html">AKE Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake-implementation.html">AKE Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake-api.html">AKE API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography-summary.html">Steganography Documentation Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography.html">Steganography Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography-architecture.html">Steganography Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography-implementation.html">Steganography Implementation Guide</a></li>
<li class="toctree-l1 current current-page"><a class="current reference internal" href="#">Steganography API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../reference/faq.html">Frequently Asked Questions</a></li>
<li class="toctree-l1"><a class="reference internal" href="../reference/changelog.html">Changelog</a></li>
</ul>

</div>
</div>

      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          <div class="view-this-page">
  <a class="muted-link" href="https://github.com/forkrul/webOTteR/blob/main/docs/sphinx/security/steganography-api.rst?plain=true" title="View this page">
    <svg><use href="#svg-eye"></use></svg>
    <span class="visually-hidden">View this page</span>
  </a>
</div><div class="edit-this-page">
  <a class="muted-link" href="https://github.com/forkrul/webOTteR/edit/main/docs/sphinx/security/steganography-api.rst" title="Edit this page">
    <svg><use href="#svg-pencil"></use></svg>
    <span class="visually-hidden">Edit this page</span>
  </a>
</div><div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto-light"><use href="#svg-sun-with-moon"></use></svg>
              <svg class="theme-icon-when-auto-dark"><use href="#svg-moon-with-sun"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main" id="furo-main-content">
          <section id="steganography-api-reference">
<h1>Steganography API Reference<a class="headerlink" href="#steganography-api-reference" title="Link to this heading">¶</a></h1>
<p>Complete API documentation for WebOTR’s steganography system components.</p>
<nav class="contents local" id="table-of-contents">
<p class="topic-title">Table of Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#steganographyengine" id="id13">SteganographyEngine</a></p>
<ul>
<li><p><a class="reference internal" href="#constructor" id="id14">Constructor</a></p></li>
<li><p><a class="reference internal" href="#methods" id="id15">Methods</a></p>
<ul>
<li><p><a class="reference internal" href="#hidemessage" id="id16">hideMessage()</a></p></li>
<li><p><a class="reference internal" href="#revealmessage" id="id17">revealMessage()</a></p></li>
<li><p><a class="reference internal" href="#detectmessage" id="id18">detectMessage()</a></p></li>
<li><p><a class="reference internal" href="#analyzecapacity" id="id19">analyzeCapacity()</a></p></li>
</ul>
</li>
</ul>
</li>
<li><p><a class="reference internal" href="#otrsteganographysession" id="id20">OTRSteganographySession</a></p>
<ul>
<li><p><a class="reference internal" href="#id1" id="id21">Constructor</a></p></li>
<li><p><a class="reference internal" href="#id2" id="id22">Methods</a></p>
<ul>
<li><p><a class="reference internal" href="#sendstegomessage" id="id23">sendStegoMessage()</a></p></li>
<li><p><a class="reference internal" href="#processstegoimage" id="id24">processStegoImage()</a></p></li>
</ul>
</li>
</ul>
</li>
<li><p><a class="reference internal" href="#coverimagemanager" id="id25">CoverImageManager</a></p>
<ul>
<li><p><a class="reference internal" href="#id7" id="id26">Constructor</a></p></li>
<li><p><a class="reference internal" href="#id8" id="id27">Methods</a></p>
<ul>
<li><p><a class="reference internal" href="#selectoptimalcover" id="id28">selectOptimalCover()</a></p></li>
<li><p><a class="reference internal" href="#analyzecover" id="id29">analyzeCover()</a></p></li>
<li><p><a class="reference internal" href="#generatecover" id="id30">generateCover()</a></p></li>
</ul>
</li>
</ul>
</li>
<li><p><a class="reference internal" href="#statisticalsecurity" id="id31">StatisticalSecurity</a></p>
<ul>
<li><p><a class="reference internal" href="#id9" id="id32">Constructor</a></p></li>
<li><p><a class="reference internal" href="#id10" id="id33">Methods</a></p>
<ul>
<li><p><a class="reference internal" href="#enhancesecuritybeforeembedding" id="id34">enhanceSecurityBeforeEmbedding()</a></p></li>
<li><p><a class="reference internal" href="#injectstatisticalnoise" id="id35">injectStatisticalNoise()</a></p></li>
<li><p><a class="reference internal" href="#generateembeddingpositions" id="id36">generateEmbeddingPositions()</a></p></li>
</ul>
</li>
</ul>
</li>
<li><p><a class="reference internal" href="#platformintegration" id="id37">PlatformIntegration</a></p>
<ul>
<li><p><a class="reference internal" href="#id11" id="id38">Constructor</a></p></li>
<li><p><a class="reference internal" href="#id12" id="id39">Methods</a></p>
<ul>
<li><p><a class="reference internal" href="#detectplatform" id="id40">detectPlatform()</a></p></li>
<li><p><a class="reference internal" href="#interceptupload" id="id41">interceptUpload()</a></p></li>
<li><p><a class="reference internal" href="#processdownloadedimages" id="id42">processDownloadedImages()</a></p></li>
</ul>
</li>
</ul>
</li>
<li><p><a class="reference internal" href="#error-classes" id="id43">Error Classes</a></p>
<ul>
<li><p><a class="reference internal" href="#steganographyerror" id="id44">SteganographyError</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#constants" id="id45">Constants</a></p>
<ul>
<li><p><a class="reference internal" href="#steganographic-methods" id="id46">Steganographic Methods</a></p></li>
<li><p><a class="reference internal" href="#image-formats" id="id47">Image Formats</a></p></li>
<li><p><a class="reference internal" href="#quality-metrics" id="id48">Quality Metrics</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#type-definitions" id="id49">Type Definitions</a></p>
<ul>
<li><p><a class="reference internal" href="#stegomessage" id="id50">StegoMessage</a></p></li>
<li><p><a class="reference internal" href="#coveranalysis" id="id51">CoverAnalysis</a></p></li>
<li><p><a class="reference internal" href="#embeddingparameters" id="id52">EmbeddingParameters</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#usage-examples" id="id53">Usage Examples</a></p>
<ul>
<li><p><a class="reference internal" href="#basic-steganography" id="id54">Basic Steganography</a></p></li>
<li><p><a class="reference internal" href="#otr-integration" id="id55">OTR Integration</a></p></li>
<li><p><a class="reference internal" href="#platform-integration" id="id56">Platform Integration</a></p></li>
</ul>
</li>
</ul>
</nav>
<section id="steganographyengine">
<h2><a class="toc-backref" href="#id13" role="doc-backlink">SteganographyEngine</a><a class="headerlink" href="#steganographyengine" title="Link to this heading">¶</a></h2>
<p>The main steganography engine for hiding and revealing messages in images.</p>
<section id="constructor">
<h3><a class="toc-backref" href="#id14" role="doc-backlink">Constructor</a><a class="headerlink" href="#constructor" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="ow">new</span><span class="w"> </span><span class="nx">SteganographyEngine</span><span class="p">(</span><span class="nx">options</span><span class="p">)</span>
</pre></div>
</div>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">options</span></code> (Object): Configuration options</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">method</span></code> (string): Steganographic method (‘LSB_ALPHA’, ‘ADAPTIVE_LSB’) (default: ‘LSB_ALPHA’)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">bitsPerPixel</span></code> (number): Bits to use per pixel (default: 1)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">compressionLevel</span></code> (number): Compression level 0-9 (default: 0)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">noiseInjection</span></code> (boolean): Enable statistical noise injection (default: true)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">adaptiveLSB</span></code> (boolean): Enable adaptive LSB positioning (default: true)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">antiDetection</span></code> (boolean): Enable anti-detection measures (default: true)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">useWebWorkers</span></code> (boolean): Use web workers for processing (default: true)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">chunkSize</span></code> (number): Processing chunk size (default: 100000)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">progressiveProcessing</span></code> (boolean): Enable progressive processing (default: true)</p></li>
</ul>
</li>
</ul>
</section>
<section id="methods">
<h3><a class="toc-backref" href="#id15" role="doc-backlink">Methods</a><a class="headerlink" href="#methods" title="Link to this heading">¶</a></h3>
<section id="hidemessage">
<h4><a class="toc-backref" href="#id16" role="doc-backlink">hideMessage()</a><a class="headerlink" href="#hidemessage" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">hideMessage</span><span class="p">(</span><span class="nx">coverImage</span><span class="p">,</span><span class="w"> </span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="nx">options</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{})</span>
</pre></div>
</div>
<p>Hide a message within a cover image using steganography.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">coverImage</span></code> (Blob|File|ImageData): Cover image for hiding the message</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">message</span></code> (string|Uint8Array): Message to hide</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">options</span></code> (Object): Hiding options</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">password</span></code> (string): Optional password for encryption</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">format</span></code> (string): Output format (‘PNG’, ‘BMP’) (default: ‘PNG’)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">quality</span></code> (number): Output quality 0-1 (default: 1.0)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">sessionKey</span></code> (Uint8Array): Session key for adaptive positioning</p></li>
</ul>
</li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Blob&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="c1">// Stego image blob with hidden message</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Example:</strong></p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">const</span><span class="w"> </span><span class="nx">stego</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">SteganographyEngine</span><span class="p">();</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">coverImage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">loadImage</span><span class="p">(</span><span class="s1">&#39;cover.png&#39;</span><span class="p">);</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">stegoImage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">stego</span><span class="p">.</span><span class="nx">hideMessage</span><span class="p">(</span><span class="nx">coverImage</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;Secret message&#39;</span><span class="p">);</span>
</pre></div>
</div>
</section>
<section id="revealmessage">
<h4><a class="toc-backref" href="#id17" role="doc-backlink">revealMessage()</a><a class="headerlink" href="#revealmessage" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">revealMessage</span><span class="p">(</span><span class="nx">stegoImage</span><span class="p">,</span><span class="w"> </span><span class="nx">options</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{})</span>
</pre></div>
</div>
<p>Extract a hidden message from a steganographic image.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">stegoImage</span></code> (Blob|File|ImageData): Steganographic image containing hidden message</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">options</span></code> (Object): Extraction options</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">password</span></code> (string): Password for decryption if used during hiding</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">sessionKey</span></code> (Uint8Array): Session key for adaptive positioning</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">maxLength</span></code> (number): Maximum expected message length</p></li>
</ul>
</li>
</ul>
<p><strong>Returns:</strong> Promise&lt;string|Uint8Array|null&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="c1">// Returns hidden message or null if none found</span>
<span class="s2">&quot;Secret message&quot;</span>
</pre></div>
</div>
</section>
<section id="detectmessage">
<h4><a class="toc-backref" href="#id18" role="doc-backlink">detectMessage()</a><a class="headerlink" href="#detectmessage" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">detectMessage</span><span class="p">(</span><span class="nx">image</span><span class="p">)</span>
</pre></div>
</div>
<p>Detect if an image contains a hidden message.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">image</span></code> (Blob|File|ImageData): Image to analyze</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">hasMessage</span><span class="o">:</span><span class="w"> </span><span class="kr">boolean</span><span class="p">,</span><span class="w">        </span><span class="c1">// True if message detected</span>
<span class="w">  </span><span class="nx">confidence</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span><span class="w">         </span><span class="c1">// Confidence level 0-1</span>
<span class="w">  </span><span class="nx">estimatedLength</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span><span class="w">    </span><span class="c1">// Estimated message length</span>
<span class="w">  </span><span class="nx">method</span><span class="o">:</span><span class="w"> </span><span class="nx">string</span><span class="w">             </span><span class="c1">// Detected steganographic method</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="analyzecapacity">
<h4><a class="toc-backref" href="#id19" role="doc-backlink">analyzeCapacity()</a><a class="headerlink" href="#analyzecapacity" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">analyzeCapacity</span><span class="p">(</span><span class="nx">image</span><span class="p">)</span>
</pre></div>
</div>
<p>Analyze the steganographic capacity of an image.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">image</span></code> (Blob|File|ImageData): Image to analyze</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">totalPixels</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span><span class="w">        </span><span class="c1">// Total pixels in image</span>
<span class="w">  </span><span class="nx">usablePixels</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span><span class="w">       </span><span class="c1">// Pixels suitable for embedding</span>
<span class="w">  </span><span class="nx">maxCapacity</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span><span class="w">        </span><span class="c1">// Maximum bytes that can be hidden</span>
<span class="w">  </span><span class="nx">recommendedCapacity</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span><span class="w"> </span><span class="c1">// Recommended capacity for security</span>
<span class="w">  </span><span class="nx">qualityScore</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="w">        </span><span class="c1">// Image quality score 0-1</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
</section>
<section id="otrsteganographysession">
<h2><a class="toc-backref" href="#id20" role="doc-backlink">OTRSteganographySession</a><a class="headerlink" href="#otrsteganographysession" title="Link to this heading">¶</a></h2>
<p>OTR session with integrated steganography capabilities.</p>
<section id="id1">
<h3><a class="toc-backref" href="#id21" role="doc-backlink">Constructor</a><a class="headerlink" href="#id1" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="ow">new</span><span class="w"> </span><span class="nx">OTRSteganographySession</span><span class="p">(</span><span class="nx">options</span><span class="p">)</span>
</pre></div>
</div>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">options</span></code> (Object): Session configuration</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">steganography</span></code> (Object): Steganography configuration</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">autoSelectCover</span></code> (boolean): Automatically select cover images (default: true)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">multiImageSupport</span></code> (boolean): Enable multi-image distribution (default: true)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">coverImageDatabase</span></code> (string): Path to cover image database</p></li>
</ul>
</li>
</ul>
</section>
<section id="id2">
<h3><a class="toc-backref" href="#id22" role="doc-backlink">Methods</a><a class="headerlink" href="#id2" title="Link to this heading">¶</a></h3>
<section id="sendstegomessage">
<h4><a class="toc-backref" href="#id23" role="doc-backlink">sendStegoMessage()</a><a class="headerlink" href="#sendstegomessage" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">sendStegoMessage</span><span class="p">(</span><span class="nx">plaintext</span><span class="p">,</span><span class="w"> </span><span class="nx">coverImage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">null</span><span class="p">,</span><span class="w"> </span><span class="nx">options</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{})</span>
</pre></div>
</div>
<p>Send an encrypted message hidden in an image.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">plaintext</span></code> (string): Message to send</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">coverImage</span></code> (Blob|File): Optional cover image (auto-selected if null)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">options</span></code> (Object): Sending options</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Blob|Array&lt;Blob&gt;&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="c1">// Returns stego image(s) ready for transmission</span>
</pre></div>
</div>
<p><a href="#id3"><span class="problematic" id="id4">``</span></a><a href="#id5"><span class="problematic" id="id6">`</span></a></p>
</section>
<section id="processstegoimage">
<h4><a class="toc-backref" href="#id24" role="doc-backlink">processStegoImage()</a><a class="headerlink" href="#processstegoimage" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">processStegoImage</span><span class="p">(</span><span class="nx">stegoImage</span><span class="p">)</span>
</pre></div>
</div>
<p>Process an incoming steganographic image and extract any hidden OTR message.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">stegoImage</span></code> (Blob|File): Steganographic image to process</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;string|null&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="c1">// Returns decrypted message or null if none found</span>
<span class="s2">&quot;Decrypted message content&quot;</span>
</pre></div>
</div>
</section>
</section>
</section>
<section id="coverimagemanager">
<h2><a class="toc-backref" href="#id25" role="doc-backlink">CoverImageManager</a><a class="headerlink" href="#coverimagemanager" title="Link to this heading">¶</a></h2>
<p>Manages cover image selection and generation.</p>
<section id="id7">
<h3><a class="toc-backref" href="#id26" role="doc-backlink">Constructor</a><a class="headerlink" href="#id7" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="ow">new</span><span class="w"> </span><span class="nx">CoverImageManager</span><span class="p">(</span><span class="nx">options</span><span class="p">)</span>
</pre></div>
</div>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">options</span></code> (Object): Manager configuration</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">databasePath</span></code> (string): Path to cover image database</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">generatorEnabled</span></code> (boolean): Enable synthetic cover generation (default: true)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">analysisEnabled</span></code> (boolean): Enable cover image analysis (default: true)</p></li>
</ul>
</li>
</ul>
</section>
<section id="id8">
<h3><a class="toc-backref" href="#id27" role="doc-backlink">Methods</a><a class="headerlink" href="#id8" title="Link to this heading">¶</a></h3>
<section id="selectoptimalcover">
<h4><a class="toc-backref" href="#id28" role="doc-backlink">selectOptimalCover()</a><a class="headerlink" href="#selectoptimalcover" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">selectOptimalCover</span><span class="p">(</span><span class="nx">messageSize</span><span class="p">,</span><span class="w"> </span><span class="nx">requirements</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{})</span>
</pre></div>
</div>
<p>Select the optimal cover image for a given message.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">messageSize</span></code> (number): Size of message to hide in bytes</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">requirements</span></code> (Object): Cover image requirements</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">minDimensions</span></code> (Object): Minimum width/height</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">maxFileSize</span></code> (number): Maximum file size in bytes</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">format</span></code> (string): Required format (‘PNG’, ‘BMP’)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">style</span></code> (string): Image style preference</p></li>
</ul>
</li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Blob&gt;</p>
</section>
<section id="analyzecover">
<h4><a class="toc-backref" href="#id29" role="doc-backlink">analyzeCover()</a><a class="headerlink" href="#analyzecover" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">analyzeCover</span><span class="p">(</span><span class="nx">image</span><span class="p">)</span>
</pre></div>
</div>
<p>Analyze a cover image for steganographic suitability.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">image</span></code> (Blob|File): Cover image to analyze</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">capacity</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span><span class="w">           </span><span class="c1">// Steganographic capacity in bytes</span>
<span class="w">  </span><span class="nx">complexity</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span><span class="w">         </span><span class="c1">// Image complexity score 0-1</span>
<span class="w">  </span><span class="nx">noiseLevel</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span><span class="w">         </span><span class="c1">// Natural noise level 0-1</span>
<span class="w">  </span><span class="nx">suitabilityScore</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span><span class="w">   </span><span class="c1">// Overall suitability 0-1</span>
<span class="w">  </span><span class="nx">recommendations</span><span class="o">:</span><span class="w"> </span><span class="nb">Array</span><span class="w">      </span><span class="c1">// Improvement recommendations</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="generatecover">
<h4><a class="toc-backref" href="#id30" role="doc-backlink">generateCover()</a><a class="headerlink" href="#generatecover" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">generateCover</span><span class="p">(</span><span class="nx">specifications</span><span class="p">)</span>
</pre></div>
</div>
<p>Generate a synthetic cover image.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">specifications</span></code> (Object): Generation specifications</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">width</span></code> (number): Image width</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">height</span></code> (number): Image height</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">style</span></code> (string): Generation style (‘natural’, ‘abstract’, ‘texture’)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">complexity</span></code> (number): Desired complexity level 0-1</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">format</span></code> (string): Output format</p></li>
</ul>
</li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Blob&gt;</p>
</section>
</section>
</section>
<section id="statisticalsecurity">
<h2><a class="toc-backref" href="#id31" role="doc-backlink">StatisticalSecurity</a><a class="headerlink" href="#statisticalsecurity" title="Link to this heading">¶</a></h2>
<p>Provides statistical security measures for steganography.</p>
<section id="id9">
<h3><a class="toc-backref" href="#id32" role="doc-backlink">Constructor</a><a class="headerlink" href="#id9" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="ow">new</span><span class="w"> </span><span class="nx">StatisticalSecurity</span><span class="p">(</span><span class="nx">sessionKey</span><span class="p">)</span>
</pre></div>
</div>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">sessionKey</span></code> (Uint8Array): Session key for deterministic randomness</p></li>
</ul>
</section>
<section id="id10">
<h3><a class="toc-backref" href="#id33" role="doc-backlink">Methods</a><a class="headerlink" href="#id10" title="Link to this heading">¶</a></h3>
<section id="enhancesecuritybeforeembedding">
<h4><a class="toc-backref" href="#id34" role="doc-backlink">enhanceSecurityBeforeEmbedding()</a><a class="headerlink" href="#enhancesecuritybeforeembedding" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">enhanceSecurityBeforeEmbedding</span><span class="p">(</span><span class="nx">imageData</span><span class="p">,</span><span class="w"> </span><span class="nx">messageData</span><span class="p">)</span>
</pre></div>
</div>
<p>Apply security enhancements before message embedding.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">imageData</span></code> (ImageData): Cover image data</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">messageData</span></code> (Uint8Array): Message data to hide</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">imageData</span><span class="o">:</span><span class="w"> </span><span class="nx">ImageData</span><span class="p">,</span><span class="w">       </span><span class="c1">// Enhanced image data</span>
<span class="w">  </span><span class="nx">messageData</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="p">,</span><span class="w">    </span><span class="c1">// Processed message data</span>
<span class="w">  </span><span class="nx">parameters</span><span class="o">:</span><span class="w"> </span><span class="nb">Object</span><span class="w">          </span><span class="c1">// Embedding parameters</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="injectstatisticalnoise">
<h4><a class="toc-backref" href="#id35" role="doc-backlink">injectStatisticalNoise()</a><a class="headerlink" href="#injectstatisticalnoise" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">injectStatisticalNoise</span><span class="p">(</span><span class="nx">imageData</span><span class="p">,</span><span class="w"> </span><span class="nx">intensity</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.1</span><span class="p">)</span>
</pre></div>
</div>
<p>Inject statistical noise to mask steganographic signatures.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">imageData</span></code> (ImageData): Image data to modify</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">intensity</span></code> (number): Noise intensity 0-1</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;void&gt;</p>
</section>
<section id="generateembeddingpositions">
<h4><a class="toc-backref" href="#id36" role="doc-backlink">generateEmbeddingPositions()</a><a class="headerlink" href="#generateembeddingpositions" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="nx">generateEmbeddingPositions</span><span class="p">(</span><span class="nx">imageData</span><span class="p">,</span><span class="w"> </span><span class="nx">bitCount</span><span class="p">)</span>
</pre></div>
</div>
<p>Generate adaptive embedding positions based on image characteristics.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">imageData</span></code> (ImageData): Image data for analysis</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">bitCount</span></code> (number): Number of bit positions needed</p></li>
</ul>
<p><strong>Returns:</strong> Array&lt;number&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="c1">// Array of pixel positions for embedding</span>
<span class="p">[</span><span class="mf">1234</span><span class="p">,</span><span class="w"> </span><span class="mf">5678</span><span class="p">,</span><span class="w"> </span><span class="mf">9012</span><span class="p">,</span><span class="w"> </span><span class="p">...]</span>
</pre></div>
</div>
</section>
</section>
</section>
<section id="platformintegration">
<h2><a class="toc-backref" href="#id37" role="doc-backlink">PlatformIntegration</a><a class="headerlink" href="#platformintegration" title="Link to this heading">¶</a></h2>
<p>Handles integration with various platforms and services.</p>
<section id="id11">
<h3><a class="toc-backref" href="#id38" role="doc-backlink">Constructor</a><a class="headerlink" href="#id11" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="ow">new</span><span class="w"> </span><span class="nx">PlatformIntegration</span><span class="p">(</span><span class="nx">options</span><span class="p">)</span>
</pre></div>
</div>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">options</span></code> (Object): Integration configuration</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">enabledPlatforms</span></code> (Array): List of enabled platforms</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">autoDetection</span></code> (boolean): Enable automatic platform detection</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">uploadInterception</span></code> (boolean): Intercept image uploads</p></li>
</ul>
</li>
</ul>
</section>
<section id="id12">
<h3><a class="toc-backref" href="#id39" role="doc-backlink">Methods</a><a class="headerlink" href="#id12" title="Link to this heading">¶</a></h3>
<section id="detectplatform">
<h4><a class="toc-backref" href="#id40" role="doc-backlink">detectPlatform()</a><a class="headerlink" href="#detectplatform" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="nx">detectPlatform</span><span class="p">()</span>
</pre></div>
</div>
<p>Detect the current platform/website.</p>
<p><strong>Returns:</strong> string</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="c1">// Platform identifier</span>
<span class="s2">&quot;facebook.com&quot;</span>
</pre></div>
</div>
</section>
<section id="interceptupload">
<h4><a class="toc-backref" href="#id41" role="doc-backlink">interceptUpload()</a><a class="headerlink" href="#interceptupload" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">interceptUpload</span><span class="p">(</span><span class="nx">fileInput</span><span class="p">,</span><span class="w"> </span><span class="nx">platform</span><span class="p">)</span>
</pre></div>
</div>
<p>Intercept image upload and optionally apply steganography.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">fileInput</span></code> (HTMLInputElement): File input element</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">platform</span></code> (string): Platform identifier</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Array&lt;File&gt;&gt;</p>
</section>
<section id="processdownloadedimages">
<h4><a class="toc-backref" href="#id42" role="doc-backlink">processDownloadedImages()</a><a class="headerlink" href="#processdownloadedimages" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">processDownloadedImages</span><span class="p">()</span>
</pre></div>
</div>
<p>Process images on the current page for hidden messages.</p>
<p><strong>Returns:</strong> Promise&lt;Array&lt;Object&gt;&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">[</span>
<span class="w">  </span><span class="p">{</span>
<span class="w">    </span><span class="nx">element</span><span class="o">:</span><span class="w"> </span><span class="nx">HTMLImageElement</span><span class="p">,</span><span class="w">  </span><span class="c1">// Image element</span>
<span class="w">    </span><span class="nx">hasMessage</span><span class="o">:</span><span class="w"> </span><span class="kr">boolean</span><span class="p">,</span><span class="w">        </span><span class="c1">// Whether message was found</span>
<span class="w">    </span><span class="nx">message</span><span class="o">:</span><span class="w"> </span><span class="nx">string</span><span class="w">            </span><span class="c1">// Extracted message (if any)</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">]</span>
</pre></div>
</div>
</section>
</section>
</section>
<section id="error-classes">
<h2><a class="toc-backref" href="#id43" role="doc-backlink">Error Classes</a><a class="headerlink" href="#error-classes" title="Link to this heading">¶</a></h2>
<section id="steganographyerror">
<h3><a class="toc-backref" href="#id44" role="doc-backlink">SteganographyError</a><a class="headerlink" href="#steganographyerror" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">class</span><span class="w"> </span><span class="nx">SteganographyError</span><span class="w"> </span><span class="k">extends</span><span class="w"> </span><span class="ne">Error</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="nx">code</span><span class="p">,</span><span class="w"> </span><span class="nx">details</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{})</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Properties:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">message</span></code> (string): Error message</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">code</span></code> (string): Error code</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">details</span></code> (Object): Additional error details</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">timestamp</span></code> (number): Error timestamp</p></li>
</ul>
<p><strong>Error Codes:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">INVALID_IMAGE</span></code>: Invalid or unsupported image format</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">INSUFFICIENT_CAPACITY</span></code>: Image too small for message</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">MESSAGE_TOO_LARGE</span></code>: Message exceeds capacity limits</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">EXTRACTION_FAILED</span></code>: Failed to extract hidden message</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">INVALID_FORMAT</span></code>: Unsupported image format</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">SECURITY_VIOLATION</span></code>: Security constraint violation</p></li>
</ul>
</section>
</section>
<section id="constants">
<h2><a class="toc-backref" href="#id45" role="doc-backlink">Constants</a><a class="headerlink" href="#constants" title="Link to this heading">¶</a></h2>
<section id="steganographic-methods">
<h3><a class="toc-backref" href="#id46" role="doc-backlink">Steganographic Methods</a><a class="headerlink" href="#steganographic-methods" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">const</span><span class="w"> </span><span class="nx">STEGO_METHODS</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">LSB_ALPHA</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;LSB_ALPHA&#39;</span><span class="p">,</span><span class="w">           </span><span class="c1">// LSB in alpha channel</span>
<span class="w">  </span><span class="nx">LSB_RGB</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;LSB_RGB&#39;</span><span class="p">,</span><span class="w">               </span><span class="c1">// LSB in RGB channels</span>
<span class="w">  </span><span class="nx">ADAPTIVE_LSB</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;ADAPTIVE_LSB&#39;</span><span class="p">,</span><span class="w">     </span><span class="c1">// Adaptive LSB positioning</span>
<span class="w">  </span><span class="nx">DCT</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;DCT&#39;</span><span class="p">,</span><span class="w">                       </span><span class="c1">// DCT domain hiding</span>
<span class="w">  </span><span class="nx">SPREAD_SPECTRUM</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;SPREAD_SPECTRUM&#39;</span><span class="w"> </span><span class="c1">// Spread spectrum method</span>
<span class="p">};</span>
</pre></div>
</div>
</section>
<section id="image-formats">
<h3><a class="toc-backref" href="#id47" role="doc-backlink">Image Formats</a><a class="headerlink" href="#image-formats" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">const</span><span class="w"> </span><span class="nx">SUPPORTED_FORMATS</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">PNG</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;image/png&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">BMP</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;image/bmp&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">TIFF</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;image/tiff&#39;</span>
<span class="p">};</span>
</pre></div>
</div>
</section>
<section id="quality-metrics">
<h3><a class="toc-backref" href="#id48" role="doc-backlink">Quality Metrics</a><a class="headerlink" href="#quality-metrics" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">const</span><span class="w"> </span><span class="nx">QUALITY_THRESHOLDS</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">PSNR_MIN</span><span class="o">:</span><span class="w"> </span><span class="mf">40</span><span class="p">,</span><span class="w">                     </span><span class="c1">// Minimum PSNR (dB)</span>
<span class="w">  </span><span class="nx">SSIM_MIN</span><span class="o">:</span><span class="w"> </span><span class="mf">0.95</span><span class="p">,</span><span class="w">                   </span><span class="c1">// Minimum SSIM</span>
<span class="w">  </span><span class="nx">FILE_SIZE_INCREASE_MAX</span><span class="o">:</span><span class="w"> </span><span class="mf">0.05</span><span class="w">      </span><span class="c1">// Maximum 5% size increase</span>
<span class="p">};</span>
</pre></div>
</div>
</section>
</section>
<section id="type-definitions">
<h2><a class="toc-backref" href="#id49" role="doc-backlink">Type Definitions</a><a class="headerlink" href="#type-definitions" title="Link to this heading">¶</a></h2>
<section id="stegomessage">
<h3><a class="toc-backref" href="#id50" role="doc-backlink">StegoMessage</a><a class="headerlink" href="#stegomessage" title="Link to this heading">¶</a></h3>
<div class="highlight-typescript notranslate"><div class="highlight"><pre><span></span><span class="kd">interface</span><span class="w"> </span><span class="nx">StegoMessage</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">header</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">magic</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span><span class="w">                  </span><span class="c1">// Magic number for identification</span>
<span class="w">    </span><span class="nx">version</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span><span class="w">                </span><span class="c1">// Format version</span>
<span class="w">    </span><span class="nx">length</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span><span class="w">                 </span><span class="c1">// Message length</span>
<span class="w">    </span><span class="nx">checksum</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span><span class="w">               </span><span class="c1">// Message checksum</span>
<span class="w">    </span><span class="nx">flags</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span><span class="w">                  </span><span class="c1">// Feature flags</span>
<span class="w">  </span><span class="p">};</span>
<span class="w">  </span><span class="nx">payload</span><span class="o">:</span><span class="w"> </span><span class="kt">Uint8Array</span><span class="p">;</span><span class="w">              </span><span class="c1">// Actual message data</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="coveranalysis">
<h3><a class="toc-backref" href="#id51" role="doc-backlink">CoverAnalysis</a><a class="headerlink" href="#coveranalysis" title="Link to this heading">¶</a></h3>
<div class="highlight-typescript notranslate"><div class="highlight"><pre><span></span><span class="kd">interface</span><span class="w"> </span><span class="nx">CoverAnalysis</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">capacity</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span><span class="w">                 </span><span class="c1">// Steganographic capacity</span>
<span class="w">  </span><span class="nx">complexity</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span><span class="w">               </span><span class="c1">// Image complexity score</span>
<span class="w">  </span><span class="nx">noiseLevel</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span><span class="w">               </span><span class="c1">// Natural noise level</span>
<span class="w">  </span><span class="nx">suitabilityScore</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span><span class="w">         </span><span class="c1">// Overall suitability</span>
<span class="w">  </span><span class="nx">recommendations</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">[];</span><span class="w">        </span><span class="c1">// Improvement suggestions</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="embeddingparameters">
<h3><a class="toc-backref" href="#id52" role="doc-backlink">EmbeddingParameters</a><a class="headerlink" href="#embeddingparameters" title="Link to this heading">¶</a></h3>
<div class="highlight-typescript notranslate"><div class="highlight"><pre><span></span><span class="kd">interface</span><span class="w"> </span><span class="nx">EmbeddingParameters</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">method</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span><span class="w">                   </span><span class="c1">// Steganographic method</span>
<span class="w">  </span><span class="nx">positions</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">[];</span><span class="w">              </span><span class="c1">// Embedding positions</span>
<span class="w">  </span><span class="nx">noiseIntensity</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span><span class="w">           </span><span class="c1">// Statistical noise level</span>
<span class="w">  </span><span class="nx">adaptiveSettings</span><span class="o">:</span><span class="w"> </span><span class="kt">Object</span><span class="p">;</span><span class="w">         </span><span class="c1">// Adaptive algorithm settings</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="usage-examples">
<h2><a class="toc-backref" href="#id53" role="doc-backlink">Usage Examples</a><a class="headerlink" href="#usage-examples" title="Link to this heading">¶</a></h2>
<section id="basic-steganography">
<h3><a class="toc-backref" href="#id54" role="doc-backlink">Basic Steganography</a><a class="headerlink" href="#basic-steganography" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="c1">// Initialize steganography engine</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">stego</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">SteganographyEngine</span><span class="p">({</span>
<span class="w">  </span><span class="nx">method</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;LSB_ALPHA&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">noiseInjection</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span>
<span class="p">});</span>

<span class="c1">// Hide message</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">coverImage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">loadImage</span><span class="p">(</span><span class="s1">&#39;cover.png&#39;</span><span class="p">);</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">stegoImage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">stego</span><span class="p">.</span><span class="nx">hideMessage</span><span class="p">(</span><span class="nx">coverImage</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;Secret message&#39;</span><span class="p">);</span>

<span class="c1">// Reveal message</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">hiddenMessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">stego</span><span class="p">.</span><span class="nx">revealMessage</span><span class="p">(</span><span class="nx">stegoImage</span><span class="p">);</span>
<span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;Hidden message:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">hiddenMessage</span><span class="p">);</span>
</pre></div>
</div>
</section>
<section id="otr-integration">
<h3><a class="toc-backref" href="#id55" role="doc-backlink">OTR Integration</a><a class="headerlink" href="#otr-integration" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="c1">// Create OTR session with steganography</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">session</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">OTRSteganographySession</span><span class="p">({</span>
<span class="w">  </span><span class="nx">steganography</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">autoSelectCover</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nx">multiImageSupport</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">});</span>

<span class="c1">// Send encrypted message via steganography</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">stegoImage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">session</span><span class="p">.</span><span class="nx">sendStegoMessage</span><span class="p">(</span><span class="s1">&#39;Hello, world!&#39;</span><span class="p">);</span>

<span class="c1">// Process incoming stego image</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">decryptedMessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">session</span><span class="p">.</span><span class="nx">processStegoImage</span><span class="p">(</span><span class="nx">stegoImage</span><span class="p">);</span>
</pre></div>
</div>
</section>
<section id="platform-integration">
<h3><a class="toc-backref" href="#id56" role="doc-backlink">Platform Integration</a><a class="headerlink" href="#platform-integration" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="c1">// Set up platform integration</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">platform</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">PlatformIntegration</span><span class="p">({</span>
<span class="w">  </span><span class="nx">enabledPlatforms</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;facebook.com&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;instagram.com&#39;</span><span class="p">],</span>
<span class="w">  </span><span class="nx">autoDetection</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span>
<span class="p">});</span>

<span class="c1">// Process images on current page</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">results</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">platform</span><span class="p">.</span><span class="nx">processDownloadedImages</span><span class="p">();</span>
<span class="nx">results</span><span class="p">.</span><span class="nx">forEach</span><span class="p">(</span><span class="nx">result</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">result</span><span class="p">.</span><span class="nx">hasMessage</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;Found hidden message:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">result</span><span class="p">.</span><span class="nx">message</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">});</span>
</pre></div>
</div>
<p>This API reference provides complete documentation for all steganography system components, methods, and data structures.</p>
</section>
</section>
</section>

        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          <a class="next-page" href="../reference/faq.html">
              <div class="page-info">
                <div class="context">
                  <span>Next</span>
                </div>
                <div class="title">Frequently Asked Questions</div>
              </div>
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
            </a>
          <a class="prev-page" href="steganography-implementation.html">
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
              <div class="page-info">
                <div class="context">
                  <span>Previous</span>
                </div>
                
                <div class="title">Steganography Implementation Guide</div>
                
              </div>
            </a>
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 1980, WebOTR Team
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer">
      
      
      <div class="toc-sticky toc-scroll">
        <div class="toc-title-container">
          <span class="toc-title">
            On this page
          </span>
        </div>
        <div class="toc-tree-container">
          <div class="toc-tree">
            <ul>
<li><a class="reference internal" href="#">Steganography API Reference</a><ul>
<li><a class="reference internal" href="#steganographyengine">SteganographyEngine</a><ul>
<li><a class="reference internal" href="#constructor">Constructor</a></li>
<li><a class="reference internal" href="#methods">Methods</a><ul>
<li><a class="reference internal" href="#hidemessage">hideMessage()</a></li>
<li><a class="reference internal" href="#revealmessage">revealMessage()</a></li>
<li><a class="reference internal" href="#detectmessage">detectMessage()</a></li>
<li><a class="reference internal" href="#analyzecapacity">analyzeCapacity()</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#otrsteganographysession">OTRSteganographySession</a><ul>
<li><a class="reference internal" href="#id1">Constructor</a></li>
<li><a class="reference internal" href="#id2">Methods</a><ul>
<li><a class="reference internal" href="#sendstegomessage">sendStegoMessage()</a></li>
<li><a class="reference internal" href="#processstegoimage">processStegoImage()</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#coverimagemanager">CoverImageManager</a><ul>
<li><a class="reference internal" href="#id7">Constructor</a></li>
<li><a class="reference internal" href="#id8">Methods</a><ul>
<li><a class="reference internal" href="#selectoptimalcover">selectOptimalCover()</a></li>
<li><a class="reference internal" href="#analyzecover">analyzeCover()</a></li>
<li><a class="reference internal" href="#generatecover">generateCover()</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#statisticalsecurity">StatisticalSecurity</a><ul>
<li><a class="reference internal" href="#id9">Constructor</a></li>
<li><a class="reference internal" href="#id10">Methods</a><ul>
<li><a class="reference internal" href="#enhancesecuritybeforeembedding">enhanceSecurityBeforeEmbedding()</a></li>
<li><a class="reference internal" href="#injectstatisticalnoise">injectStatisticalNoise()</a></li>
<li><a class="reference internal" href="#generateembeddingpositions">generateEmbeddingPositions()</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#platformintegration">PlatformIntegration</a><ul>
<li><a class="reference internal" href="#id11">Constructor</a></li>
<li><a class="reference internal" href="#id12">Methods</a><ul>
<li><a class="reference internal" href="#detectplatform">detectPlatform()</a></li>
<li><a class="reference internal" href="#interceptupload">interceptUpload()</a></li>
<li><a class="reference internal" href="#processdownloadedimages">processDownloadedImages()</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#error-classes">Error Classes</a><ul>
<li><a class="reference internal" href="#steganographyerror">SteganographyError</a></li>
</ul>
</li>
<li><a class="reference internal" href="#constants">Constants</a><ul>
<li><a class="reference internal" href="#steganographic-methods">Steganographic Methods</a></li>
<li><a class="reference internal" href="#image-formats">Image Formats</a></li>
<li><a class="reference internal" href="#quality-metrics">Quality Metrics</a></li>
</ul>
</li>
<li><a class="reference internal" href="#type-definitions">Type Definitions</a><ul>
<li><a class="reference internal" href="#stegomessage">StegoMessage</a></li>
<li><a class="reference internal" href="#coveranalysis">CoverAnalysis</a></li>
<li><a class="reference internal" href="#embeddingparameters">EmbeddingParameters</a></li>
</ul>
</li>
<li><a class="reference internal" href="#usage-examples">Usage Examples</a><ul>
<li><a class="reference internal" href="#basic-steganography">Basic Steganography</a></li>
<li><a class="reference internal" href="#otr-integration">OTR Integration</a></li>
<li><a class="reference internal" href="#platform-integration">Platform Integration</a></li>
</ul>
</li>
</ul>
</li>
</ul>

          </div>
        </div>
      </div>
      
      
    </aside>
  </div>
</div><script src="../_static/documentation_options.js?v=01f34227"></script>
    <script src="../_static/doctools.js?v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/scripts/furo.js?v=5fa4622c"></script>
    <script src="../_static/mermaid-renderer.js?v=b683124c"></script>
    </body>
</html>