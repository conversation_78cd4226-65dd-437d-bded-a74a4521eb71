<!doctype html>
<html class="no-js" lang="en" data-content_root="../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><meta name="viewport" content="width=device-width, initial-scale=1" />
<link rel="index" title="Index" href="../genindex.html" /><link rel="search" title="Search" href="../search.html" /><link rel="next" title="Steganography Architecture" href="steganography-architecture.html" /><link rel="prev" title="Steganography Documentation Summary" href="steganography-summary.html" />

    <link rel="shortcut icon" href="../_static/favicon.svg"/><!-- Generated with Sphinx 7.4.7 and Furo 2024.08.06 -->
        <title>Steganography Implementation - WebOTR Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../_static/styles/furo.css?v=354aac6f" />
    <link rel="stylesheet" type="text/css" href="../_static/styles/furo-extensions.css?v=302659d7" />
    <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=21a7f706" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  --color-brand-primary: #6366f1;
  --color-brand-content: #6366f1;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8b5cf6;
  --color-brand-content: #8b5cf6;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8b5cf6;
  --color-brand-content: #8b5cf6;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-with-moon" viewBox="0 0 24 24">
    <title>Auto light/dark, in light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
      class="icon-custom-derived-from-feather-sun-and-tabler-moon">
      <path style="opacity: 50%" d="M 5.411 14.504 C 5.471 14.504 5.532 14.504 5.591 14.504 C 3.639 16.319 4.383 19.569 6.931 20.352 C 7.693 20.586 8.512 20.551 9.25 20.252 C 8.023 23.207 4.056 23.725 2.11 21.184 C 0.166 18.642 1.702 14.949 4.874 14.536 C 5.051 14.512 5.231 14.5 5.411 14.5 L 5.411 14.504 Z"/>
      <line x1="14.5" y1="3.25" x2="14.5" y2="1.25"/>
      <line x1="14.5" y1="15.85" x2="14.5" y2="17.85"/>
      <line x1="10.044" y1="5.094" x2="8.63" y2="3.68"/>
      <line x1="19" y1="14.05" x2="20.414" y2="15.464"/>
      <line x1="8.2" y1="9.55" x2="6.2" y2="9.55"/>
      <line x1="20.8" y1="9.55" x2="22.8" y2="9.55"/>
      <line x1="10.044" y1="14.006" x2="8.63" y2="15.42"/>
      <line x1="19" y1="5.05" x2="20.414" y2="3.636"/>
      <circle cx="14.5" cy="9.55" r="3.6"/>
    </svg>
  </symbol>
  <symbol id="svg-moon-with-sun" viewBox="0 0 24 24">
    <title>Auto light/dark, in dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
      class="icon-custom-derived-from-feather-sun-and-tabler-moon">
      <path d="M 8.282 7.007 C 8.385 7.007 8.494 7.007 8.595 7.007 C 5.18 10.184 6.481 15.869 10.942 17.24 C 12.275 17.648 13.706 17.589 15 17.066 C 12.851 22.236 5.91 23.143 2.505 18.696 C -0.897 14.249 1.791 7.786 7.342 7.063 C 7.652 7.021 7.965 7 8.282 7 L 8.282 7.007 Z"/>
      <line style="opacity: 50%" x1="18" y1="3.705" x2="18" y2="2.5"/>
      <line style="opacity: 50%" x1="18" y1="11.295" x2="18" y2="12.5"/>
      <line style="opacity: 50%" x1="15.316" y1="4.816" x2="14.464" y2="3.964"/>
      <line style="opacity: 50%" x1="20.711" y1="10.212" x2="21.563" y2="11.063"/>
      <line style="opacity: 50%" x1="14.205" y1="7.5" x2="13.001" y2="7.5"/>
      <line style="opacity: 50%" x1="21.795" y1="7.5" x2="23" y2="7.5"/>
      <line style="opacity: 50%" x1="15.316" y1="10.184" x2="14.464" y2="11.036"/>
      <line style="opacity: 50%" x1="20.711" y1="4.789" x2="21.563" y2="3.937"/>
      <circle style="opacity: 50%" cx="18" cy="7.5" r="2.169"/>
    </svg>
  </symbol>
  <symbol id="svg-pencil" viewBox="0 0 24 24">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-pencil-code">
      <path d="M4 20h4l10.5 -10.5a2.828 2.828 0 1 0 -4 -4l-10.5 10.5v4" />
      <path d="M13.5 6.5l4 4" />
      <path d="M20 21l2 -2l-2 -2" />
      <path d="M17 17l-2 2l2 2" />
    </svg>
  </symbol>
  <symbol id="svg-eye" viewBox="0 0 24 24">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-eye-code">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0" />
      <path
        d="M11.11 17.958c-3.209 -.307 -5.91 -2.293 -8.11 -5.958c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6c-.21 .352 -.427 .688 -.647 1.008" />
      <path d="M20 21l2 -2l-2 -2" />
      <path d="M17 17l-2 2l2 2" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>

<a class="skip-to-content muted-link" href="#furo-main-content">Skip to content</a>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../index.html"><div class="brand">WebOTR Documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto-light"><use href="#svg-sun-with-moon"></use></svg>
          <svg class="theme-icon-when-auto-dark"><use href="#svg-moon-with-sun"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><a class="sidebar-brand" href="../index.html">
  
  <div class="sidebar-logo-container">
    <img class="sidebar-logo" src="../_static/logo.svg" alt="Logo"/>
  </div>
  
  <span class="sidebar-brand-text">WebOTR Documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-scroll"><div class="sidebar-tree">
  <p class="caption" role="heading"><span class="caption-text">User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user-guide/installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../user-guide/getting-started.html">Getting Started</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../developer-guide/architecture.html">Architecture Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../developer-guide/contributing.html">Contributing to WebOTR</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Protocol Compliance &amp; Advanced Features</span></p>
<ul>
<li class="toctree-l1 has-children"><a class="reference internal" href="../protocol/index.html">Protocol Compliance &amp; Advanced Features</a><input class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of Protocol Compliance &amp; Advanced Features</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul>
<li class="toctree-l2"><a class="reference internal" href="../protocol/overview.html">Protocol Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/version-negotiation.html">Version Negotiation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/message-ordering.html">Message Ordering</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/enhanced-smp.html">Enhanced SMP</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/policy-manager.html">Policy Manager</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/integration-guide.html">Integration Guide</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/troubleshooting.html">Troubleshooting Guide</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Security</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="overview.html">Security Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements-summary.html">libOTR Security Enhancements Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements.html">libOTR Security Enhancements</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements-implementation.html">libOTR Enhancements Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements-api.html">libOTR Enhancements API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements-testing.html">libOTR Enhancements Testing and Validation</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy-summary.html">Forward Secrecy Documentation Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy.html">Forward Secrecy Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy-architecture.html">Forward Secrecy Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy-implementation.html">Forward Secrecy Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy-api.html">Forward Secrecy API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake-summary.html">AKE Documentation Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake.html">Authenticated Key Exchange (AKE)</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake-architecture.html">AKE Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake-implementation.html">AKE Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake-api.html">AKE API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography-summary.html">Steganography Documentation Summary</a></li>
<li class="toctree-l1 current current-page"><a class="current reference internal" href="#">Steganography Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography-architecture.html">Steganography Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography-implementation.html">Steganography Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography-api.html">Steganography API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../reference/faq.html">Frequently Asked Questions</a></li>
<li class="toctree-l1"><a class="reference internal" href="../reference/changelog.html">Changelog</a></li>
</ul>

</div>
</div>

      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          <div class="view-this-page">
  <a class="muted-link" href="https://github.com/forkrul/webOTteR/blob/main/docs/sphinx/security/steganography.rst?plain=true" title="View this page">
    <svg><use href="#svg-eye"></use></svg>
    <span class="visually-hidden">View this page</span>
  </a>
</div><div class="edit-this-page">
  <a class="muted-link" href="https://github.com/forkrul/webOTteR/edit/main/docs/sphinx/security/steganography.rst" title="Edit this page">
    <svg><use href="#svg-pencil"></use></svg>
    <span class="visually-hidden">Edit this page</span>
  </a>
</div><div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto-light"><use href="#svg-sun-with-moon"></use></svg>
              <svg class="theme-icon-when-auto-dark"><use href="#svg-moon-with-sun"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main" id="furo-main-content">
          <section id="steganography-implementation">
<h1>Steganography Implementation<a class="headerlink" href="#steganography-implementation" title="Link to this heading">¶</a></h1>
<p>WebOTR’s steganography system enables covert communication by hiding encrypted OTR messages inside innocent-looking images. This implementation provides true steganographic security while maintaining full OTR protocol guarantees.</p>
<nav class="contents local" id="table-of-contents">
<p class="topic-title">Table of Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#overview" id="id1">Overview</a></p></li>
<li><p><a class="reference internal" href="#core-architecture" id="id2">Core Architecture</a></p>
<ul>
<li><p><a class="reference internal" href="#system-components" id="id3">System Components</a></p></li>
<li><p><a class="reference internal" href="#steganographic-methods" id="id4">Steganographic Methods</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#implementation-details" id="id5">Implementation Details</a></p>
<ul>
<li><p><a class="reference internal" href="#core-steganography-engine" id="id6">Core Steganography Engine</a></p></li>
<li><p><a class="reference internal" href="#lsb-embedding-algorithm" id="id7">LSB Embedding Algorithm</a></p></li>
<li><p><a class="reference internal" href="#otr-integration" id="id8">OTR Integration</a></p></li>
<li><p><a class="reference internal" href="#message-format-specification" id="id9">Message Format Specification</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#security-features" id="id10">Security Features</a></p>
<ul>
<li><p><a class="reference internal" href="#statistical-security" id="id11">Statistical Security</a></p></li>
<li><p><a class="reference internal" href="#cover-image-management" id="id12">Cover Image Management</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#platform-integration" id="id13">Platform Integration</a></p>
<ul>
<li><p><a class="reference internal" href="#browser-extension-integration" id="id14">Browser Extension Integration</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#performance-optimization" id="id15">Performance Optimization</a></p>
<ul>
<li><p><a class="reference internal" href="#image-processing-optimization" id="id16">Image Processing Optimization</a></p></li>
</ul>
</li>
</ul>
</nav>
<section id="overview">
<h2><a class="toc-backref" href="#id1" role="doc-backlink">Overview</a><a class="headerlink" href="#overview" title="Link to this heading">¶</a></h2>
<p>The steganography system allows users to communicate securely through image sharing on social media platforms, email, and file sharing services. By embedding encrypted OTR messages within image data, communications appear as normal image sharing while providing military-grade security.</p>
</section>
<section id="core-architecture">
<h2><a class="toc-backref" href="#id2" role="doc-backlink">Core Architecture</a><a class="headerlink" href="#core-architecture" title="Link to this heading">¶</a></h2>
<section id="system-components">
<h3><a class="toc-backref" href="#id3" role="doc-backlink">System Components</a><a class="headerlink" href="#system-components" title="Link to this heading">¶</a></h3>
<dl class="simple">
<dt><strong>Steganography Engine</strong></dt><dd><p>Core LSB (Least Significant Bit) implementation for hiding data in image alpha channels.</p>
</dd>
<dt><strong>OTR Integration Layer</strong></dt><dd><p>Seamless integration with existing OTR protocol for encryption and authentication.</p>
</dd>
<dt><strong>Image Processing Pipeline</strong></dt><dd><p>Canvas-based image manipulation for embedding and extraction operations.</p>
</dd>
<dt><strong>Platform Integration</strong></dt><dd><p>Browser extension hooks for automatic detection and processing of images.</p>
</dd>
<dt><strong>Cover Image Management</strong></dt><dd><p>Intelligent selection and generation of suitable cover images.</p>
</dd>
</dl>
</section>
<section id="steganographic-methods">
<h3><a class="toc-backref" href="#id4" role="doc-backlink">Steganographic Methods</a><a class="headerlink" href="#steganographic-methods" title="Link to this heading">¶</a></h3>
<dl class="simple">
<dt><strong>LSB Alpha Channel Embedding</strong></dt><dd><p>Primary method using least significant bits of alpha channel pixels for maximum capacity and minimal visual impact.</p>
</dd>
<dt><strong>Adaptive LSB</strong></dt><dd><p>Dynamic bit selection based on image characteristics and OTR session keys for enhanced security.</p>
</dd>
<dt><strong>Multi-Image Distribution</strong></dt><dd><p>Large messages split across multiple images with redundancy and error correction.</p>
</dd>
<dt><strong>Noise Injection</strong></dt><dd><p>Statistical noise added to mask steganographic signatures and improve security.</p>
</dd>
</dl>
</section>
</section>
<section id="implementation-details">
<h2><a class="toc-backref" href="#id5" role="doc-backlink">Implementation Details</a><a class="headerlink" href="#implementation-details" title="Link to this heading">¶</a></h2>
<section id="core-steganography-engine">
<h3><a class="toc-backref" href="#id6" role="doc-backlink">Core Steganography Engine</a><a class="headerlink" href="#core-steganography-engine" title="Link to this heading">¶</a></h3>
<p>The steganography engine provides the fundamental hiding and revealing capabilities:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">class</span><span class="w"> </span><span class="nx">SteganographyEngine</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span><span class="nx">options</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{})</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">options</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">method</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;LSB_ALPHA&#39;</span><span class="p">,</span>
<span class="w">      </span><span class="nx">bitsPerPixel</span><span class="o">:</span><span class="w"> </span><span class="mf">1</span><span class="p">,</span>
<span class="w">      </span><span class="nx">compressionLevel</span><span class="o">:</span><span class="w"> </span><span class="mf">0</span><span class="p">,</span>
<span class="w">      </span><span class="nx">noiseInjection</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">      </span><span class="nx">adaptiveLSB</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">      </span><span class="p">...</span><span class="nx">options</span>
<span class="w">    </span><span class="p">};</span>

<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">canvas</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">document</span><span class="p">.</span><span class="nx">createElement</span><span class="p">(</span><span class="s1">&#39;canvas&#39;</span><span class="p">);</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">context</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">canvas</span><span class="p">.</span><span class="nx">getContext</span><span class="p">(</span><span class="s1">&#39;2d&#39;</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">hideMessage</span><span class="p">(</span><span class="nx">coverImage</span><span class="p">,</span><span class="w"> </span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="nx">password</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Load cover image</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">imageData</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">loadImage</span><span class="p">(</span><span class="nx">coverImage</span><span class="p">);</span>

<span class="w">    </span><span class="c1">// Prepare message for embedding</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">messageData</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">prepareMessage</span><span class="p">(</span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="nx">password</span><span class="p">);</span>

<span class="w">    </span><span class="c1">// Embed message using LSB</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">stegoImageData</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">embedLSB</span><span class="p">(</span><span class="nx">imageData</span><span class="p">,</span><span class="w"> </span><span class="nx">messageData</span><span class="p">);</span>

<span class="w">    </span><span class="c1">// Apply noise injection for security</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">options</span><span class="p">.</span><span class="nx">noiseInjection</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">injectNoise</span><span class="p">(</span><span class="nx">stegoImageData</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">imageDataToBlob</span><span class="p">(</span><span class="nx">stegoImageData</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">revealMessage</span><span class="p">(</span><span class="nx">stegoImage</span><span class="p">,</span><span class="w"> </span><span class="nx">password</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Load stego image</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">imageData</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">loadImage</span><span class="p">(</span><span class="nx">stegoImage</span><span class="p">);</span>

<span class="w">    </span><span class="c1">// Extract message using LSB</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">messageData</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">extractLSB</span><span class="p">(</span><span class="nx">imageData</span><span class="p">);</span>

<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="nx">messageData</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">return</span><span class="w"> </span><span class="kc">null</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="c1">// Decode and verify message</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">decodeMessage</span><span class="p">(</span><span class="nx">messageData</span><span class="p">,</span><span class="w"> </span><span class="nx">password</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="lsb-embedding-algorithm">
<h3><a class="toc-backref" href="#id7" role="doc-backlink">LSB Embedding Algorithm</a><a class="headerlink" href="#lsb-embedding-algorithm" title="Link to this heading">¶</a></h3>
<p>The LSB embedding algorithm modifies the least significant bits of pixel values:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">embedLSB</span><span class="p">(</span><span class="nx">imageData</span><span class="p">,</span><span class="w"> </span><span class="nx">messageData</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">pixels</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">imageData</span><span class="p">.</span><span class="nx">data</span><span class="p">;</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">messageBits</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">messageToBits</span><span class="p">(</span><span class="nx">messageData</span><span class="p">);</span>

<span class="w">  </span><span class="c1">// Add header with message length and checksum</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">header</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">createHeader</span><span class="p">(</span><span class="nx">messageData</span><span class="p">.</span><span class="nx">length</span><span class="p">);</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">fullMessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[...</span><span class="nx">header</span><span class="p">,</span><span class="w"> </span><span class="p">...</span><span class="nx">messageBits</span><span class="p">];</span>

<span class="w">  </span><span class="kd">let</span><span class="w"> </span><span class="nx">bitIndex</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0</span><span class="p">;</span>
<span class="w">  </span><span class="kd">let</span><span class="w"> </span><span class="nx">pixelIndex</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0</span><span class="p">;</span>

<span class="w">  </span><span class="c1">// Embed in alpha channel (every 4th byte)</span>
<span class="w">  </span><span class="k">while</span><span class="w"> </span><span class="p">(</span><span class="nx">bitIndex</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="nx">fullMessage</span><span class="p">.</span><span class="nx">length</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="nx">pixelIndex</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="nx">pixels</span><span class="p">.</span><span class="nx">length</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">alphaIndex</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">pixelIndex</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mf">4</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mf">3</span><span class="p">;</span><span class="w"> </span><span class="c1">// Alpha channel</span>

<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">alphaIndex</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="nx">pixels</span><span class="p">.</span><span class="nx">length</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="c1">// Modify LSB of alpha channel</span>
<span class="w">      </span><span class="nx">pixels</span><span class="p">[</span><span class="nx">alphaIndex</span><span class="p">]</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="nx">pixels</span><span class="p">[</span><span class="nx">alphaIndex</span><span class="p">]</span><span class="w"> </span><span class="o">&amp;</span><span class="w"> </span><span class="mh">0xFE</span><span class="p">)</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="nx">fullMessage</span><span class="p">[</span><span class="nx">bitIndex</span><span class="p">];</span>
<span class="w">      </span><span class="nx">bitIndex</span><span class="o">++</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nx">pixelIndex</span><span class="o">++</span><span class="p">;</span>

<span class="w">    </span><span class="c1">// Skip pixels based on adaptive algorithm</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">options</span><span class="p">.</span><span class="nx">adaptiveLSB</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">pixelIndex</span><span class="w"> </span><span class="o">+=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">getAdaptiveSkip</span><span class="p">(</span><span class="nx">pixelIndex</span><span class="p">,</span><span class="w"> </span><span class="nx">imageData</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">bitIndex</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="nx">fullMessage</span><span class="p">.</span><span class="nx">length</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">throw</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="ne">Error</span><span class="p">(</span><span class="s1">&#39;Image too small for message&#39;</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="nx">imageData</span><span class="p">;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="otr-integration">
<h3><a class="toc-backref" href="#id8" role="doc-backlink">OTR Integration</a><a class="headerlink" href="#otr-integration" title="Link to this heading">¶</a></h3>
<p>Integration with the OTR protocol maintains all security properties:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">class</span><span class="w"> </span><span class="nx">OTRSteganographySession</span><span class="w"> </span><span class="k">extends</span><span class="w"> </span><span class="nx">OtrSession</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span><span class="nx">options</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{})</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">super</span><span class="p">(</span><span class="nx">options</span><span class="p">);</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">stego</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">SteganographyEngine</span><span class="p">(</span><span class="nx">options</span><span class="p">.</span><span class="nx">steganography</span><span class="p">);</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">coverImageManager</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">CoverImageManager</span><span class="p">();</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">sendStegoMessage</span><span class="p">(</span><span class="nx">plaintext</span><span class="p">,</span><span class="w"> </span><span class="nx">coverImage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Encrypt message using OTR</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">encryptedMessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">encryptMessage</span><span class="p">(</span><span class="nx">plaintext</span><span class="p">);</span>

<span class="w">    </span><span class="c1">// Get or generate cover image</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">cover</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">coverImage</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">coverImageManager</span><span class="p">.</span><span class="nx">selectCover</span><span class="p">();</span>

<span class="w">    </span><span class="c1">// Hide encrypted message in image</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">stegoImage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">stego</span><span class="p">.</span><span class="nx">hideMessage</span><span class="p">(</span><span class="nx">cover</span><span class="p">,</span><span class="w"> </span><span class="nx">encryptedMessage</span><span class="p">);</span>

<span class="w">    </span><span class="c1">// Emit stego image for transmission</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">emit</span><span class="p">(</span><span class="s1">&#39;stegoImageReady&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">image</span><span class="o">:</span><span class="w"> </span><span class="nx">stegoImage</span><span class="p">,</span>
<span class="w">      </span><span class="nx">metadata</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="nb">Date</span><span class="p">.</span><span class="nx">now</span><span class="p">(),</span>
<span class="w">        </span><span class="nx">coverType</span><span class="o">:</span><span class="w"> </span><span class="nx">coverImage</span><span class="w"> </span><span class="o">?</span><span class="w"> </span><span class="s1">&#39;user&#39;</span><span class="w"> </span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;generated&#39;</span><span class="p">,</span>
<span class="w">        </span><span class="nx">messageLength</span><span class="o">:</span><span class="w"> </span><span class="nx">encryptedMessage</span><span class="p">.</span><span class="nx">length</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nx">stegoImage</span><span class="p">;</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">processStegoImage</span><span class="p">(</span><span class="nx">stegoImage</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="c1">// Extract encrypted message</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">encryptedMessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">stego</span><span class="p">.</span><span class="nx">revealMessage</span><span class="p">(</span><span class="nx">stegoImage</span><span class="p">);</span>

<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="nx">encryptedMessage</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="kc">null</span><span class="p">;</span><span class="w"> </span><span class="c1">// No hidden message found</span>
<span class="w">      </span><span class="p">}</span>

<span class="w">      </span><span class="c1">// Decrypt using OTR</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">decryptedMessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">decryptMessage</span><span class="p">(</span><span class="nx">encryptedMessage</span><span class="p">);</span>

<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">emit</span><span class="p">(</span><span class="s1">&#39;messageReceived&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">message</span><span class="o">:</span><span class="w"> </span><span class="nx">decryptedMessage</span><span class="p">,</span>
<span class="w">        </span><span class="nx">source</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;steganography&#39;</span><span class="p">,</span>
<span class="w">        </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="nb">Date</span><span class="p">.</span><span class="nx">now</span><span class="p">()</span>
<span class="w">      </span><span class="p">});</span>

<span class="w">      </span><span class="k">return</span><span class="w"> </span><span class="nx">decryptedMessage</span><span class="p">;</span>

<span class="w">    </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">emit</span><span class="p">(</span><span class="s1">&#39;stegoError&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="nx">error</span><span class="p">.</span><span class="nx">message</span><span class="p">,</span>
<span class="w">        </span><span class="nx">image</span><span class="o">:</span><span class="w"> </span><span class="nx">stegoImage</span>
<span class="w">      </span><span class="p">});</span>
<span class="w">      </span><span class="k">return</span><span class="w"> </span><span class="kc">null</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="message-format-specification">
<h3><a class="toc-backref" href="#id9" role="doc-backlink">Message Format Specification</a><a class="headerlink" href="#message-format-specification" title="Link to this heading">¶</a></h3>
<p>Steganographic messages use a structured format for reliability:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="c1">// Message Header (32 bits)</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">STEGO_HEADER</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">MAGIC</span><span class="o">:</span><span class="w"> </span><span class="mh">0x574F5452</span><span class="p">,</span><span class="w">        </span><span class="c1">// &#39;WOTR&#39; magic number</span>
<span class="w">  </span><span class="nx">VERSION</span><span class="o">:</span><span class="w"> </span><span class="mh">0x01</span><span class="p">,</span><span class="w">            </span><span class="c1">// Format version</span>
<span class="w">  </span><span class="nx">FLAGS</span><span class="o">:</span><span class="w"> </span><span class="mh">0x00</span><span class="p">,</span><span class="w">              </span><span class="c1">// Feature flags</span>
<span class="w">  </span><span class="nx">LENGTH_OFFSET</span><span class="o">:</span><span class="w"> </span><span class="mf">8</span><span class="p">,</span><span class="w">         </span><span class="c1">// Message length field offset</span>
<span class="w">  </span><span class="nx">CHECKSUM_OFFSET</span><span class="o">:</span><span class="w"> </span><span class="mf">12</span><span class="w">       </span><span class="c1">// Checksum field offset</span>
<span class="p">};</span>

<span class="kd">function</span><span class="w"> </span><span class="nx">createStegoMessage</span><span class="p">(</span><span class="nx">otrMessage</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">header</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nb">ArrayBuffer</span><span class="p">(</span><span class="mf">32</span><span class="p">);</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">view</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nb">DataView</span><span class="p">(</span><span class="nx">header</span><span class="p">);</span>

<span class="w">  </span><span class="c1">// Magic number</span>
<span class="w">  </span><span class="nx">view</span><span class="p">.</span><span class="nx">setUint32</span><span class="p">(</span><span class="mf">0</span><span class="p">,</span><span class="w"> </span><span class="nx">STEGO_HEADER</span><span class="p">.</span><span class="nx">MAGIC</span><span class="p">,</span><span class="w"> </span><span class="kc">false</span><span class="p">);</span>

<span class="w">  </span><span class="c1">// Version and flags</span>
<span class="w">  </span><span class="nx">view</span><span class="p">.</span><span class="nx">setUint8</span><span class="p">(</span><span class="mf">4</span><span class="p">,</span><span class="w"> </span><span class="nx">STEGO_HEADER</span><span class="p">.</span><span class="nx">VERSION</span><span class="p">);</span>
<span class="w">  </span><span class="nx">view</span><span class="p">.</span><span class="nx">setUint8</span><span class="p">(</span><span class="mf">5</span><span class="p">,</span><span class="w"> </span><span class="nx">STEGO_HEADER</span><span class="p">.</span><span class="nx">FLAGS</span><span class="p">);</span>

<span class="w">  </span><span class="c1">// Message length</span>
<span class="w">  </span><span class="nx">view</span><span class="p">.</span><span class="nx">setUint32</span><span class="p">(</span><span class="nx">STEGO_HEADER</span><span class="p">.</span><span class="nx">LENGTH_OFFSET</span><span class="p">,</span><span class="w"> </span><span class="nx">otrMessage</span><span class="p">.</span><span class="nx">length</span><span class="p">,</span><span class="w"> </span><span class="kc">false</span><span class="p">);</span>

<span class="w">  </span><span class="c1">// Checksum (CRC32)</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">checksum</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">calculateCRC32</span><span class="p">(</span><span class="nx">otrMessage</span><span class="p">);</span>
<span class="w">  </span><span class="nx">view</span><span class="p">.</span><span class="nx">setUint32</span><span class="p">(</span><span class="nx">STEGO_HEADER</span><span class="p">.</span><span class="nx">CHECKSUM_OFFSET</span><span class="p">,</span><span class="w"> </span><span class="nx">checksum</span><span class="p">,</span><span class="w"> </span><span class="kc">false</span><span class="p">);</span>

<span class="w">  </span><span class="c1">// Combine header and message</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">fullMessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="p">(</span><span class="nx">header</span><span class="p">.</span><span class="nx">byteLength</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="nx">otrMessage</span><span class="p">.</span><span class="nx">length</span><span class="p">);</span>
<span class="w">  </span><span class="nx">fullMessage</span><span class="p">.</span><span class="nx">set</span><span class="p">(</span><span class="ow">new</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="p">(</span><span class="nx">header</span><span class="p">),</span><span class="w"> </span><span class="mf">0</span><span class="p">);</span>
<span class="w">  </span><span class="nx">fullMessage</span><span class="p">.</span><span class="nx">set</span><span class="p">(</span><span class="nx">otrMessage</span><span class="p">,</span><span class="w"> </span><span class="nx">header</span><span class="p">.</span><span class="nx">byteLength</span><span class="p">);</span>

<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="nx">fullMessage</span><span class="p">;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="security-features">
<h2><a class="toc-backref" href="#id10" role="doc-backlink">Security Features</a><a class="headerlink" href="#security-features" title="Link to this heading">¶</a></h2>
<section id="statistical-security">
<h3><a class="toc-backref" href="#id11" role="doc-backlink">Statistical Security</a><a class="headerlink" href="#statistical-security" title="Link to this heading">¶</a></h3>
<p>Multiple techniques ensure steganographic security:</p>
<dl class="simple">
<dt><strong>Adaptive LSB Selection</strong></dt><dd><p>Bit positions selected based on image characteristics and OTR session keys.</p>
</dd>
<dt><strong>Noise Injection</strong></dt><dd><p>Random noise added to mask statistical signatures of embedded data.</p>
</dd>
<dt><strong>Cover Image Analysis</strong></dt><dd><p>Automatic analysis of cover images to optimize embedding parameters.</p>
</dd>
<dt><strong>Anti-Detection Measures</strong></dt><dd><p>Countermeasures against common steganalysis techniques.</p>
</dd>
</dl>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">class</span><span class="w"> </span><span class="nx">StatisticalSecurity</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span><span class="nx">sessionKey</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">sessionKey</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">sessionKey</span><span class="p">;</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">prng</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">SecurePRNG</span><span class="p">(</span><span class="nx">sessionKey</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">getAdaptiveBitPositions</span><span class="p">(</span><span class="nx">imageData</span><span class="p">,</span><span class="w"> </span><span class="nx">messageLength</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">positions</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[];</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">totalPixels</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">imageData</span><span class="p">.</span><span class="nx">width</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="nx">imageData</span><span class="p">.</span><span class="nx">height</span><span class="p">;</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">requiredPositions</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">messageLength</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mf">8</span><span class="p">;</span>

<span class="w">    </span><span class="c1">// Use session key to seed position selection</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">prng</span><span class="p">.</span><span class="nx">seed</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">sessionKey</span><span class="p">);</span>

<span class="w">    </span><span class="k">while</span><span class="w"> </span><span class="p">(</span><span class="nx">positions</span><span class="p">.</span><span class="nx">length</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="nx">requiredPositions</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">position</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">prng</span><span class="p">.</span><span class="nx">nextInt</span><span class="p">(</span><span class="nx">totalPixels</span><span class="p">);</span>

<span class="w">      </span><span class="c1">// Check if position is suitable for embedding</span>
<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">isPositionSuitable</span><span class="p">(</span><span class="nx">imageData</span><span class="p">,</span><span class="w"> </span><span class="nx">position</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">positions</span><span class="p">.</span><span class="nx">push</span><span class="p">(</span><span class="nx">position</span><span class="p">);</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nx">positions</span><span class="p">;</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">injectStatisticalNoise</span><span class="p">(</span><span class="nx">imageData</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">pixels</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">imageData</span><span class="p">.</span><span class="nx">data</span><span class="p">;</span>

<span class="w">    </span><span class="c1">// Add subtle noise to alpha channel</span>
<span class="w">    </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="kd">let</span><span class="w"> </span><span class="nx">i</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">3</span><span class="p">;</span><span class="w"> </span><span class="nx">i</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="nx">pixels</span><span class="p">.</span><span class="nx">length</span><span class="p">;</span><span class="w"> </span><span class="nx">i</span><span class="w"> </span><span class="o">+=</span><span class="w"> </span><span class="mf">4</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">prng</span><span class="p">.</span><span class="nx">nextFloat</span><span class="p">()</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mf">0.1</span><span class="p">)</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="c1">// 10% of pixels</span>
<span class="w">        </span><span class="kd">const</span><span class="w"> </span><span class="nx">noise</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">prng</span><span class="p">.</span><span class="nx">nextInt</span><span class="p">(</span><span class="mf">3</span><span class="p">)</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="mf">1</span><span class="p">;</span><span class="w"> </span><span class="c1">// -1, 0, or 1</span>
<span class="w">        </span><span class="nx">pixels</span><span class="p">[</span><span class="nx">i</span><span class="p">]</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">Math</span><span class="p">.</span><span class="nx">max</span><span class="p">(</span><span class="mf">0</span><span class="p">,</span><span class="w"> </span><span class="nb">Math</span><span class="p">.</span><span class="nx">min</span><span class="p">(</span><span class="mf">255</span><span class="p">,</span><span class="w"> </span><span class="nx">pixels</span><span class="p">[</span><span class="nx">i</span><span class="p">]</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="nx">noise</span><span class="p">));</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="cover-image-management">
<h3><a class="toc-backref" href="#id12" role="doc-backlink">Cover Image Management</a><a class="headerlink" href="#cover-image-management" title="Link to this heading">¶</a></h3>
<p>Intelligent cover image selection and generation:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">class</span><span class="w"> </span><span class="nx">CoverImageManager</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">imageDatabase</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">CoverImageDatabase</span><span class="p">();</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">generator</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">CoverImageGenerator</span><span class="p">();</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">selectOptimalCover</span><span class="p">(</span><span class="nx">messageSize</span><span class="p">,</span><span class="w"> </span><span class="nx">requirements</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{})</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">criteria</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">minCapacity</span><span class="o">:</span><span class="w"> </span><span class="nx">messageSize</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mf">8</span><span class="p">,</span><span class="w"> </span><span class="c1">// bits needed</span>
<span class="w">      </span><span class="nx">format</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;PNG&#39;</span><span class="p">,</span>
<span class="w">      </span><span class="nx">minDimensions</span><span class="o">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">width</span><span class="o">:</span><span class="w"> </span><span class="mf">800</span><span class="p">,</span><span class="w"> </span><span class="nx">height</span><span class="o">:</span><span class="w"> </span><span class="mf">600</span><span class="w"> </span><span class="p">},</span>
<span class="w">      </span><span class="nx">maxFileSize</span><span class="o">:</span><span class="w"> </span><span class="mf">5</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mf">1024</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mf">1024</span><span class="p">,</span><span class="w"> </span><span class="c1">// 5MB</span>
<span class="w">      </span><span class="p">...</span><span class="nx">requirements</span>
<span class="w">    </span><span class="p">};</span>

<span class="w">    </span><span class="c1">// Try to find suitable image from database</span>
<span class="w">    </span><span class="kd">let</span><span class="w"> </span><span class="nx">cover</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">imageDatabase</span><span class="p">.</span><span class="nx">findSuitable</span><span class="p">(</span><span class="nx">criteria</span><span class="p">);</span>

<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="nx">cover</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="c1">// Generate synthetic cover image</span>
<span class="w">      </span><span class="nx">cover</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">generator</span><span class="p">.</span><span class="nx">createCover</span><span class="p">(</span><span class="nx">criteria</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="c1">// Analyze cover suitability</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">analysis</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">analyzeCover</span><span class="p">(</span><span class="nx">cover</span><span class="p">);</span>

<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">analysis</span><span class="p">.</span><span class="nx">suitabilityScore</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mf">0.7</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="c1">// Enhance cover image for better steganography</span>
<span class="w">      </span><span class="nx">cover</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">enhanceCover</span><span class="p">(</span><span class="nx">cover</span><span class="p">,</span><span class="w"> </span><span class="nx">analysis</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nx">cover</span><span class="p">;</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">analyzeCover</span><span class="p">(</span><span class="nx">image</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">imageData</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">loadImageData</span><span class="p">(</span><span class="nx">image</span><span class="p">);</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">capacity</span><span class="o">:</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">calculateCapacity</span><span class="p">(</span><span class="nx">imageData</span><span class="p">),</span>
<span class="w">      </span><span class="nx">complexity</span><span class="o">:</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">calculateComplexity</span><span class="p">(</span><span class="nx">imageData</span><span class="p">),</span>
<span class="w">      </span><span class="nx">noiseLevel</span><span class="o">:</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">calculateNoiseLevel</span><span class="p">(</span><span class="nx">imageData</span><span class="p">),</span>
<span class="w">      </span><span class="nx">suitabilityScore</span><span class="o">:</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">calculateSuitability</span><span class="p">(</span><span class="nx">imageData</span><span class="p">)</span>
<span class="w">    </span><span class="p">};</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="platform-integration">
<h2><a class="toc-backref" href="#id13" role="doc-backlink">Platform Integration</a><a class="headerlink" href="#platform-integration" title="Link to this heading">¶</a></h2>
<section id="browser-extension-integration">
<h3><a class="toc-backref" href="#id14" role="doc-backlink">Browser Extension Integration</a><a class="headerlink" href="#browser-extension-integration" title="Link to this heading">¶</a></h3>
<p>Seamless integration with social media and file sharing platforms:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">class</span><span class="w"> </span><span class="nx">PlatformIntegration</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">platforms</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nb">Map</span><span class="p">();</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">setupPlatformHandlers</span><span class="p">();</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">setupPlatformHandlers</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Facebook integration</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">platforms</span><span class="p">.</span><span class="nx">set</span><span class="p">(</span><span class="s1">&#39;facebook.com&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">imageSelector</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;img[src*=&quot;scontent&quot;]&#39;</span><span class="p">,</span>
<span class="w">      </span><span class="nx">uploadHandler</span><span class="o">:</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">handleFacebookUpload</span><span class="p">.</span><span class="nx">bind</span><span class="p">(</span><span class="k">this</span><span class="p">),</span>
<span class="w">      </span><span class="nx">downloadHandler</span><span class="o">:</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">handleFacebookDownload</span><span class="p">.</span><span class="nx">bind</span><span class="p">(</span><span class="k">this</span><span class="p">)</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="c1">// Instagram integration</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">platforms</span><span class="p">.</span><span class="nx">set</span><span class="p">(</span><span class="s1">&#39;instagram.com&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">imageSelector</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;img[src*=&quot;cdninstagram&quot;]&#39;</span><span class="p">,</span>
<span class="w">      </span><span class="nx">uploadHandler</span><span class="o">:</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">handleInstagramUpload</span><span class="p">.</span><span class="nx">bind</span><span class="p">(</span><span class="k">this</span><span class="p">),</span>
<span class="w">      </span><span class="nx">downloadHandler</span><span class="o">:</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">handleInstagramDownload</span><span class="p">.</span><span class="nx">bind</span><span class="p">(</span><span class="k">this</span><span class="p">)</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="c1">// Discord integration</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">platforms</span><span class="p">.</span><span class="nx">set</span><span class="p">(</span><span class="s1">&#39;discord.com&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">imageSelector</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;img[src*=&quot;cdn.discordapp.com&quot;]&#39;</span><span class="p">,</span>
<span class="w">      </span><span class="nx">uploadHandler</span><span class="o">:</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">handleDiscordUpload</span><span class="p">.</span><span class="nx">bind</span><span class="p">(</span><span class="k">this</span><span class="p">),</span>
<span class="w">      </span><span class="nx">downloadHandler</span><span class="o">:</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">handleDiscordDownload</span><span class="p">.</span><span class="nx">bind</span><span class="p">(</span><span class="k">this</span><span class="p">)</span>
<span class="w">    </span><span class="p">});</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">handleImageUpload</span><span class="p">(</span><span class="nx">file</span><span class="p">,</span><span class="w"> </span><span class="nx">platform</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="k">this</span><span class="p">.</span><span class="nx">isImageFile</span><span class="p">(</span><span class="nx">file</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">return</span><span class="w"> </span><span class="nx">file</span><span class="p">;</span><span class="w"> </span><span class="c1">// Pass through non-images</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="c1">// Check if user wants to hide a message</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">shouldHideMessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">promptForSteganography</span><span class="p">();</span>

<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">shouldHideMessage</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">message</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">getMessageToHide</span><span class="p">();</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">stegoImage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">otrStego</span><span class="p">.</span><span class="nx">sendStegoMessage</span><span class="p">(</span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="nx">file</span><span class="p">);</span>
<span class="w">      </span><span class="k">return</span><span class="w"> </span><span class="nx">stegoImage</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nx">file</span><span class="p">;</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">handleImageDownload</span><span class="p">(</span><span class="nx">imageUrl</span><span class="p">,</span><span class="w"> </span><span class="nx">platform</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="c1">// Download image</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">response</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">fetch</span><span class="p">(</span><span class="nx">imageUrl</span><span class="p">);</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">imageBlob</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">response</span><span class="p">.</span><span class="nx">blob</span><span class="p">();</span>

<span class="w">      </span><span class="c1">// Check for hidden messages</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">hiddenMessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">otrStego</span><span class="p">.</span><span class="nx">processStegoImage</span><span class="p">(</span><span class="nx">imageBlob</span><span class="p">);</span>

<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">hiddenMessage</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">displayHiddenMessage</span><span class="p">(</span><span class="nx">hiddenMessage</span><span class="p">);</span>
<span class="w">      </span><span class="p">}</span>

<span class="w">    </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">console</span><span class="p">.</span><span class="nx">error</span><span class="p">(</span><span class="s1">&#39;Error processing image:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="performance-optimization">
<h2><a class="toc-backref" href="#id15" role="doc-backlink">Performance Optimization</a><a class="headerlink" href="#performance-optimization" title="Link to this heading">¶</a></h2>
<section id="image-processing-optimization">
<h3><a class="toc-backref" href="#id16" role="doc-backlink">Image Processing Optimization</a><a class="headerlink" href="#image-processing-optimization" title="Link to this heading">¶</a></h3>
<p>Efficient algorithms for real-time steganography:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">class</span><span class="w"> </span><span class="nx">PerformanceOptimizer</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">workerPool</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">WorkerPool</span><span class="p">(</span><span class="mf">4</span><span class="p">);</span><span class="w"> </span><span class="c1">// 4 web workers</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">imageCache</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">LRUCache</span><span class="p">(</span><span class="mf">50</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">optimizedEmbedding</span><span class="p">(</span><span class="nx">imageData</span><span class="p">,</span><span class="w"> </span><span class="nx">messageData</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Use web workers for large images</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">imageData</span><span class="p">.</span><span class="nx">width</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="nx">imageData</span><span class="p">.</span><span class="nx">height</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mf">1000000</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">return</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">workerEmbedding</span><span class="p">(</span><span class="nx">imageData</span><span class="p">,</span><span class="w"> </span><span class="nx">messageData</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="c1">// Use main thread for smaller images</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">directEmbedding</span><span class="p">(</span><span class="nx">imageData</span><span class="p">,</span><span class="w"> </span><span class="nx">messageData</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">workerEmbedding</span><span class="p">(</span><span class="nx">imageData</span><span class="p">,</span><span class="w"> </span><span class="nx">messageData</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Split image into chunks for parallel processing</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">chunks</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">splitImageData</span><span class="p">(</span><span class="nx">imageData</span><span class="p">,</span><span class="w"> </span><span class="mf">4</span><span class="p">);</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">messageChunks</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">splitMessage</span><span class="p">(</span><span class="nx">messageData</span><span class="p">,</span><span class="w"> </span><span class="mf">4</span><span class="p">);</span>

<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">promises</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">chunks</span><span class="p">.</span><span class="nx">map</span><span class="p">((</span><span class="nx">chunk</span><span class="p">,</span><span class="w"> </span><span class="nx">index</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">workerPool</span><span class="p">.</span><span class="nx">execute</span><span class="p">(</span><span class="s1">&#39;embedChunk&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">imageChunk</span><span class="o">:</span><span class="w"> </span><span class="nx">chunk</span><span class="p">,</span>
<span class="w">        </span><span class="nx">messageChunk</span><span class="o">:</span><span class="w"> </span><span class="nx">messageChunks</span><span class="p">[</span><span class="nx">index</span><span class="p">]</span>
<span class="w">      </span><span class="p">});</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">results</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nb">Promise</span><span class="p">.</span><span class="nx">all</span><span class="p">(</span><span class="nx">promises</span><span class="p">);</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">mergeImageChunks</span><span class="p">(</span><span class="nx">results</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="c1">// Progressive loading for large images</span>
<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">progressiveProcessing</span><span class="p">(</span><span class="nx">image</span><span class="p">,</span><span class="w"> </span><span class="nx">onProgress</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">totalPixels</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">image</span><span class="p">.</span><span class="nx">width</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="nx">image</span><span class="p">.</span><span class="nx">height</span><span class="p">;</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">chunkSize</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">100000</span><span class="p">;</span><span class="w"> </span><span class="c1">// Process 100k pixels at a time</span>

<span class="w">    </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="kd">let</span><span class="w"> </span><span class="nx">offset</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0</span><span class="p">;</span><span class="w"> </span><span class="nx">offset</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="nx">totalPixels</span><span class="p">;</span><span class="w"> </span><span class="nx">offset</span><span class="w"> </span><span class="o">+=</span><span class="w"> </span><span class="nx">chunkSize</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">chunk</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">getImageChunk</span><span class="p">(</span><span class="nx">image</span><span class="p">,</span><span class="w"> </span><span class="nx">offset</span><span class="p">,</span><span class="w"> </span><span class="nx">chunkSize</span><span class="p">);</span>
<span class="w">      </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">processChunk</span><span class="p">(</span><span class="nx">chunk</span><span class="p">);</span>

<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">progress</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">Math</span><span class="p">.</span><span class="nx">min</span><span class="p">(</span><span class="mf">100</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">offset</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="nx">totalPixels</span><span class="p">)</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mf">100</span><span class="p">);</span>
<span class="w">      </span><span class="nx">onProgress</span><span class="p">(</span><span class="nx">progress</span><span class="p">);</span>

<span class="w">      </span><span class="c1">// Yield control to prevent UI blocking</span>
<span class="w">      </span><span class="k">await</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nb">Promise</span><span class="p">(</span><span class="nx">resolve</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">setTimeout</span><span class="p">(</span><span class="nx">resolve</span><span class="p">,</span><span class="w"> </span><span class="mf">0</span><span class="p">));</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p>This steganography implementation provides covert communication capabilities while maintaining the full security guarantees of the OTR protocol, enabling truly invisible secure messaging through innocent image sharing.</p>
</section>
</section>
</section>

        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          <a class="next-page" href="steganography-architecture.html">
              <div class="page-info">
                <div class="context">
                  <span>Next</span>
                </div>
                <div class="title">Steganography Architecture</div>
              </div>
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
            </a>
          <a class="prev-page" href="steganography-summary.html">
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
              <div class="page-info">
                <div class="context">
                  <span>Previous</span>
                </div>
                
                <div class="title">Steganography Documentation Summary</div>
                
              </div>
            </a>
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 1980, WebOTR Team
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer">
      
      
      <div class="toc-sticky toc-scroll">
        <div class="toc-title-container">
          <span class="toc-title">
            On this page
          </span>
        </div>
        <div class="toc-tree-container">
          <div class="toc-tree">
            <ul>
<li><a class="reference internal" href="#">Steganography Implementation</a><ul>
<li><a class="reference internal" href="#overview">Overview</a></li>
<li><a class="reference internal" href="#core-architecture">Core Architecture</a><ul>
<li><a class="reference internal" href="#system-components">System Components</a></li>
<li><a class="reference internal" href="#steganographic-methods">Steganographic Methods</a></li>
</ul>
</li>
<li><a class="reference internal" href="#implementation-details">Implementation Details</a><ul>
<li><a class="reference internal" href="#core-steganography-engine">Core Steganography Engine</a></li>
<li><a class="reference internal" href="#lsb-embedding-algorithm">LSB Embedding Algorithm</a></li>
<li><a class="reference internal" href="#otr-integration">OTR Integration</a></li>
<li><a class="reference internal" href="#message-format-specification">Message Format Specification</a></li>
</ul>
</li>
<li><a class="reference internal" href="#security-features">Security Features</a><ul>
<li><a class="reference internal" href="#statistical-security">Statistical Security</a></li>
<li><a class="reference internal" href="#cover-image-management">Cover Image Management</a></li>
</ul>
</li>
<li><a class="reference internal" href="#platform-integration">Platform Integration</a><ul>
<li><a class="reference internal" href="#browser-extension-integration">Browser Extension Integration</a></li>
</ul>
</li>
<li><a class="reference internal" href="#performance-optimization">Performance Optimization</a><ul>
<li><a class="reference internal" href="#image-processing-optimization">Image Processing Optimization</a></li>
</ul>
</li>
</ul>
</li>
</ul>

          </div>
        </div>
      </div>
      
      
    </aside>
  </div>
</div><script src="../_static/documentation_options.js?v=01f34227"></script>
    <script src="../_static/doctools.js?v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/scripts/furo.js?v=5fa4622c"></script>
    <script src="../_static/mermaid-renderer.js?v=b683124c"></script>
    </body>
</html>