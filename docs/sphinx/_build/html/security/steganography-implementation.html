<!doctype html>
<html class="no-js" lang="en" data-content_root="../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><meta name="viewport" content="width=device-width, initial-scale=1" />
<link rel="index" title="Index" href="../genindex.html" /><link rel="search" title="Search" href="../search.html" /><link rel="next" title="Steganography API Reference" href="steganography-api.html" /><link rel="prev" title="Steganography Architecture" href="steganography-architecture.html" />

    <link rel="shortcut icon" href="../_static/favicon.svg"/><!-- Generated with Sphinx 7.4.7 and Furo 2024.08.06 -->
        <title>Steganography Implementation Guide - WebOTR Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../_static/styles/furo.css?v=354aac6f" />
    <link rel="stylesheet" type="text/css" href="../_static/styles/furo-extensions.css?v=302659d7" />
    <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=21a7f706" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  --color-brand-primary: #6366f1;
  --color-brand-content: #6366f1;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8b5cf6;
  --color-brand-content: #8b5cf6;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8b5cf6;
  --color-brand-content: #8b5cf6;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-with-moon" viewBox="0 0 24 24">
    <title>Auto light/dark, in light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
      class="icon-custom-derived-from-feather-sun-and-tabler-moon">
      <path style="opacity: 50%" d="M 5.411 14.504 C 5.471 14.504 5.532 14.504 5.591 14.504 C 3.639 16.319 4.383 19.569 6.931 20.352 C 7.693 20.586 8.512 20.551 9.25 20.252 C 8.023 23.207 4.056 23.725 2.11 21.184 C 0.166 18.642 1.702 14.949 4.874 14.536 C 5.051 14.512 5.231 14.5 5.411 14.5 L 5.411 14.504 Z"/>
      <line x1="14.5" y1="3.25" x2="14.5" y2="1.25"/>
      <line x1="14.5" y1="15.85" x2="14.5" y2="17.85"/>
      <line x1="10.044" y1="5.094" x2="8.63" y2="3.68"/>
      <line x1="19" y1="14.05" x2="20.414" y2="15.464"/>
      <line x1="8.2" y1="9.55" x2="6.2" y2="9.55"/>
      <line x1="20.8" y1="9.55" x2="22.8" y2="9.55"/>
      <line x1="10.044" y1="14.006" x2="8.63" y2="15.42"/>
      <line x1="19" y1="5.05" x2="20.414" y2="3.636"/>
      <circle cx="14.5" cy="9.55" r="3.6"/>
    </svg>
  </symbol>
  <symbol id="svg-moon-with-sun" viewBox="0 0 24 24">
    <title>Auto light/dark, in dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
      class="icon-custom-derived-from-feather-sun-and-tabler-moon">
      <path d="M 8.282 7.007 C 8.385 7.007 8.494 7.007 8.595 7.007 C 5.18 10.184 6.481 15.869 10.942 17.24 C 12.275 17.648 13.706 17.589 15 17.066 C 12.851 22.236 5.91 23.143 2.505 18.696 C -0.897 14.249 1.791 7.786 7.342 7.063 C 7.652 7.021 7.965 7 8.282 7 L 8.282 7.007 Z"/>
      <line style="opacity: 50%" x1="18" y1="3.705" x2="18" y2="2.5"/>
      <line style="opacity: 50%" x1="18" y1="11.295" x2="18" y2="12.5"/>
      <line style="opacity: 50%" x1="15.316" y1="4.816" x2="14.464" y2="3.964"/>
      <line style="opacity: 50%" x1="20.711" y1="10.212" x2="21.563" y2="11.063"/>
      <line style="opacity: 50%" x1="14.205" y1="7.5" x2="13.001" y2="7.5"/>
      <line style="opacity: 50%" x1="21.795" y1="7.5" x2="23" y2="7.5"/>
      <line style="opacity: 50%" x1="15.316" y1="10.184" x2="14.464" y2="11.036"/>
      <line style="opacity: 50%" x1="20.711" y1="4.789" x2="21.563" y2="3.937"/>
      <circle style="opacity: 50%" cx="18" cy="7.5" r="2.169"/>
    </svg>
  </symbol>
  <symbol id="svg-pencil" viewBox="0 0 24 24">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-pencil-code">
      <path d="M4 20h4l10.5 -10.5a2.828 2.828 0 1 0 -4 -4l-10.5 10.5v4" />
      <path d="M13.5 6.5l4 4" />
      <path d="M20 21l2 -2l-2 -2" />
      <path d="M17 17l-2 2l2 2" />
    </svg>
  </symbol>
  <symbol id="svg-eye" viewBox="0 0 24 24">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-eye-code">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0" />
      <path
        d="M11.11 17.958c-3.209 -.307 -5.91 -2.293 -8.11 -5.958c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6c-.21 .352 -.427 .688 -.647 1.008" />
      <path d="M20 21l2 -2l-2 -2" />
      <path d="M17 17l-2 2l2 2" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>

<a class="skip-to-content muted-link" href="#furo-main-content">Skip to content</a>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../index.html"><div class="brand">WebOTR Documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto-light"><use href="#svg-sun-with-moon"></use></svg>
          <svg class="theme-icon-when-auto-dark"><use href="#svg-moon-with-sun"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><a class="sidebar-brand" href="../index.html">
  
  <div class="sidebar-logo-container">
    <img class="sidebar-logo" src="../_static/logo.svg" alt="Logo"/>
  </div>
  
  <span class="sidebar-brand-text">WebOTR Documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-scroll"><div class="sidebar-tree">
  <p class="caption" role="heading"><span class="caption-text">User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user-guide/installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../user-guide/getting-started.html">Getting Started</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../developer-guide/architecture.html">Architecture Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../developer-guide/contributing.html">Contributing to WebOTR</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Protocol Compliance &amp; Advanced Features</span></p>
<ul>
<li class="toctree-l1 has-children"><a class="reference internal" href="../protocol/index.html">Protocol Compliance &amp; Advanced Features</a><input class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of Protocol Compliance &amp; Advanced Features</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul>
<li class="toctree-l2"><a class="reference internal" href="../protocol/overview.html">Protocol Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/version-negotiation.html">Version Negotiation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/message-ordering.html">Message Ordering</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/enhanced-smp.html">Enhanced SMP</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/policy-manager.html">Policy Manager</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/integration-guide.html">Integration Guide</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/troubleshooting.html">Troubleshooting Guide</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Security</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="overview.html">Security Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements-summary.html">libOTR Security Enhancements Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements.html">libOTR Security Enhancements</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements-implementation.html">libOTR Enhancements Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements-api.html">libOTR Enhancements API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements-testing.html">libOTR Enhancements Testing and Validation</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy-summary.html">Forward Secrecy Documentation Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy.html">Forward Secrecy Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy-architecture.html">Forward Secrecy Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy-implementation.html">Forward Secrecy Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy-api.html">Forward Secrecy API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake-summary.html">AKE Documentation Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake.html">Authenticated Key Exchange (AKE)</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake-architecture.html">AKE Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake-implementation.html">AKE Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake-api.html">AKE API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography-summary.html">Steganography Documentation Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography.html">Steganography Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography-architecture.html">Steganography Architecture</a></li>
<li class="toctree-l1 current current-page"><a class="current reference internal" href="#">Steganography Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography-api.html">Steganography API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../reference/faq.html">Frequently Asked Questions</a></li>
<li class="toctree-l1"><a class="reference internal" href="../reference/changelog.html">Changelog</a></li>
</ul>

</div>
</div>

      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          <div class="view-this-page">
  <a class="muted-link" href="https://github.com/forkrul/webOTteR/blob/main/docs/sphinx/security/steganography-implementation.rst?plain=true" title="View this page">
    <svg><use href="#svg-eye"></use></svg>
    <span class="visually-hidden">View this page</span>
  </a>
</div><div class="edit-this-page">
  <a class="muted-link" href="https://github.com/forkrul/webOTteR/edit/main/docs/sphinx/security/steganography-implementation.rst" title="Edit this page">
    <svg><use href="#svg-pencil"></use></svg>
    <span class="visually-hidden">Edit this page</span>
  </a>
</div><div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto-light"><use href="#svg-sun-with-moon"></use></svg>
              <svg class="theme-icon-when-auto-dark"><use href="#svg-moon-with-sun"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main" id="furo-main-content">
          <section id="steganography-implementation-guide">
<h1>Steganography Implementation Guide<a class="headerlink" href="#steganography-implementation-guide" title="Link to this heading">¶</a></h1>
<p>This guide provides detailed implementation instructions for integrating WebOTR’s steganography system into your application.</p>
<nav class="contents local" id="table-of-contents">
<p class="topic-title">Table of Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#quick-start" id="id1">Quick Start</a></p>
<ul>
<li><p><a class="reference internal" href="#basic-integration" id="id2">Basic Integration</a></p></li>
<li><p><a class="reference internal" href="#advanced-configuration" id="id3">Advanced Configuration</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#component-integration" id="id4">Component Integration</a></p>
<ul>
<li><p><a class="reference internal" href="#steganography-engine" id="id5">Steganography Engine</a></p></li>
<li><p><a class="reference internal" href="#lsb-algorithm-implementation" id="id6">LSB Algorithm Implementation</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#platform-integration" id="id7">Platform Integration</a></p>
<ul>
<li><p><a class="reference internal" href="#browser-extension-integration" id="id8">Browser Extension Integration</a></p></li>
<li><p><a class="reference internal" href="#multi-image-distribution" id="id9">Multi-Image Distribution</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#security-implementation" id="id10">Security Implementation</a></p>
<ul>
<li><p><a class="reference internal" href="#statistical-security" id="id11">Statistical Security</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#performance-optimization" id="id12">Performance Optimization</a></p>
<ul>
<li><p><a class="reference internal" href="#web-worker-implementation" id="id13">Web Worker Implementation</a></p></li>
<li><p><a class="reference internal" href="#progressive-processing" id="id14">Progressive Processing</a></p></li>
</ul>
</li>
</ul>
</nav>
<section id="quick-start">
<h2><a class="toc-backref" href="#id1" role="doc-backlink">Quick Start</a><a class="headerlink" href="#quick-start" title="Link to this heading">¶</a></h2>
<section id="basic-integration">
<h3><a class="toc-backref" href="#id2" role="doc-backlink">Basic Integration</a><a class="headerlink" href="#basic-integration" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">import</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">SteganographyEngine</span><span class="p">,</span>
<span class="w">  </span><span class="nx">OTRSteganographySession</span><span class="p">,</span>
<span class="w">  </span><span class="nx">CoverImageManager</span>
<span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;webOTR/core/steganography&#39;</span><span class="p">;</span>

<span class="c1">// Initialize steganography engine</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">stego</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">SteganographyEngine</span><span class="p">({</span>
<span class="w">  </span><span class="nx">method</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;LSB_ALPHA&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">bitsPerPixel</span><span class="o">:</span><span class="w"> </span><span class="mf">1</span><span class="p">,</span>
<span class="w">  </span><span class="nx">noiseInjection</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nx">adaptiveLSB</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span>
<span class="p">});</span>

<span class="c1">// Create OTR session with steganography</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">session</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">OTRSteganographySession</span><span class="p">({</span>
<span class="w">  </span><span class="nx">steganography</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">engine</span><span class="o">:</span><span class="w"> </span><span class="nx">stego</span><span class="p">,</span>
<span class="w">    </span><span class="nx">autoSelectCover</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nx">multiImageSupport</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">});</span>

<span class="c1">// Hide message in image</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">coverImage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">loadImage</span><span class="p">(</span><span class="s1">&#39;cover.png&#39;</span><span class="p">);</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">stegoImage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">session</span><span class="p">.</span><span class="nx">sendStegoMessage</span><span class="p">(</span><span class="s1">&#39;Hello, secret world!&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">coverImage</span><span class="p">);</span>

<span class="c1">// Extract message from image</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">hiddenMessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">session</span><span class="p">.</span><span class="nx">processStegoImage</span><span class="p">(</span><span class="nx">stegoImage</span><span class="p">);</span>
<span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;Hidden message:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">hiddenMessage</span><span class="p">);</span>
</pre></div>
</div>
</section>
<section id="advanced-configuration">
<h3><a class="toc-backref" href="#id3" role="doc-backlink">Advanced Configuration</a><a class="headerlink" href="#advanced-configuration" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">import</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">SteganographyEngine</span><span class="p">,</span><span class="w"> </span><span class="nx">StatisticalSecurity</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;webOTR/core/steganography&#39;</span><span class="p">;</span>

<span class="c1">// Advanced steganography configuration</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">advancedStego</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">SteganographyEngine</span><span class="p">({</span>
<span class="w">  </span><span class="c1">// Core settings</span>
<span class="w">  </span><span class="nx">method</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;ADAPTIVE_LSB&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">bitsPerPixel</span><span class="o">:</span><span class="w"> </span><span class="mf">1</span><span class="p">,</span>
<span class="w">  </span><span class="nx">compressionLevel</span><span class="o">:</span><span class="w"> </span><span class="mf">0</span><span class="p">,</span>

<span class="w">  </span><span class="c1">// Security settings</span>
<span class="w">  </span><span class="nx">noiseInjection</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nx">statisticalSecurity</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nx">adaptiveLSB</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nx">antiDetection</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>

<span class="w">  </span><span class="c1">// Performance settings</span>
<span class="w">  </span><span class="nx">useWebWorkers</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nx">chunkSize</span><span class="o">:</span><span class="w"> </span><span class="mf">100000</span><span class="p">,</span>
<span class="w">  </span><span class="nx">progressiveProcessing</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>

<span class="w">  </span><span class="c1">// Platform settings</span>
<span class="w">  </span><span class="nx">platformOptimization</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nx">metadataStripping</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nx">formatNormalization</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>

<span class="w">  </span><span class="c1">// Advanced features</span>
<span class="w">  </span><span class="nx">multiImageDistribution</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nx">errorCorrection</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nx">redundancy</span><span class="o">:</span><span class="w"> </span><span class="mf">0.1</span><span class="w"> </span><span class="c1">// 10% redundancy</span>
<span class="p">});</span>
</pre></div>
</div>
</section>
</section>
<section id="component-integration">
<h2><a class="toc-backref" href="#id4" role="doc-backlink">Component Integration</a><a class="headerlink" href="#component-integration" title="Link to this heading">¶</a></h2>
<section id="steganography-engine">
<h3><a class="toc-backref" href="#id5" role="doc-backlink">Steganography Engine</a><a class="headerlink" href="#steganography-engine" title="Link to this heading">¶</a></h3>
<p>Direct integration with the core steganography engine:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">class</span><span class="w"> </span><span class="nx">SteganographyManager</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span><span class="nx">options</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{})</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">options</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">defaultMethod</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;LSB_ALPHA&#39;</span><span class="p">,</span>
<span class="w">      </span><span class="nx">maxImageSize</span><span class="o">:</span><span class="w"> </span><span class="mf">10</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mf">1024</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mf">1024</span><span class="p">,</span><span class="w"> </span><span class="c1">// 10MB</span>
<span class="w">      </span><span class="nx">supportedFormats</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;PNG&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;BMP&#39;</span><span class="p">],</span>
<span class="w">      </span><span class="nx">qualityThreshold</span><span class="o">:</span><span class="w"> </span><span class="mf">0.95</span><span class="p">,</span>
<span class="w">      </span><span class="p">...</span><span class="nx">options</span>
<span class="w">    </span><span class="p">};</span>

<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">engine</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">SteganographyEngine</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">options</span><span class="p">);</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">coverManager</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">CoverImageManager</span><span class="p">();</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">securityManager</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">StatisticalSecurity</span><span class="p">();</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">hideMessage</span><span class="p">(</span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="nx">coverImage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">null</span><span class="p">,</span><span class="w"> </span><span class="nx">options</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{})</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="c1">// Validate inputs</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">validateMessage</span><span class="p">(</span><span class="nx">message</span><span class="p">);</span>

<span class="w">      </span><span class="c1">// Get or select cover image</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">cover</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">coverImage</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">coverManager</span><span class="p">.</span><span class="nx">selectOptimalCover</span><span class="p">(</span>
<span class="w">        </span><span class="nx">message</span><span class="p">.</span><span class="nx">length</span><span class="p">,</span>
<span class="w">        </span><span class="nx">options</span><span class="p">.</span><span class="nx">coverRequirements</span>
<span class="w">      </span><span class="p">);</span>

<span class="w">      </span><span class="c1">// Analyze cover suitability</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">analysis</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">coverManager</span><span class="p">.</span><span class="nx">analyzeCover</span><span class="p">(</span><span class="nx">cover</span><span class="p">);</span>
<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">analysis</span><span class="p">.</span><span class="nx">suitabilityScore</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mf">0.7</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">throw</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="ne">Error</span><span class="p">(</span><span class="s1">&#39;Cover image not suitable for steganography&#39;</span><span class="p">);</span>
<span class="w">      </span><span class="p">}</span>

<span class="w">      </span><span class="c1">// Apply security measures</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">secureMessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">securityManager</span><span class="p">.</span><span class="nx">prepareMessage</span><span class="p">(</span>
<span class="w">        </span><span class="nx">message</span><span class="p">,</span>
<span class="w">        </span><span class="nx">options</span><span class="p">.</span><span class="nx">sessionKey</span>
<span class="w">      </span><span class="p">);</span>

<span class="w">      </span><span class="c1">// Perform embedding</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">stegoImage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">engine</span><span class="p">.</span><span class="nx">hideMessage</span><span class="p">(</span><span class="nx">cover</span><span class="p">,</span><span class="w"> </span><span class="nx">secureMessage</span><span class="p">);</span>

<span class="w">      </span><span class="c1">// Verify embedding quality</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">quality</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">verifyEmbeddingQuality</span><span class="p">(</span><span class="nx">cover</span><span class="p">,</span><span class="w"> </span><span class="nx">stegoImage</span><span class="p">);</span>
<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">quality</span><span class="p">.</span><span class="nx">psnr</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mf">40</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">console</span><span class="p">.</span><span class="nx">warn</span><span class="p">(</span><span class="s1">&#39;Low embedding quality detected&#39;</span><span class="p">);</span>
<span class="w">      </span><span class="p">}</span>

<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">emit</span><span class="p">(</span><span class="s1">&#39;messageHidden&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">originalSize</span><span class="o">:</span><span class="w"> </span><span class="nx">cover</span><span class="p">.</span><span class="nx">size</span><span class="p">,</span>
<span class="w">        </span><span class="nx">stegoSize</span><span class="o">:</span><span class="w"> </span><span class="nx">stegoImage</span><span class="p">.</span><span class="nx">size</span><span class="p">,</span>
<span class="w">        </span><span class="nx">messageLength</span><span class="o">:</span><span class="w"> </span><span class="nx">message</span><span class="p">.</span><span class="nx">length</span><span class="p">,</span>
<span class="w">        </span><span class="nx">quality</span><span class="o">:</span><span class="w"> </span><span class="nx">quality</span>
<span class="w">      </span><span class="p">});</span>

<span class="w">      </span><span class="k">return</span><span class="w"> </span><span class="nx">stegoImage</span><span class="p">;</span>

<span class="w">    </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">handleError</span><span class="p">(</span><span class="s1">&#39;HIDE_MESSAGE_FAILED&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">);</span>
<span class="w">      </span><span class="k">throw</span><span class="w"> </span><span class="nx">error</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">revealMessage</span><span class="p">(</span><span class="nx">stegoImage</span><span class="p">,</span><span class="w"> </span><span class="nx">options</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{})</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="c1">// Validate image</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">validateImage</span><span class="p">(</span><span class="nx">stegoImage</span><span class="p">);</span>

<span class="w">      </span><span class="c1">// Extract message</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">extractedData</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">engine</span><span class="p">.</span><span class="nx">revealMessage</span><span class="p">(</span><span class="nx">stegoImage</span><span class="p">);</span>

<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="nx">extractedData</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="kc">null</span><span class="p">;</span><span class="w"> </span><span class="c1">// No hidden message found</span>
<span class="w">      </span><span class="p">}</span>

<span class="w">      </span><span class="c1">// Apply security verification</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">message</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">securityManager</span><span class="p">.</span><span class="nx">verifyMessage</span><span class="p">(</span>
<span class="w">        </span><span class="nx">extractedData</span><span class="p">,</span>
<span class="w">        </span><span class="nx">options</span><span class="p">.</span><span class="nx">sessionKey</span>
<span class="w">      </span><span class="p">);</span>

<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">emit</span><span class="p">(</span><span class="s1">&#39;messageRevealed&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">imageSize</span><span class="o">:</span><span class="w"> </span><span class="nx">stegoImage</span><span class="p">.</span><span class="nx">size</span><span class="p">,</span>
<span class="w">        </span><span class="nx">messageLength</span><span class="o">:</span><span class="w"> </span><span class="nx">message</span><span class="p">.</span><span class="nx">length</span><span class="p">,</span>
<span class="w">        </span><span class="nx">extractionTime</span><span class="o">:</span><span class="w"> </span><span class="nx">performance</span><span class="p">.</span><span class="nx">now</span><span class="p">()</span>
<span class="w">      </span><span class="p">});</span>

<span class="w">      </span><span class="k">return</span><span class="w"> </span><span class="nx">message</span><span class="p">;</span>

<span class="w">    </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">handleError</span><span class="p">(</span><span class="s1">&#39;REVEAL_MESSAGE_FAILED&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">);</span>
<span class="w">      </span><span class="k">throw</span><span class="w"> </span><span class="nx">error</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="lsb-algorithm-implementation">
<h3><a class="toc-backref" href="#id6" role="doc-backlink">LSB Algorithm Implementation</a><a class="headerlink" href="#lsb-algorithm-implementation" title="Link to this heading">¶</a></h3>
<p>Custom LSB algorithm with security enhancements:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">class</span><span class="w"> </span><span class="nx">AdaptiveLSBAlgorithm</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span><span class="nx">sessionKey</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">sessionKey</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">sessionKey</span><span class="p">;</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">prng</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">SecurePRNG</span><span class="p">(</span><span class="nx">sessionKey</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">embedMessage</span><span class="p">(</span><span class="nx">imageData</span><span class="p">,</span><span class="w"> </span><span class="nx">messageData</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">pixels</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">imageData</span><span class="p">.</span><span class="nx">data</span><span class="p">;</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">messageBits</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">messageToBits</span><span class="p">(</span><span class="nx">messageData</span><span class="p">);</span>

<span class="w">    </span><span class="c1">// Generate adaptive embedding positions</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">positions</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">generateEmbeddingPositions</span><span class="p">(</span>
<span class="w">      </span><span class="nx">imageData</span><span class="p">,</span>
<span class="w">      </span><span class="nx">messageBits</span><span class="p">.</span><span class="nx">length</span>
<span class="w">    </span><span class="p">);</span>

<span class="w">    </span><span class="c1">// Embed message bits</span>
<span class="w">    </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="kd">let</span><span class="w"> </span><span class="nx">i</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0</span><span class="p">;</span><span class="w"> </span><span class="nx">i</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="nx">messageBits</span><span class="p">.</span><span class="nx">length</span><span class="p">;</span><span class="w"> </span><span class="nx">i</span><span class="o">++</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">position</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">positions</span><span class="p">[</span><span class="nx">i</span><span class="p">];</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">pixelIndex</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">position</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mf">4</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mf">3</span><span class="p">;</span><span class="w"> </span><span class="c1">// Alpha channel</span>

<span class="w">      </span><span class="c1">// Modify LSB</span>
<span class="w">      </span><span class="nx">pixels</span><span class="p">[</span><span class="nx">pixelIndex</span><span class="p">]</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="nx">pixels</span><span class="p">[</span><span class="nx">pixelIndex</span><span class="p">]</span><span class="w"> </span><span class="o">&amp;</span><span class="w"> </span><span class="mh">0xFE</span><span class="p">)</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="nx">messageBits</span><span class="p">[</span><span class="nx">i</span><span class="p">];</span>

<span class="w">      </span><span class="c1">// Apply noise injection randomly</span>
<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">prng</span><span class="p">.</span><span class="nx">nextFloat</span><span class="p">()</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mf">0.1</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">injectNoise</span><span class="p">(</span><span class="nx">pixels</span><span class="p">,</span><span class="w"> </span><span class="nx">position</span><span class="p">);</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nx">imageData</span><span class="p">;</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">extractMessage</span><span class="p">(</span><span class="nx">imageData</span><span class="p">,</span><span class="w"> </span><span class="nx">messageLength</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">pixels</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">imageData</span><span class="p">.</span><span class="nx">data</span><span class="p">;</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">positions</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">generateEmbeddingPositions</span><span class="p">(</span><span class="nx">imageData</span><span class="p">,</span><span class="w"> </span><span class="nx">messageLength</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mf">8</span><span class="p">);</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">messageBits</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[];</span>

<span class="w">    </span><span class="c1">// Extract message bits</span>
<span class="w">    </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="kd">const</span><span class="w"> </span><span class="nx">position</span><span class="w"> </span><span class="k">of</span><span class="w"> </span><span class="nx">positions</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">pixelIndex</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">position</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mf">4</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mf">3</span><span class="p">;</span><span class="w"> </span><span class="c1">// Alpha channel</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">bit</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">pixels</span><span class="p">[</span><span class="nx">pixelIndex</span><span class="p">]</span><span class="w"> </span><span class="o">&amp;</span><span class="w"> </span><span class="mh">0x01</span><span class="p">;</span>
<span class="w">      </span><span class="nx">messageBits</span><span class="p">.</span><span class="nx">push</span><span class="p">(</span><span class="nx">bit</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">bitsToMessage</span><span class="p">(</span><span class="nx">messageBits</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">generateEmbeddingPositions</span><span class="p">(</span><span class="nx">imageData</span><span class="p">,</span><span class="w"> </span><span class="nx">bitCount</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">totalPixels</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">imageData</span><span class="p">.</span><span class="nx">width</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="nx">imageData</span><span class="p">.</span><span class="nx">height</span><span class="p">;</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">positions</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[];</span>

<span class="w">    </span><span class="c1">// Use session key for deterministic randomness</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">prng</span><span class="p">.</span><span class="nx">seed</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">sessionKey</span><span class="p">);</span>

<span class="w">    </span><span class="k">while</span><span class="w"> </span><span class="p">(</span><span class="nx">positions</span><span class="p">.</span><span class="nx">length</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="nx">bitCount</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">position</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">prng</span><span class="p">.</span><span class="nx">nextInt</span><span class="p">(</span><span class="nx">totalPixels</span><span class="p">);</span>

<span class="w">      </span><span class="c1">// Check if position is suitable</span>
<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">isPositionSuitable</span><span class="p">(</span><span class="nx">imageData</span><span class="p">,</span><span class="w"> </span><span class="nx">position</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">positions</span><span class="p">.</span><span class="nx">push</span><span class="p">(</span><span class="nx">position</span><span class="p">);</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nx">positions</span><span class="p">;</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">isPositionSuitable</span><span class="p">(</span><span class="nx">imageData</span><span class="p">,</span><span class="w"> </span><span class="nx">position</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">x</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">position</span><span class="w"> </span><span class="o">%</span><span class="w"> </span><span class="nx">imageData</span><span class="p">.</span><span class="nx">width</span><span class="p">;</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">y</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">Math</span><span class="p">.</span><span class="nx">floor</span><span class="p">(</span><span class="nx">position</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="nx">imageData</span><span class="p">.</span><span class="nx">width</span><span class="p">);</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">pixelIndex</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="nx">y</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="nx">imageData</span><span class="p">.</span><span class="nx">width</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="nx">x</span><span class="p">)</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mf">4</span><span class="p">;</span>

<span class="w">    </span><span class="c1">// Check alpha channel value</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">alpha</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">imageData</span><span class="p">.</span><span class="nx">data</span><span class="p">[</span><span class="nx">pixelIndex</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mf">3</span><span class="p">];</span>

<span class="w">    </span><span class="c1">// Avoid fully transparent or opaque pixels</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nx">alpha</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mf">10</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="nx">alpha</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mf">245</span><span class="p">;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="platform-integration">
<h2><a class="toc-backref" href="#id7" role="doc-backlink">Platform Integration</a><a class="headerlink" href="#platform-integration" title="Link to this heading">¶</a></h2>
<section id="browser-extension-integration">
<h3><a class="toc-backref" href="#id8" role="doc-backlink">Browser Extension Integration</a><a class="headerlink" href="#browser-extension-integration" title="Link to this heading">¶</a></h3>
<p>Seamless integration with social media platforms:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">class</span><span class="w"> </span><span class="nx">PlatformSteganography</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">platforms</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nb">Map</span><span class="p">();</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">stegoManager</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">SteganographyManager</span><span class="p">();</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">setupPlatformHandlers</span><span class="p">();</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">setupPlatformHandlers</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Facebook integration</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">platforms</span><span class="p">.</span><span class="nx">set</span><span class="p">(</span><span class="s1">&#39;facebook.com&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">name</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;Facebook&#39;</span><span class="p">,</span>
<span class="w">      </span><span class="nx">imageSelectors</span><span class="o">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="s1">&#39;input[type=&quot;file&quot;][accept*=&quot;image&quot;]&#39;</span><span class="p">,</span>
<span class="w">        </span><span class="s1">&#39;img[src*=&quot;scontent&quot;]&#39;</span>
<span class="w">      </span><span class="p">],</span>
<span class="w">      </span><span class="nx">uploadInterceptor</span><span class="o">:</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">interceptFacebookUpload</span><span class="p">.</span><span class="nx">bind</span><span class="p">(</span><span class="k">this</span><span class="p">),</span>
<span class="w">      </span><span class="nx">downloadProcessor</span><span class="o">:</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">processFacebookImages</span><span class="p">.</span><span class="nx">bind</span><span class="p">(</span><span class="k">this</span><span class="p">)</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="c1">// Instagram integration</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">platforms</span><span class="p">.</span><span class="nx">set</span><span class="p">(</span><span class="s1">&#39;instagram.com&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">name</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;Instagram&#39;</span><span class="p">,</span>
<span class="w">      </span><span class="nx">imageSelectors</span><span class="o">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="s1">&#39;input[type=&quot;file&quot;]&#39;</span><span class="p">,</span>
<span class="w">        </span><span class="s1">&#39;img[src*=&quot;cdninstagram&quot;]&#39;</span>
<span class="w">      </span><span class="p">],</span>
<span class="w">      </span><span class="nx">uploadInterceptor</span><span class="o">:</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">interceptInstagramUpload</span><span class="p">.</span><span class="nx">bind</span><span class="p">(</span><span class="k">this</span><span class="p">),</span>
<span class="w">      </span><span class="nx">downloadProcessor</span><span class="o">:</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">processInstagramImages</span><span class="p">.</span><span class="nx">bind</span><span class="p">(</span><span class="k">this</span><span class="p">)</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="c1">// Discord integration</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">platforms</span><span class="p">.</span><span class="nx">set</span><span class="p">(</span><span class="s1">&#39;discord.com&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">name</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;Discord&#39;</span><span class="p">,</span>
<span class="w">      </span><span class="nx">imageSelectors</span><span class="o">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="s1">&#39;input[type=&quot;file&quot;]&#39;</span><span class="p">,</span>
<span class="w">        </span><span class="s1">&#39;img[src*=&quot;cdn.discordapp.com&quot;]&#39;</span>
<span class="w">      </span><span class="p">],</span>
<span class="w">      </span><span class="nx">uploadInterceptor</span><span class="o">:</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">interceptDiscordUpload</span><span class="p">.</span><span class="nx">bind</span><span class="p">(</span><span class="k">this</span><span class="p">),</span>
<span class="w">      </span><span class="nx">downloadProcessor</span><span class="o">:</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">processDiscordImages</span><span class="p">.</span><span class="nx">bind</span><span class="p">(</span><span class="k">this</span><span class="p">)</span>
<span class="w">    </span><span class="p">});</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">interceptUpload</span><span class="p">(</span><span class="nx">fileInput</span><span class="p">,</span><span class="w"> </span><span class="nx">platform</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">files</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">Array</span><span class="p">.</span><span class="kr">from</span><span class="p">(</span><span class="nx">fileInput</span><span class="p">.</span><span class="nx">files</span><span class="p">);</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">processedFiles</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[];</span>

<span class="w">    </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="kd">const</span><span class="w"> </span><span class="nx">file</span><span class="w"> </span><span class="k">of</span><span class="w"> </span><span class="nx">files</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">isImageFile</span><span class="p">(</span><span class="nx">file</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="kd">const</span><span class="w"> </span><span class="nx">shouldHideMessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">promptForSteganography</span><span class="p">(</span><span class="nx">platform</span><span class="p">);</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">shouldHideMessage</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="kd">const</span><span class="w"> </span><span class="nx">message</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">getMessageToHide</span><span class="p">();</span>
<span class="w">          </span><span class="kd">const</span><span class="w"> </span><span class="nx">stegoFile</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">stegoManager</span><span class="p">.</span><span class="nx">hideMessage</span><span class="p">(</span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="nx">file</span><span class="p">);</span>
<span class="w">          </span><span class="nx">processedFiles</span><span class="p">.</span><span class="nx">push</span><span class="p">(</span><span class="nx">stegoFile</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nx">processedFiles</span><span class="p">.</span><span class="nx">push</span><span class="p">(</span><span class="nx">file</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">      </span><span class="p">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">processedFiles</span><span class="p">.</span><span class="nx">push</span><span class="p">(</span><span class="nx">file</span><span class="p">);</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nx">processedFiles</span><span class="p">;</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">processDownloadedImages</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">platform</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">getCurrentPlatform</span><span class="p">();</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">config</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">platforms</span><span class="p">.</span><span class="nx">get</span><span class="p">(</span><span class="nx">platform</span><span class="p">);</span>

<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="nx">config</span><span class="p">)</span><span class="w"> </span><span class="k">return</span><span class="p">;</span>

<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">images</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">document</span><span class="p">.</span><span class="nx">querySelectorAll</span><span class="p">(</span><span class="nx">config</span><span class="p">.</span><span class="nx">imageSelectors</span><span class="p">.</span><span class="nx">join</span><span class="p">(</span><span class="s1">&#39;, &#39;</span><span class="p">));</span>

<span class="w">    </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="kd">const</span><span class="w"> </span><span class="nx">img</span><span class="w"> </span><span class="k">of</span><span class="w"> </span><span class="nx">images</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">shouldProcessImage</span><span class="p">(</span><span class="nx">img</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">checkForHiddenMessage</span><span class="p">(</span><span class="nx">img</span><span class="p">);</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">checkForHiddenMessage</span><span class="p">(</span><span class="nx">imgElement</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="c1">// Convert image to blob</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">canvas</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">document</span><span class="p">.</span><span class="nx">createElement</span><span class="p">(</span><span class="s1">&#39;canvas&#39;</span><span class="p">);</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">ctx</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">canvas</span><span class="p">.</span><span class="nx">getContext</span><span class="p">(</span><span class="s1">&#39;2d&#39;</span><span class="p">);</span>

<span class="w">      </span><span class="nx">canvas</span><span class="p">.</span><span class="nx">width</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">imgElement</span><span class="p">.</span><span class="nx">naturalWidth</span><span class="p">;</span>
<span class="w">      </span><span class="nx">canvas</span><span class="p">.</span><span class="nx">height</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">imgElement</span><span class="p">.</span><span class="nx">naturalHeight</span><span class="p">;</span>
<span class="w">      </span><span class="nx">ctx</span><span class="p">.</span><span class="nx">drawImage</span><span class="p">(</span><span class="nx">imgElement</span><span class="p">,</span><span class="w"> </span><span class="mf">0</span><span class="p">,</span><span class="w"> </span><span class="mf">0</span><span class="p">);</span>

<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">blob</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nb">Promise</span><span class="p">(</span><span class="nx">resolve</span><span class="w"> </span><span class="p">=&gt;</span>
<span class="w">        </span><span class="nx">canvas</span><span class="p">.</span><span class="nx">toBlob</span><span class="p">(</span><span class="nx">resolve</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;image/png&#39;</span><span class="p">)</span>
<span class="w">      </span><span class="p">);</span>

<span class="w">      </span><span class="c1">// Check for hidden message</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">hiddenMessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">stegoManager</span><span class="p">.</span><span class="nx">revealMessage</span><span class="p">(</span><span class="nx">blob</span><span class="p">);</span>

<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">hiddenMessage</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">displayHiddenMessage</span><span class="p">(</span><span class="nx">hiddenMessage</span><span class="p">,</span><span class="w"> </span><span class="nx">imgElement</span><span class="p">);</span>
<span class="w">      </span><span class="p">}</span>

<span class="w">    </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">console</span><span class="p">.</span><span class="nx">error</span><span class="p">(</span><span class="s1">&#39;Error checking for hidden message:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="multi-image-distribution">
<h3><a class="toc-backref" href="#id9" role="doc-backlink">Multi-Image Distribution</a><a class="headerlink" href="#multi-image-distribution" title="Link to this heading">¶</a></h3>
<p>Handling large messages across multiple images:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">class</span><span class="w"> </span><span class="nx">MultiImageSteganography</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span><span class="nx">stegoEngine</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">engine</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">stegoEngine</span><span class="p">;</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">maxChunkSize</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">8000</span><span class="p">;</span><span class="w"> </span><span class="c1">// 8KB per image</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">distributeMessage</span><span class="p">(</span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="nx">coverImages</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Split message into chunks</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">chunks</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">splitMessage</span><span class="p">(</span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">maxChunkSize</span><span class="p">);</span>

<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">chunks</span><span class="p">.</span><span class="nx">length</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="nx">coverImages</span><span class="p">.</span><span class="nx">length</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">throw</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="ne">Error</span><span class="p">(</span><span class="s1">&#39;Not enough cover images for message distribution&#39;</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">stegoImages</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[];</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">manifest</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">createManifest</span><span class="p">(</span><span class="nx">chunks</span><span class="p">.</span><span class="nx">length</span><span class="p">,</span><span class="w"> </span><span class="nx">message</span><span class="p">.</span><span class="nx">length</span><span class="p">);</span>

<span class="w">    </span><span class="c1">// Embed manifest in first image</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">firstStego</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">engine</span><span class="p">.</span><span class="nx">hideMessage</span><span class="p">(</span>
<span class="w">      </span><span class="nx">coverImages</span><span class="p">[</span><span class="mf">0</span><span class="p">],</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">serializeManifest</span><span class="p">(</span><span class="nx">manifest</span><span class="p">)</span>
<span class="w">    </span><span class="p">);</span>
<span class="w">    </span><span class="nx">stegoImages</span><span class="p">.</span><span class="nx">push</span><span class="p">(</span><span class="nx">firstStego</span><span class="p">);</span>

<span class="w">    </span><span class="c1">// Embed message chunks in remaining images</span>
<span class="w">    </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="kd">let</span><span class="w"> </span><span class="nx">i</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0</span><span class="p">;</span><span class="w"> </span><span class="nx">i</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="nx">chunks</span><span class="p">.</span><span class="nx">length</span><span class="p">;</span><span class="w"> </span><span class="nx">i</span><span class="o">++</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">chunkData</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">createChunkData</span><span class="p">(</span><span class="nx">i</span><span class="p">,</span><span class="w"> </span><span class="nx">chunks</span><span class="p">[</span><span class="nx">i</span><span class="p">]);</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">stegoImage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">engine</span><span class="p">.</span><span class="nx">hideMessage</span><span class="p">(</span>
<span class="w">        </span><span class="nx">coverImages</span><span class="p">[</span><span class="nx">i</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mf">1</span><span class="p">],</span>
<span class="w">        </span><span class="nx">chunkData</span>
<span class="w">      </span><span class="p">);</span>
<span class="w">      </span><span class="nx">stegoImages</span><span class="p">.</span><span class="nx">push</span><span class="p">(</span><span class="nx">stegoImage</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">images</span><span class="o">:</span><span class="w"> </span><span class="nx">stegoImages</span><span class="p">,</span>
<span class="w">      </span><span class="nx">manifest</span><span class="o">:</span><span class="w"> </span><span class="nx">manifest</span><span class="p">,</span>
<span class="w">      </span><span class="nx">totalChunks</span><span class="o">:</span><span class="w"> </span><span class="nx">chunks</span><span class="p">.</span><span class="nx">length</span>
<span class="w">    </span><span class="p">};</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">reconstructMessage</span><span class="p">(</span><span class="nx">stegoImages</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Extract manifest from first image</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">manifestData</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">engine</span><span class="p">.</span><span class="nx">revealMessage</span><span class="p">(</span><span class="nx">stegoImages</span><span class="p">[</span><span class="mf">0</span><span class="p">]);</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="nx">manifestData</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">throw</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="ne">Error</span><span class="p">(</span><span class="s1">&#39;No manifest found in first image&#39;</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">manifest</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">parseManifest</span><span class="p">(</span><span class="nx">manifestData</span><span class="p">);</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">chunks</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nb">Array</span><span class="p">(</span><span class="nx">manifest</span><span class="p">.</span><span class="nx">totalChunks</span><span class="p">);</span>

<span class="w">    </span><span class="c1">// Extract chunks from remaining images</span>
<span class="w">    </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="kd">let</span><span class="w"> </span><span class="nx">i</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">1</span><span class="p">;</span><span class="w"> </span><span class="nx">i</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="nx">stegoImages</span><span class="p">.</span><span class="nx">length</span><span class="p">;</span><span class="w"> </span><span class="nx">i</span><span class="o">++</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">chunkData</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">engine</span><span class="p">.</span><span class="nx">revealMessage</span><span class="p">(</span><span class="nx">stegoImages</span><span class="p">[</span><span class="nx">i</span><span class="p">]);</span>
<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">chunkData</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="kd">const</span><span class="w"> </span><span class="nx">chunk</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">parseChunkData</span><span class="p">(</span><span class="nx">chunkData</span><span class="p">);</span>
<span class="w">        </span><span class="nx">chunks</span><span class="p">[</span><span class="nx">chunk</span><span class="p">.</span><span class="nx">index</span><span class="p">]</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">chunk</span><span class="p">.</span><span class="nx">data</span><span class="p">;</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="c1">// Verify all chunks received</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">missingChunks</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">chunks</span><span class="p">.</span><span class="nx">findIndex</span><span class="p">(</span><span class="nx">chunk</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">chunk</span><span class="w"> </span><span class="o">===</span><span class="w"> </span><span class="kc">undefined</span><span class="p">);</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">missingChunks</span><span class="w"> </span><span class="o">!==</span><span class="w"> </span><span class="o">-</span><span class="mf">1</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">throw</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="ne">Error</span><span class="p">(</span><span class="sb">`Missing chunk </span><span class="si">${</span><span class="nx">missingChunks</span><span class="si">}</span><span class="sb">`</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="c1">// Reconstruct original message</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">combineChunks</span><span class="p">(</span><span class="nx">chunks</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">createManifest</span><span class="p">(</span><span class="nx">totalChunks</span><span class="p">,</span><span class="w"> </span><span class="nx">originalLength</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">version</span><span class="o">:</span><span class="w"> </span><span class="mf">1</span><span class="p">,</span>
<span class="w">      </span><span class="nx">totalChunks</span><span class="o">:</span><span class="w"> </span><span class="nx">totalChunks</span><span class="p">,</span>
<span class="w">      </span><span class="nx">originalLength</span><span class="o">:</span><span class="w"> </span><span class="nx">originalLength</span><span class="p">,</span>
<span class="w">      </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="nb">Date</span><span class="p">.</span><span class="nx">now</span><span class="p">(),</span>
<span class="w">      </span><span class="nx">checksum</span><span class="o">:</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">calculateChecksum</span><span class="p">(</span><span class="nx">originalLength</span><span class="p">.</span><span class="nx">toString</span><span class="p">())</span>
<span class="w">    </span><span class="p">};</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="security-implementation">
<h2><a class="toc-backref" href="#id10" role="doc-backlink">Security Implementation</a><a class="headerlink" href="#security-implementation" title="Link to this heading">¶</a></h2>
<section id="statistical-security">
<h3><a class="toc-backref" href="#id11" role="doc-backlink">Statistical Security</a><a class="headerlink" href="#statistical-security" title="Link to this heading">¶</a></h3>
<p>Advanced security measures to prevent detection:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">class</span><span class="w"> </span><span class="nx">StatisticalSecurityManager</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span><span class="nx">sessionKey</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">sessionKey</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">sessionKey</span><span class="p">;</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">prng</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">SecurePRNG</span><span class="p">(</span><span class="nx">sessionKey</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">enhanceSecurityBeforeEmbedding</span><span class="p">(</span><span class="nx">imageData</span><span class="p">,</span><span class="w"> </span><span class="nx">messageData</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Analyze image characteristics</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">analysis</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">analyzeImage</span><span class="p">(</span><span class="nx">imageData</span><span class="p">);</span>

<span class="w">    </span><span class="c1">// Adjust embedding parameters based on analysis</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">embeddingParams</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">optimizeEmbeddingParameters</span><span class="p">(</span><span class="nx">analysis</span><span class="p">);</span>

<span class="w">    </span><span class="c1">// Add statistical noise</span>
<span class="w">    </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">injectStatisticalNoise</span><span class="p">(</span><span class="nx">imageData</span><span class="p">,</span><span class="w"> </span><span class="nx">embeddingParams</span><span class="p">);</span>

<span class="w">    </span><span class="c1">// Randomize message order</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">randomizedMessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">randomizeMessageBits</span><span class="p">(</span><span class="nx">messageData</span><span class="p">);</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">imageData</span><span class="o">:</span><span class="w"> </span><span class="nx">imageData</span><span class="p">,</span>
<span class="w">      </span><span class="nx">messageData</span><span class="o">:</span><span class="w"> </span><span class="nx">randomizedMessage</span><span class="p">,</span>
<span class="w">      </span><span class="nx">parameters</span><span class="o">:</span><span class="w"> </span><span class="nx">embeddingParams</span>
<span class="w">    </span><span class="p">};</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">analyzeImage</span><span class="p">(</span><span class="nx">imageData</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">pixels</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">imageData</span><span class="p">.</span><span class="nx">data</span><span class="p">;</span>
<span class="w">    </span><span class="kd">let</span><span class="w"> </span><span class="nx">complexity</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0</span><span class="p">;</span>
<span class="w">    </span><span class="kd">let</span><span class="w"> </span><span class="nx">noiseLevel</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0</span><span class="p">;</span>

<span class="w">    </span><span class="c1">// Calculate image complexity</span>
<span class="w">    </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="kd">let</span><span class="w"> </span><span class="nx">i</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0</span><span class="p">;</span><span class="w"> </span><span class="nx">i</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="nx">pixels</span><span class="p">.</span><span class="nx">length</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="mf">4</span><span class="p">;</span><span class="w"> </span><span class="nx">i</span><span class="w"> </span><span class="o">+=</span><span class="w"> </span><span class="mf">4</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">current</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">pixels</span><span class="p">[</span><span class="nx">i</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mf">3</span><span class="p">];</span><span class="w"> </span><span class="c1">// Alpha channel</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">next</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">pixels</span><span class="p">[</span><span class="nx">i</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mf">7</span><span class="p">];</span>
<span class="w">      </span><span class="nx">complexity</span><span class="w"> </span><span class="o">+=</span><span class="w"> </span><span class="nb">Math</span><span class="p">.</span><span class="nx">abs</span><span class="p">(</span><span class="nx">current</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="nx">next</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="c1">// Calculate noise level</span>
<span class="w">    </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="kd">let</span><span class="w"> </span><span class="nx">i</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0</span><span class="p">;</span><span class="w"> </span><span class="nx">i</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="nx">pixels</span><span class="p">.</span><span class="nx">length</span><span class="p">;</span><span class="w"> </span><span class="nx">i</span><span class="w"> </span><span class="o">+=</span><span class="w"> </span><span class="mf">4</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">alpha</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">pixels</span><span class="p">[</span><span class="nx">i</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mf">3</span><span class="p">];</span>
<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">alpha</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mf">0</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="nx">alpha</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mf">255</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">noiseLevel</span><span class="o">++</span><span class="p">;</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">complexity</span><span class="o">:</span><span class="w"> </span><span class="nx">complexity</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="p">(</span><span class="nx">pixels</span><span class="p">.</span><span class="nx">length</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="mf">4</span><span class="p">),</span>
<span class="w">      </span><span class="nx">noiseLevel</span><span class="o">:</span><span class="w"> </span><span class="nx">noiseLevel</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="p">(</span><span class="nx">pixels</span><span class="p">.</span><span class="nx">length</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="mf">4</span><span class="p">),</span>
<span class="w">      </span><span class="nx">totalPixels</span><span class="o">:</span><span class="w"> </span><span class="nx">pixels</span><span class="p">.</span><span class="nx">length</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="mf">4</span>
<span class="w">    </span><span class="p">};</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">injectStatisticalNoise</span><span class="p">(</span><span class="nx">imageData</span><span class="p">,</span><span class="w"> </span><span class="nx">params</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">pixels</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">imageData</span><span class="p">.</span><span class="nx">data</span><span class="p">;</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">noiseIntensity</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">Math</span><span class="p">.</span><span class="nx">min</span><span class="p">(</span><span class="mf">0.1</span><span class="p">,</span><span class="w"> </span><span class="nx">params</span><span class="p">.</span><span class="nx">complexity</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="mf">1000</span><span class="p">);</span>

<span class="w">    </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="kd">let</span><span class="w"> </span><span class="nx">i</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">3</span><span class="p">;</span><span class="w"> </span><span class="nx">i</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="nx">pixels</span><span class="p">.</span><span class="nx">length</span><span class="p">;</span><span class="w"> </span><span class="nx">i</span><span class="w"> </span><span class="o">+=</span><span class="w"> </span><span class="mf">4</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">prng</span><span class="p">.</span><span class="nx">nextFloat</span><span class="p">()</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="nx">noiseIntensity</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="kd">const</span><span class="w"> </span><span class="nx">noise</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">prng</span><span class="p">.</span><span class="nx">nextInt</span><span class="p">(</span><span class="mf">3</span><span class="p">)</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="mf">1</span><span class="p">;</span><span class="w"> </span><span class="c1">// -1, 0, or 1</span>
<span class="w">        </span><span class="nx">pixels</span><span class="p">[</span><span class="nx">i</span><span class="p">]</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">Math</span><span class="p">.</span><span class="nx">max</span><span class="p">(</span><span class="mf">0</span><span class="p">,</span><span class="w"> </span><span class="nb">Math</span><span class="p">.</span><span class="nx">min</span><span class="p">(</span><span class="mf">255</span><span class="p">,</span><span class="w"> </span><span class="nx">pixels</span><span class="p">[</span><span class="nx">i</span><span class="p">]</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="nx">noise</span><span class="p">));</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">randomizeMessageBits</span><span class="p">(</span><span class="nx">messageData</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">bits</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">messageToBits</span><span class="p">(</span><span class="nx">messageData</span><span class="p">);</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">positions</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">Array</span><span class="p">.</span><span class="kr">from</span><span class="p">({</span><span class="w"> </span><span class="nx">length</span><span class="o">:</span><span class="w"> </span><span class="nx">bits</span><span class="p">.</span><span class="nx">length</span><span class="w"> </span><span class="p">},</span><span class="w"> </span><span class="p">(</span><span class="nx">_</span><span class="p">,</span><span class="w"> </span><span class="nx">i</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">i</span><span class="p">);</span>

<span class="w">    </span><span class="c1">// Fisher-Yates shuffle with deterministic randomness</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">prng</span><span class="p">.</span><span class="nx">seed</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">sessionKey</span><span class="p">);</span>
<span class="w">    </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="kd">let</span><span class="w"> </span><span class="nx">i</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">positions</span><span class="p">.</span><span class="nx">length</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="mf">1</span><span class="p">;</span><span class="w"> </span><span class="nx">i</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mf">0</span><span class="p">;</span><span class="w"> </span><span class="nx">i</span><span class="o">--</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">j</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">prng</span><span class="p">.</span><span class="nx">nextInt</span><span class="p">(</span><span class="nx">i</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mf">1</span><span class="p">);</span>
<span class="w">      </span><span class="p">[</span><span class="nx">positions</span><span class="p">[</span><span class="nx">i</span><span class="p">],</span><span class="w"> </span><span class="nx">positions</span><span class="p">[</span><span class="nx">j</span><span class="p">]]</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[</span><span class="nx">positions</span><span class="p">[</span><span class="nx">j</span><span class="p">],</span><span class="w"> </span><span class="nx">positions</span><span class="p">[</span><span class="nx">i</span><span class="p">]];</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">bits</span><span class="o">:</span><span class="w"> </span><span class="nx">bits</span><span class="p">,</span>
<span class="w">      </span><span class="nx">positions</span><span class="o">:</span><span class="w"> </span><span class="nx">positions</span>
<span class="w">    </span><span class="p">};</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="performance-optimization">
<h2><a class="toc-backref" href="#id12" role="doc-backlink">Performance Optimization</a><a class="headerlink" href="#performance-optimization" title="Link to this heading">¶</a></h2>
<section id="web-worker-implementation">
<h3><a class="toc-backref" href="#id13" role="doc-backlink">Web Worker Implementation</a><a class="headerlink" href="#web-worker-implementation" title="Link to this heading">¶</a></h3>
<p>Parallel processing for large images:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">class</span><span class="w"> </span><span class="nx">SteganographyWorkerPool</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span><span class="nx">workerCount</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">4</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">workers</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[];</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">taskQueue</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[];</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">activeJobs</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nb">Map</span><span class="p">();</span>

<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">initializeWorkers</span><span class="p">(</span><span class="nx">workerCount</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">initializeWorkers</span><span class="p">(</span><span class="nx">count</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="kd">let</span><span class="w"> </span><span class="nx">i</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0</span><span class="p">;</span><span class="w"> </span><span class="nx">i</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="nx">count</span><span class="p">;</span><span class="w"> </span><span class="nx">i</span><span class="o">++</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">worker</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">Worker</span><span class="p">(</span><span class="s1">&#39;/workers/steganography-worker.js&#39;</span><span class="p">);</span>

<span class="w">      </span><span class="nx">worker</span><span class="p">.</span><span class="nx">onmessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="nx">event</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">handleWorkerMessage</span><span class="p">(</span><span class="nx">worker</span><span class="p">,</span><span class="w"> </span><span class="nx">event</span><span class="p">.</span><span class="nx">data</span><span class="p">);</span>
<span class="w">      </span><span class="p">};</span>

<span class="w">      </span><span class="nx">worker</span><span class="p">.</span><span class="nx">onerror</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">handleWorkerError</span><span class="p">(</span><span class="nx">worker</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">);</span>
<span class="w">      </span><span class="p">};</span>

<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">workers</span><span class="p">.</span><span class="nx">push</span><span class="p">({</span>
<span class="w">        </span><span class="nx">worker</span><span class="o">:</span><span class="w"> </span><span class="nx">worker</span><span class="p">,</span>
<span class="w">        </span><span class="nx">busy</span><span class="o">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span>
<span class="w">        </span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="nx">i</span>
<span class="w">      </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">processImageChunks</span><span class="p">(</span><span class="nx">imageData</span><span class="p">,</span><span class="w"> </span><span class="nx">messageData</span><span class="p">,</span><span class="w"> </span><span class="nx">operation</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">chunks</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">splitImageIntoChunks</span><span class="p">(</span><span class="nx">imageData</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">workers</span><span class="p">.</span><span class="nx">length</span><span class="p">);</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">messageChunks</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">splitMessage</span><span class="p">(</span><span class="nx">messageData</span><span class="p">,</span><span class="w"> </span><span class="nx">chunks</span><span class="p">.</span><span class="nx">length</span><span class="p">);</span>

<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">promises</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">chunks</span><span class="p">.</span><span class="nx">map</span><span class="p">((</span><span class="nx">chunk</span><span class="p">,</span><span class="w"> </span><span class="nx">index</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">executeTask</span><span class="p">({</span>
<span class="w">        </span><span class="nx">operation</span><span class="o">:</span><span class="w"> </span><span class="nx">operation</span><span class="p">,</span>
<span class="w">        </span><span class="nx">imageChunk</span><span class="o">:</span><span class="w"> </span><span class="nx">chunk</span><span class="p">,</span>
<span class="w">        </span><span class="nx">messageChunk</span><span class="o">:</span><span class="w"> </span><span class="nx">messageChunks</span><span class="p">[</span><span class="nx">index</span><span class="p">],</span>
<span class="w">        </span><span class="nx">chunkIndex</span><span class="o">:</span><span class="w"> </span><span class="nx">index</span>
<span class="w">      </span><span class="p">});</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">results</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nb">Promise</span><span class="p">.</span><span class="nx">all</span><span class="p">(</span><span class="nx">promises</span><span class="p">);</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">mergeChunkResults</span><span class="p">(</span><span class="nx">results</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">executeTask</span><span class="p">(</span><span class="nx">task</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nb">Promise</span><span class="p">((</span><span class="nx">resolve</span><span class="p">,</span><span class="w"> </span><span class="nx">reject</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">taskId</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">generateTaskId</span><span class="p">();</span>

<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">activeJobs</span><span class="p">.</span><span class="nx">set</span><span class="p">(</span><span class="nx">taskId</span><span class="p">,</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">resolve</span><span class="o">:</span><span class="w"> </span><span class="nx">resolve</span><span class="p">,</span>
<span class="w">        </span><span class="nx">reject</span><span class="o">:</span><span class="w"> </span><span class="nx">reject</span><span class="p">,</span>
<span class="w">        </span><span class="nx">task</span><span class="o">:</span><span class="w"> </span><span class="nx">task</span>
<span class="w">      </span><span class="p">});</span>

<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">taskQueue</span><span class="p">.</span><span class="nx">push</span><span class="p">({</span>
<span class="w">        </span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="nx">taskId</span><span class="p">,</span>
<span class="w">        </span><span class="nx">task</span><span class="o">:</span><span class="w"> </span><span class="nx">task</span>
<span class="w">      </span><span class="p">});</span>

<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">processQueue</span><span class="p">();</span>
<span class="w">    </span><span class="p">});</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">processQueue</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">taskQueue</span><span class="p">.</span><span class="nx">length</span><span class="w"> </span><span class="o">===</span><span class="w"> </span><span class="mf">0</span><span class="p">)</span><span class="w"> </span><span class="k">return</span><span class="p">;</span>

<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">availableWorker</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">workers</span><span class="p">.</span><span class="nx">find</span><span class="p">(</span><span class="nx">w</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="o">!</span><span class="nx">w</span><span class="p">.</span><span class="nx">busy</span><span class="p">);</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="nx">availableWorker</span><span class="p">)</span><span class="w"> </span><span class="k">return</span><span class="p">;</span>

<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">queueItem</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">taskQueue</span><span class="p">.</span><span class="nx">shift</span><span class="p">();</span>
<span class="w">    </span><span class="nx">availableWorker</span><span class="p">.</span><span class="nx">busy</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>

<span class="w">    </span><span class="nx">availableWorker</span><span class="p">.</span><span class="nx">worker</span><span class="p">.</span><span class="nx">postMessage</span><span class="p">({</span>
<span class="w">      </span><span class="nx">taskId</span><span class="o">:</span><span class="w"> </span><span class="nx">queueItem</span><span class="p">.</span><span class="nx">id</span><span class="p">,</span>
<span class="w">      </span><span class="p">...</span><span class="nx">queueItem</span><span class="p">.</span><span class="nx">task</span>
<span class="w">    </span><span class="p">});</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="progressive-processing">
<h3><a class="toc-backref" href="#id14" role="doc-backlink">Progressive Processing</a><a class="headerlink" href="#progressive-processing" title="Link to this heading">¶</a></h3>
<p>Handling large images without blocking the UI:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">class</span><span class="w"> </span><span class="nx">ProgressiveSteganography</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span><span class="nx">engine</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">engine</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">engine</span><span class="p">;</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">chunkSize</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">100000</span><span class="p">;</span><span class="w"> </span><span class="c1">// Process 100k pixels at a time</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">processImageProgressively</span><span class="p">(</span><span class="nx">imageData</span><span class="p">,</span><span class="w"> </span><span class="nx">messageData</span><span class="p">,</span><span class="w"> </span><span class="nx">operation</span><span class="p">,</span><span class="w"> </span><span class="nx">onProgress</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">totalPixels</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">imageData</span><span class="p">.</span><span class="nx">width</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="nx">imageData</span><span class="p">.</span><span class="nx">height</span><span class="p">;</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">chunks</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">Math</span><span class="p">.</span><span class="nx">ceil</span><span class="p">(</span><span class="nx">totalPixels</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">chunkSize</span><span class="p">);</span>

<span class="w">    </span><span class="kd">let</span><span class="w"> </span><span class="nx">processedPixels</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0</span><span class="p">;</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">results</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[];</span>

<span class="w">    </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="kd">let</span><span class="w"> </span><span class="nx">i</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0</span><span class="p">;</span><span class="w"> </span><span class="nx">i</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="nx">chunks</span><span class="p">;</span><span class="w"> </span><span class="nx">i</span><span class="o">++</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">startPixel</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">i</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">chunkSize</span><span class="p">;</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">endPixel</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">Math</span><span class="p">.</span><span class="nx">min</span><span class="p">(</span><span class="nx">startPixel</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">chunkSize</span><span class="p">,</span><span class="w"> </span><span class="nx">totalPixels</span><span class="p">);</span>

<span class="w">      </span><span class="c1">// Extract chunk</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">chunk</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">extractImageChunk</span><span class="p">(</span><span class="nx">imageData</span><span class="p">,</span><span class="w"> </span><span class="nx">startPixel</span><span class="p">,</span><span class="w"> </span><span class="nx">endPixel</span><span class="p">);</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">messageChunk</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">extractMessageChunk</span><span class="p">(</span><span class="nx">messageData</span><span class="p">,</span><span class="w"> </span><span class="nx">i</span><span class="p">,</span><span class="w"> </span><span class="nx">chunks</span><span class="p">);</span>

<span class="w">      </span><span class="c1">// Process chunk</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">processChunk</span><span class="p">(</span><span class="nx">chunk</span><span class="p">,</span><span class="w"> </span><span class="nx">messageChunk</span><span class="p">,</span><span class="w"> </span><span class="nx">operation</span><span class="p">);</span>
<span class="w">      </span><span class="nx">results</span><span class="p">.</span><span class="nx">push</span><span class="p">(</span><span class="nx">result</span><span class="p">);</span>

<span class="w">      </span><span class="c1">// Update progress</span>
<span class="w">      </span><span class="nx">processedPixels</span><span class="w"> </span><span class="o">+=</span><span class="w"> </span><span class="p">(</span><span class="nx">endPixel</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="nx">startPixel</span><span class="p">);</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">progress</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="nx">processedPixels</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="nx">totalPixels</span><span class="p">)</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mf">100</span><span class="p">;</span>
<span class="w">      </span><span class="nx">onProgress</span><span class="p">(</span><span class="nx">progress</span><span class="p">);</span>

<span class="w">      </span><span class="c1">// Yield control to prevent UI blocking</span>
<span class="w">      </span><span class="k">await</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nb">Promise</span><span class="p">(</span><span class="nx">resolve</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">setTimeout</span><span class="p">(</span><span class="nx">resolve</span><span class="p">,</span><span class="w"> </span><span class="mf">0</span><span class="p">));</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">combineChunkResults</span><span class="p">(</span><span class="nx">results</span><span class="p">,</span><span class="w"> </span><span class="nx">imageData</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">processChunk</span><span class="p">(</span><span class="nx">imageChunk</span><span class="p">,</span><span class="w"> </span><span class="nx">messageChunk</span><span class="p">,</span><span class="w"> </span><span class="nx">operation</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">switch</span><span class="w"> </span><span class="p">(</span><span class="nx">operation</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">case</span><span class="w"> </span><span class="s1">&#39;embed&#39;</span><span class="o">:</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">engine</span><span class="p">.</span><span class="nx">embedInChunk</span><span class="p">(</span><span class="nx">imageChunk</span><span class="p">,</span><span class="w"> </span><span class="nx">messageChunk</span><span class="p">);</span>
<span class="w">      </span><span class="k">case</span><span class="w"> </span><span class="s1">&#39;extract&#39;</span><span class="o">:</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">engine</span><span class="p">.</span><span class="nx">extractFromChunk</span><span class="p">(</span><span class="nx">imageChunk</span><span class="p">);</span>
<span class="w">      </span><span class="k">default</span><span class="o">:</span>
<span class="w">        </span><span class="k">throw</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="ne">Error</span><span class="p">(</span><span class="sb">`Unknown operation: </span><span class="si">${</span><span class="nx">operation</span><span class="si">}</span><span class="sb">`</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p>This implementation guide provides comprehensive instructions for integrating WebOTR’s steganography system into production applications with proper security, performance optimization, and platform integration.</p>
</section>
</section>
</section>

        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          <a class="next-page" href="steganography-api.html">
              <div class="page-info">
                <div class="context">
                  <span>Next</span>
                </div>
                <div class="title">Steganography API Reference</div>
              </div>
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
            </a>
          <a class="prev-page" href="steganography-architecture.html">
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
              <div class="page-info">
                <div class="context">
                  <span>Previous</span>
                </div>
                
                <div class="title">Steganography Architecture</div>
                
              </div>
            </a>
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 1980, WebOTR Team
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer">
      
      
      <div class="toc-sticky toc-scroll">
        <div class="toc-title-container">
          <span class="toc-title">
            On this page
          </span>
        </div>
        <div class="toc-tree-container">
          <div class="toc-tree">
            <ul>
<li><a class="reference internal" href="#">Steganography Implementation Guide</a><ul>
<li><a class="reference internal" href="#quick-start">Quick Start</a><ul>
<li><a class="reference internal" href="#basic-integration">Basic Integration</a></li>
<li><a class="reference internal" href="#advanced-configuration">Advanced Configuration</a></li>
</ul>
</li>
<li><a class="reference internal" href="#component-integration">Component Integration</a><ul>
<li><a class="reference internal" href="#steganography-engine">Steganography Engine</a></li>
<li><a class="reference internal" href="#lsb-algorithm-implementation">LSB Algorithm Implementation</a></li>
</ul>
</li>
<li><a class="reference internal" href="#platform-integration">Platform Integration</a><ul>
<li><a class="reference internal" href="#browser-extension-integration">Browser Extension Integration</a></li>
<li><a class="reference internal" href="#multi-image-distribution">Multi-Image Distribution</a></li>
</ul>
</li>
<li><a class="reference internal" href="#security-implementation">Security Implementation</a><ul>
<li><a class="reference internal" href="#statistical-security">Statistical Security</a></li>
</ul>
</li>
<li><a class="reference internal" href="#performance-optimization">Performance Optimization</a><ul>
<li><a class="reference internal" href="#web-worker-implementation">Web Worker Implementation</a></li>
<li><a class="reference internal" href="#progressive-processing">Progressive Processing</a></li>
</ul>
</li>
</ul>
</li>
</ul>

          </div>
        </div>
      </div>
      
      
    </aside>
  </div>
</div><script src="../_static/documentation_options.js?v=01f34227"></script>
    <script src="../_static/doctools.js?v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/scripts/furo.js?v=5fa4622c"></script>
    <script src="../_static/mermaid-renderer.js?v=b683124c"></script>
    </body>
</html>