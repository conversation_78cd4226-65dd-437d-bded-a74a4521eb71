<!doctype html>
<html class="no-js" lang="en" data-content_root="../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><meta name="viewport" content="width=device-width, initial-scale=1" />
<link rel="index" title="Index" href="../genindex.html" /><link rel="search" title="Search" href="../search.html" /><link rel="next" title="Steganography Documentation Summary" href="steganography-summary.html" /><link rel="prev" title="AKE Implementation Guide" href="ake-implementation.html" />

    <link rel="shortcut icon" href="../_static/favicon.svg"/><!-- Generated with Sphinx 7.4.7 and Furo 2024.08.06 -->
        <title>AKE API Reference - WebOTR Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../_static/styles/furo.css?v=354aac6f" />
    <link rel="stylesheet" type="text/css" href="../_static/styles/furo-extensions.css?v=302659d7" />
    <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=21a7f706" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  --color-brand-primary: #6366f1;
  --color-brand-content: #6366f1;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8b5cf6;
  --color-brand-content: #8b5cf6;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8b5cf6;
  --color-brand-content: #8b5cf6;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-with-moon" viewBox="0 0 24 24">
    <title>Auto light/dark, in light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
      class="icon-custom-derived-from-feather-sun-and-tabler-moon">
      <path style="opacity: 50%" d="M 5.411 14.504 C 5.471 14.504 5.532 14.504 5.591 14.504 C 3.639 16.319 4.383 19.569 6.931 20.352 C 7.693 20.586 8.512 20.551 9.25 20.252 C 8.023 23.207 4.056 23.725 2.11 21.184 C 0.166 18.642 1.702 14.949 4.874 14.536 C 5.051 14.512 5.231 14.5 5.411 14.5 L 5.411 14.504 Z"/>
      <line x1="14.5" y1="3.25" x2="14.5" y2="1.25"/>
      <line x1="14.5" y1="15.85" x2="14.5" y2="17.85"/>
      <line x1="10.044" y1="5.094" x2="8.63" y2="3.68"/>
      <line x1="19" y1="14.05" x2="20.414" y2="15.464"/>
      <line x1="8.2" y1="9.55" x2="6.2" y2="9.55"/>
      <line x1="20.8" y1="9.55" x2="22.8" y2="9.55"/>
      <line x1="10.044" y1="14.006" x2="8.63" y2="15.42"/>
      <line x1="19" y1="5.05" x2="20.414" y2="3.636"/>
      <circle cx="14.5" cy="9.55" r="3.6"/>
    </svg>
  </symbol>
  <symbol id="svg-moon-with-sun" viewBox="0 0 24 24">
    <title>Auto light/dark, in dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
      class="icon-custom-derived-from-feather-sun-and-tabler-moon">
      <path d="M 8.282 7.007 C 8.385 7.007 8.494 7.007 8.595 7.007 C 5.18 10.184 6.481 15.869 10.942 17.24 C 12.275 17.648 13.706 17.589 15 17.066 C 12.851 22.236 5.91 23.143 2.505 18.696 C -0.897 14.249 1.791 7.786 7.342 7.063 C 7.652 7.021 7.965 7 8.282 7 L 8.282 7.007 Z"/>
      <line style="opacity: 50%" x1="18" y1="3.705" x2="18" y2="2.5"/>
      <line style="opacity: 50%" x1="18" y1="11.295" x2="18" y2="12.5"/>
      <line style="opacity: 50%" x1="15.316" y1="4.816" x2="14.464" y2="3.964"/>
      <line style="opacity: 50%" x1="20.711" y1="10.212" x2="21.563" y2="11.063"/>
      <line style="opacity: 50%" x1="14.205" y1="7.5" x2="13.001" y2="7.5"/>
      <line style="opacity: 50%" x1="21.795" y1="7.5" x2="23" y2="7.5"/>
      <line style="opacity: 50%" x1="15.316" y1="10.184" x2="14.464" y2="11.036"/>
      <line style="opacity: 50%" x1="20.711" y1="4.789" x2="21.563" y2="3.937"/>
      <circle style="opacity: 50%" cx="18" cy="7.5" r="2.169"/>
    </svg>
  </symbol>
  <symbol id="svg-pencil" viewBox="0 0 24 24">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-pencil-code">
      <path d="M4 20h4l10.5 -10.5a2.828 2.828 0 1 0 -4 -4l-10.5 10.5v4" />
      <path d="M13.5 6.5l4 4" />
      <path d="M20 21l2 -2l-2 -2" />
      <path d="M17 17l-2 2l2 2" />
    </svg>
  </symbol>
  <symbol id="svg-eye" viewBox="0 0 24 24">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-eye-code">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0" />
      <path
        d="M11.11 17.958c-3.209 -.307 -5.91 -2.293 -8.11 -5.958c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6c-.21 .352 -.427 .688 -.647 1.008" />
      <path d="M20 21l2 -2l-2 -2" />
      <path d="M17 17l-2 2l2 2" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>

<a class="skip-to-content muted-link" href="#furo-main-content">Skip to content</a>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../index.html"><div class="brand">WebOTR Documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto-light"><use href="#svg-sun-with-moon"></use></svg>
          <svg class="theme-icon-when-auto-dark"><use href="#svg-moon-with-sun"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><a class="sidebar-brand" href="../index.html">
  
  <div class="sidebar-logo-container">
    <img class="sidebar-logo" src="../_static/logo.svg" alt="Logo"/>
  </div>
  
  <span class="sidebar-brand-text">WebOTR Documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-scroll"><div class="sidebar-tree">
  <p class="caption" role="heading"><span class="caption-text">User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user-guide/installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../user-guide/getting-started.html">Getting Started</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../developer-guide/architecture.html">Architecture Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../developer-guide/contributing.html">Contributing to WebOTR</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Protocol Compliance &amp; Advanced Features</span></p>
<ul>
<li class="toctree-l1 has-children"><a class="reference internal" href="../protocol/index.html">Protocol Compliance &amp; Advanced Features</a><input class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of Protocol Compliance &amp; Advanced Features</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul>
<li class="toctree-l2"><a class="reference internal" href="../protocol/overview.html">Protocol Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/version-negotiation.html">Version Negotiation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/message-ordering.html">Message Ordering</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/enhanced-smp.html">Enhanced SMP</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/policy-manager.html">Policy Manager</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/integration-guide.html">Integration Guide</a></li>
<li class="toctree-l2"><a class="reference internal" href="../protocol/troubleshooting.html">Troubleshooting Guide</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Security</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="overview.html">Security Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements-summary.html">libOTR Security Enhancements Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements.html">libOTR Security Enhancements</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements-implementation.html">libOTR Enhancements Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements-api.html">libOTR Enhancements API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="libotr-enhancements-testing.html">libOTR Enhancements Testing and Validation</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy-summary.html">Forward Secrecy Documentation Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy.html">Forward Secrecy Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy-architecture.html">Forward Secrecy Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy-implementation.html">Forward Secrecy Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="forward-secrecy-api.html">Forward Secrecy API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake-summary.html">AKE Documentation Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake.html">Authenticated Key Exchange (AKE)</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake-architecture.html">AKE Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="ake-implementation.html">AKE Implementation Guide</a></li>
<li class="toctree-l1 current current-page"><a class="current reference internal" href="#">AKE API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography-summary.html">Steganography Documentation Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography.html">Steganography Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography-architecture.html">Steganography Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography-implementation.html">Steganography Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="steganography-api.html">Steganography API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../reference/faq.html">Frequently Asked Questions</a></li>
<li class="toctree-l1"><a class="reference internal" href="../reference/changelog.html">Changelog</a></li>
</ul>

</div>
</div>

      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          <div class="view-this-page">
  <a class="muted-link" href="https://github.com/forkrul/webOTteR/blob/main/docs/sphinx/security/ake-api.rst?plain=true" title="View this page">
    <svg><use href="#svg-eye"></use></svg>
    <span class="visually-hidden">View this page</span>
  </a>
</div><div class="edit-this-page">
  <a class="muted-link" href="https://github.com/forkrul/webOTteR/edit/main/docs/sphinx/security/ake-api.rst" title="Edit this page">
    <svg><use href="#svg-pencil"></use></svg>
    <span class="visually-hidden">Edit this page</span>
  </a>
</div><div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto-light"><use href="#svg-sun-with-moon"></use></svg>
              <svg class="theme-icon-when-auto-dark"><use href="#svg-moon-with-sun"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main" id="furo-main-content">
          <section id="ake-api-reference">
<h1>AKE API Reference<a class="headerlink" href="#ake-api-reference" title="Link to this heading">¶</a></h1>
<p>Complete API documentation for WebOTR’s Authenticated Key Exchange (AKE) system components.</p>
<nav class="contents local" id="table-of-contents">
<p class="topic-title">Table of Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#core-functions" id="id2">Core Functions</a></p>
<ul>
<li><p><a class="reference internal" href="#startake" id="id3">startAKE()</a></p></li>
<li><p><a class="reference internal" href="#createdhcommit" id="id4">createDHCommit()</a></p></li>
<li><p><a class="reference internal" href="#createdhkey" id="id5">createDHKey()</a></p></li>
<li><p><a class="reference internal" href="#createrevealsignature" id="id6">createRevealSignature()</a></p></li>
<li><p><a class="reference internal" href="#createsignature" id="id7">createSignature()</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#message-processing-functions" id="id8">Message Processing Functions</a></p>
<ul>
<li><p><a class="reference internal" href="#processdhcommit" id="id9">processDHCommit()</a></p></li>
<li><p><a class="reference internal" href="#processdhkey" id="id10">processDHKey()</a></p></li>
<li><p><a class="reference internal" href="#processrevealsignature" id="id11">processRevealSignature()</a></p></li>
<li><p><a class="reference internal" href="#processsignature" id="id12">processSignature()</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#state-management" id="id13">State Management</a></p>
<ul>
<li><p><a class="reference internal" href="#otrstate-class" id="id14">OtrState Class</a></p>
<ul>
<li><p><a class="reference internal" href="#id1" id="id15">startAKE()</a></p></li>
<li><p><a class="reference internal" href="#goencrypted" id="id16">goEncrypted()</a></p></li>
<li><p><a class="reference internal" href="#goplaintext" id="id17">goPlaintext()</a></p></li>
<li><p><a class="reference internal" href="#setsessionkeys" id="id18">setSessionKeys()</a></p></li>
<li><p><a class="reference internal" href="#cansendencrypted" id="id19">canSendEncrypted()</a></p></li>
<li><p><a class="reference internal" href="#isakeinprogress" id="id20">isAKEInProgress()</a></p></li>
</ul>
</li>
</ul>
</li>
<li><p><a class="reference internal" href="#constants" id="id21">Constants</a></p>
<ul>
<li><p><a class="reference internal" href="#protocol-states" id="id22">Protocol States</a></p></li>
<li><p><a class="reference internal" href="#message-types" id="id23">Message Types</a></p></li>
<li><p><a class="reference internal" href="#protocol-versions" id="id24">Protocol Versions</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#cryptographic-functions" id="id25">Cryptographic Functions</a></p>
<ul>
<li><p><a class="reference internal" href="#generatedhkeypair" id="id26">generateDHKeyPair()</a></p></li>
<li><p><a class="reference internal" href="#dhexchange" id="id27">dhExchange()</a></p></li>
<li><p><a class="reference internal" href="#derivekeys" id="id28">deriveKeys()</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#error-classes" id="id29">Error Classes</a></p>
<ul>
<li><p><a class="reference internal" href="#akeerror" id="id30">AKEError</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#type-definitions" id="id31">Type Definitions</a></p>
<ul>
<li><p><a class="reference internal" href="#dhkeypair" id="id32">DHKeyPair</a></p></li>
<li><p><a class="reference internal" href="#dsakeypair" id="id33">DSAKeyPair</a></p></li>
<li><p><a class="reference internal" href="#akemessage" id="id34">AKEMessage</a></p></li>
<li><p><a class="reference internal" href="#sessionkeys" id="id35">SessionKeys</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#usage-examples" id="id36">Usage Examples</a></p>
<ul>
<li><p><a class="reference internal" href="#complete-ake-handshake" id="id37">Complete AKE Handshake</a></p></li>
<li><p><a class="reference internal" href="#error-handling" id="id38">Error Handling</a></p></li>
</ul>
</li>
</ul>
</nav>
<section id="core-functions">
<h2><a class="toc-backref" href="#id2" role="doc-backlink">Core Functions</a><a class="headerlink" href="#core-functions" title="Link to this heading">¶</a></h2>
<section id="startake">
<h3><a class="toc-backref" href="#id3" role="doc-backlink">startAKE()</a><a class="headerlink" href="#startake" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">startAKE</span><span class="p">(</span><span class="nx">state</span><span class="p">)</span>
</pre></div>
</div>
<p>Initiates the Authenticated Key Exchange protocol.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">state</span></code> (OtrState): Current OTR state object</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">state</span><span class="o">:</span><span class="w"> </span><span class="nx">OtrState</span><span class="p">,</span><span class="w">        </span><span class="c1">// Updated state object</span>
<span class="w">  </span><span class="nx">message</span><span class="o">:</span><span class="w"> </span><span class="nx">string</span><span class="p">,</span><span class="w">        </span><span class="c1">// Message to send (&#39;?OTR:AKESTART&#39;)</span>
<span class="w">  </span><span class="nx">dhCommit</span><span class="o">:</span><span class="w"> </span><span class="nb">Object</span><span class="w">        </span><span class="c1">// DH commit message data</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Example:</strong></p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">const</span><span class="w"> </span><span class="nx">state</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">OtrState</span><span class="p">();</span>
<span class="nx">state</span><span class="p">.</span><span class="nx">dsaKeyPair</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">generateDSAKeyPair</span><span class="p">();</span>

<span class="kd">const</span><span class="w"> </span><span class="nx">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">startAKE</span><span class="p">(</span><span class="nx">state</span><span class="p">);</span>
<span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;AKE started:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">result</span><span class="p">.</span><span class="nx">message</span><span class="p">);</span>
</pre></div>
</div>
</section>
<section id="createdhcommit">
<h3><a class="toc-backref" href="#id4" role="doc-backlink">createDHCommit()</a><a class="headerlink" href="#createdhcommit" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">createDHCommit</span><span class="p">(</span><span class="nx">dhKeyPair</span><span class="p">,</span><span class="w"> </span><span class="nx">protocolVersion</span><span class="p">,</span><span class="w"> </span><span class="nx">instanceTag</span><span class="p">,</span><span class="w"> </span><span class="nx">receiverInstanceTag</span><span class="p">)</span>
</pre></div>
</div>
<p>Creates a DH Commit message to initiate the AKE handshake.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">dhKeyPair</span></code> (Object): Diffie-Hellman key pair</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">protocolVersion</span></code> (number): OTR protocol version (default: 3)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">instanceTag</span></code> (number): Sender’s instance tag</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">receiverInstanceTag</span></code> (number): Receiver’s instance tag</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">protocolVersion</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">messageType</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;DH_COMMIT&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">senderInstanceTag</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">receiverInstanceTag</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">encryptedGx</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="p">,</span><span class="w">      </span><span class="c1">// Encrypted DH public key</span>
<span class="w">  </span><span class="nx">hashOfEncryptedGx</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="p">,</span><span class="w"> </span><span class="c1">// Hash of encrypted key</span>
<span class="w">  </span><span class="nx">aesKey</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="p">,</span><span class="w">           </span><span class="c1">// AES key for encryption</span>
<span class="w">  </span><span class="nx">iv</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="w">                </span><span class="c1">// Initialization vector</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="createdhkey">
<h3><a class="toc-backref" href="#id5" role="doc-backlink">createDHKey()</a><a class="headerlink" href="#createdhkey" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">createDHKey</span><span class="p">(</span><span class="nx">dhKeyPair</span><span class="p">,</span><span class="w"> </span><span class="nx">protocolVersion</span><span class="p">,</span><span class="w"> </span><span class="nx">instanceTag</span><span class="p">,</span><span class="w"> </span><span class="nx">receiverInstanceTag</span><span class="p">)</span>
</pre></div>
</div>
<p>Creates a DH Key message in response to a DH Commit.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">dhKeyPair</span></code> (Object): Diffie-Hellman key pair</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">protocolVersion</span></code> (number): OTR protocol version</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">instanceTag</span></code> (number): Sender’s instance tag</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">receiverInstanceTag</span></code> (number): Receiver’s instance tag</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">protocolVersion</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">messageType</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;DH_KEY&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">senderInstanceTag</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">receiverInstanceTag</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">publicKey</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="w">         </span><span class="c1">// DH public key</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="createrevealsignature">
<h3><a class="toc-backref" href="#id6" role="doc-backlink">createRevealSignature()</a><a class="headerlink" href="#createrevealsignature" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">createRevealSignature</span><span class="p">(</span><span class="nx">dhKeyPair</span><span class="p">,</span><span class="w"> </span><span class="nx">dsaKeyPair</span><span class="p">,</span><span class="w"> </span><span class="nx">sharedSecret</span><span class="p">,</span><span class="w"> </span><span class="nx">protocolVersion</span><span class="p">,</span><span class="w"> </span><span class="nx">instanceTag</span><span class="p">,</span><span class="w"> </span><span class="nx">receiverInstanceTag</span><span class="p">,</span><span class="w"> </span><span class="nx">aesKey</span><span class="p">,</span><span class="w"> </span><span class="nx">iv</span><span class="p">)</span>
</pre></div>
</div>
<p>Creates a Reveal Signature message with authentication.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">dhKeyPair</span></code> (Object): Diffie-Hellman key pair</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">dsaKeyPair</span></code> (Object): DSA key pair for signing</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">sharedSecret</span></code> (Uint8Array): Computed shared secret</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">protocolVersion</span></code> (number): OTR protocol version</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">instanceTag</span></code> (number): Sender’s instance tag</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">receiverInstanceTag</span></code> (number): Receiver’s instance tag</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">aesKey</span></code> (Uint8Array): AES key from DH commit</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">iv</span></code> (Uint8Array): Initialization vector</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">protocolVersion</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">messageType</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;REVEAL_SIGNATURE&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">senderInstanceTag</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">receiverInstanceTag</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">revealedKey</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="p">,</span><span class="w">      </span><span class="c1">// Revealed AES key</span>
<span class="w">  </span><span class="nx">encryptedSignature</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="p">,</span><span class="w"> </span><span class="c1">// Encrypted DSA signature</span>
<span class="w">  </span><span class="nx">macKey</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="w">            </span><span class="c1">// HMAC for integrity</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="createsignature">
<h3><a class="toc-backref" href="#id7" role="doc-backlink">createSignature()</a><a class="headerlink" href="#createsignature" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">createSignature</span><span class="p">(</span><span class="nx">dhKeyPair</span><span class="p">,</span><span class="w"> </span><span class="nx">dsaKeyPair</span><span class="p">,</span><span class="w"> </span><span class="nx">sharedSecret</span><span class="p">,</span><span class="w"> </span><span class="nx">protocolVersion</span><span class="p">,</span><span class="w"> </span><span class="nx">instanceTag</span><span class="p">,</span><span class="w"> </span><span class="nx">receiverInstanceTag</span><span class="p">,</span><span class="w"> </span><span class="nx">sessionKey</span><span class="p">)</span>
</pre></div>
</div>
<p>Creates a Signature message to complete the AKE handshake.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">dhKeyPair</span></code> (Object): Diffie-Hellman key pair</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">dsaKeyPair</span></code> (Object): DSA key pair for signing</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">sharedSecret</span></code> (Uint8Array): Computed shared secret</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">protocolVersion</span></code> (number): OTR protocol version</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">instanceTag</span></code> (number): Sender’s instance tag</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">receiverInstanceTag</span></code> (number): Receiver’s instance tag</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">sessionKey</span></code> (Uint8Array): Derived session key</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">protocolVersion</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">messageType</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;SIGNATURE&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">senderInstanceTag</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">receiverInstanceTag</span><span class="o">:</span><span class="w"> </span><span class="nx">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">encryptedSignature</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="p">,</span><span class="w"> </span><span class="c1">// Encrypted DSA signature</span>
<span class="w">  </span><span class="nx">macKey</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="w">            </span><span class="c1">// HMAC for integrity</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="message-processing-functions">
<h2><a class="toc-backref" href="#id8" role="doc-backlink">Message Processing Functions</a><a class="headerlink" href="#message-processing-functions" title="Link to this heading">¶</a></h2>
<section id="processdhcommit">
<h3><a class="toc-backref" href="#id9" role="doc-backlink">processDHCommit()</a><a class="headerlink" href="#processdhcommit" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">processDHCommit</span><span class="p">(</span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="nx">state</span><span class="p">)</span>
</pre></div>
</div>
<p>Processes an incoming DH Commit message.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">message</span></code> (Object): DH Commit message data</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">state</span></code> (OtrState): Current OTR state</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">state</span><span class="o">:</span><span class="w"> </span><span class="nx">OtrState</span><span class="p">,</span><span class="w">        </span><span class="c1">// Updated state</span>
<span class="w">  </span><span class="nx">message</span><span class="o">:</span><span class="w"> </span><span class="nb">Object</span><span class="w">         </span><span class="c1">// DH Key response message</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Example:</strong></p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">const</span><span class="w"> </span><span class="nx">dhCommitMessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">parseMessage</span><span class="p">(</span><span class="nx">incomingMessage</span><span class="p">);</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">processDHCommit</span><span class="p">(</span><span class="nx">dhCommitMessage</span><span class="p">,</span><span class="w"> </span><span class="nx">state</span><span class="p">);</span>
<span class="nx">sendMessage</span><span class="p">(</span><span class="nx">result</span><span class="p">.</span><span class="nx">message</span><span class="p">);</span>
</pre></div>
</div>
</section>
<section id="processdhkey">
<h3><a class="toc-backref" href="#id10" role="doc-backlink">processDHKey()</a><a class="headerlink" href="#processdhkey" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">processDHKey</span><span class="p">(</span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="nx">state</span><span class="p">)</span>
</pre></div>
</div>
<p>Processes an incoming DH Key message.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">message</span></code> (Object): DH Key message data</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">state</span></code> (OtrState): Current OTR state</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">state</span><span class="o">:</span><span class="w"> </span><span class="nx">OtrState</span><span class="p">,</span><span class="w">        </span><span class="c1">// Updated state</span>
<span class="w">  </span><span class="nx">message</span><span class="o">:</span><span class="w"> </span><span class="nb">Object</span><span class="w">         </span><span class="c1">// Reveal Signature response message</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="processrevealsignature">
<h3><a class="toc-backref" href="#id11" role="doc-backlink">processRevealSignature()</a><a class="headerlink" href="#processrevealsignature" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">processRevealSignature</span><span class="p">(</span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="nx">state</span><span class="p">)</span>
</pre></div>
</div>
<p>Processes an incoming Reveal Signature message.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">message</span></code> (Object): Reveal Signature message data</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">state</span></code> (OtrState): Current OTR state</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">state</span><span class="o">:</span><span class="w"> </span><span class="nx">OtrState</span><span class="p">,</span><span class="w">        </span><span class="c1">// Updated state</span>
<span class="w">  </span><span class="nx">message</span><span class="o">:</span><span class="w"> </span><span class="nb">Object</span><span class="w">         </span><span class="c1">// Signature response message</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="processsignature">
<h3><a class="toc-backref" href="#id12" role="doc-backlink">processSignature()</a><a class="headerlink" href="#processsignature" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">processSignature</span><span class="p">(</span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="nx">state</span><span class="p">)</span>
</pre></div>
</div>
<p>Processes an incoming Signature message to complete AKE.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">message</span></code> (Object): Signature message data</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">state</span></code> (OtrState): Current OTR state</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">state</span><span class="o">:</span><span class="w"> </span><span class="nx">OtrState</span><span class="p">,</span><span class="w">        </span><span class="c1">// Updated state (ENCRYPTED)</span>
<span class="w">  </span><span class="nx">success</span><span class="o">:</span><span class="w"> </span><span class="kr">boolean</span><span class="w">        </span><span class="c1">// True if AKE completed successfully</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="state-management">
<h2><a class="toc-backref" href="#id13" role="doc-backlink">State Management</a><a class="headerlink" href="#state-management" title="Link to this heading">¶</a></h2>
<section id="otrstate-class">
<h3><a class="toc-backref" href="#id14" role="doc-backlink">OtrState Class</a><a class="headerlink" href="#otrstate-class" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">class</span><span class="w"> </span><span class="nx">OtrState</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span><span class="nx">version</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">PROTOCOL_VERSION</span><span class="p">.</span><span class="nx">V3</span><span class="p">)</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Properties:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">version</span></code> (number): OTR protocol version</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">state</span></code> (number): Current protocol state</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">authState</span></code> (string): Current authentication state</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">instanceTag</span></code> (number): Our instance tag</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">theirInstanceTag</span></code> (number): Their instance tag</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">dhKeyPair</span></code> (Object): Our DH key pair</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">dsaKeyPair</span></code> (Object): Our DSA key pair</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">theirPublicKey</span></code> (Uint8Array): Their DH public key</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">sharedSecret</span></code> (Uint8Array): Computed shared secret</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">sendingAESKey</span></code> (Uint8Array): AES key for sending</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">receivingAESKey</span></code> (Uint8Array): AES key for receiving</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">sendingMACKey</span></code> (Uint8Array): MAC key for sending</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">receivingMACKey</span></code> (Uint8Array): MAC key for receiving</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">ssid</span></code> (Uint8Array): Session ID</p></li>
</ul>
<p><strong>Methods:</strong></p>
<section id="id1">
<h4><a class="toc-backref" href="#id15" role="doc-backlink">startAKE()</a><a class="headerlink" href="#id1" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="nx">startAKE</span><span class="p">()</span>
</pre></div>
</div>
<p>Transitions state to begin AKE handshake.</p>
<p><strong>Returns:</strong> boolean - True if transition successful</p>
</section>
<section id="goencrypted">
<h4><a class="toc-backref" href="#id16" role="doc-backlink">goEncrypted()</a><a class="headerlink" href="#goencrypted" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="nx">goEncrypted</span><span class="p">()</span>
</pre></div>
</div>
<p>Transitions to encrypted state after successful AKE.</p>
<p><strong>Returns:</strong> boolean - True if transition successful</p>
</section>
<section id="goplaintext">
<h4><a class="toc-backref" href="#id17" role="doc-backlink">goPlaintext()</a><a class="headerlink" href="#goplaintext" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="nx">goPlaintext</span><span class="p">()</span>
</pre></div>
</div>
<p>Resets to plaintext state and clears sensitive data.</p>
<p><strong>Returns:</strong> boolean - Always true</p>
</section>
<section id="setsessionkeys">
<h4><a class="toc-backref" href="#id18" role="doc-backlink">setSessionKeys()</a><a class="headerlink" href="#setsessionkeys" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="nx">setSessionKeys</span><span class="p">(</span><span class="nx">keys</span><span class="p">)</span>
</pre></div>
</div>
<p>Sets the derived session keys.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">keys</span></code> (Object): Session keys object</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">sendingAESKey</span></code> (Uint8Array): AES key for sending</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">receivingAESKey</span></code> (Uint8Array): AES key for receiving</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">sendingMACKey</span></code> (Uint8Array): MAC key for sending</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">receivingMACKey</span></code> (Uint8Array): MAC key for receiving</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">ssid</span></code> (Uint8Array): Session ID</p></li>
</ul>
</li>
</ul>
</section>
<section id="cansendencrypted">
<h4><a class="toc-backref" href="#id19" role="doc-backlink">canSendEncrypted()</a><a class="headerlink" href="#cansendencrypted" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="nx">canSendEncrypted</span><span class="p">()</span>
</pre></div>
</div>
<p>Checks if encrypted communication is possible.</p>
<p><strong>Returns:</strong> boolean - True if in encrypted state</p>
</section>
<section id="isakeinprogress">
<h4><a class="toc-backref" href="#id20" role="doc-backlink">isAKEInProgress()</a><a class="headerlink" href="#isakeinprogress" title="Link to this heading">¶</a></h4>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="nx">isAKEInProgress</span><span class="p">()</span>
</pre></div>
</div>
<p>Checks if AKE handshake is in progress.</p>
<p><strong>Returns:</strong> boolean - True if AKE is active</p>
</section>
</section>
</section>
<section id="constants">
<h2><a class="toc-backref" href="#id21" role="doc-backlink">Constants</a><a class="headerlink" href="#constants" title="Link to this heading">¶</a></h2>
<section id="protocol-states">
<h3><a class="toc-backref" href="#id22" role="doc-backlink">Protocol States</a><a class="headerlink" href="#protocol-states" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">const</span><span class="w"> </span><span class="nx">STATE</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">PLAINTEXT</span><span class="o">:</span><span class="w"> </span><span class="mf">0</span><span class="p">,</span><span class="w">           </span><span class="c1">// No encryption</span>
<span class="w">  </span><span class="nx">AWAITING_DHKEY</span><span class="o">:</span><span class="w"> </span><span class="mf">1</span><span class="p">,</span><span class="w">      </span><span class="c1">// Waiting for DH Key message</span>
<span class="w">  </span><span class="nx">AWAITING_REVEALSIG</span><span class="o">:</span><span class="w"> </span><span class="mf">2</span><span class="p">,</span><span class="w">  </span><span class="c1">// Waiting for Reveal Signature</span>
<span class="w">  </span><span class="nx">AWAITING_SIG</span><span class="o">:</span><span class="w"> </span><span class="mf">3</span><span class="p">,</span><span class="w">        </span><span class="c1">// Waiting for Signature message</span>
<span class="w">  </span><span class="nx">ENCRYPTED</span><span class="o">:</span><span class="w"> </span><span class="mf">4</span><span class="p">,</span><span class="w">           </span><span class="c1">// Secure communication established</span>
<span class="w">  </span><span class="nx">FINISHED</span><span class="o">:</span><span class="w"> </span><span class="mf">5</span><span class="w">             </span><span class="c1">// Session ended</span>
<span class="p">};</span>
</pre></div>
</div>
</section>
<section id="message-types">
<h3><a class="toc-backref" href="#id23" role="doc-backlink">Message Types</a><a class="headerlink" href="#message-types" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">const</span><span class="w"> </span><span class="nx">MESSAGE_TYPE</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">QUERY</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;QUERY&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">DH_COMMIT</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;DH_COMMIT&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">DH_KEY</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;DH_KEY&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">REVEAL_SIGNATURE</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;REVEAL_SIGNATURE&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">SIGNATURE</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;SIGNATURE&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">DATA</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;DATA&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">ERROR</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;ERROR&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">UNKNOWN</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;UNKNOWN&#39;</span>
<span class="p">};</span>
</pre></div>
</div>
</section>
<section id="protocol-versions">
<h3><a class="toc-backref" href="#id24" role="doc-backlink">Protocol Versions</a><a class="headerlink" href="#protocol-versions" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">const</span><span class="w"> </span><span class="nx">PROTOCOL_VERSION</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">V2</span><span class="o">:</span><span class="w"> </span><span class="mf">2</span><span class="p">,</span>
<span class="w">  </span><span class="nx">V3</span><span class="o">:</span><span class="w"> </span><span class="mf">3</span><span class="p">,</span>
<span class="w">  </span><span class="nx">CURRENT</span><span class="o">:</span><span class="w"> </span><span class="mf">3</span><span class="w">              </span><span class="c1">// Default version</span>
<span class="p">};</span>
</pre></div>
</div>
</section>
</section>
<section id="cryptographic-functions">
<h2><a class="toc-backref" href="#id25" role="doc-backlink">Cryptographic Functions</a><a class="headerlink" href="#cryptographic-functions" title="Link to this heading">¶</a></h2>
<section id="generatedhkeypair">
<h3><a class="toc-backref" href="#id26" role="doc-backlink">generateDHKeyPair()</a><a class="headerlink" href="#generatedhkeypair" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">generateDHKeyPair</span><span class="p">()</span>
</pre></div>
</div>
<p>Generates a Diffie-Hellman key pair.</p>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">privateKey</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="p">,</span><span class="w">  </span><span class="c1">// Private key (256 bytes)</span>
<span class="w">  </span><span class="nx">publicKey</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="w">    </span><span class="c1">// Public key (256 bytes)</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="dhexchange">
<h3><a class="toc-backref" href="#id27" role="doc-backlink">dhExchange()</a><a class="headerlink" href="#dhexchange" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">dhExchange</span><span class="p">(</span><span class="nx">privateKey</span><span class="p">,</span><span class="w"> </span><span class="nx">publicKey</span><span class="p">)</span>
</pre></div>
</div>
<p>Performs Diffie-Hellman key exchange.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">privateKey</span></code> (Uint8Array): Our private key</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">publicKey</span></code> (Uint8Array): Their public key</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Uint8Array&gt; - Shared secret (32 bytes)</p>
</section>
<section id="derivekeys">
<h3><a class="toc-backref" href="#id28" role="doc-backlink">deriveKeys()</a><a class="headerlink" href="#derivekeys" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">async</span><span class="w"> </span><span class="nx">deriveKeys</span><span class="p">(</span><span class="nx">sharedSecret</span><span class="p">)</span>
</pre></div>
</div>
<p>Derives session keys from shared secret using HKDF.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">sharedSecret</span></code> (Uint8Array): Shared secret from DH exchange</p></li>
</ul>
<p><strong>Returns:</strong> Promise&lt;Object&gt;</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">sendingAESKey</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="p">,</span><span class="w">    </span><span class="c1">// 32 bytes</span>
<span class="w">  </span><span class="nx">receivingAESKey</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="p">,</span><span class="w">  </span><span class="c1">// 32 bytes</span>
<span class="w">  </span><span class="nx">sendingMACKey</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="p">,</span><span class="w">    </span><span class="c1">// 32 bytes</span>
<span class="w">  </span><span class="nx">receivingMACKey</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="p">,</span><span class="w">  </span><span class="c1">// 32 bytes</span>
<span class="w">  </span><span class="nx">ssid</span><span class="o">:</span><span class="w"> </span><span class="nb">Uint8Array</span><span class="w">              </span><span class="c1">// 8 bytes</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="error-classes">
<h2><a class="toc-backref" href="#id29" role="doc-backlink">Error Classes</a><a class="headerlink" href="#error-classes" title="Link to this heading">¶</a></h2>
<section id="akeerror">
<h3><a class="toc-backref" href="#id30" role="doc-backlink">AKEError</a><a class="headerlink" href="#akeerror" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">class</span><span class="w"> </span><span class="nx">AKEError</span><span class="w"> </span><span class="k">extends</span><span class="w"> </span><span class="ne">Error</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="nx">code</span><span class="p">,</span><span class="w"> </span><span class="nx">details</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{})</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Properties:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">message</span></code> (string): Error message</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">code</span></code> (string): Error code</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">details</span></code> (Object): Additional error details</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">timestamp</span></code> (number): Error timestamp</p></li>
</ul>
<p><strong>Error Codes:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">INVALID_STATE</span></code>: Invalid protocol state for operation</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">INVALID_MESSAGE</span></code>: Malformed or invalid message</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">CRYPTO_ERROR</span></code>: Cryptographic operation failed</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">SIGNATURE_VERIFICATION_FAILED</span></code>: Invalid signature</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">TIMEOUT</span></code>: Operation timed out</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">REPLAY_ATTACK</span></code>: Replay attack detected</p></li>
</ul>
</section>
</section>
<section id="type-definitions">
<h2><a class="toc-backref" href="#id31" role="doc-backlink">Type Definitions</a><a class="headerlink" href="#type-definitions" title="Link to this heading">¶</a></h2>
<section id="dhkeypair">
<h3><a class="toc-backref" href="#id32" role="doc-backlink">DHKeyPair</a><a class="headerlink" href="#dhkeypair" title="Link to this heading">¶</a></h3>
<div class="highlight-typescript notranslate"><div class="highlight"><pre><span></span><span class="kd">interface</span><span class="w"> </span><span class="nx">DHKeyPair</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">privateKey</span><span class="o">:</span><span class="w"> </span><span class="kt">Uint8Array</span><span class="p">;</span><span class="w">  </span><span class="c1">// 256-byte private key</span>
<span class="w">  </span><span class="nx">publicKey</span><span class="o">:</span><span class="w"> </span><span class="kt">Uint8Array</span><span class="p">;</span><span class="w">   </span><span class="c1">// 256-byte public key</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="dsakeypair">
<h3><a class="toc-backref" href="#id33" role="doc-backlink">DSAKeyPair</a><a class="headerlink" href="#dsakeypair" title="Link to this heading">¶</a></h3>
<div class="highlight-typescript notranslate"><div class="highlight"><pre><span></span><span class="kd">interface</span><span class="w"> </span><span class="nx">DSAKeyPair</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">privateKey</span><span class="o">:</span><span class="w"> </span><span class="kt">Uint8Array</span><span class="p">;</span><span class="w">  </span><span class="c1">// Private key for signing</span>
<span class="w">  </span><span class="nx">publicKey</span><span class="o">:</span><span class="w"> </span><span class="kt">Uint8Array</span><span class="p">;</span><span class="w">   </span><span class="c1">// Public key for verification</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="akemessage">
<h3><a class="toc-backref" href="#id34" role="doc-backlink">AKEMessage</a><a class="headerlink" href="#akemessage" title="Link to this heading">¶</a></h3>
<div class="highlight-typescript notranslate"><div class="highlight"><pre><span></span><span class="kd">interface</span><span class="w"> </span><span class="nx">AKEMessage</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">protocolVersion</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">  </span><span class="nx">messageType</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="nx">senderInstanceTag</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">  </span><span class="nx">receiverInstanceTag</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">  </span><span class="p">[</span><span class="nx">key</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">]</span><span class="o">:</span><span class="w"> </span><span class="nx">any</span><span class="p">;</span><span class="w">      </span><span class="c1">// Message-specific fields</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="sessionkeys">
<h3><a class="toc-backref" href="#id35" role="doc-backlink">SessionKeys</a><a class="headerlink" href="#sessionkeys" title="Link to this heading">¶</a></h3>
<div class="highlight-typescript notranslate"><div class="highlight"><pre><span></span><span class="kd">interface</span><span class="w"> </span><span class="nx">SessionKeys</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">sendingAESKey</span><span class="o">:</span><span class="w"> </span><span class="kt">Uint8Array</span><span class="p">;</span>
<span class="w">  </span><span class="nx">receivingAESKey</span><span class="o">:</span><span class="w"> </span><span class="kt">Uint8Array</span><span class="p">;</span>
<span class="w">  </span><span class="nx">sendingMACKey</span><span class="o">:</span><span class="w"> </span><span class="kt">Uint8Array</span><span class="p">;</span>
<span class="w">  </span><span class="nx">receivingMACKey</span><span class="o">:</span><span class="w"> </span><span class="kt">Uint8Array</span><span class="p">;</span>
<span class="w">  </span><span class="nx">ssid</span><span class="o">:</span><span class="w"> </span><span class="kt">Uint8Array</span><span class="p">;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="usage-examples">
<h2><a class="toc-backref" href="#id36" role="doc-backlink">Usage Examples</a><a class="headerlink" href="#usage-examples" title="Link to this heading">¶</a></h2>
<section id="complete-ake-handshake">
<h3><a class="toc-backref" href="#id37" role="doc-backlink">Complete AKE Handshake</a><a class="headerlink" href="#complete-ake-handshake" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="c1">// Alice initiates AKE</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">alice</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">OtrState</span><span class="p">();</span>
<span class="nx">alice</span><span class="p">.</span><span class="nx">dsaKeyPair</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">generateDSAKeyPair</span><span class="p">();</span>

<span class="kd">const</span><span class="w"> </span><span class="nx">akeStart</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">startAKE</span><span class="p">(</span><span class="nx">alice</span><span class="p">);</span>

<span class="c1">// Bob processes DH commit</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">bob</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">OtrState</span><span class="p">();</span>
<span class="nx">bob</span><span class="p">.</span><span class="nx">dsaKeyPair</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">generateDSAKeyPair</span><span class="p">();</span>

<span class="kd">const</span><span class="w"> </span><span class="nx">dhKeyResponse</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">processDHCommit</span><span class="p">(</span><span class="nx">akeStart</span><span class="p">.</span><span class="nx">dhCommit</span><span class="p">,</span><span class="w"> </span><span class="nx">bob</span><span class="p">);</span>

<span class="c1">// Alice processes DH key</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">revealSigResponse</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">processDHKey</span><span class="p">(</span><span class="nx">dhKeyResponse</span><span class="p">.</span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="nx">alice</span><span class="p">);</span>

<span class="c1">// Bob processes reveal signature</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">sigResponse</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">processRevealSignature</span><span class="p">(</span><span class="nx">revealSigResponse</span><span class="p">.</span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="nx">bob</span><span class="p">);</span>

<span class="c1">// Alice processes signature</span>
<span class="k">await</span><span class="w"> </span><span class="nx">processSignature</span><span class="p">(</span><span class="nx">sigResponse</span><span class="p">.</span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="nx">alice</span><span class="p">);</span>

<span class="c1">// Both parties now have encrypted communication</span>
<span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;Alice state:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">alice</span><span class="p">.</span><span class="nx">getState</span><span class="p">());</span><span class="w"> </span><span class="c1">// ENCRYPTED</span>
<span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;Bob state:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">bob</span><span class="p">.</span><span class="nx">getState</span><span class="p">());</span><span class="w">     </span><span class="c1">// ENCRYPTED</span>
</pre></div>
</div>
</section>
<section id="error-handling">
<h3><a class="toc-backref" href="#id38" role="doc-backlink">Error Handling</a><a class="headerlink" href="#error-handling" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">processDHCommit</span><span class="p">(</span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="nx">state</span><span class="p">);</span>
<span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="w"> </span><span class="ow">instanceof</span><span class="w"> </span><span class="nx">AKEError</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">switch</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="p">.</span><span class="nx">code</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">case</span><span class="w"> </span><span class="s1">&#39;INVALID_STATE&#39;</span><span class="o">:</span>
<span class="w">        </span><span class="nx">console</span><span class="p">.</span><span class="nx">error</span><span class="p">(</span><span class="s1">&#39;Invalid state for DH commit processing&#39;</span><span class="p">);</span>
<span class="w">        </span><span class="nx">state</span><span class="p">.</span><span class="nx">goPlaintext</span><span class="p">();</span>
<span class="w">        </span><span class="k">break</span><span class="p">;</span>
<span class="w">      </span><span class="k">case</span><span class="w"> </span><span class="s1">&#39;CRYPTO_ERROR&#39;</span><span class="o">:</span>
<span class="w">        </span><span class="nx">console</span><span class="p">.</span><span class="nx">error</span><span class="p">(</span><span class="s1">&#39;Cryptographic operation failed&#39;</span><span class="p">);</span>
<span class="w">        </span><span class="k">throw</span><span class="w"> </span><span class="nx">error</span><span class="p">;</span>
<span class="w">      </span><span class="k">default</span><span class="o">:</span>
<span class="w">        </span><span class="nx">console</span><span class="p">.</span><span class="nx">error</span><span class="p">(</span><span class="s1">&#39;Unknown AKE error:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">.</span><span class="nx">message</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p>This API reference provides complete documentation for all AKE system components, methods, events, and data structures.</p>
</section>
</section>
</section>

        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          <a class="next-page" href="steganography-summary.html">
              <div class="page-info">
                <div class="context">
                  <span>Next</span>
                </div>
                <div class="title">Steganography Documentation Summary</div>
              </div>
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
            </a>
          <a class="prev-page" href="ake-implementation.html">
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
              <div class="page-info">
                <div class="context">
                  <span>Previous</span>
                </div>
                
                <div class="title">AKE Implementation Guide</div>
                
              </div>
            </a>
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 1980, WebOTR Team
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer">
      
      
      <div class="toc-sticky toc-scroll">
        <div class="toc-title-container">
          <span class="toc-title">
            On this page
          </span>
        </div>
        <div class="toc-tree-container">
          <div class="toc-tree">
            <ul>
<li><a class="reference internal" href="#">AKE API Reference</a><ul>
<li><a class="reference internal" href="#core-functions">Core Functions</a><ul>
<li><a class="reference internal" href="#startake">startAKE()</a></li>
<li><a class="reference internal" href="#createdhcommit">createDHCommit()</a></li>
<li><a class="reference internal" href="#createdhkey">createDHKey()</a></li>
<li><a class="reference internal" href="#createrevealsignature">createRevealSignature()</a></li>
<li><a class="reference internal" href="#createsignature">createSignature()</a></li>
</ul>
</li>
<li><a class="reference internal" href="#message-processing-functions">Message Processing Functions</a><ul>
<li><a class="reference internal" href="#processdhcommit">processDHCommit()</a></li>
<li><a class="reference internal" href="#processdhkey">processDHKey()</a></li>
<li><a class="reference internal" href="#processrevealsignature">processRevealSignature()</a></li>
<li><a class="reference internal" href="#processsignature">processSignature()</a></li>
</ul>
</li>
<li><a class="reference internal" href="#state-management">State Management</a><ul>
<li><a class="reference internal" href="#otrstate-class">OtrState Class</a><ul>
<li><a class="reference internal" href="#id1">startAKE()</a></li>
<li><a class="reference internal" href="#goencrypted">goEncrypted()</a></li>
<li><a class="reference internal" href="#goplaintext">goPlaintext()</a></li>
<li><a class="reference internal" href="#setsessionkeys">setSessionKeys()</a></li>
<li><a class="reference internal" href="#cansendencrypted">canSendEncrypted()</a></li>
<li><a class="reference internal" href="#isakeinprogress">isAKEInProgress()</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#constants">Constants</a><ul>
<li><a class="reference internal" href="#protocol-states">Protocol States</a></li>
<li><a class="reference internal" href="#message-types">Message Types</a></li>
<li><a class="reference internal" href="#protocol-versions">Protocol Versions</a></li>
</ul>
</li>
<li><a class="reference internal" href="#cryptographic-functions">Cryptographic Functions</a><ul>
<li><a class="reference internal" href="#generatedhkeypair">generateDHKeyPair()</a></li>
<li><a class="reference internal" href="#dhexchange">dhExchange()</a></li>
<li><a class="reference internal" href="#derivekeys">deriveKeys()</a></li>
</ul>
</li>
<li><a class="reference internal" href="#error-classes">Error Classes</a><ul>
<li><a class="reference internal" href="#akeerror">AKEError</a></li>
</ul>
</li>
<li><a class="reference internal" href="#type-definitions">Type Definitions</a><ul>
<li><a class="reference internal" href="#dhkeypair">DHKeyPair</a></li>
<li><a class="reference internal" href="#dsakeypair">DSAKeyPair</a></li>
<li><a class="reference internal" href="#akemessage">AKEMessage</a></li>
<li><a class="reference internal" href="#sessionkeys">SessionKeys</a></li>
</ul>
</li>
<li><a class="reference internal" href="#usage-examples">Usage Examples</a><ul>
<li><a class="reference internal" href="#complete-ake-handshake">Complete AKE Handshake</a></li>
<li><a class="reference internal" href="#error-handling">Error Handling</a></li>
</ul>
</li>
</ul>
</li>
</ul>

          </div>
        </div>
      </div>
      
      
    </aside>
  </div>
</div><script src="../_static/documentation_options.js?v=01f34227"></script>
    <script src="../_static/doctools.js?v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/scripts/furo.js?v=5fa4622c"></script>
    <script src="../_static/mermaid-renderer.js?v=b683124c"></script>
    </body>
</html>