Forward Secrecy API Reference
=============================

Complete API documentation for WebOTR's Forward Secrecy system components.

.. contents:: Table of Contents
   :local:
   :depth: 3

ForwardSecrecyManager
---------------------

The main coordinator class for all forward secrecy operations.

Constructor
~~~~~~~~~~~

.. code-block:: javascript

   new ForwardSecrecyManager(options)

**Parameters:**

- ``options`` (Object): Configuration options

  - ``autoRotation`` (boolean): Enable automatic key rotation (default: true)
  - ``rotationInterval`` (number): Time between rotations in milliseconds (default: 3600000)
  - ``messageCountThreshold`` (number): Rotate after N messages (default: 1000)
  - ``dataVolumeThreshold`` (number): Rotate after N bytes (default: 10485760)
  - ``secureMemory`` (boolean): Enable secure memory management (default: true)
  - ``cryptographicErasure`` (boolean): Enable cryptographic erasure (default: true)
  - ``zeroKnowledgeProofs`` (boolean): Enable ZK proofs (default: true)
  - ``auditTrails`` (boolean): Enable audit logging (default: true)
  - ``rotationTimeout`` (number): Max rotation time in ms (default: 100)
  - ``deletionTimeout`` (number): Max deletion time in ms (default: 50)
  - ``verificationTimeout`` (number): Max verification time in ms (default: 100)
  - ``fipsCompliance`` (boolean): Enable FIPS compliance mode (default: true)
  - ``auditRetention`` (number): Audit log retention in ms (default: 7776000000)
  - ``enterpriseIntegration`` (boolean): Enable enterprise features (default: false)

Methods
~~~~~~~

initialize()
^^^^^^^^^^^^

.. code-block:: javascript

   async initialize()

Initialize the Forward Secrecy Manager and all its components.

**Returns:** Promise<Object>

.. code-block:: javascript

   {
     success: boolean,
     timestamp: number,
     components: {
       keyRotation: boolean,
       secureDeletion: boolean,
       zeroKnowledge: boolean,
       auditTrail: boolean
     }
   }

rotateKeysManually()
^^^^^^^^^^^^^^^^^^^^

.. code-block:: javascript

   async rotateKeysManually(reason = 'MANUAL_REQUEST')

Perform immediate manual key rotation.

**Parameters:**

- ``reason`` (string): Reason for manual rotation

**Returns:** Promise<Object>

.. code-block:: javascript

   {
     success: boolean,
     oldKeys: Object,
     newKeys: Object,
     rotationData: Object,
     rotationTime: number,
     trigger: string,
     timestamp: number
   }

emergencyRotation()
^^^^^^^^^^^^^^^^^^^

.. code-block:: javascript

   async emergencyRotation(threat = 'UNKNOWN')

Perform emergency key rotation with immediate secure deletion.

**Parameters:**

- ``threat`` (string): Type of security threat detected

**Returns:** Promise<Object>

.. code-block:: javascript

   {
     success: boolean,
     emergencyResponse: Object,
     rotationTime: number,
     deletionTime: number,
     timestamp: number
   }

getSecurityStatus()
^^^^^^^^^^^^^^^^^^^

.. code-block:: javascript

   async getSecurityStatus()

Get current security status and metrics.

**Returns:** Promise<Object>

.. code-block:: javascript

   {
     initialized: boolean,
     currentGeneration: number,
     lastRotation: number,
     nextRotation: number,
     rotationCount: number,
     deletionCount: number,
     complianceStatus: Object,
     performanceMetrics: Object
   }

generateComplianceReport()
^^^^^^^^^^^^^^^^^^^^^^^^^^

.. code-block:: javascript

   async generateComplianceReport(options = {})

Generate compliance report for audit purposes.

**Parameters:**

- ``options`` (Object): Report configuration

  - ``startDate`` (number): Report start timestamp
  - ``endDate`` (number): Report end timestamp
  - ``standards`` (Array): Compliance standards to include
  - ``format`` (string): Output format ('json', 'pdf', 'csv')
  - ``includeMetrics`` (boolean): Include performance metrics
  - ``includeRecommendations`` (boolean): Include recommendations

**Returns:** Promise<Object>

shutdown()
^^^^^^^^^^

.. code-block:: javascript

   async shutdown()

Gracefully shutdown the Forward Secrecy Manager.

**Returns:** Promise<Object>

Events
~~~~~~

The ForwardSecrecyManager extends EventEmitter and emits the following events:

initialized
^^^^^^^^^^^

Emitted when the system is fully initialized.

.. code-block:: javascript

   forwardSecrecy.on('initialized', (event) => {
     // event.timestamp: number
     // event.configuration: Object
   });

rotationTriggered
^^^^^^^^^^^^^^^^^

Emitted when key rotation is initiated.

.. code-block:: javascript

   forwardSecrecy.on('rotationTriggered', (event) => {
     // event.trigger: string
     // event.currentGeneration: number
     // event.timestamp: number
   });

rotationCompleted
^^^^^^^^^^^^^^^^^

Emitted when key rotation completes successfully.

.. code-block:: javascript

   forwardSecrecy.on('rotationCompleted', (event) => {
     // event.success: boolean
     // event.oldKeys: Object
     // event.newKeys: Object
     // event.rotationTime: number
     // event.trigger: string
     // event.timestamp: number
   });

deletionCompleted
^^^^^^^^^^^^^^^^^

Emitted when secure deletion is verified.

.. code-block:: javascript

   forwardSecrecy.on('deletionCompleted', (event) => {
     // event.keyGeneration: number
     // event.deletionTime: number
     // event.verified: boolean
     // event.passes: number
     // event.timestamp: number
   });

KeyRotationEngine
-----------------

Handles automatic and manual key rotation operations.

Constructor
~~~~~~~~~~~

.. code-block:: javascript

   new KeyRotationEngine(options)

**Parameters:**

- ``options`` (Object): Configuration options

  - ``autoRotation`` (boolean): Enable automatic rotation
  - ``rotationInterval`` (number): Time between rotations
  - ``messageCountThreshold`` (number): Message count trigger
  - ``dataVolumeThreshold`` (number): Data volume trigger
  - ``emergencyRotation`` (boolean): Enable emergency rotation
  - ``maxRotationTime`` (number): Maximum rotation time
  - ``keySize`` (number): Key size in bytes (default: 32)

Methods
~~~~~~~

initialize()
^^^^^^^^^^^^

.. code-block:: javascript

   async initialize()

Initialize the key rotation engine.

generateKeySet()
^^^^^^^^^^^^^^^^

.. code-block:: javascript

   async generateKeySet(generation)

Generate a new set of cryptographic keys.

**Parameters:**

- ``generation`` (number): Key generation number

**Returns:** Promise<Object>

.. code-block:: javascript

   {
     generation: number,
     masterKey: Uint8Array,
     encryptionKey: Uint8Array,
     macKey: Uint8Array,
     nextKeyMaterial: Uint8Array,
     keyFingerprint: string,
     createdAt: number,
     expiresAt: number
   }

rotateKeys()
^^^^^^^^^^^^

.. code-block:: javascript

   async rotateKeys(rotationRequest)

Perform key rotation operation.

**Parameters:**

- ``rotationRequest`` (Object): Rotation request details

  - ``trigger`` (string): Rotation trigger type
  - ``currentGeneration`` (number): Current key generation
  - ``reason`` (string): Rotation reason

**Returns:** Promise<Object>

SecureDeletionManager
---------------------

Provides DoD 5220.22-M compliant secure deletion of cryptographic material.

Constructor
~~~~~~~~~~~

.. code-block:: javascript

   new SecureDeletionManager(options)

**Parameters:**

- ``options`` (Object): Configuration options

  - ``cryptographicErasure`` (boolean): Enable cryptographic erasure
  - ``secureMemory`` (boolean): Enable secure memory management
  - ``deletionTimeout`` (number): Maximum deletion time
  - ``overwritePasses`` (number): Number of overwrite passes (default: 7)
  - ``verificationEnabled`` (boolean): Enable deletion verification
  - ``fipsCompliance`` (boolean): Enable FIPS compliance
  - ``enhancedErasure`` (boolean): Enable enhanced erasure patterns
  - ``memoryForensicsResistance`` (boolean): Enable anti-forensics measures

Methods
~~~~~~~

performSecureDeletion()
^^^^^^^^^^^^^^^^^^^^^^^

.. code-block:: javascript

   async performSecureDeletion(keyMaterial)

Perform secure deletion of cryptographic material.

**Parameters:**

- ``keyMaterial`` (Object): Key material to delete

  - ``data`` (Uint8Array): Key data
  - ``type`` (string): Key type
  - ``generation`` (number): Key generation

**Returns:** Promise<Object>

.. code-block:: javascript

   {
     success: boolean,
     deletionTime: number,
     passes: number,
     verified: boolean,
     entropy: number,
     timestamp: number
   }

verifyDeletion()
^^^^^^^^^^^^^^^^

.. code-block:: javascript

   async verifyDeletion(keyMaterial)

Verify that secure deletion was successful.

**Parameters:**

- ``keyMaterial`` (Object): Key material to verify

**Returns:** Promise<Object>

.. code-block:: javascript

   {
     success: boolean,
     verified: boolean,
     entropy: number,
     residualData: boolean,
     timestamp: number
   }

ZeroKnowledgeVerifier
---------------------

Generates and verifies cryptographic proofs without revealing sensitive data.

Constructor
~~~~~~~~~~~

.. code-block:: javascript

   new ZeroKnowledgeVerifier(options)

**Parameters:**

- ``options`` (Object): Configuration options

  - ``enabled`` (boolean): Enable zero-knowledge verification
  - ``verificationTimeout`` (number): Maximum verification time
  - ``fipsCompliance`` (boolean): Enable FIPS compliance
  - ``proofRetention`` (number): Proof retention period
  - ``advancedProofs`` (boolean): Enable advanced proof protocols
  - ``enterpriseFeatures`` (boolean): Enable enterprise features
  - ``batchVerification`` (boolean): Enable batch verification
  - ``proofCompression`` (boolean): Enable proof compression

Methods
~~~~~~~

generateRotationProof()
^^^^^^^^^^^^^^^^^^^^^^^

.. code-block:: javascript

   async generateRotationProof(rotationData)

Generate zero-knowledge proof of key rotation.

**Parameters:**

- ``rotationData`` (Object): Rotation data

  - ``oldKeys`` (Object): Previous key set
  - ``newKeys`` (Object): New key set
  - ``rotationTime`` (number): Rotation time
  - ``trigger`` (string): Rotation trigger

**Returns:** Promise<Object>

.. code-block:: javascript

   {
     proof: Object,
     metadata: Object,
     commitment: Object,
     timestamp: number
   }

generateDeletionProof()
^^^^^^^^^^^^^^^^^^^^^^^

.. code-block:: javascript

   async generateDeletionProof(deletionData)

Generate zero-knowledge proof of secure deletion.

**Parameters:**

- ``deletionData`` (Object): Deletion data

**Returns:** Promise<Object>

verifyProof()
^^^^^^^^^^^^^

.. code-block:: javascript

   async verifyProof(proofData)

Verify a zero-knowledge proof.

**Parameters:**

- ``proofData`` (Object): Proof to verify

**Returns:** Promise<Object>

.. code-block:: javascript

   {
     valid: boolean,
     proofType: string,
     verificationTime: number,
     metadata: Object,
     timestamp: number
   }

verifyBatchProofs()
^^^^^^^^^^^^^^^^^^^

.. code-block:: javascript

   async verifyBatchProofs(proofBatch)

Verify multiple proofs in batch for performance.

**Parameters:**

- ``proofBatch`` (Array): Array of proofs to verify

**Returns:** Promise<Object>

.. code-block:: javascript

   {
     totalProofs: number,
     successfulVerifications: number,
     failedVerifications: number,
     batchVerificationTime: number,
     averageVerificationTime: number,
     results: Array
   }

AuditTrailSystem
----------------

Maintains tamper-evident logs for compliance and security monitoring.

Constructor
~~~~~~~~~~~

.. code-block:: javascript

   new AuditTrailSystem(options)

**Parameters:**

- ``options`` (Object): Configuration options

  - ``enabled`` (boolean): Enable audit trails
  - ``retentionPeriod`` (number): Log retention period
  - ``fipsCompliance`` (boolean): Enable FIPS compliance
  - ``maxLogSize`` (number): Maximum log entries
  - ``compressionEnabled`` (boolean): Enable log compression
  - ``encryptionEnabled`` (boolean): Enable log encryption

Methods
~~~~~~~

logEvent()
^^^^^^^^^^

.. code-block:: javascript

   async logEvent(eventData)

Log an audit event.

**Parameters:**

- ``eventData`` (Object): Event data

  - ``type`` (string): Event type
  - ``timestamp`` (number): Event timestamp
  - ``details`` (Object): Event details
  - ``metadata`` (Object): Additional metadata

**Returns:** Promise<Object>

.. code-block:: javascript

   {
     id: string,
     type: string,
     timestamp: number,
     details: Object,
     metadata: Object,
     signature: string
   }

getAuditLog()
^^^^^^^^^^^^^

.. code-block:: javascript

   async getAuditLog(options = {})

Retrieve audit log entries.

**Parameters:**

- ``options`` (Object): Query options

  - ``startDate`` (number): Start timestamp
  - ``endDate`` (number): End timestamp
  - ``eventTypes`` (Array): Event types to include
  - ``limit`` (number): Maximum entries to return

**Returns:** Promise<Array>

generateAuditReport()
^^^^^^^^^^^^^^^^^^^^^

.. code-block:: javascript

   async generateAuditReport(options = {})

Generate comprehensive audit report.

**Parameters:**

- ``options`` (Object): Report options

**Returns:** Promise<Object>

verifyAuditIntegrity()
^^^^^^^^^^^^^^^^^^^^^^

.. code-block:: javascript

   async verifyAuditIntegrity()

Verify the integrity of the audit log chain.

**Returns:** Promise<Object>

.. code-block:: javascript

   {
     valid: boolean,
     totalEvents: number,
     verifiedEvents: number,
     integrityViolations: Array,
     timestamp: number
   }

EnterprisePolicyManager
-----------------------

Manages enterprise policies and compliance requirements.

Constructor
~~~~~~~~~~~

.. code-block:: javascript

   new EnterprisePolicyManager(options)

**Parameters:**

- ``options`` (Object): Configuration options

  - ``policyEnforcement`` (boolean): Enable policy enforcement
  - ``complianceStandards`` (Array): Compliance standards to enforce
  - ``auditLevel`` (string): Audit level ('basic', 'comprehensive')
  - ``retentionPolicies`` (Object): Data retention policies
  - ``alerting`` (Object): Alerting configuration

Methods
~~~~~~~

validateCompliance()
^^^^^^^^^^^^^^^^^^^^

.. code-block:: javascript

   async validateCompliance(operation)

Validate operation against enterprise policies.

**Parameters:**

- ``operation`` (Object): Operation to validate

**Returns:** Promise<Object>

.. code-block:: javascript

   {
     compliant: boolean,
     violations: Array,
     recommendations: Array,
     timestamp: number
   }

enforcePolicy()
^^^^^^^^^^^^^^^

.. code-block:: javascript

   async enforcePolicy(policyName, context)

Enforce a specific enterprise policy.

**Parameters:**

- ``policyName`` (string): Name of policy to enforce
- ``context`` (Object): Operation context

**Returns:** Promise<Object>

generateComplianceReport()
^^^^^^^^^^^^^^^^^^^^^^^^^^

.. code-block:: javascript

   async generateComplianceReport(options)

Generate enterprise compliance report.

**Parameters:**

- ``options`` (Object): Report configuration

**Returns:** Promise<Object>

Error Classes
-------------

ForwardSecrecyError
~~~~~~~~~~~~~~~~~~~

Base error class for Forward Secrecy operations.

.. code-block:: javascript

   class ForwardSecrecyError extends Error {
     constructor(message, code, details = {})
   }

**Properties:**

- ``message`` (string): Error message
- ``code`` (string): Error code
- ``details`` (Object): Additional error details
- ``timestamp`` (number): Error timestamp

KeyRotationError
~~~~~~~~~~~~~~~~

Error class for key rotation failures.

.. code-block:: javascript

   class KeyRotationError extends ForwardSecrecyError {
     constructor(message, rotationContext = {})
   }

**Additional Properties:**

- ``rotationContext`` (Object): Rotation operation context
- ``currentGeneration`` (number): Current key generation
- ``trigger`` (string): Rotation trigger

SecureDeletionError
~~~~~~~~~~~~~~~~~~~

Error class for secure deletion failures.

.. code-block:: javascript

   class SecureDeletionError extends ForwardSecrecyError {
     constructor(message, deletionContext = {})
   }

**Additional Properties:**

- ``deletionContext`` (Object): Deletion operation context
- ``keyGeneration`` (number): Key generation being deleted
- ``passes`` (number): Completed overwrite passes

ZeroKnowledgeError
~~~~~~~~~~~~~~~~~~

Error class for zero-knowledge proof failures.

.. code-block:: javascript

   class ZeroKnowledgeError extends ForwardSecrecyError {
     constructor(message, proofContext = {})
   }

**Additional Properties:**

- ``proofContext`` (Object): Proof operation context
- ``proofType`` (string): Type of proof
- ``verificationStage`` (string): Stage where error occurred

Constants
---------

Event Types
~~~~~~~~~~~

.. code-block:: javascript

   const EVENT_TYPES = {
     // Initialization events
     SYSTEM_INITIALIZED: 'SYSTEM_INITIALIZED',
     COMPONENT_INITIALIZED: 'COMPONENT_INITIALIZED',

     // Rotation events
     ROTATION_TRIGGERED: 'ROTATION_TRIGGERED',
     ROTATION_STARTED: 'ROTATION_STARTED',
     ROTATION_COMPLETED: 'ROTATION_COMPLETED',
     ROTATION_FAILED: 'ROTATION_FAILED',

     // Deletion events
     DELETION_STARTED: 'DELETION_STARTED',
     DELETION_COMPLETED: 'DELETION_COMPLETED',
     DELETION_FAILED: 'DELETION_FAILED',
     DELETION_VERIFIED: 'DELETION_VERIFIED',

     // Proof events
     PROOF_GENERATED: 'PROOF_GENERATED',
     PROOF_VERIFIED: 'PROOF_VERIFIED',
     PROOF_FAILED: 'PROOF_FAILED',

     // Compliance events
     COMPLIANCE_CHECK: 'COMPLIANCE_CHECK',
     COMPLIANCE_VIOLATION: 'COMPLIANCE_VIOLATION',
     AUDIT_EVENT: 'AUDIT_EVENT',

     // Performance events
     PERFORMANCE_METRIC: 'PERFORMANCE_METRIC',
     PERFORMANCE_WARNING: 'PERFORMANCE_WARNING',

     // Security events
     SECURITY_ALERT: 'SECURITY_ALERT',
     EMERGENCY_ROTATION: 'EMERGENCY_ROTATION',
     THREAT_DETECTED: 'THREAT_DETECTED'
   };

Rotation Triggers
~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   const ROTATION_TRIGGERS = {
     TIME_BASED: 'TIME_BASED',
     MESSAGE_COUNT: 'MESSAGE_COUNT',
     DATA_VOLUME: 'DATA_VOLUME',
     MANUAL_REQUEST: 'MANUAL_REQUEST',
     EMERGENCY: 'EMERGENCY',
     POLICY_ENFORCEMENT: 'POLICY_ENFORCEMENT',
     SECURITY_EVENT: 'SECURITY_EVENT'
   };

Compliance Standards
~~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   const COMPLIANCE_STANDARDS = {
     FIPS_140_2: 'FIPS-140-2',
     DOD_5220_22_M: 'DoD-5220.22-M',
     SOX: 'SOX',
     HIPAA: 'HIPAA',
     GDPR: 'GDPR',
     ISO_27001: 'ISO-27001'
   };

Deletion Patterns
~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   const DELETION_PATTERNS = {
     RANDOM: 'random',
     ZEROS: 'zeros',
     ONES: 'ones',
     ALTERNATING: 'alternating',
     INVERSE_ALTERNATING: 'inverse_alternating',
     HASH_BASED: 'hash_based'
   };

Type Definitions
----------------

KeySet
~~~~~~

.. code-block:: typescript

   interface KeySet {
     generation: number;
     masterKey: Uint8Array;
     encryptionKey: Uint8Array;
     macKey: Uint8Array;
     nextKeyMaterial: Uint8Array;
     keyFingerprint: string;
     createdAt: number;
     expiresAt: number;
   }

RotationResult
~~~~~~~~~~~~~~

.. code-block:: typescript

   interface RotationResult {
     success: boolean;
     oldKeys: KeySet;
     newKeys: KeySet;
     rotationData: Object;
     rotationTime: number;
     trigger: string;
     timestamp: number;
   }

DeletionResult
~~~~~~~~~~~~~~

.. code-block:: typescript

   interface DeletionResult {
     success: boolean;
     deletionTime: number;
     passes: number;
     verified: boolean;
     entropy: number;
     timestamp: number;
   }

ProofData
~~~~~~~~~

.. code-block:: typescript

   interface ProofData {
     proof: Object;
     metadata: {
       proofId: string;
       timestamp: number;
       generation: number;
       verificationTime: number;
     };
     commitment: {
       old: string;
       new: string;
     };
   }

AuditEvent
~~~~~~~~~~

.. code-block:: typescript

   interface AuditEvent {
     id: string;
     type: string;
     timestamp: number;
     details: Object;
     metadata: {
       source: string;
       version: string;
       sequenceNumber: number;
       chainHash?: string;
     };
     signature: string;
     compliance?: {
       fipsCompliant: boolean;
       dodCompliant: boolean;
       retentionPeriod: number;
     };
   }

ComplianceReport
~~~~~~~~~~~~~~~~

.. code-block:: typescript

   interface ComplianceReport {
     reportId: string;
     generatedAt: number;
     period: {
       startDate: number;
       endDate: number;
     };
     standards: string[];
     metrics: Object;
     events: number;
     compliance: {
       overallScore: number;
       standardsCompliance: Object;
       recommendations: string[];
     };
   }

Usage Examples
--------------

Basic Usage
~~~~~~~~~~~

.. code-block:: javascript

   import { ForwardSecrecyManager } from 'webOTR/core/forward-secrecy';

   // Initialize with default configuration
   const forwardSecrecy = new ForwardSecrecyManager();

   // Set up event listeners
   forwardSecrecy.on('rotationCompleted', (event) => {
     console.log(`Key rotation completed in ${event.rotationTime}ms`);
   });

   // Initialize and start
   await forwardSecrecy.initialize();

Advanced Configuration
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   const forwardSecrecy = new ForwardSecrecyManager({
     autoRotation: true,
     rotationInterval: 1800000,        // 30 minutes
     messageCountThreshold: 500,
     dataVolumeThreshold: 5242880,     // 5MB
     fipsCompliance: true,
     enterpriseIntegration: true,
     auditTrails: true
   });

   await forwardSecrecy.initialize();

Manual Operations
~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   // Manual key rotation
   const rotationResult = await forwardSecrecy.rotateKeysManually('Security audit');

   // Emergency rotation
   const emergencyResult = await forwardSecrecy.emergencyRotation('Suspected compromise');

   // Get security status
   const status = await forwardSecrecy.getSecurityStatus();

   // Generate compliance report
   const report = await forwardSecrecy.generateComplianceReport({
     standards: ['FIPS-140-2', 'DoD-5220.22-M'],
     format: 'json'
   });

Error Handling
~~~~~~~~~~~~~~

.. code-block:: javascript

   try {
     await forwardSecrecy.rotateKeysManually();
   } catch (error) {
     if (error instanceof KeyRotationError) {
       console.error('Key rotation failed:', error.message);
       console.error('Context:', error.rotationContext);
     } else if (error instanceof ForwardSecrecyError) {
       console.error('Forward secrecy error:', error.code, error.details);
     } else {
       console.error('Unexpected error:', error);
     }
   }

This API reference provides complete documentation for all Forward Secrecy system components, methods, events, and data structures.
