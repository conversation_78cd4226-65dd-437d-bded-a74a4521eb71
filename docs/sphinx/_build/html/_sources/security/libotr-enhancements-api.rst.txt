libOTR Enhancements API Reference
==================================

This document provides comprehensive API reference for WebOTR's libOTR security enhancements.

Constant-Time Operations API
----------------------------

.. js:class:: ConstantTimeOps

   Provides timing attack resistant cryptographic operations.

   .. js:staticmethod:: constantTimeEqual(a, b)

      Performs constant-time equality comparison of two arrays.

      :param Uint8Array a: First array to compare
      :param Uint8Array b: Second array to compare
      :returns: True if arrays are equal, false otherwise
      :rtype: boolean

      **Example:**

      .. code-block:: javascript

         import { ConstantTimeOps } from 'webOTR';

         const mac1 = new Uint8Array([0x01, 0x02, 0x03]);
         const mac2 = new Uint8Array([0x01, 0x02, 0x03]);
         const isEqual = ConstantTimeOps.constantTimeEqual(mac1, mac2); // true

   .. js:staticmethod:: constantTimeCompare(a, b)

      Performs constant-time comparison returning ordering information.

      :param Uint8Array a: First array to compare
      :param Uint8Array b: Second array to compare
      :returns: 0 if equal, <0 if a < b, >0 if a > b
      :rtype: number

   .. js:staticmethod:: conditionalSelect(condition, valueA, valueB)

      Selects value based on condition without branching.

      :param boolean condition: Selection condition
      :param number valueA: Value to select if condition is true
      :param number valueB: Value to select if condition is false
      :returns: Selected value
      :rtype: number

   .. js:staticmethod:: memoryDiffer(buffer1, buffer2)

      Checks if two memory buffers differ (libOTR ``otrl_mem_differ`` equivalent).

      :param Uint8Array buffer1: First buffer
      :param Uint8Array buffer2: Second buffer
      :returns: True if buffers differ, false if identical
      :rtype: boolean

   .. js:staticmethod:: selfTest()

      Performs self-test of constant-time operations.

      :returns: True if all tests pass
      :rtype: boolean

Input Validation API
--------------------

.. js:class:: CryptoValidation

   Comprehensive cryptographic parameter validation framework.

   .. js:staticmethod:: validateDHPublicKey(publicKey)

      Validates Diffie-Hellman public key according to RFC 3526.

      :param BigInteger|string|Uint8Array publicKey: DH public key to validate
      :throws SecurityValidationError: If key is invalid
      :returns: True if valid
      :rtype: boolean

      **Validation Rules:**

      - Key must be in range [2, p-2] where p is the DH modulus
      - Key must not be a weak key (generator, identity, etc.)
      - Key must be properly formatted

      **Example:**

      .. code-block:: javascript

         import { CryptoValidation } from 'webOTR';
         import { BigInteger } from 'jsbn';

         const publicKey = new BigInteger('12345678901234567890ABCDEF', 16);
         CryptoValidation.validateDHPublicKey(publicKey); // Throws if invalid

   .. js:staticmethod:: validateSMPGroupElement(element)

      Validates SMP group element for Socialist Millionaire Protocol.

      :param BigInteger|string|Uint8Array element: Group element to validate
      :throws SecurityValidationError: If element is invalid
      :returns: True if valid
      :rtype: boolean

   .. js:staticmethod:: validateZKProofStructure(proof)

      Validates zero-knowledge proof structure.

      :param Object proof: ZK proof object with c and d components
      :throws SecurityValidationError: If proof structure is invalid
      :returns: True if valid
      :rtype: boolean

   .. js:staticmethod:: validateProtocolMessage(message, expectedType)

      Validates OTR protocol message structure and content.

      :param Object message: Protocol message to validate
      :param string expectedType: Expected message type (optional)
      :throws SecurityValidationError: If message is invalid
      :returns: True if valid
      :rtype: boolean

   .. js:staticmethod:: validateInstanceTag(tag)

      Validates OTR instance tag.

      :param number tag: Instance tag to validate
      :throws SecurityValidationError: If tag is invalid
      :returns: True if valid
      :rtype: boolean

   .. js:staticmethod:: validateMessageCounter(newCounter, lastCounter)

      Validates message counter for replay protection.

      :param number newCounter: New message counter
      :param number lastCounter: Last seen counter
      :throws SecurityValidationError: If counter is invalid
      :returns: True if valid
      :rtype: boolean

   .. js:staticmethod:: batchValidate(values, validator)

      Performs batch validation of multiple values.

      :param Array values: Array of values to validate
      :param Function validator: Validation function to apply
      :throws SecurityValidationError: If any validation fails
      :returns: True if all valid
      :rtype: boolean

.. js:class:: SecurityValidationError

   Custom error class for validation failures.

   .. js:attribute:: code

      Error code for programmatic handling.

      :type: string

   .. js:attribute:: message

      Human-readable error message.

      :type: string

Secure Memory Management API
-----------------------------

.. js:class:: SecureMemory

   Secure memory allocation with automatic wiping and lifecycle management.

   .. js:method:: constructor(size, options)

      Creates a new secure memory buffer.

      :param number size: Size of buffer in bytes
      :param Object options: Configuration options

      **Options:**

      - ``autoWipe`` (boolean): Automatic wiping on destroy (default: true)
      - ``wipePatterns`` (Array): Patterns for secure wiping (default: [0xFF, 0xAA, 0x55, 0x00])
      - ``trackAccess`` (boolean): Enable access tracking (default: false)

   .. js:method:: write(data, offset)

      Writes data to secure memory.

      :param Uint8Array|Array data: Data to write
      :param number offset: Offset to start writing (default: 0)

   .. js:method:: read(length, offset)

      Reads data from secure memory.

      :param number length: Number of bytes to read (default: all)
      :param number offset: Offset to start reading (default: 0)
      :returns: Copy of the data
      :rtype: Uint8Array

   .. js:method:: getView()

      Gets a view of the secure memory buffer.

      :returns: View of the memory buffer
      :rtype: Uint8Array

   .. js:method:: secureWipe()

      Securely wipes the memory buffer using multiple passes.

   .. js:method:: destroy()

      Destroys the secure memory buffer with secure wiping.

   .. js:method:: getStats()

      Gets memory usage statistics.

      :returns: Statistics object
      :rtype: Object

   .. js:staticmethod:: cleanupAll()

      Cleans up all secure memory instances.

   .. js:staticmethod:: getGlobalStats()

      Gets global memory statistics.

      :returns: Global statistics object
      :rtype: Object

.. js:class:: SecureMemoryPool

   Memory pool for efficient secure memory allocation and reuse.

   .. js:method:: constructor(options)

      Creates a new secure memory pool.

      :param Object options: Pool configuration options

   .. js:method:: allocate(size)

      Allocates secure memory from the pool.

      :param number size: Size of memory to allocate
      :returns: Secure memory instance
      :rtype: SecureMemory

   .. js:method:: deallocate(memory)

      Returns secure memory to the pool.

      :param SecureMemory memory: Memory to return to pool

   .. js:method:: getStats()

      Gets pool statistics.

      :returns: Pool statistics object
      :rtype: Object

   .. js:method:: cleanup()

      Cleans up expired memory from pools.

   .. js:method:: destroy()

      Destroys all pooled memory.

Enhanced Error Recovery API
----------------------------

.. js:class:: ProtocolErrorRecovery

   Robust protocol error handling and state recovery system.

   .. js:method:: constructor(options)

      Creates a new error recovery instance.

      :param Object options: Configuration options

      **Options:**

      - ``maxRetries`` (number): Maximum retry attempts (default: 3)
      - ``retryDelay`` (number): Delay between retries in ms (default: 1000)
      - ``enableLogging`` (boolean): Enable console logging (default: true)
      - ``securityEventHandler`` (Function): Custom event handler

   .. js:method:: handleAKEError(error, context)

      Handles AKE protocol errors with automatic recovery.

      :param Error error: The error that occurred
      :param Object context: Protocol context
      :returns: Recovery action object
      :rtype: Object

      **Recovery Object:**

      - ``strategy`` (string): Recovery strategy to apply
      - ``action`` (string): Specific action to take
      - ``message`` (string): Human-readable description

   .. js:method:: getStats()

      Gets recovery statistics.

      :returns: Statistics object with recovery metrics
      :rtype: Object

   .. js:method:: resetStats()

      Resets recovery statistics.

   .. js:staticmethod:: setAccessLogger(logger)

      Sets access logger for monitoring.

      :param Function logger: Logger function

.. js:class:: SecurityEvent

   Security event for logging and monitoring.

   .. js:method:: constructor(type, severity, message, context)

      Creates a new security event.

      :param string type: Event type
      :param string severity: Event severity (LOW, MEDIUM, HIGH, CRITICAL)
      :param string message: Event message
      :param Object context: Event context

   .. js:method:: toJSON()

      Serializes event to JSON.

      :returns: JSON representation
      :rtype: Object

Constants and Enums
-------------------

.. js:data:: ERROR_TYPES

   Error type constants for classification.

   **Protocol Errors:**

   - ``PROTOCOL_VIOLATION``: General protocol violation
   - ``INVALID_MESSAGE_TYPE``: Invalid message type
   - ``SEQUENCE_ERROR``: Message sequence error

   **Cryptographic Errors:**

   - ``INVALID_SIGNATURE``: Signature verification failure
   - ``INVALID_MAC``: MAC verification failure
   - ``INVALID_DH_KEY``: Invalid DH public key

   **Security Errors:**

   - ``REPLAY_ATTACK``: Potential replay attack
   - ``COMPETING_DH_COMMIT``: Competing DH commits

.. js:data:: RECOVERY_STRATEGIES

   Recovery strategy constants.

   - ``IGNORE``: Ignore the error and continue
   - ``RETRY``: Retry the operation
   - ``RESET_STATE``: Reset protocol state
   - ``GRACEFUL_DEGRADATION``: Degrade to plaintext
   - ``ABORT_SESSION``: Abort the session
   - ``RESTART_PROTOCOL``: Restart the protocol

Global Instances
----------------

.. js:data:: globalErrorRecovery

   Global error recovery instance for consistent error handling across the application.

   :type: ProtocolErrorRecovery

.. js:data:: globalSecureMemoryPool

   Global secure memory pool for efficient memory management.

   :type: SecureMemoryPool

Usage Examples
--------------

**Complete Security Integration Example:**

.. code-block:: javascript

   import {
     ConstantTimeOps,
     CryptoValidation,
     SecureMemory,
     ProtocolErrorRecovery,
     ERROR_TYPES
   } from 'webOTR';

   // Secure key handling
   const keyMemory = new SecureMemory(32);
   const sessionKey = new Uint8Array(32);
   crypto.getRandomValues(sessionKey);
   keyMemory.write(sessionKey);

   try {
     // Validate DH public key
     CryptoValidation.validateDHPublicKey(dhPublicKey);

     // Constant-time MAC verification
     const isValidMAC = ConstantTimeOps.constantTimeEqual(
       computedMAC, 
       receivedMAC
     );

     if (!isValidMAC) {
       throw new Error('MAC verification failed');
     }

   } catch (error) {
     // Handle errors with recovery system
     const recovery = new ProtocolErrorRecovery();
     const result = recovery.handleAKEError(error, protocolContext);
     
     console.log(`Recovery strategy: ${result.strategy}`);
     
   } finally {
     // Secure cleanup
     keyMemory.destroy();
   }

**Performance Monitoring Example:**

.. code-block:: javascript

   import { ConstantTimeOps, SecureMemory } from 'webOTR';

   // Monitor timing consistency
   function benchmarkConstantTime() {
     const iterations = 1000;
     const data1 = new Uint8Array(256);
     const data2 = new Uint8Array(256);
     
     crypto.getRandomValues(data1);
     crypto.getRandomValues(data2);

     const start = performance.now();
     for (let i = 0; i < iterations; i++) {
       ConstantTimeOps.constantTimeEqual(data1, data2);
     }
     const duration = performance.now() - start;
     
     console.log(`Constant-time ops: ${duration.toFixed(2)}ms for ${iterations} iterations`);
   }

   // Monitor memory usage
   function monitorMemoryUsage() {
     const stats = SecureMemory.getGlobalStats();
     console.log('Memory Statistics:', {
       totalInstances: stats.totalInstances,
       activeInstances: stats.activeInstances,
       totalSize: stats.totalSize,
       averageSize: stats.averageSize
     });
   }

For implementation details, see :doc:`libotr-enhancements-implementation`.
