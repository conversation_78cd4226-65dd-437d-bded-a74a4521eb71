AKE API Reference
==================

Complete API documentation for WebOTR's Authenticated Key Exchange (AKE) system components.

.. contents:: Table of Contents
   :local:
   :depth: 3

Core Functions
--------------

startAKE()
~~~~~~~~~~

.. code-block:: javascript

   async startAKE(state)

Initiates the Authenticated Key Exchange protocol.

**Parameters:**

- ``state`` (OtrState): Current OTR state object

**Returns:** Promise<Object>

.. code-block:: javascript

   {
     state: OtrState,        // Updated state object
     message: string,        // Message to send ('?OTR:AKESTART')
     dhCommit: Object        // DH commit message data
   }

**Example:**

.. code-block:: javascript

   const state = new OtrState();
   state.dsaKeyPair = await generateDSAKeyPair();
   
   const result = await startAKE(state);
   console.log('AKE started:', result.message);

createDHCommit()
~~~~~~~~~~~~~~~~

.. code-block:: javascript

   async createDHCommit(dhKeyPair, protocolVersion, instanceTag, receiverInstanceTag)

Creates a DH Commit message to initiate the AKE handshake.

**Parameters:**

- ``dhKeyPair`` (Object): Diffie-Hellman key pair
- ``protocolVersion`` (number): OTR protocol version (default: 3)
- ``instanceTag`` (number): Sender's instance tag
- ``receiverInstanceTag`` (number): Receiver's instance tag

**Returns:** Promise<Object>

.. code-block:: javascript

   {
     protocolVersion: number,
     messageType: 'DH_COMMIT',
     senderInstanceTag: number,
     receiverInstanceTag: number,
     encryptedGx: Uint8Array,      // Encrypted DH public key
     hashOfEncryptedGx: Uint8Array, // Hash of encrypted key
     aesKey: Uint8Array,           // AES key for encryption
     iv: Uint8Array                // Initialization vector
   }

createDHKey()
~~~~~~~~~~~~~

.. code-block:: javascript

   async createDHKey(dhKeyPair, protocolVersion, instanceTag, receiverInstanceTag)

Creates a DH Key message in response to a DH Commit.

**Parameters:**

- ``dhKeyPair`` (Object): Diffie-Hellman key pair
- ``protocolVersion`` (number): OTR protocol version
- ``instanceTag`` (number): Sender's instance tag
- ``receiverInstanceTag`` (number): Receiver's instance tag

**Returns:** Promise<Object>

.. code-block:: javascript

   {
     protocolVersion: number,
     messageType: 'DH_KEY',
     senderInstanceTag: number,
     receiverInstanceTag: number,
     publicKey: Uint8Array         // DH public key
   }

createRevealSignature()
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   async createRevealSignature(dhKeyPair, dsaKeyPair, sharedSecret, protocolVersion, instanceTag, receiverInstanceTag, aesKey, iv)

Creates a Reveal Signature message with authentication.

**Parameters:**

- ``dhKeyPair`` (Object): Diffie-Hellman key pair
- ``dsaKeyPair`` (Object): DSA key pair for signing
- ``sharedSecret`` (Uint8Array): Computed shared secret
- ``protocolVersion`` (number): OTR protocol version
- ``instanceTag`` (number): Sender's instance tag
- ``receiverInstanceTag`` (number): Receiver's instance tag
- ``aesKey`` (Uint8Array): AES key from DH commit
- ``iv`` (Uint8Array): Initialization vector

**Returns:** Promise<Object>

.. code-block:: javascript

   {
     protocolVersion: number,
     messageType: 'REVEAL_SIGNATURE',
     senderInstanceTag: number,
     receiverInstanceTag: number,
     revealedKey: Uint8Array,      // Revealed AES key
     encryptedSignature: Uint8Array, // Encrypted DSA signature
     macKey: Uint8Array            // HMAC for integrity
   }

createSignature()
~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   async createSignature(dhKeyPair, dsaKeyPair, sharedSecret, protocolVersion, instanceTag, receiverInstanceTag, sessionKey)

Creates a Signature message to complete the AKE handshake.

**Parameters:**

- ``dhKeyPair`` (Object): Diffie-Hellman key pair
- ``dsaKeyPair`` (Object): DSA key pair for signing
- ``sharedSecret`` (Uint8Array): Computed shared secret
- ``protocolVersion`` (number): OTR protocol version
- ``instanceTag`` (number): Sender's instance tag
- ``receiverInstanceTag`` (number): Receiver's instance tag
- ``sessionKey`` (Uint8Array): Derived session key

**Returns:** Promise<Object>

.. code-block:: javascript

   {
     protocolVersion: number,
     messageType: 'SIGNATURE',
     senderInstanceTag: number,
     receiverInstanceTag: number,
     encryptedSignature: Uint8Array, // Encrypted DSA signature
     macKey: Uint8Array            // HMAC for integrity
   }

Message Processing Functions
----------------------------

processDHCommit()
~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   async processDHCommit(message, state)

Processes an incoming DH Commit message.

**Parameters:**

- ``message`` (Object): DH Commit message data
- ``state`` (OtrState): Current OTR state

**Returns:** Promise<Object>

.. code-block:: javascript

   {
     state: OtrState,        // Updated state
     message: Object         // DH Key response message
   }

**Example:**

.. code-block:: javascript

   const dhCommitMessage = parseMessage(incomingMessage);
   const result = await processDHCommit(dhCommitMessage, state);
   sendMessage(result.message);

processDHKey()
~~~~~~~~~~~~~~

.. code-block:: javascript

   async processDHKey(message, state)

Processes an incoming DH Key message.

**Parameters:**

- ``message`` (Object): DH Key message data
- ``state`` (OtrState): Current OTR state

**Returns:** Promise<Object>

.. code-block:: javascript

   {
     state: OtrState,        // Updated state
     message: Object         // Reveal Signature response message
   }

processRevealSignature()
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   async processRevealSignature(message, state)

Processes an incoming Reveal Signature message.

**Parameters:**

- ``message`` (Object): Reveal Signature message data
- ``state`` (OtrState): Current OTR state

**Returns:** Promise<Object>

.. code-block:: javascript

   {
     state: OtrState,        // Updated state
     message: Object         // Signature response message
   }

processSignature()
~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   async processSignature(message, state)

Processes an incoming Signature message to complete AKE.

**Parameters:**

- ``message`` (Object): Signature message data
- ``state`` (OtrState): Current OTR state

**Returns:** Promise<Object>

.. code-block:: javascript

   {
     state: OtrState,        // Updated state (ENCRYPTED)
     success: boolean        // True if AKE completed successfully
   }

State Management
----------------

OtrState Class
~~~~~~~~~~~~~~

.. code-block:: javascript

   class OtrState {
     constructor(version = PROTOCOL_VERSION.V3)
   }

**Properties:**

- ``version`` (number): OTR protocol version
- ``state`` (number): Current protocol state
- ``authState`` (string): Current authentication state
- ``instanceTag`` (number): Our instance tag
- ``theirInstanceTag`` (number): Their instance tag
- ``dhKeyPair`` (Object): Our DH key pair
- ``dsaKeyPair`` (Object): Our DSA key pair
- ``theirPublicKey`` (Uint8Array): Their DH public key
- ``sharedSecret`` (Uint8Array): Computed shared secret
- ``sendingAESKey`` (Uint8Array): AES key for sending
- ``receivingAESKey`` (Uint8Array): AES key for receiving
- ``sendingMACKey`` (Uint8Array): MAC key for sending
- ``receivingMACKey`` (Uint8Array): MAC key for receiving
- ``ssid`` (Uint8Array): Session ID

**Methods:**

startAKE()
^^^^^^^^^^

.. code-block:: javascript

   startAKE()

Transitions state to begin AKE handshake.

**Returns:** boolean - True if transition successful

goEncrypted()
^^^^^^^^^^^^^

.. code-block:: javascript

   goEncrypted()

Transitions to encrypted state after successful AKE.

**Returns:** boolean - True if transition successful

goPlaintext()
^^^^^^^^^^^^^

.. code-block:: javascript

   goPlaintext()

Resets to plaintext state and clears sensitive data.

**Returns:** boolean - Always true

setSessionKeys()
^^^^^^^^^^^^^^^^

.. code-block:: javascript

   setSessionKeys(keys)

Sets the derived session keys.

**Parameters:**

- ``keys`` (Object): Session keys object

  - ``sendingAESKey`` (Uint8Array): AES key for sending
  - ``receivingAESKey`` (Uint8Array): AES key for receiving
  - ``sendingMACKey`` (Uint8Array): MAC key for sending
  - ``receivingMACKey`` (Uint8Array): MAC key for receiving
  - ``ssid`` (Uint8Array): Session ID

canSendEncrypted()
^^^^^^^^^^^^^^^^^^

.. code-block:: javascript

   canSendEncrypted()

Checks if encrypted communication is possible.

**Returns:** boolean - True if in encrypted state

isAKEInProgress()
^^^^^^^^^^^^^^^^^

.. code-block:: javascript

   isAKEInProgress()

Checks if AKE handshake is in progress.

**Returns:** boolean - True if AKE is active

Constants
---------

Protocol States
~~~~~~~~~~~~~~~

.. code-block:: javascript

   const STATE = {
     PLAINTEXT: 0,           // No encryption
     AWAITING_DHKEY: 1,      // Waiting for DH Key message
     AWAITING_REVEALSIG: 2,  // Waiting for Reveal Signature
     AWAITING_SIG: 3,        // Waiting for Signature message
     ENCRYPTED: 4,           // Secure communication established
     FINISHED: 5             // Session ended
   };

Message Types
~~~~~~~~~~~~~

.. code-block:: javascript

   const MESSAGE_TYPE = {
     QUERY: 'QUERY',
     DH_COMMIT: 'DH_COMMIT',
     DH_KEY: 'DH_KEY',
     REVEAL_SIGNATURE: 'REVEAL_SIGNATURE',
     SIGNATURE: 'SIGNATURE',
     DATA: 'DATA',
     ERROR: 'ERROR',
     UNKNOWN: 'UNKNOWN'
   };

Protocol Versions
~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   const PROTOCOL_VERSION = {
     V2: 2,
     V3: 3,
     CURRENT: 3              // Default version
   };

Cryptographic Functions
-----------------------

generateDHKeyPair()
~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   async generateDHKeyPair()

Generates a Diffie-Hellman key pair.

**Returns:** Promise<Object>

.. code-block:: javascript

   {
     privateKey: Uint8Array,  // Private key (256 bytes)
     publicKey: Uint8Array    // Public key (256 bytes)
   }

dhExchange()
~~~~~~~~~~~~

.. code-block:: javascript

   async dhExchange(privateKey, publicKey)

Performs Diffie-Hellman key exchange.

**Parameters:**

- ``privateKey`` (Uint8Array): Our private key
- ``publicKey`` (Uint8Array): Their public key

**Returns:** Promise<Uint8Array> - Shared secret (32 bytes)

deriveKeys()
~~~~~~~~~~~~

.. code-block:: javascript

   async deriveKeys(sharedSecret)

Derives session keys from shared secret using HKDF.

**Parameters:**

- ``sharedSecret`` (Uint8Array): Shared secret from DH exchange

**Returns:** Promise<Object>

.. code-block:: javascript

   {
     sendingAESKey: Uint8Array,    // 32 bytes
     receivingAESKey: Uint8Array,  // 32 bytes
     sendingMACKey: Uint8Array,    // 32 bytes
     receivingMACKey: Uint8Array,  // 32 bytes
     ssid: Uint8Array              // 8 bytes
   }

Error Classes
-------------

AKEError
~~~~~~~~

.. code-block:: javascript

   class AKEError extends Error {
     constructor(message, code, details = {})
   }

**Properties:**

- ``message`` (string): Error message
- ``code`` (string): Error code
- ``details`` (Object): Additional error details
- ``timestamp`` (number): Error timestamp

**Error Codes:**

- ``INVALID_STATE``: Invalid protocol state for operation
- ``INVALID_MESSAGE``: Malformed or invalid message
- ``CRYPTO_ERROR``: Cryptographic operation failed
- ``SIGNATURE_VERIFICATION_FAILED``: Invalid signature
- ``TIMEOUT``: Operation timed out
- ``REPLAY_ATTACK``: Replay attack detected

Type Definitions
----------------

DHKeyPair
~~~~~~~~~

.. code-block:: typescript

   interface DHKeyPair {
     privateKey: Uint8Array;  // 256-byte private key
     publicKey: Uint8Array;   // 256-byte public key
   }

DSAKeyPair
~~~~~~~~~~

.. code-block:: typescript

   interface DSAKeyPair {
     privateKey: Uint8Array;  // Private key for signing
     publicKey: Uint8Array;   // Public key for verification
   }

AKEMessage
~~~~~~~~~~

.. code-block:: typescript

   interface AKEMessage {
     protocolVersion: number;
     messageType: string;
     senderInstanceTag: number;
     receiverInstanceTag: number;
     [key: string]: any;      // Message-specific fields
   }

SessionKeys
~~~~~~~~~~~

.. code-block:: typescript

   interface SessionKeys {
     sendingAESKey: Uint8Array;
     receivingAESKey: Uint8Array;
     sendingMACKey: Uint8Array;
     receivingMACKey: Uint8Array;
     ssid: Uint8Array;
   }

Usage Examples
--------------

Complete AKE Handshake
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   // Alice initiates AKE
   const alice = new OtrState();
   alice.dsaKeyPair = await generateDSAKeyPair();
   
   const akeStart = await startAKE(alice);
   
   // Bob processes DH commit
   const bob = new OtrState();
   bob.dsaKeyPair = await generateDSAKeyPair();
   
   const dhKeyResponse = await processDHCommit(akeStart.dhCommit, bob);
   
   // Alice processes DH key
   const revealSigResponse = await processDHKey(dhKeyResponse.message, alice);
   
   // Bob processes reveal signature
   const sigResponse = await processRevealSignature(revealSigResponse.message, bob);
   
   // Alice processes signature
   await processSignature(sigResponse.message, alice);
   
   // Both parties now have encrypted communication
   console.log('Alice state:', alice.getState()); // ENCRYPTED
   console.log('Bob state:', bob.getState());     // ENCRYPTED

Error Handling
~~~~~~~~~~~~~~

.. code-block:: javascript

   try {
     const result = await processDHCommit(message, state);
   } catch (error) {
     if (error instanceof AKEError) {
       switch (error.code) {
         case 'INVALID_STATE':
           console.error('Invalid state for DH commit processing');
           state.goPlaintext();
           break;
         case 'CRYPTO_ERROR':
           console.error('Cryptographic operation failed');
           throw error;
         default:
           console.error('Unknown AKE error:', error.message);
       }
     }
   }

This API reference provides complete documentation for all AKE system components, methods, events, and data structures.
