Steganography Implementation Guide
==================================

This guide provides detailed implementation instructions for integrating WebOTR's steganography system into your application.

.. contents:: Table of Contents
   :local:
   :depth: 3

Quick Start
-----------

Basic Integration
~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   import { 
     SteganographyEngine,
     OTRSteganographySession,
     CoverImageManager
   } from 'webOTR/core/steganography';
   
   // Initialize steganography engine
   const stego = new SteganographyEngine({
     method: 'LSB_ALPHA',
     bitsPerPixel: 1,
     noiseInjection: true,
     adaptiveLSB: true
   });
   
   // Create OTR session with steganography
   const session = new OTRSteganographySession({
     steganography: {
       engine: stego,
       autoSelectCover: true,
       multiImageSupport: true
     }
   });
   
   // Hide message in image
   const coverImage = await loadImage('cover.png');
   const stegoImage = await session.sendStegoMessage('Hello, secret world!', coverImage);
   
   // Extract message from image
   const hiddenMessage = await session.processStegoImage(stegoImage);
   console.log('Hidden message:', hiddenMessage);

Advanced Configuration
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   import { SteganographyEngine, StatisticalSecurity } from 'webOTR/core/steganography';
   
   // Advanced steganography configuration
   const advancedStego = new SteganographyEngine({
     // Core settings
     method: 'ADAPTIVE_LSB',
     bitsPerPixel: 1,
     compressionLevel: 0,
     
     // Security settings
     noiseInjection: true,
     statisticalSecurity: true,
     adaptiveLSB: true,
     antiDetection: true,
     
     // Performance settings
     useWebWorkers: true,
     chunkSize: 100000,
     progressiveProcessing: true,
     
     // Platform settings
     platformOptimization: true,
     metadataStripping: true,
     formatNormalization: true,
     
     // Advanced features
     multiImageDistribution: true,
     errorCorrection: true,
     redundancy: 0.1 // 10% redundancy
   });

Component Integration
---------------------

Steganography Engine
~~~~~~~~~~~~~~~~~~~

Direct integration with the core steganography engine:

.. code-block:: javascript

   class SteganographyManager {
     constructor(options = {}) {
       this.options = {
         defaultMethod: 'LSB_ALPHA',
         maxImageSize: 10 * 1024 * 1024, // 10MB
         supportedFormats: ['PNG', 'BMP'],
         qualityThreshold: 0.95,
         ...options
       };
       
       this.engine = new SteganographyEngine(this.options);
       this.coverManager = new CoverImageManager();
       this.securityManager = new StatisticalSecurity();
     }
     
     async hideMessage(message, coverImage = null, options = {}) {
       try {
         // Validate inputs
         this.validateMessage(message);
         
         // Get or select cover image
         const cover = coverImage || await this.coverManager.selectOptimalCover(
           message.length,
           options.coverRequirements
         );
         
         // Analyze cover suitability
         const analysis = await this.coverManager.analyzeCover(cover);
         if (analysis.suitabilityScore < 0.7) {
           throw new Error('Cover image not suitable for steganography');
         }
         
         // Apply security measures
         const secureMessage = await this.securityManager.prepareMessage(
           message,
           options.sessionKey
         );
         
         // Perform embedding
         const stegoImage = await this.engine.hideMessage(cover, secureMessage);
         
         // Verify embedding quality
         const quality = await this.verifyEmbeddingQuality(cover, stegoImage);
         if (quality.psnr < 40) {
           console.warn('Low embedding quality detected');
         }
         
         this.emit('messageHidden', {
           originalSize: cover.size,
           stegoSize: stegoImage.size,
           messageLength: message.length,
           quality: quality
         });
         
         return stegoImage;
         
       } catch (error) {
         this.handleError('HIDE_MESSAGE_FAILED', error);
         throw error;
       }
     }
     
     async revealMessage(stegoImage, options = {}) {
       try {
         // Validate image
         this.validateImage(stegoImage);
         
         // Extract message
         const extractedData = await this.engine.revealMessage(stegoImage);
         
         if (!extractedData) {
           return null; // No hidden message found
         }
         
         // Apply security verification
         const message = await this.securityManager.verifyMessage(
           extractedData,
           options.sessionKey
         );
         
         this.emit('messageRevealed', {
           imageSize: stegoImage.size,
           messageLength: message.length,
           extractionTime: performance.now()
         });
         
         return message;
         
       } catch (error) {
         this.handleError('REVEAL_MESSAGE_FAILED', error);
         throw error;
       }
     }
   }

LSB Algorithm Implementation
~~~~~~~~~~~~~~~~~~~~~~~~~~~

Custom LSB algorithm with security enhancements:

.. code-block:: javascript

   class AdaptiveLSBAlgorithm {
     constructor(sessionKey) {
       this.sessionKey = sessionKey;
       this.prng = new SecurePRNG(sessionKey);
     }
     
     async embedMessage(imageData, messageData) {
       const pixels = imageData.data;
       const messageBits = this.messageToBits(messageData);
       
       // Generate adaptive embedding positions
       const positions = this.generateEmbeddingPositions(
         imageData,
         messageBits.length
       );
       
       // Embed message bits
       for (let i = 0; i < messageBits.length; i++) {
         const position = positions[i];
         const pixelIndex = position * 4 + 3; // Alpha channel
         
         // Modify LSB
         pixels[pixelIndex] = (pixels[pixelIndex] & 0xFE) | messageBits[i];
         
         // Apply noise injection randomly
         if (this.prng.nextFloat() < 0.1) {
           this.injectNoise(pixels, position);
         }
       }
       
       return imageData;
     }
     
     async extractMessage(imageData, messageLength) {
       const pixels = imageData.data;
       const positions = this.generateEmbeddingPositions(imageData, messageLength * 8);
       const messageBits = [];
       
       // Extract message bits
       for (const position of positions) {
         const pixelIndex = position * 4 + 3; // Alpha channel
         const bit = pixels[pixelIndex] & 0x01;
         messageBits.push(bit);
       }
       
       return this.bitsToMessage(messageBits);
     }
     
     generateEmbeddingPositions(imageData, bitCount) {
       const totalPixels = imageData.width * imageData.height;
       const positions = [];
       
       // Use session key for deterministic randomness
       this.prng.seed(this.sessionKey);
       
       while (positions.length < bitCount) {
         const position = this.prng.nextInt(totalPixels);
         
         // Check if position is suitable
         if (this.isPositionSuitable(imageData, position)) {
           positions.push(position);
         }
       }
       
       return positions;
     }
     
     isPositionSuitable(imageData, position) {
       const x = position % imageData.width;
       const y = Math.floor(position / imageData.width);
       const pixelIndex = (y * imageData.width + x) * 4;
       
       // Check alpha channel value
       const alpha = imageData.data[pixelIndex + 3];
       
       // Avoid fully transparent or opaque pixels
       return alpha > 10 && alpha < 245;
     }
   }

Platform Integration
--------------------

Browser Extension Integration
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Seamless integration with social media platforms:

.. code-block:: javascript

   class PlatformSteganography {
     constructor() {
       this.platforms = new Map();
       this.stegoManager = new SteganographyManager();
       this.setupPlatformHandlers();
     }
     
     setupPlatformHandlers() {
       // Facebook integration
       this.platforms.set('facebook.com', {
         name: 'Facebook',
         imageSelectors: [
           'input[type="file"][accept*="image"]',
           'img[src*="scontent"]'
         ],
         uploadInterceptor: this.interceptFacebookUpload.bind(this),
         downloadProcessor: this.processFacebookImages.bind(this)
       });
       
       // Instagram integration
       this.platforms.set('instagram.com', {
         name: 'Instagram',
         imageSelectors: [
           'input[type="file"]',
           'img[src*="cdninstagram"]'
         ],
         uploadInterceptor: this.interceptInstagramUpload.bind(this),
         downloadProcessor: this.processInstagramImages.bind(this)
       });
       
       // Discord integration
       this.platforms.set('discord.com', {
         name: 'Discord',
         imageSelectors: [
           'input[type="file"]',
           'img[src*="cdn.discordapp.com"]'
         ],
         uploadInterceptor: this.interceptDiscordUpload.bind(this),
         downloadProcessor: this.processDiscordImages.bind(this)
       });
     }
     
     async interceptUpload(fileInput, platform) {
       const files = Array.from(fileInput.files);
       const processedFiles = [];
       
       for (const file of files) {
         if (this.isImageFile(file)) {
           const shouldHideMessage = await this.promptForSteganography(platform);
           
           if (shouldHideMessage) {
             const message = await this.getMessageToHide();
             const stegoFile = await this.stegoManager.hideMessage(message, file);
             processedFiles.push(stegoFile);
           } else {
             processedFiles.push(file);
           }
         } else {
           processedFiles.push(file);
         }
       }
       
       return processedFiles;
     }
     
     async processDownloadedImages() {
       const platform = this.getCurrentPlatform();
       const config = this.platforms.get(platform);
       
       if (!config) return;
       
       const images = document.querySelectorAll(config.imageSelectors.join(', '));
       
       for (const img of images) {
         if (this.shouldProcessImage(img)) {
           await this.checkForHiddenMessage(img);
         }
       }
     }
     
     async checkForHiddenMessage(imgElement) {
       try {
         // Convert image to blob
         const canvas = document.createElement('canvas');
         const ctx = canvas.getContext('2d');
         
         canvas.width = imgElement.naturalWidth;
         canvas.height = imgElement.naturalHeight;
         ctx.drawImage(imgElement, 0, 0);
         
         const blob = await new Promise(resolve => 
           canvas.toBlob(resolve, 'image/png')
         );
         
         // Check for hidden message
         const hiddenMessage = await this.stegoManager.revealMessage(blob);
         
         if (hiddenMessage) {
           this.displayHiddenMessage(hiddenMessage, imgElement);
         }
         
       } catch (error) {
         console.error('Error checking for hidden message:', error);
       }
     }
   }

Multi-Image Distribution
~~~~~~~~~~~~~~~~~~~~~~~~

Handling large messages across multiple images:

.. code-block:: javascript

   class MultiImageSteganography {
     constructor(stegoEngine) {
       this.engine = stegoEngine;
       this.maxChunkSize = 8000; // 8KB per image
     }
     
     async distributeMessage(message, coverImages) {
       // Split message into chunks
       const chunks = this.splitMessage(message, this.maxChunkSize);
       
       if (chunks.length > coverImages.length) {
         throw new Error('Not enough cover images for message distribution');
       }
       
       const stegoImages = [];
       const manifest = this.createManifest(chunks.length, message.length);
       
       // Embed manifest in first image
       const firstStego = await this.engine.hideMessage(
         coverImages[0],
         this.serializeManifest(manifest)
       );
       stegoImages.push(firstStego);
       
       // Embed message chunks in remaining images
       for (let i = 0; i < chunks.length; i++) {
         const chunkData = this.createChunkData(i, chunks[i]);
         const stegoImage = await this.engine.hideMessage(
           coverImages[i + 1],
           chunkData
         );
         stegoImages.push(stegoImage);
       }
       
       return {
         images: stegoImages,
         manifest: manifest,
         totalChunks: chunks.length
       };
     }
     
     async reconstructMessage(stegoImages) {
       // Extract manifest from first image
       const manifestData = await this.engine.revealMessage(stegoImages[0]);
       if (!manifestData) {
         throw new Error('No manifest found in first image');
       }
       
       const manifest = this.parseManifest(manifestData);
       const chunks = new Array(manifest.totalChunks);
       
       // Extract chunks from remaining images
       for (let i = 1; i < stegoImages.length; i++) {
         const chunkData = await this.engine.revealMessage(stegoImages[i]);
         if (chunkData) {
           const chunk = this.parseChunkData(chunkData);
           chunks[chunk.index] = chunk.data;
         }
       }
       
       // Verify all chunks received
       const missingChunks = chunks.findIndex(chunk => chunk === undefined);
       if (missingChunks !== -1) {
         throw new Error(`Missing chunk ${missingChunks}`);
       }
       
       // Reconstruct original message
       return this.combineChunks(chunks);
     }
     
     createManifest(totalChunks, originalLength) {
       return {
         version: 1,
         totalChunks: totalChunks,
         originalLength: originalLength,
         timestamp: Date.now(),
         checksum: this.calculateChecksum(originalLength.toString())
       };
     }
   }

Security Implementation
-----------------------

Statistical Security
~~~~~~~~~~~~~~~~~~~~

Advanced security measures to prevent detection:

.. code-block:: javascript

   class StatisticalSecurityManager {
     constructor(sessionKey) {
       this.sessionKey = sessionKey;
       this.prng = new SecurePRNG(sessionKey);
     }
     
     async enhanceSecurityBeforeEmbedding(imageData, messageData) {
       // Analyze image characteristics
       const analysis = await this.analyzeImage(imageData);
       
       // Adjust embedding parameters based on analysis
       const embeddingParams = this.optimizeEmbeddingParameters(analysis);
       
       // Add statistical noise
       await this.injectStatisticalNoise(imageData, embeddingParams);
       
       // Randomize message order
       const randomizedMessage = this.randomizeMessageBits(messageData);
       
       return {
         imageData: imageData,
         messageData: randomizedMessage,
         parameters: embeddingParams
       };
     }
     
     analyzeImage(imageData) {
       const pixels = imageData.data;
       let complexity = 0;
       let noiseLevel = 0;
       
       // Calculate image complexity
       for (let i = 0; i < pixels.length - 4; i += 4) {
         const current = pixels[i + 3]; // Alpha channel
         const next = pixels[i + 7];
         complexity += Math.abs(current - next);
       }
       
       // Calculate noise level
       for (let i = 0; i < pixels.length; i += 4) {
         const alpha = pixels[i + 3];
         if (alpha > 0 && alpha < 255) {
           noiseLevel++;
         }
       }
       
       return {
         complexity: complexity / (pixels.length / 4),
         noiseLevel: noiseLevel / (pixels.length / 4),
         totalPixels: pixels.length / 4
       };
     }
     
     injectStatisticalNoise(imageData, params) {
       const pixels = imageData.data;
       const noiseIntensity = Math.min(0.1, params.complexity / 1000);
       
       for (let i = 3; i < pixels.length; i += 4) {
         if (this.prng.nextFloat() < noiseIntensity) {
           const noise = this.prng.nextInt(3) - 1; // -1, 0, or 1
           pixels[i] = Math.max(0, Math.min(255, pixels[i] + noise));
         }
       }
     }
     
     randomizeMessageBits(messageData) {
       const bits = this.messageToBits(messageData);
       const positions = Array.from({ length: bits.length }, (_, i) => i);
       
       // Fisher-Yates shuffle with deterministic randomness
       this.prng.seed(this.sessionKey);
       for (let i = positions.length - 1; i > 0; i--) {
         const j = this.prng.nextInt(i + 1);
         [positions[i], positions[j]] = [positions[j], positions[i]];
       }
       
       return {
         bits: bits,
         positions: positions
       };
     }
   }

Performance Optimization
------------------------

Web Worker Implementation
~~~~~~~~~~~~~~~~~~~~~~~~

Parallel processing for large images:

.. code-block:: javascript

   class SteganographyWorkerPool {
     constructor(workerCount = 4) {
       this.workers = [];
       this.taskQueue = [];
       this.activeJobs = new Map();
       
       this.initializeWorkers(workerCount);
     }
     
     initializeWorkers(count) {
       for (let i = 0; i < count; i++) {
         const worker = new Worker('/workers/steganography-worker.js');
         
         worker.onmessage = (event) => {
           this.handleWorkerMessage(worker, event.data);
         };
         
         worker.onerror = (error) => {
           this.handleWorkerError(worker, error);
         };
         
         this.workers.push({
           worker: worker,
           busy: false,
           id: i
         });
       }
     }
     
     async processImageChunks(imageData, messageData, operation) {
       const chunks = this.splitImageIntoChunks(imageData, this.workers.length);
       const messageChunks = this.splitMessage(messageData, chunks.length);
       
       const promises = chunks.map((chunk, index) => {
         return this.executeTask({
           operation: operation,
           imageChunk: chunk,
           messageChunk: messageChunks[index],
           chunkIndex: index
         });
       });
       
       const results = await Promise.all(promises);
       return this.mergeChunkResults(results);
     }
     
     executeTask(task) {
       return new Promise((resolve, reject) => {
         const taskId = this.generateTaskId();
         
         this.activeJobs.set(taskId, {
           resolve: resolve,
           reject: reject,
           task: task
         });
         
         this.taskQueue.push({
           id: taskId,
           task: task
         });
         
         this.processQueue();
       });
     }
     
     processQueue() {
       if (this.taskQueue.length === 0) return;
       
       const availableWorker = this.workers.find(w => !w.busy);
       if (!availableWorker) return;
       
       const queueItem = this.taskQueue.shift();
       availableWorker.busy = true;
       
       availableWorker.worker.postMessage({
         taskId: queueItem.id,
         ...queueItem.task
       });
     }
   }

Progressive Processing
~~~~~~~~~~~~~~~~~~~~~

Handling large images without blocking the UI:

.. code-block:: javascript

   class ProgressiveSteganography {
     constructor(engine) {
       this.engine = engine;
       this.chunkSize = 100000; // Process 100k pixels at a time
     }
     
     async processImageProgressively(imageData, messageData, operation, onProgress) {
       const totalPixels = imageData.width * imageData.height;
       const chunks = Math.ceil(totalPixels / this.chunkSize);
       
       let processedPixels = 0;
       const results = [];
       
       for (let i = 0; i < chunks; i++) {
         const startPixel = i * this.chunkSize;
         const endPixel = Math.min(startPixel + this.chunkSize, totalPixels);
         
         // Extract chunk
         const chunk = this.extractImageChunk(imageData, startPixel, endPixel);
         const messageChunk = this.extractMessageChunk(messageData, i, chunks);
         
         // Process chunk
         const result = await this.processChunk(chunk, messageChunk, operation);
         results.push(result);
         
         // Update progress
         processedPixels += (endPixel - startPixel);
         const progress = (processedPixels / totalPixels) * 100;
         onProgress(progress);
         
         // Yield control to prevent UI blocking
         await new Promise(resolve => setTimeout(resolve, 0));
       }
       
       return this.combineChunkResults(results, imageData);
     }
     
     async processChunk(imageChunk, messageChunk, operation) {
       switch (operation) {
         case 'embed':
           return await this.engine.embedInChunk(imageChunk, messageChunk);
         case 'extract':
           return await this.engine.extractFromChunk(imageChunk);
         default:
           throw new Error(`Unknown operation: ${operation}`);
       }
     }
   }

This implementation guide provides comprehensive instructions for integrating WebOTR's steganography system into production applications with proper security, performance optimization, and platform integration.
