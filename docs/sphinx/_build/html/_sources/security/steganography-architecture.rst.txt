Steganography Architecture
==========================

This document provides detailed architectural diagrams and flow charts for WebOTR's steganography implementation.

.. contents:: Table of Contents
   :local:
   :depth: 3

System Architecture
-------------------

High-Level Architecture
~~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TB
       subgraph "WebOTR Application"
           App[Application Layer]
           OTR[OTR Protocol Layer]
           Stego[Steganography Layer]
       end
       
       subgraph "Steganography Components"
           SE[Steganography Engine]
           IPP[Image Processing Pipeline]
           CIM[Cover Image Manager]
           PI[Platform Integration]
           SM[Security Manager]
       end
       
       subgraph "Image Processing"
           Canvas[Canvas API]
           LSB[LSB Algorithm]
           Noise[Noise Injection]
           Analysis[Image Analysis]
       end
       
       subgraph "Platform Targets"
           Social[Social Media]
           Email[Email Attachments]
           FileShare[File Sharing]
           Messaging[Messaging Apps]
       end
       
       App --> OTR
       OTR --> Stego
       Stego --> SE
       
       SE --> IPP
       SE --> CIM
       SE --> PI
       SE --> SM
       
       IPP --> Canvas
       IPP --> LSB
       IPP --> Noise
       IPP --> Analysis
       
       PI --> Social
       PI --> Email
       PI --> FileShare
       PI --> Messaging

Component Interaction
~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph LR
       subgraph "Message Hiding Flow"
           H1[Plaintext Message] --> H2[OTR Encryption]
           H2 --> H3[Message Preparation]
           H3 --> H4[Cover Selection]
           H4 --> H5[LSB Embedding]
           H5 --> H6[Noise Injection]
           H6 --> H7[Stego Image]
       end
       
       subgraph "Message Extraction Flow"
           E1[Stego Image] --> E2[Image Analysis]
           E2 --> E3[LSB Extraction]
           E3 --> E4[Message Validation]
           E4 --> E5[OTR Decryption]
           E5 --> E6[Plaintext Message]
       end
       
       H7 --> E1

Steganographic Flow Architecture
-------------------------------

LSB Embedding Process
~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   sequenceDiagram
       participant User
       participant OTR as OTR Session
       participant Stego as Stego Engine
       participant Canvas as Canvas API
       participant Platform
       
       User->>OTR: Send Message
       OTR->>OTR: Encrypt Message
       OTR->>Stego: Hide Encrypted Message
       
       Stego->>Stego: Select Cover Image
       Stego->>Canvas: Load Cover Image
       Canvas->>Stego: Image Data
       
       Stego->>Stego: Prepare Message Header
       Stego->>Stego: Convert to Bit Stream
       
       loop For Each Message Bit
           Stego->>Canvas: Modify Alpha LSB
       end
       
       Stego->>Stego: Inject Statistical Noise
       Stego->>Canvas: Generate Stego Image
       Canvas->>Platform: Upload Stego Image
       
       Platform->>User: Image Shared

Message Extraction Process
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   sequenceDiagram
       participant Platform
       participant Stego as Stego Engine
       participant Canvas as Canvas API
       participant OTR as OTR Session
       participant User
       
       Platform->>Stego: Download Image
       Stego->>Canvas: Load Image Data
       Canvas->>Stego: Pixel Data
       
       Stego->>Stego: Analyze Image
       
       alt Hidden Message Detected
           loop Extract Message Bits
               Stego->>Canvas: Read Alpha LSB
           end
           
           Stego->>Stego: Reconstruct Message
           Stego->>Stego: Validate Header/Checksum
           Stego->>OTR: Decrypt Message
           OTR->>User: Display Message
       else No Hidden Message
           Stego->>User: Display Normal Image
       end

Image Processing Pipeline
~~~~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   flowchart TD
       A[Input Image] --> B[Format Validation]
       B --> C{PNG Format?}
       C -->|Yes| D[Load Image Data]
       C -->|No| E[Convert to PNG]
       E --> D
       
       D --> F[Analyze Image Properties]
       F --> G[Calculate Capacity]
       G --> H{Sufficient Capacity?}
       
       H -->|Yes| I[Prepare Message]
       H -->|No| J[Split Message/Multi-Image]
       
       I --> K[Create Message Header]
       K --> L[Convert to Bit Stream]
       L --> M[Select Embedding Positions]
       
       M --> N[Adaptive LSB Embedding]
       N --> O[Statistical Noise Injection]
       O --> P[Quality Verification]
       P --> Q[Generate Stego Image]

Security Architecture
---------------------

Steganographic Security Model
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TB
       subgraph "Security Layers"
           L1[OTR Encryption Layer]
           L2[Steganographic Hiding Layer]
           L3[Statistical Security Layer]
           L4[Platform Camouflage Layer]
       end
       
       subgraph "Threat Vectors"
           T1[Passive Observation]
           T2[Statistical Analysis]
           T3[Visual Inspection]
           T4[Metadata Analysis]
           T5[Platform Detection]
       end
       
       subgraph "Countermeasures"
           C1[Perfect Forward Secrecy]
           C2[Adaptive LSB Selection]
           C3[Noise Injection]
           C4[Metadata Stripping]
           C5[Normal Usage Patterns]
       end
       
       T1 --> C1
       T2 --> C2
       T3 --> C3
       T4 --> C4
       T5 --> C5
       
       C1 --> L1
       C2 --> L2
       C3 --> L3
       C4 --> L3
       C5 --> L4

Defense in Depth
~~~~~~~~~~~~~~~~

.. mermaid::

   graph LR
       subgraph "Layer 1: Cryptographic Protection"
           L1A[OTR Encryption]
           L1B[Perfect Forward Secrecy]
           L1C[Message Authentication]
           L1D[Deniable Authentication]
       end
       
       subgraph "Layer 2: Steganographic Hiding"
           L2A[LSB Embedding]
           L2B[Alpha Channel Usage]
           L2C[Adaptive Positioning]
           L2D[Multi-Image Distribution]
       end
       
       subgraph "Layer 3: Statistical Security"
           L3A[Noise Injection]
           L3B[Pattern Randomization]
           L3C[Cover Analysis]
           L3D[Anti-Detection Measures]
       end
       
       subgraph "Layer 4: Platform Integration"
           L4A[Metadata Normalization]
           L4B[Format Compliance]
           L4C[Usage Pattern Mimicry]
           L4D[Social Engineering Resistance]
       end
       
       L1A --> L2A
       L1B --> L2B
       L1C --> L2C
       L1D --> L2D
       
       L2A --> L3A
       L2B --> L3B
       L2C --> L3C
       L2D --> L3D
       
       L3A --> L4A
       L3B --> L4B
       L3C --> L4C
       L3D --> L4D

Performance Architecture
------------------------

Processing Pipeline Optimization
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TB
       subgraph "Input Processing"
           IP1[Image Loading]
           IP2[Format Validation]
           IP3[Size Analysis]
           IP4[Capacity Calculation]
       end
       
       subgraph "Parallel Processing"
           PP1[Web Worker 1]
           PP2[Web Worker 2]
           PP3[Web Worker 3]
           PP4[Web Worker 4]
       end
       
       subgraph "Optimization Strategies"
           OS1[Chunk Processing]
           OS2[Progressive Loading]
           OS3[Memory Management]
           OS4[Cache Utilization]
       end
       
       subgraph "Output Generation"
           OG1[Image Reconstruction]
           OG2[Quality Verification]
           OG3[Format Conversion]
           OG4[Compression Optimization]
       end
       
       IP1 --> PP1
       IP2 --> PP2
       IP3 --> PP3
       IP4 --> PP4
       
       PP1 --> OS1
       PP2 --> OS2
       PP3 --> OS3
       PP4 --> OS4
       
       OS1 --> OG1
       OS2 --> OG2
       OS3 --> OG3
       OS4 --> OG4

Memory Management
~~~~~~~~~~~~~~~~~

.. mermaid::

   flowchart TD
       A[Large Image Input] --> B[Memory Assessment]
       B --> C{Memory Available?}
       
       C -->|Sufficient| D[Direct Processing]
       C -->|Limited| E[Chunked Processing]
       
       D --> F[Load Full Image]
       E --> G[Load Image Chunks]
       
       F --> H[Process in Main Thread]
       G --> I[Process in Web Workers]
       
       H --> J[Generate Output]
       I --> K[Merge Chunk Results]
       K --> J
       
       J --> L[Memory Cleanup]
       L --> M[Garbage Collection Hint]

Platform Integration Architecture
---------------------------------

Social Media Integration
~~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TB
       subgraph "Platform Detection"
           PD1[URL Analysis]
           PD2[DOM Structure Detection]
           PD3[API Endpoint Identification]
           PD4[Upload Flow Recognition]
       end
       
       subgraph "Facebook Integration"
           FB1[Image Upload Interception]
           FB2[News Feed Image Processing]
           FB3[Messenger Image Handling]
           FB4[Story Image Processing]
       end
       
       subgraph "Instagram Integration"
           IG1[Post Upload Interception]
           IG2[Story Upload Processing]
           IG3[Direct Message Images]
           IG4[Feed Image Analysis]
       end
       
       subgraph "Discord Integration"
           DC1[Channel Image Upload]
           DC2[Direct Message Images]
           DC3[Server Image Processing]
           DC4[Attachment Handling]
       end
       
       PD1 --> FB1
       PD2 --> IG1
       PD3 --> DC1
       PD4 --> FB1
       
       FB1 --> FB2
       FB2 --> FB3
       FB3 --> FB4
       
       IG1 --> IG2
       IG2 --> IG3
       IG3 --> IG4
       
       DC1 --> DC2
       DC2 --> DC3
       DC3 --> DC4

Browser Extension Architecture
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph LR
       subgraph "Content Scripts"
           CS1[Page Injection]
           CS2[DOM Monitoring]
           CS3[Event Interception]
           CS4[UI Integration]
       end
       
       subgraph "Background Service"
           BS1[Image Processing]
           BS2[OTR Session Management]
           BS3[Storage Management]
           BS4[Cross-Tab Communication]
       end
       
       subgraph "Popup Interface"
           PU1[Steganography Controls]
           PU2[Message Composition]
           PU3[Settings Management]
           PU4[Status Display]
       end
       
       subgraph "Options Page"
           OP1[Platform Configuration]
           OP2[Security Settings]
           OP3[Performance Tuning]
           OP4[Cover Image Management]
       end
       
       CS1 --> BS1
       CS2 --> BS2
       CS3 --> BS3
       CS4 --> BS4
       
       PU1 --> BS1
       PU2 --> BS2
       PU3 --> BS3
       PU4 --> BS4
       
       OP1 --> BS1
       OP2 --> BS2
       OP3 --> BS3
       OP4 --> BS4

Implementation Architecture
---------------------------

Module Structure
~~~~~~~~~~~~~~~~

.. mermaid::

   graph TB
       subgraph "Core Steganography Module"
           CSM1[steganography.js - Main Engine]
           CSM2[lsb.js - LSB Algorithm]
           CSM3[image-processor.js - Image Handling]
           CSM4[message-format.js - Message Structure]
       end
       
       subgraph "Security Modules"
           SM1[statistical-security.js - Anti-Detection]
           SM2[noise-injection.js - Statistical Noise]
           SM3[adaptive-lsb.js - Dynamic Positioning]
           SM4[cover-analysis.js - Image Analysis]
       end
       
       subgraph "Platform Integration"
           PI1[platform-detector.js - Platform Recognition]
           PI2[social-media.js - Social Platform Handlers]
           PI3[file-sharing.js - File Platform Handlers]
           PI4[messaging.js - Messaging Platform Handlers]
       end
       
       subgraph "Utility Modules"
           UM1[image-utils.js - Image Utilities]
           UM2[canvas-helper.js - Canvas Operations]
           UM3[format-converter.js - Format Handling]
           UM4[performance-optimizer.js - Optimization]
       end
       
       CSM1 --> SM1
       CSM2 --> SM2
       CSM3 --> SM3
       CSM4 --> SM4
       
       SM1 --> PI1
       SM2 --> PI2
       SM3 --> PI3
       SM4 --> PI4
       
       PI1 --> UM1
       PI2 --> UM2
       PI3 --> UM3
       PI4 --> UM4

Error Handling Architecture
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   flowchart TD
       A[Steganography Operation] --> B{Success?}
       
       B -->|Yes| C[Continue Processing]
       B -->|No| D[Classify Error]
       
       D --> E{Image Error?}
       D --> F{Message Error?}
       D --> G{Platform Error?}
       D --> H{Security Error?}
       
       E -->|Yes| I[Image Format/Size Issues]
       F -->|Yes| J[Message Too Large/Invalid]
       G -->|Yes| K[Platform Compatibility]
       H -->|Yes| L[Security Violation]
       
       I --> M[Suggest Alternative Format]
       J --> N[Offer Multi-Image Solution]
       K --> O[Fallback Platform Method]
       L --> P[Abort with Security Warning]
       
       M --> Q[Retry Operation]
       N --> Q
       O --> Q
       P --> R[Log Security Event]

Testing Architecture
~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TB
       subgraph "Unit Tests"
           UT1[LSB Algorithm Tests]
           UT2[Image Processing Tests]
           UT3[Message Format Tests]
           UT4[Security Feature Tests]
       end
       
       subgraph "Integration Tests"
           IT1[OTR Integration Tests]
           IT2[Platform Integration Tests]
           IT3[End-to-End Workflow Tests]
           IT4[Performance Tests]
       end
       
       subgraph "Security Tests"
           ST1[Steganalysis Resistance]
           ST2[Statistical Security Tests]
           ST3[Metadata Security Tests]
           ST4[Platform Detection Tests]
       end
       
       subgraph "Compatibility Tests"
           CT1[Browser Compatibility]
           CT2[Platform Compatibility]
           CT3[Image Format Tests]
           CT4[Performance Benchmarks]
       end
       
       UT1 --> IT1
       UT2 --> IT2
       UT3 --> IT3
       UT4 --> IT4
       
       IT1 --> ST1
       IT2 --> ST2
       IT3 --> ST3
       IT4 --> ST4
       
       ST1 --> CT1
       ST2 --> CT2
       ST3 --> CT3
       ST4 --> CT4

This architectural documentation provides comprehensive diagrams and flow charts that illustrate the design, implementation, and security properties of WebOTR's steganography system across all levels of abstraction.
