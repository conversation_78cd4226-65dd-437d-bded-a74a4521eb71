Troubleshooting Guide
====================

This guide provides comprehensive troubleshooting information for Phase 3 protocol features, including common issues, diagnostic procedures, and resolution strategies.

Diagnostic Overview
------------------

Phase 3 features include comprehensive diagnostic capabilities:

.. code-block:: mermaid

   graph TB
       subgraph "Diagnostic Categories"
           CONN[Connection Issues]
           VER[Version Negotiation]
           MSG[Message Ordering]
           SMP[Enhanced SMP]
           POL[Policy Manager]
           PERF[Performance Issues]
       end
       
       subgraph "Diagnostic Tools"
           LOGS[Debug Logging]
           METRICS[Performance Metrics]
           STATS[Component Statistics]
           TRACE[Protocol Tracing]
           AUDIT[Audit Logs]
       end
       
       subgraph "Resolution Actions"
           CONFIG[Configuration Changes]
           RESET[State Reset]
           RETRY[Retry Operations]
           ESCALATE[Escalate to Support]
       end
       
       CONN --> LOGS
       VER --> METRICS
       MSG --> STATS
       SMP --> TRACE
       POL --> AUDIT
       PERF --> METRICS
       
       LOGS --> CONFIG
       METRICS --> RESET
       STATS --> RETRY
       TRACE --> ESCALATE

Common Issues
------------

Version Negotiation Issues
~~~~~~~~~~~~~~~~~~~~~~~~~

**Issue: Version negotiation fails**

.. code-block:: mermaid

   flowchart TD
       START[Version Negotiation Fails] --> CHECK_QUERY[Check Query Message Format]
       CHECK_QUERY --> VALID_QUERY{Valid Query?}
       VALID_QUERY -->|No| FIX_QUERY[Fix Query Message Format]
       VALID_QUERY -->|Yes| CHECK_POLICY[Check Version Policies]
       
       CHECK_POLICY --> POLICY_ALLOW{Policies Allow Versions?}
       POLICY_ALLOW -->|No| UPDATE_POLICY[Update Version Policies]
       POLICY_ALLOW -->|Yes| CHECK_COMPAT[Check Version Compatibility]
       
       CHECK_COMPAT --> COMPAT_OK{Compatible Versions?}
       COMPAT_OK -->|No| NEGOTIATE[Negotiate Alternative]
       COMPAT_OK -->|Yes| CHECK_DOWNGRADE[Check for Downgrade Attack]
       
       CHECK_DOWNGRADE --> DOWNGRADE{Downgrade Detected?}
       DOWNGRADE -->|Yes| SECURITY_REVIEW[Review Security Settings]
       DOWNGRADE -->|No| SUCCESS[Negotiation Should Succeed]

*Symptoms:*
- OTR session fails to establish
- "No compatible versions" error
- Version downgrade warnings

*Diagnostic Steps:*

.. code-block:: javascript

   // Check version negotiation status
   const debug = otr.getVersionNegotiationDebug();
   console.log('Version negotiation debug:', {
     ourVersions: debug.ourVersions,
     theirVersions: debug.theirVersions,
     commonVersions: debug.commonVersions,
     selectedVersion: debug.selectedVersion,
     policies: debug.policies,
     errors: debug.errors
   });

   // Check policy configuration
   const policies = otr.policyManager.getEffectivePolicy();
   console.log('Version policies:', {
     allowV2: policies.security.allowV2,
     allowV3: policies.security.allowV3,
     preferV3: policies.security.preferV3,
     requireEncryption: policies.security.requireEncryption
   });

*Resolution:*

.. code-block:: javascript

   // Update version policies if needed
   if (!policies.security.allowV3) {
     otr.policyManager.setPolicy('security.allowV3', true, {
       reason: 'Enable OTR v3 for compatibility'
     });
   }

   // Reset version negotiation state
   otr.resetVersionNegotiation();

   // Retry negotiation
   await otr.startVersionNegotiation();

Message Ordering Issues
~~~~~~~~~~~~~~~~~~~~~~

**Issue: Messages arrive out of order**

.. code-block:: mermaid

   sequenceDiagram
       participant App as Application
       participant MO as Message Ordering
       participant Buffer as Message Buffer
       participant Diag as Diagnostics
       
       Note over App,Diag: Out-of-Order Detection
       App->>MO: Message Seq 5 arrives
       MO->>MO: Expected Seq 3
       MO->>Buffer: Buffer message 5
       MO->>Diag: Log gap detected
       
       Note over App,Diag: Gap Analysis
       App->>MO: Message Seq 3 arrives
       MO->>MO: Process message 3
       MO->>Buffer: Check for ready messages
       Buffer-->>MO: No message 4 yet
       
       Note over App,Diag: Timeout Handling
       MO->>MO: Gap timeout reached
       MO->>Diag: Log timeout event
       MO->>App: Process available messages

*Symptoms:*
- Messages displayed out of order
- Long delays in message delivery
- Gap timeout warnings

*Diagnostic Steps:*

.. code-block:: javascript

   // Check message ordering statistics
   const stats = otr.messageOrdering.getStats();
   console.log('Message ordering stats:', {
     messagesProcessed: stats.messagesProcessed,
     messagesReordered: stats.messagesReordered,
     duplicatesDetected: stats.duplicatesDetected,
     gapsDetected: stats.gapsDetected,
     timeouts: stats.timeouts,
     pendingMessages: stats.pendingMessages,
     nextExpectedSequence: stats.nextExpectedSequence
   });

   // Check for specific issues
   if (stats.gapsDetected > stats.messagesReordered) {
     console.warn('High gap detection rate - possible network issues');
   }

   if (stats.duplicatesDetected > 0) {
     console.warn('Duplicate messages detected - possible replay attack');
   }

*Resolution:*

.. code-block:: javascript

   // Adjust buffer settings for network conditions
   if (stats.timeouts > stats.messagesReordered * 0.1) {
     // Increase gap timeout for slow networks
     otr.policyManager.setPolicy('protocol.gapTimeoutMs', 60000);
   }

   // Increase buffer size for high out-of-order rates
   if (stats.messagesReordered > stats.messagesProcessed * 0.2) {
     otr.policyManager.setPolicy('protocol.maxBufferSize', 200);
   }

   // Reset ordering state if needed
   otr.messageOrdering.reset();

Enhanced SMP Issues
~~~~~~~~~~~~~~~~~~

**Issue: SMP session fails or times out**

.. code-block:: mermaid

   stateDiagram-v2
       [*] --> Diagnosing
       Diagnosing --> CheckState: Get SMP state
       CheckState --> StateOK: State valid
       CheckState --> StateError: State invalid
       
       StateOK --> CheckTimeout: Check timeout
       CheckTimeout --> TimeoutOK: Within limits
       CheckTimeout --> TimeoutError: Timeout exceeded
       
       StateError --> ResetState: Reset SMP state
       TimeoutError --> ExtendTimeout: Extend timeout
       
       ResetState --> Retry
       ExtendTimeout --> Retry
       TimeoutOK --> CheckSecret: Validate secret
       
       CheckSecret --> SecretOK: Secret valid
       CheckSecret --> SecretError: Secret invalid
       
       SecretOK --> Success
       SecretError --> [*]
       Retry --> [*]

*Symptoms:*
- SMP sessions timeout unexpectedly
- SMP state becomes corrupted
- Authentication failures

*Diagnostic Steps:*

.. code-block:: javascript

   // Get detailed SMP state
   const smpState = otr.enhancedSMP.getDetailedState();
   console.log('Enhanced SMP state:', {
     enhancedState: smpState.enhancedState,
     sessionId: smpState.sessionId,
     sessionDuration: smpState.sessionDuration,
     retryCount: smpState.retryCount,
     abortReason: smpState.abortReason,
     canPause: smpState.canPause,
     canResume: smpState.canResume,
     canAbort: smpState.canAbort
   });

   // Generate comprehensive diagnostics
   const diagnostics = otr.enhancedSMP.generateDiagnostics();
   console.log('SMP diagnostics:', {
     performance: diagnostics.performance,
     security: diagnostics.security,
     resources: diagnostics.resources,
     recommendations: diagnostics.recommendations
   });

*Resolution:*

.. code-block:: javascript

   // Extend session timeout if needed
   if (smpState.sessionDuration > 0.8 * smpState.config.sessionTimeoutMs) {
     otr.policyManager.setPolicy('security.sessionTimeoutMs', 600000); // 10 minutes
   }

   // Pause and resume session if state is corrupted
   if (smpState.enhancedState === 'error') {
     if (smpState.canPause) {
       otr.enhancedSMP.pauseSession('state_recovery');
       await new Promise(resolve => setTimeout(resolve, 1000));
       otr.enhancedSMP.resumeSession();
     } else {
       // Reset and restart
       otr.enhancedSMP.handleAbort('state_error');
       await otr.enhancedSMP.initiateEnhancedSMP(secret);
     }
   }

Policy Manager Issues
~~~~~~~~~~~~~~~~~~~

**Issue: Policy changes are rejected**

.. code-block:: mermaid

   graph TB
       subgraph "Policy Rejection Analysis"
           REJECT[Policy Change Rejected]
           CHECK_ACCESS[Check Access Permissions]
           CHECK_VALIDATION[Check Validation Rules]
           CHECK_SECURITY[Check Security Impact]
           
           ACCESS_OK{Access OK?}
           VALIDATION_OK{Validation OK?}
           SECURITY_OK{Security OK?}
           
           FIX_ACCESS[Fix Access Permissions]
           FIX_VALIDATION[Fix Validation Issues]
           FIX_SECURITY[Review Security Impact]
           
           SUCCESS[Policy Change Accepted]
       end
       
       REJECT --> CHECK_ACCESS
       CHECK_ACCESS --> ACCESS_OK
       ACCESS_OK -->|No| FIX_ACCESS
       ACCESS_OK -->|Yes| CHECK_VALIDATION
       
       CHECK_VALIDATION --> VALIDATION_OK
       VALIDATION_OK -->|No| FIX_VALIDATION
       VALIDATION_OK -->|Yes| CHECK_SECURITY
       
       CHECK_SECURITY --> SECURITY_OK
       SECURITY_OK -->|No| FIX_SECURITY
       SECURITY_OK -->|Yes| SUCCESS

*Symptoms:*
- Policy changes fail silently
- Access denied errors
- Validation failures

*Diagnostic Steps:*

.. code-block:: javascript

   // Check policy manager statistics
   const stats = otr.policyManager.getStats();
   console.log('Policy manager stats:', {
     totalPolicies: stats.totalPolicies,
     policiesSet: stats.policiesSet,
     policiesRead: stats.policiesRead,
     validationFailures: stats.validationFailures,
     accessDenials: stats.accessDenials,
     contextualOverrides: stats.contextualOverrides
   });

   // Check user permissions
   console.log('Current user:', otr.policyManager.currentUser);
   console.log('User roles:', Array.from(otr.policyManager.userRoles));

   // Validate specific policy change
   const validation = otr.policyManager.validatePolicyChange(
     'security.requireEncryption',
     true,
     false
   );
   console.log('Policy change validation:', validation);

*Resolution:*

.. code-block:: javascript

   // Grant necessary permissions
   if (stats.accessDenials > 0) {
     // Add admin role if needed
     otr.policyManager.userRoles.add('admin');
     
     // Or create policy manager with appropriate permissions
     const adminPolicyManager = new PolicyManager({
       currentUser: 'admin',
       userRoles: ['admin', 'user'],
       enableAccessControl: true
     });
   }

   // Fix validation issues
   if (validation.errors.length > 0) {
     console.log('Validation errors:', validation.errors);
     // Address each validation error specifically
   }

Performance Issues
~~~~~~~~~~~~~~~~~

**Issue: Poor performance with Phase 3 features**

.. code-block:: mermaid

   graph LR
       subgraph "Performance Analysis"
           PERF[Performance Issue]
           MEASURE[Measure Performance]
           IDENTIFY[Identify Bottleneck]
           OPTIMIZE[Apply Optimization]
           VERIFY[Verify Improvement]
       end
       
       subgraph "Common Bottlenecks"
           BUFFER[Large Message Buffers]
           VALIDATION[Excessive Validation]
           LOGGING[Verbose Logging]
           PERSISTENCE[State Persistence]
       end
       
       PERF --> MEASURE
       MEASURE --> IDENTIFY
       IDENTIFY --> OPTIMIZE
       OPTIMIZE --> VERIFY
       
       IDENTIFY -.-> BUFFER
       IDENTIFY -.-> VALIDATION
       IDENTIFY -.-> LOGGING
       IDENTIFY -.-> PERSISTENCE

*Symptoms:*
- Slow message processing
- High memory usage
- CPU spikes during operations

*Diagnostic Steps:*

.. code-block:: javascript

   // Measure performance metrics
   const performanceMetrics = {
     messageOrdering: otr.messageOrdering.getStats(),
     enhancedSMP: otr.enhancedSMP.generateDiagnostics(),
     policyManager: otr.policyManager.getStats()
   };

   console.log('Performance metrics:', performanceMetrics);

   // Check memory usage
   const memoryUsage = {
     messageBuffer: performanceMetrics.messageOrdering.pendingMessages * 1024, // Estimate
     smpSessions: performanceMetrics.enhancedSMP.resources.memoryUsage,
     policyCache: performanceMetrics.policyManager.validationCacheSize * 100 // Estimate
   };

   console.log('Memory usage estimate:', memoryUsage);

*Resolution:*

.. code-block:: javascript

   // Optimize message ordering
   otr.policyManager.setPolicy('protocol.maxBufferSize', 50);
   otr.policyManager.setPolicy('protocol.cleanupIntervalMs', 30000);

   // Optimize policy manager
   otr.policyManager.setPolicy('logging.enableDetailedLogging', false);
   otr.policyManager.options.validationLevel = 'basic';

   // Optimize enhanced SMP
   otr.enhancedSMP.config.enableStatePersistence = false;
   otr.enhancedSMP.config.enableDetailedLogging = false;

Debugging Tools
--------------

Debug Logging
~~~~~~~~~~~~

Enable comprehensive debug logging:

.. code-block:: javascript

   // Enable debug logging for all components
   const debugConfig = {
     logging: {
       'logging.enableDetailedLogging': true,
       'logging.logLevel': 'debug'
     }
   };

   // Apply debug configuration
   for (const [key, value] of Object.entries(debugConfig.logging)) {
     otr.policyManager.setPolicy(key, value);
   }

   // Set up debug event handlers
   otr.on('debug', (event) => {
     console.log(`[DEBUG] ${event.component}: ${event.message}`, event.data);
   });

Protocol Tracing
~~~~~~~~~~~~~~~

Enable protocol-level tracing:

.. code-block:: javascript

   // Enable protocol tracing
   const tracer = new ProtocolTracer({
     traceVersionNegotiation: true,
     traceMessageOrdering: true,
     traceEnhancedSMP: true,
     tracePolicyChanges: true
   });

   // Attach tracer to OTR instance
   otr.attachTracer(tracer);

   // Get trace data
   const traceData = tracer.getTraceData();
   console.log('Protocol trace:', traceData);

Performance Monitoring
~~~~~~~~~~~~~~~~~~~~

Set up performance monitoring:

.. code-block:: javascript

   // Performance monitoring setup
   const monitor = new PerformanceMonitor({
     sampleInterval: 1000,  // 1 second
     metricsRetention: 3600000  // 1 hour
   });

   // Monitor key metrics
   monitor.track('messageProcessingTime', () => {
     return otr.messageOrdering.getStats().averageProcessingTime;
   });

   monitor.track('smpSessionDuration', () => {
     return otr.enhancedSMP.getDetailedState().sessionDuration;
   });

   monitor.track('policyAccessTime', () => {
     return otr.policyManager.getStats().averageAccessTime;
   });

   // Get performance report
   const report = monitor.generateReport();
   console.log('Performance report:', report);

Error Recovery
-------------

Automatic Recovery
~~~~~~~~~~~~~~~~

Phase 3 features include automatic error recovery:

.. code-block:: javascript

   // Configure error recovery
   const recoveryConfig = {
     enableAutoRecovery: true,
     maxRetryAttempts: 3,
     retryDelayMs: 5000,
     escalationThreshold: 5
   };

   // Set up recovery handlers
   otr.on('recoveryAttempt', (event) => {
     console.log(`Recovery attempt ${event.attempt} for ${event.component}`);
   });

   otr.on('recoverySuccess', (event) => {
     console.log(`Recovery successful for ${event.component}`);
   });

   otr.on('recoveryFailed', (event) => {
     console.error(`Recovery failed for ${event.component}: ${event.reason}`);
   });

Manual Recovery
~~~~~~~~~~~~~

Manual recovery procedures:

.. code-block:: javascript

   // Manual recovery toolkit
   class ManualRecovery {
     static async resetVersionNegotiation(otr) {
       otr.versionNegotiation.reset();
       await otr.startVersionNegotiation();
     }
     
     static resetMessageOrdering(otr) {
       otr.messageOrdering.reset();
     }
     
     static async resetEnhancedSMP(otr) {
       otr.enhancedSMP.handleAbort('manual_reset');
       await new Promise(resolve => setTimeout(resolve, 1000));
       otr.enhancedSMP.reset();
     }
     
     static resetPolicyManager(otr) {
       otr.policyManager.resetToDefaults();
     }
     
     static async fullReset(otr) {
       await this.resetVersionNegotiation(otr);
       this.resetMessageOrdering(otr);
       await this.resetEnhancedSMP(otr);
       this.resetPolicyManager(otr);
     }
   }

Support Information
------------------

When contacting support, provide:

1. **Component versions and configuration**
2. **Debug logs and error messages**
3. **Performance metrics and statistics**
4. **Steps to reproduce the issue**
5. **Network and environment information**

.. code-block:: javascript

   // Generate support information
   function generateSupportInfo(otr) {
     return {
       timestamp: Date.now(),
       version: otr.getVersion(),
       configuration: otr.getConfiguration(),
       statistics: {
         versionNegotiation: otr.versionNegotiation.getStats(),
         messageOrdering: otr.messageOrdering.getStats(),
         enhancedSMP: otr.enhancedSMP.generateDiagnostics(),
         policyManager: otr.policyManager.getStats()
       },
       recentErrors: otr.getRecentErrors(),
       environment: {
         userAgent: navigator.userAgent,
         platform: navigator.platform,
         language: navigator.language
       }
     };
   }

.. note::
   This troubleshooting guide covers the most common issues with Phase 3 features. For complex issues or those not covered here, enable debug logging and contact support with the generated diagnostic information.
