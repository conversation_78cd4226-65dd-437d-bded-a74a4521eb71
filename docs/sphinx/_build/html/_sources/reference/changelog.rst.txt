Changelog
=========

All notable changes to WebOTR will be documented in this file.

[Unreleased]
------------

### Added
- Comprehensive Sphinx documentation system
- GitHub Actions workflow for documentation deployment
- API reference documentation
- Security overview and threat model documentation
- Platform-specific integration guides
- Troubleshooting and FAQ sections

### Changed
- Improved documentation structure and organization
- Enhanced developer contribution guidelines

### Security
- Added detailed security documentation
- Documented cryptographic implementation details

[0.1.0] - 2024-01-XX
--------------------

### Added
- Initial WebOTR browser extension implementation
- End-to-end encryption using Signal Protocol
- Discord platform integration
- Slack platform integration
- Microsoft Teams platform integration (beta)
- Browser extension manifest and core functionality
- React-based user interface components
- Secure key storage and management
- Message encryption and decryption
- Forward secrecy implementation
- Key exchange protocol (X3DH)
- Double Ratchet algorithm implementation

### Security
- AES-256-GCM message encryption
- X25519 key exchange
- Ed25519 digital signatures
- HMAC-SHA256 message authentication
- Secure random number generation
- Memory protection for sensitive data

### Platforms
- **Discord**: Full support for channels, DMs, and file sharing
- **Slack**: Complete workspace integration with threading support
- **Teams**: Basic chat encryption with enterprise considerations
- **Generic**: Experimental support for other web chat platforms
