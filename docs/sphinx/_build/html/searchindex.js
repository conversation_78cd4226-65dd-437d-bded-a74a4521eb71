Search.setIndex({"alltitles": {"AKE API Reference": [[14, null]], "AKE Architecture": [[15, null]], "AKE Documentation": [[18, "ake-documentation"]], "AKE Documentation Summary": [[17, null]], "AKE Handshake Flow": [[15, "ake-handshake-flow"]], "AKE Implementation Guide": [[16, null]], "AKE Protocol Engine": [[13, "ake-protocol-engine"], [16, "ake-protocol-engine"]], "AKE Protocol Flow": [[13, "ake-protocol-flow"]], "AKE Protocol System": [[18, "ake-protocol-system"]], "AKEError": [[14, "akeerror"]], "AKEMessage": [[14, "akemessage"]], "API Reference": [[3, "api-reference"], [6, "api-reference"], [8, "api-reference"], [10, "api-reference"], [17, "api-reference"], [18, "api-reference"], [19, "api-reference"], [23, "api-reference"], [34, "api-reference"]], "Abort Handling": [[3, "abort-handling"]], "Abort Reason Codes": [[3, "abort-reason-codes"]], "Access Control": [[8, "access-control"]], "Access Control Configuration": [[8, "access-control-configuration"]], "Advanced Configuration": [[16, "advanced-configuration"], [20, "advanced-configuration"], [22, "advanced-configuration"], [33, "advanced-configuration"]], "Advanced Features": [[16, "advanced-features"]], "Advanced Forward Secrecy": [[29, "advanced-forward-secrecy"]], "Advanced Integration": [[5, "advanced-integration"], [6, "advanced-integration"], [10, "advanced-integration"]], "Advanced Session Management": [[3, "advanced-session-management"]], "Architecture": [[19, "architecture"]], "Architecture Documentation": [[17, "architecture-documentation"], [18, "architecture-documentation"], [23, "architecture-documentation"], [34, "architecture-documentation"]], "Architecture Overview": [[0, null], [4, "architecture-overview"], [26, "architecture-overview"]], "Audit Architecture": [[21, "audit-architecture"]], "Audit Configuration": [[8, "audit-configuration"]], "Audit Event Structure": [[19, "audit-event-structure"]], "Audit Logging": [[8, "audit-logging"]], "Audit Trail System": [[19, "audit-trail-system"]], "AuditEvent": [[20, "auditevent"]], "AuditTrailSystem": [[20, "audittrailsystem"]], "Authenticated Key Exchange (AKE)": [[13, null], [29, "authenticated-key-exchange-ake"]], "Authentication Architecture": [[15, "authentication-architecture"]], "Automatic Recovery": [[9, "automatic-recovery"]], "Basic Enhanced SMP Usage": [[3, "basic-enhanced-smp-usage"]], "Basic Integration": [[5, "basic-integration"], [6, "basic-integration"], [10, "basic-integration"], [16, "basic-integration"], [22, "basic-integration"], [33, "basic-integration"]], "Basic Policy Management": [[8, "basic-policy-management"]], "Basic Steganography": [[31, "basic-steganography"]], "Basic Usage": [[20, "basic-usage"]], "Batch Verification": [[19, "batch-verification"]], "Best Practices": [[19, "best-practices"]], "Browser Compatibility": [[27, "browser-compatibility"]], "Browser Compatibility Considerations": [[26, "browser-compatibility-considerations"]], "Browser Extension Architecture": [[32, "browser-extension-architecture"]], "Browser Extension Deployment": [[21, "browser-extension-deployment"]], "Browser Extension Installation": [[36, "browser-extension-installation"]], "Browser Extension Integration": [[30, "browser-extension-integration"], [33, "browser-extension-integration"]], "Buffer Security": [[6, "buffer-security"]], "Chain Integrity": [[19, "chain-integrity"]], "Changelog": [[11, null]], "Chrome/Chromium": [[36, "chrome-chromium"]], "Code Examples": [[17, "code-examples"], [23, "code-examples"], [34, "code-examples"]], "Coding Standards": [[1, "coding-standards"]], "Common Issues": [[9, "common-issues"], [19, "common-issues"], [22, "common-issues"]], "Community & Support": [[2, "community-support"]], "Complete AKE Handshake": [[14, "complete-ake-handshake"]], "Complete API Coverage": [[17, "complete-api-coverage"], [23, "complete-api-coverage"], [34, "complete-api-coverage"]], "Complete Coverage": [[18, "complete-coverage"]], "Compliance Architecture": [[21, "compliance-architecture"]], "Compliance Reporting": [[19, "compliance-reporting"], [22, "compliance-reporting"]], "Compliance Standards": [[20, "compliance-standards"]], "Compliance Testing": [[28, "compliance-testing"]], "Compliance and Standards": [[24, "compliance-and-standards"], [27, "compliance-and-standards"]], "ComplianceReport": [[20, "compliancereport"]], "Component Integration": [[16, "component-integration"], [22, "component-integration"], [33, "component-integration"]], "Component Interaction": [[15, "component-interaction"], [21, "component-interaction"], [32, "component-interaction"]], "Component Interaction Flow": [[7, "component-interaction-flow"]], "Comprehensive Testing": [[18, "comprehensive-testing"]], "Conclusion": [[17, "conclusion"], [18, "conclusion"], [19, "conclusion"], [23, "conclusion"], [27, "conclusion"], [34, "conclusion"]], "Configuration": [[19, "configuration"], [19, "id1"]], "Configuration Options": [[6, "configuration-options"], [10, "configuration-options"], [19, "configuration-options"]], "Configuration Patterns": [[5, "configuration-patterns"]], "Constant-Time Operations": [[24, "constant-time-operations"]], "Constant-Time Operations API": [[25, "constant-time-operations-api"]], "Constant-Time Operations Implementation": [[26, "constant-time-operations-implementation"]], "Constants": [[14, "constants"], [20, "constants"], [31, "constants"]], "Constants and Enums": [[25, "constants-and-enums"]], "Constructor": [[20, "constructor"], [20, "id1"], [20, "id4"], [20, "id6"], [20, "id8"], [20, "id10"], [31, "constructor"], [31, "id1"], [31, "id7"], [31, "id9"], [31, "id11"]], "Contextual Policies": [[8, "contextual-policies"]], "Contextual Policy Configuration": [[8, "contextual-policy-configuration"]], "Contributing to WebOTR": [[1, null]], "Core Architecture": [[30, "core-architecture"]], "Core Components": [[13, "core-components"], [19, "core-components"], [24, "core-components"]], "Core Events": [[16, "core-events"], [22, "core-events"]], "Core Features": [[6, "core-features"]], "Core Functions": [[14, "core-functions"]], "Core Steganography Engine": [[30, "core-steganography-engine"]], "Cover Image Management": [[30, "cover-image-management"]], "CoverAnalysis": [[31, "coveranalysis"]], "CoverImageManager": [[31, "coverimagemanager"]], "Cryptographic Architecture": [[0, "cryptographic-architecture"], [15, "cryptographic-architecture"]], "Cryptographic Foundation": [[17, "cryptographic-foundation"], [29, "cryptographic-foundation"]], "Cryptographic Functions": [[14, "cryptographic-functions"]], "Cryptographic Implementation": [[13, "cryptographic-implementation"]], "Cryptographic Optimizations": [[13, "cryptographic-optimizations"]], "Cryptographic Security": [[17, "cryptographic-security"]], "Custom Configuration": [[10, "custom-configuration"]], "DHKeyPair": [[14, "dhkeypair"]], "DSAKeyPair": [[14, "dsakeypair"]], "Data Flow Architecture": [[7, "data-flow-architecture"], [21, "data-flow-architecture"]], "Debug Configuration": [[22, "debug-configuration"]], "Debug Logging": [[9, "debug-logging"]], "Debugging": [[19, "debugging"]], "Debugging Tools": [[9, "debugging-tools"]], "Default Configuration": [[6, "default-configuration"], [10, "default-configuration"]], "Defense Mechanisms": [[15, "defense-mechanisms"]], "Defense in Depth": [[21, "defense-in-depth"], [32, "defense-in-depth"]], "Deletion Patterns": [[20, "deletion-patterns"]], "Deletion Process": [[19, "deletion-process"]], "DeletionResult": [[20, "deletionresult"]], "Deniable Authentication": [[13, "deniable-authentication"]], "Deployment Architecture": [[21, "deployment-architecture"]], "Deployment Considerations": [[7, "deployment-considerations"], [16, "deployment-considerations"], [22, "deployment-considerations"], [26, "deployment-considerations"]], "Design Principles": [[26, "design-principles"]], "Developer Guide": [[2, null]], "Developer Integration": [[17, "developer-integration"], [23, "developer-integration"], [34, "developer-integration"]], "Developer Resources": [[18, "developer-resources"]], "Development Environment": [[5, "development-environment"]], "Development Workflow": [[1, "development-workflow"]], "Diagnostic Overview": [[9, "diagnostic-overview"]], "Diagnostics API": [[3, "diagnostics-api"]], "Diagnostics and Monitoring": [[3, "diagnostics-and-monitoring"]], "Diffie-Hellman Key Exchange": [[13, "diffie-hellman-key-exchange"]], "Digital Signatures": [[13, "digital-signatures"]], "DoD 5220.22-M Standard": [[19, "dod-5220-22-m-standard"]], "Documentation Features": [[18, "documentation-features"], [23, "documentation-features"]], "Documentation Improvements": [[17, "documentation-improvements"], [34, "documentation-improvements"]], "Documentation Lifecycle": [[23, "documentation-lifecycle"]], "Documentation Overview": [[17, "documentation-overview"], [23, "documentation-overview"], [34, "documentation-overview"]], "Documentation Sections": [[2, "documentation-sections"]], "Documentation Standards": [[17, "documentation-standards"], [23, "documentation-standards"], [34, "documentation-standards"]], "Documentation Suite Overview": [[18, "documentation-suite-overview"]], "EmbeddingParameters": [[31, "embeddingparameters"]], "Enhanced Error Recovery": [[24, "enhanced-error-recovery"]], "Enhanced Error Recovery API": [[25, "enhanced-error-recovery-api"]], "Enhanced Error Recovery Implementation": [[26, "enhanced-error-recovery-implementation"]], "Enhanced SMP": [[3, null]], "Enhanced SMP Issues": [[9, "enhanced-smp-issues"]], "Enhanced SMP States": [[3, "enhanced-smp-states"]], "EnhancedSMP Class": [[3, "enhancedsmp-class"]], "Enterprise Deployment": [[5, "enterprise-deployment"], [21, "enterprise-deployment"], [23, "enterprise-deployment"]], "Enterprise Features": [[18, "enterprise-features"], [23, "enterprise-features"]], "Enterprise Integration": [[19, "enterprise-integration"], [22, "enterprise-integration"]], "Enterprise Policy Management": [[8, "enterprise-policy-management"]], "EnterprisePolicyManager": [[20, "enterprisepolicymanager"]], "Error Classes": [[14, "error-classes"], [20, "error-classes"], [31, "error-classes"]], "Error Handling": [[6, "error-handling"], [10, "error-handling"], [13, "error-handling"], [14, "error-handling"], [16, "error-handling"], [20, "error-handling"], [22, "error-handling"]], "Error Handling Architecture": [[15, "error-handling-architecture"], [32, "error-handling-architecture"]], "Error Handling Strategy": [[7, "error-handling-strategy"]], "Error Recovery": [[9, "error-recovery"], [16, "error-recovery"]], "Event Handler Implementation": [[5, "event-handler-implementation"]], "Event Handling": [[5, "event-handling"], [16, "event-handling"], [22, "event-handling"]], "Event Types": [[19, "event-types"], [20, "event-types"]], "Event-Driven Architecture": [[17, "event-driven-architecture"]], "Events": [[19, "events"], [20, "events"]], "Example Usage": [[4, "example-usage"]], "Executive Summary": [[27, "executive-summary"]], "Features": [[2, "features"]], "Firefox": [[36, "firefox"]], "Flow Diagrams": [[23, "flow-diagrams"]], "Forward Secrecy API Reference": [[20, null]], "Forward Secrecy Architecture": [[21, null]], "Forward Secrecy Documentation": [[18, "forward-secrecy-documentation"]], "Forward Secrecy Documentation Summary": [[23, null]], "Forward Secrecy Implementation": [[19, null]], "Forward Secrecy Implementation Guide": [[22, null]], "Forward Secrecy System": [[18, "forward-secrecy-system"]], "ForwardSecrecyError": [[20, "forwardsecrecyerror"]], "ForwardSecrecyManager": [[19, "forwardsecrecymanager"], [20, "forwardsecrecymanager"]], "Four-Message Handshake": [[13, "four-message-handshake"]], "Frequently Asked Questions": [[12, null]], "Future Enhancements": [[17, "future-enhancements"], [23, "future-enhancements"], [27, "future-enhancements"], [34, "future-enhancements"]], "General Questions": [[12, "general-questions"]], "Getting Started": [[1, "getting-started"], [4, "getting-started"], [35, null]], "Global Instances": [[25, "global-instances"]], "High-Level Architecture": [[15, "high-level-architecture"], [21, "high-level-architecture"], [32, "high-level-architecture"]], "Image Formats": [[31, "image-formats"]], "Image Processing Optimization": [[30, "image-processing-optimization"]], "Image Processing Pipeline": [[32, "image-processing-pipeline"]], "Implementation Architecture": [[15, "implementation-architecture"], [32, "implementation-architecture"]], "Implementation Details": [[13, "implementation-details"], [23, "implementation-details"], [30, "implementation-details"]], "Implementation Enhancements": [[17, "implementation-enhancements"], [34, "implementation-enhancements"]], "Implementation Guidance": [[18, "implementation-guidance"]], "Implementation Guide": [[17, "implementation-guide"], [23, "implementation-guide"], [34, "implementation-guide"]], "Implementation Guidelines": [[19, "implementation-guidelines"]], "Implementation Highlights": [[24, "implementation-highlights"], [27, "implementation-highlights"]], "Implementation Quality": [[17, "implementation-quality"], [34, "implementation-quality"]], "Implementation Status": [[4, "implementation-status"]], "Import/Export API": [[8, "import-export-api"]], "Indices and tables": [[2, "indices-and-tables"]], "Input Validation API": [[25, "input-validation-api"]], "Input Validation Framework": [[24, "input-validation-framework"]], "Input Validation Framework Implementation": [[26, "input-validation-framework-implementation"]], "Installation Guide": [[36, null]], "Installation and Setup": [[12, "installation-and-setup"]], "Integration Examples": [[3, "integration-examples"], [6, "integration-examples"], [8, "integration-examples"], [10, "integration-examples"]], "Integration Guide": [[5, null]], "Integration Overview": [[5, "integration-overview"]], "Integration Patterns": [[17, "integration-patterns"], [34, "integration-patterns"]], "Integration Points": [[7, "integration-points"]], "Integration Testing": [[16, "integration-testing"], [22, "integration-testing"]], "Integration Testing Framework": [[28, "integration-testing-framework"]], "Interactive Elements": [[18, "interactive-elements"], [23, "interactive-elements"]], "Key Derivation": [[13, "key-derivation"]], "Key Derivation Architecture": [[15, "key-derivation-architecture"]], "Key Exchange Architecture": [[15, "key-exchange-architecture"]], "Key Features": [[4, "key-features"]], "Key Features Documented": [[17, "key-features-documented"], [18, "key-features-documented"], [23, "key-features-documented"], [34, "key-features-documented"]], "Key Generation Process": [[19, "key-generation-process"]], "Key Lifecycle Flow": [[21, "key-lifecycle-flow"]], "Key Rotation Engine": [[19, "key-rotation-engine"], [22, "key-rotation-engine"]], "KeyRotationEngine": [[20, "keyrotationengine"]], "KeyRotationError": [[20, "keyrotationerror"]], "KeySet": [[20, "keyset"]], "LSB Algorithm Implementation": [[33, "lsb-algorithm-implementation"]], "LSB Embedding Algorithm": [[30, "lsb-embedding-algorithm"]], "LSB Embedding Process": [[32, "lsb-embedding-process"]], "License": [[2, "license"]], "Maintenance and Updates": [[23, "maintenance-and-updates"]], "Manual Operations": [[20, "manual-operations"]], "Manual Recovery": [[9, "manual-recovery"]], "Memory Management": [[32, "memory-management"]], "Memory Sanitization": [[19, "memory-sanitization"]], "Message Creation Functions": [[13, "message-creation-functions"]], "Message Extraction Process": [[32, "message-extraction-process"]], "Message Flow States": [[6, "message-flow-states"]], "Message Format Specification": [[30, "message-format-specification"]], "Message Handling": [[16, "message-handling"]], "Message Ordering": [[6, null]], "Message Ordering Issues": [[9, "message-ordering-issues"]], "Message Processing Flow": [[6, "message-processing-flow"], [15, "message-processing-flow"]], "Message Processing Functions": [[13, "message-processing-functions"], [14, "message-processing-functions"]], "Message Structure": [[13, "message-structure"]], "Message Types": [[14, "message-types"]], "MessageOrdering Class": [[6, "messageordering-class"]], "Method Details": [[3, "method-details"], [6, "method-details"], [8, "method-details"], [10, "method-details"]], "Methods": [[20, "methods"], [20, "id2"], [20, "id5"], [20, "id7"], [20, "id9"], [20, "id11"], [31, "methods"], [31, "id2"], [31, "id8"], [31, "id10"], [31, "id12"]], "Migration Guide": [[24, "migration-guide"]], "Migration and Deployment": [[27, "migration-and-deployment"]], "Migration from Existing WebOTR": [[5, "migration-from-existing-webotr"]], "Military-Grade Security": [[23, "military-grade-security"]], "Mobile Application": [[5, "mobile-application"]], "Module Structure": [[15, "module-structure"], [32, "module-structure"]], "Monitoring Architecture": [[21, "monitoring-architecture"]], "Monitoring and Alerting": [[22, "monitoring-and-alerting"]], "Multi-Image Distribution": [[33, "multi-image-distribution"]], "Multi-Session Support": [[16, "multi-session-support"]], "Mutual Authentication": [[13, "mutual-authentication"]], "Negotiation Process": [[10, "negotiation-process"]], "Next Steps": [[4, "next-steps"], [35, "next-steps"], [36, "next-steps"]], "OTR Integration": [[30, "otr-integration"], [31, "otr-integration"], [34, "otr-integration"]], "OTRSteganographySession": [[31, "otrsteganographysession"]], "Optimization Strategies": [[34, "optimization-strategies"]], "Optimization Strategy": [[15, "optimization-strategy"]], "OtrState Class": [[14, "otrstate-class"]], "Out-of-Order Message Handling": [[6, "out-of-order-message-handling"]], "Overview": [[3, "overview"], [4, "overview"], [6, "overview"], [8, "overview"], [10, "overview"], [13, "overview"], [19, "overview"], [24, "overview"], [30, "overview"]], "Perfect Forward Secrecy": [[13, "perfect-forward-secrecy"]], "Performance Analysis": [[17, "performance-analysis"], [27, "performance-analysis"], [34, "performance-analysis"]], "Performance Architecture": [[15, "performance-architecture"], [21, "performance-architecture"], [32, "performance-architecture"]], "Performance Benchmarks": [[24, "id1"]], "Performance Characteristics": [[4, "performance-characteristics"], [6, "performance-characteristics"], [7, "performance-characteristics"]], "Performance Excellence": [[23, "performance-excellence"]], "Performance Impact": [[24, "performance-impact"], [27, "id2"]], "Performance Issues": [[9, "performance-issues"]], "Performance Metrics": [[15, "performance-metrics"], [19, "performance-metrics"]], "Performance Monitoring": [[9, "performance-monitoring"], [19, "performance-monitoring"], [22, "performance-monitoring"]], "Performance Optimization": [[5, "performance-optimization"], [16, "performance-optimization"], [17, "performance-optimization"], [30, "performance-optimization"], [33, "performance-optimization"]], "Performance Optimization Strategies": [[26, "performance-optimization-strategies"]], "Performance Optimizations": [[13, "performance-optimizations"]], "Performance Testing": [[16, "performance-testing"], [22, "performance-testing"]], "Performance Testing Framework": [[28, "performance-testing-framework"]], "Performance Tuning": [[6, "performance-tuning"]], "Platform Deployment": [[34, "platform-deployment"]], "Platform Integration": [[30, "platform-integration"], [31, "platform-integration"], [33, "platform-integration"], [34, "platform-integration"]], "Platform Integration Architecture": [[32, "platform-integration-architecture"]], "Platform Integration Layer": [[0, "platform-integration-layer"]], "PlatformIntegration": [[31, "platformintegration"]], "Policy Architecture": [[8, "policy-architecture"]], "Policy Categories": [[8, "policy-categories"]], "Policy Configuration": [[22, "policy-configuration"]], "Policy Enforcement": [[10, "policy-enforcement"]], "Policy Import/Export": [[8, "policy-import-export"]], "Policy Lifecycle": [[8, "policy-lifecycle"]], "Policy Management": [[19, "policy-management"]], "Policy Manager": [[8, null]], "Policy Manager Issues": [[9, "policy-manager-issues"]], "Policy Validation": [[8, "policy-validation"]], "PolicyManager Class": [[8, "policymanager-class"]], "Processing Performance": [[34, "processing-performance"]], "Processing Pipeline Optimization": [[32, "processing-pipeline-optimization"]], "Production Configuration": [[16, "production-configuration"], [22, "production-configuration"]], "Professional Documentation": [[18, "professional-documentation"]], "Progressive Processing": [[33, "progressive-processing"]], "Proof Generation": [[19, "proof-generation"]], "Proof Types": [[19, "proof-types"]], "ProofData": [[20, "proofdata"]], "Protocol Architecture": [[13, "protocol-architecture"]], "Protocol Compliance & Advanced Features": [[2, null], [4, null]], "Protocol Features": [[4, null]], "Protocol Flow Architecture": [[15, "protocol-flow-architecture"], [17, "protocol-flow-architecture"]], "Protocol Implementation": [[17, "protocol-implementation"]], "Protocol Optimizations": [[13, "protocol-optimizations"]], "Protocol Overview": [[7, null]], "Protocol States": [[14, "protocol-states"]], "Protocol Tracing": [[9, "protocol-tracing"]], "Protocol Versions": [[14, "protocol-versions"]], "Quality Assurance": [[17, "quality-assurance"], [23, "quality-assurance"]], "Quality Metrics": [[31, "quality-metrics"]], "Quality Standards": [[17, "quality-standards"], [34, "quality-standards"]], "Query Message Format": [[10, "query-message-format"]], "Quick Start": [[2, "quick-start"], [5, "quick-start"], [16, "quick-start"], [22, "quick-start"], [23, "quick-start"], [33, "quick-start"]], "Real-time Monitoring": [[19, "real-time-monitoring"], [21, "real-time-monitoring"], [22, "real-time-monitoring"]], "Reference": [[2, null]], "Replay Attack Prevention": [[6, "replay-attack-prevention"]], "Replay Protection": [[6, "replay-protection"]], "Resource Usage": [[17, "resource-usage"]], "Rotation Flow": [[19, "rotation-flow"]], "Rotation Triggers": [[19, "rotation-triggers"], [20, "rotation-triggers"]], "RotationResult": [[20, "rotationresult"]], "SMP Session Lifecycle": [[3, "smp-session-lifecycle"]], "Scalability Architecture": [[15, "scalability-architecture"], [21, "scalability-architecture"]], "Scaling Considerations": [[22, "scaling-considerations"]], "Secure Deletion Flow": [[21, "secure-deletion-flow"]], "Secure Deletion Manager": [[19, "secure-deletion-manager"], [22, "secure-deletion-manager"]], "Secure Memory Management": [[3, "secure-memory-management"], [24, "secure-memory-management"]], "Secure Memory Management API": [[25, "secure-memory-management-api"]], "Secure Memory Management Implementation": [[26, "secure-memory-management-implementation"]], "SecureDeletionError": [[20, "securedeletionerror"]], "SecureDeletionManager": [[20, "securedeletionmanager"]], "Security": [[2, null]], "Security Analysis": [[17, "security-analysis"], [18, "security-analysis"], [34, "security-analysis"]], "Security Architecture": [[15, "security-architecture"], [21, "security-architecture"], [32, "security-architecture"], [34, "security-architecture"]], "Security Auditing": [[23, "security-auditing"]], "Security Considerations": [[3, "security-considerations"], [6, "security-considerations"], [10, "security-considerations"], [19, "security-considerations"]], "Security Enhancements": [[4, "security-enhancements"]], "Security Events": [[16, "security-events"]], "Security Features": [[13, "security-features"], [30, "security-features"]], "Security Goals": [[29, "security-goals"]], "Security Impact": [[24, "security-impact"]], "Security Impact Assessment": [[27, "security-impact-assessment"]], "Security Implementation": [[33, "security-implementation"], [34, "security-implementation"]], "Security Model": [[7, "security-model"]], "Security Overview": [[29, null]], "Security Properties": [[17, "security-properties"]], "Security Review": [[17, "security-review"], [34, "security-review"]], "Security Risk Mitigation": [[27, "id1"]], "Security Testing Methodologies": [[28, "security-testing-methodologies"]], "Security Validation": [[24, "security-validation"]], "Security and Privacy": [[12, "security-and-privacy"]], "Sequence Number Security": [[6, "sequence-number-security"]], "Sequence Number Validation": [[6, "sequence-number-validation"]], "Session Management": [[3, "session-management"]], "Session Management API": [[3, "session-management-api"]], "SessionKeys": [[14, "sessionkeys"]], "Social Media Integration": [[32, "social-media-integration"]], "Standards Mapping": [[21, "standards-mapping"]], "State Machine": [[13, "state-machine"]], "State Management": [[14, "state-management"]], "State Persistence": [[3, "state-persistence"]], "State Transition Flow": [[15, "state-transition-flow"]], "Statistical Security": [[30, "statistical-security"], [33, "statistical-security"]], "StatisticalSecurity": [[31, "statisticalsecurity"]], "Steganographic Communication": [[29, "steganographic-communication"]], "Steganographic Flow Architecture": [[32, "steganographic-flow-architecture"]], "Steganographic Implementation": [[34, "steganographic-implementation"]], "Steganographic Methods": [[30, "steganographic-methods"], [31, "steganographic-methods"]], "Steganographic Security": [[34, "steganographic-security"]], "Steganographic Security Model": [[32, "steganographic-security-model"]], "Steganography API Reference": [[31, null]], "Steganography Architecture": [[32, null]], "Steganography Documentation Summary": [[34, null]], "Steganography Engine": [[33, "steganography-engine"]], "Steganography Implementation": [[30, null]], "Steganography Implementation Guide": [[33, null]], "SteganographyEngine": [[31, "steganographyengine"]], "SteganographyError": [[31, "steganographyerror"]], "StegoMessage": [[31, "stegomessage"]], "Step-by-Step Migration": [[5, "step-by-step-migration"]], "Support Information": [[9, "support-information"]], "Supported Versions": [[10, "supported-versions"]], "System Architecture": [[0, "system-architecture"], [7, "system-architecture"], [15, "system-architecture"], [17, "system-architecture"], [21, "system-architecture"], [23, "system-architecture"], [32, "system-architecture"]], "System Components": [[30, "system-components"]], "System Design": [[18, "system-design"], [34, "system-design"]], "System Flow": [[19, "system-flow"]], "System Requirements": [[36, "system-requirements"]], "Table of Contents": [[13, "table-of-contents"], [14, "table-of-contents"], [15, "table-of-contents"], [16, "table-of-contents"], [17, "table-of-contents"], [18, "table-of-contents"], [19, "table-of-contents"], [20, "table-of-contents"], [21, "table-of-contents"], [22, "table-of-contents"], [23, "table-of-contents"], [30, "table-of-contents"], [31, "table-of-contents"], [32, "table-of-contents"], [33, "table-of-contents"], [34, "table-of-contents"]], "Target Audiences": [[18, "target-audiences"]], "Technical Accuracy": [[23, "technical-accuracy"]], "Test Coverage": [[17, "test-coverage"]], "Test Execution and Reporting": [[28, "test-execution-and-reporting"]], "Test Suite Architecture": [[28, "test-suite-architecture"]], "Testing Architecture": [[15, "testing-architecture"], [32, "testing-architecture"]], "Testing Documentation": [[17, "testing-documentation"], [18, "testing-documentation"]], "Testing Overview": [[28, "testing-overview"]], "Testing and Validation": [[6, "testing-and-validation"], [8, "testing-and-validation"], [10, "testing-and-validation"], [13, "testing-and-validation"], [16, "testing-and-validation"], [22, "testing-and-validation"], [27, "testing-and-validation"]], "Testing and Validation Implementation": [[26, "testing-and-validation-implementation"]], "Threat Model": [[15, "threat-model"], [21, "threat-model"], [29, "threat-model"]], "Threat Modeling": [[17, "threat-modeling"], [34, "threat-modeling"]], "Timing Analysis": [[17, "timing-analysis"]], "Timing Requirements": [[21, "timing-requirements"]], "Total Documentation Scope": [[18, "total-documentation-scope"]], "Troubleshooting": [[5, "troubleshooting"], [19, "troubleshooting"]], "Troubleshooting Guide": [[9, null], [22, "troubleshooting-guide"]], "Type Definitions": [[14, "type-definitions"], [20, "type-definitions"], [31, "type-definitions"]], "Types of Contributions": [[1, "types-of-contributions"]], "Unit Testing": [[16, "unit-testing"], [22, "unit-testing"]], "Unit Testing Framework": [[28, "unit-testing-framework"]], "Usage Examples": [[14, "usage-examples"], [20, "usage-examples"], [25, "usage-examples"], [31, "usage-examples"]], "Usage Scenarios": [[17, "usage-scenarios"], [18, "usage-scenarios"], [23, "usage-scenarios"], [34, "usage-scenarios"]], "Usage and Features": [[12, "usage-and-features"]], "User Guide": [[2, null]], "Validation Configuration": [[8, "validation-configuration"]], "Verification Process": [[19, "verification-process"]], "Version Capabilities": [[10, "version-capabilities"]], "Version Downgrade Protection": [[10, "version-downgrade-protection"]], "Version Negotiation": [[10, null]], "Version Negotiation Issues": [[9, "version-negotiation-issues"]], "VersionNegotiation Class": [[10, "versionnegotiation-class"]], "Visual Design": [[23, "visual-design"]], "Web Worker Implementation": [[33, "web-worker-implementation"]], "WebOTR Documentation": [[2, null]], "WebOTR Security Documentation Summary": [[18, null]], "Your First Encrypted Conversation": [[35, "your-first-encrypted-conversation"]], "Zero-Knowledge Proof Flow": [[21, "zero-knowledge-proof-flow"]], "Zero-Knowledge Verifier": [[19, "zero-knowledge-verifier"], [22, "zero-knowledge-verifier"]], "ZeroKnowledgeError": [[20, "zeroknowledgeerror"]], "ZeroKnowledgeVerifier": [[20, "zeroknowledgeverifier"]], "[0.1.0] - 2024-01-XX": [[11, "xx"]], "[Unreleased]": [[11, "unreleased"]], "analyzeCapacity()": [[31, "analyzecapacity"]], "analyzeCover()": [[31, "analyzecover"]], "canSendEncrypted()": [[14, "cansendencrypted"]], "createDHCommit()": [[14, "createdhcommit"]], "createDHKey()": [[14, "createdhkey"]], "createRevealSignature()": [[14, "createrevealsignature"]], "createSignature()": [[14, "createsignature"]], "deletionCompleted": [[20, "deletioncompleted"]], "deriveKeys()": [[14, "derivekeys"]], "detectMessage()": [[31, "detectmessage"]], "detectPlatform()": [[31, "detectplatform"]], "dhExchange()": [[14, "dhexchange"]], "emergencyRotation()": [[20, "emergencyrotation"]], "enforcePolicy()": [[20, "enforcepolicy"]], "enhanceSecurityBeforeEmbedding()": [[31, "enhancesecuritybeforeembedding"]], "generateAuditReport()": [[20, "generateauditreport"]], "generateComplianceReport()": [[20, "generatecompliancereport"], [20, "id12"]], "generateCover()": [[31, "generatecover"]], "generateDHKeyPair()": [[14, "generatedhkeypair"]], "generateDeletionProof()": [[20, "generatedeletionproof"]], "generateEmbeddingPositions()": [[31, "generateembeddingpositions"]], "generateKeySet()": [[20, "generatekeyset"]], "generateRotationProof()": [[20, "generaterotationproof"]], "getAuditLog()": [[20, "getauditlog"]], "getSecurityStatus()": [[20, "getsecuritystatus"]], "goEncrypted()": [[14, "goencrypted"]], "goPlaintext()": [[14, "goplaintext"]], "hideMessage()": [[31, "hidemessage"]], "initialize()": [[20, "initialize"], [20, "id3"]], "initialized": [[20, "initialized"]], "injectStatisticalNoise()": [[31, "injectstatisticalnoise"]], "interceptUpload()": [[31, "interceptupload"]], "isAKEInProgress()": [[14, "isakeinprogress"]], "libOTR Enhancements API Reference": [[25, null]], "libOTR Enhancements Implementation": [[26, null]], "libOTR Enhancements Testing and Validation": [[28, null]], "libOTR Security Enhancements": [[24, null], [29, "libotr-security-enhancements"]], "libOTR Security Enhancements Summary": [[27, null]], "logEvent()": [[20, "logevent"]], "performSecureDeletion()": [[20, "performsecuredeletion"]], "processDHCommit()": [[14, "processdhcommit"]], "processDHKey()": [[14, "processdhkey"]], "processDownloadedImages()": [[31, "processdownloadedimages"]], "processRevealSignature()": [[14, "processrevealsignature"]], "processSignature()": [[14, "processsignature"]], "processStegoImage()": [[31, "processstegoimage"]], "revealMessage()": [[31, "revealmessage"]], "rotateKeys()": [[20, "rotatekeys"]], "rotateKeysManually()": [[20, "rotatekeysmanually"]], "rotationCompleted": [[20, "rotationcompleted"]], "rotationTriggered": [[20, "rotationtriggered"]], "selectOptimalCover()": [[31, "selectoptimalcover"]], "sendStegoMessage()": [[31, "sendstegomessage"]], "setSessionKeys()": [[14, "setsessionkeys"]], "shutdown()": [[20, "shutdown"]], "startAKE()": [[14, "startake"], [14, "id1"]], "validateCompliance()": [[20, "validatecompliance"]], "verifyAuditIntegrity()": [[20, "verifyauditintegrity"]], "verifyBatchProofs()": [[20, "verifybatchproofs"]], "verifyDeletion()": [[20, "verifydeletion"]], "verifyProof()": [[20, "verifyproof"]]}, "docnames": ["developer-guide/architecture", "developer-guide/contributing", "index", "protocol/enhanced-smp", "protocol/index", "protocol/integration-guide", "protocol/message-ordering", "protocol/overview", "protocol/policy-manager", "protocol/troubleshooting", "protocol/version-negotiation", "reference/changelog", "reference/faq", "security/ake", "security/ake-api", "security/ake-architecture", "security/ake-implementation", "security/ake-summary", "security/documentation-summary", "security/forward-secrecy", "security/forward-secrecy-api", "security/forward-secrecy-architecture", "security/forward-secrecy-implementation", "security/forward-secrecy-summary", "security/libotr-enhancements", "security/libotr-enhancements-api", "security/libotr-enhancements-implementation", "security/libotr-enhancements-summary", "security/libotr-enhancements-testing", "security/overview", "security/steganography", "security/steganography-api", "security/steganography-architecture", "security/steganography-implementation", "security/steganography-summary", "user-guide/getting-started", "user-guide/installation"], "envversion": {"sphinx": 62, "sphinx.domains.c": 3, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 9, "sphinx.domains.index": 1, "sphinx.domains.javascript": 3, "sphinx.domains.math": 2, "sphinx.domains.python": 4, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinx.ext.intersphinx": 1, "sphinx.ext.todo": 2, "sphinx.ext.viewcode": 1}, "filenames": ["developer-guide/architecture.rst", "developer-guide/contributing.rst", "index.rst", "protocol/enhanced-smp.rst", "protocol/index.rst", "protocol/integration-guide.rst", "protocol/message-ordering.rst", "protocol/overview.rst", "protocol/policy-manager.rst", "protocol/troubleshooting.rst", "protocol/version-negotiation.rst", "reference/changelog.rst", "reference/faq.rst", "security/ake.rst", "security/ake-api.rst", "security/ake-architecture.rst", "security/ake-implementation.rst", "security/ake-summary.rst", "security/documentation-summary.rst", "security/forward-secrecy.rst", "security/forward-secrecy-api.rst", "security/forward-secrecy-architecture.rst", "security/forward-secrecy-implementation.rst", "security/forward-secrecy-summary.rst", "security/libotr-enhancements.rst", "security/libotr-enhancements-api.rst", "security/libotr-enhancements-implementation.rst", "security/libotr-enhancements-summary.rst", "security/libotr-enhancements-testing.rst", "security/overview.rst", "security/steganography.rst", "security/steganography-api.rst", "security/steganography-architecture.rst", "security/steganography-implementation.rst", "security/steganography-summary.rst", "user-guide/getting-started.rst", "user-guide/installation.rst"], "indexentries": {"constanttimeops() (class)": [[25, "ConstantTimeOps", false]], "cryptovalidation() (class)": [[25, "CryptoValidation", false]], "error_types (global variable or constant)": [[25, "ERROR_TYPES", false]], "globalerrorrecovery (global variable or constant)": [[25, "globalErrorRecovery", false]], "globalsecurememorypool (global variable or constant)": [[25, "globalSecureMemoryPool", false]], "protocolerrorrecovery() (class)": [[25, "ProtocolErrorRecovery", false]], "recovery_strategies (global variable or constant)": [[25, "RECOVERY_STRATEGIES", false]], "securememory() (class)": [[25, "SecureMemory", false]], "securememorypool() (class)": [[25, "SecureMemoryPool", false]], "securityevent() (class)": [[25, "SecurityEvent", false]], "securityvalidationerror() (class)": [[25, "SecurityValidationError", false]], "securityvalidationerror.code (securityvalidationerror attribute)": [[25, "SecurityValidationError.code", false]], "securityvalidationerror.message (securityvalidationerror attribute)": [[25, "SecurityValidationError.message", false]]}, "objects": {"": [[25, 0, 1, "", "ConstantTimeOps"], [25, 0, 1, "", "CryptoValidation"], [25, 1, 1, "", "ERROR_TYPES"], [25, 0, 1, "", "ProtocolErrorRecovery"], [25, 1, 1, "", "RECOVERY_STRATEGIES"], [25, 0, 1, "", "SecureMemory"], [25, 0, 1, "", "SecureMemoryPool"], [25, 0, 1, "", "SecurityEvent"], [25, 0, 1, "", "SecurityValidationError"], [25, 1, 1, "", "globalErrorRecovery"], [25, 1, 1, "", "globalSecureMemoryPool"]], "ProtocolErrorRecovery": [[25, 2, 1, "", "constructor"], [25, 2, 1, "", "getStats"], [25, 2, 1, "", "handleAKEError"], [25, 2, 1, "", "resetStats"]], "SecureMemory": [[25, 2, 1, "", "constructor"], [25, 2, 1, "", "destroy"], [25, 2, 1, "", "getStats"], [25, 2, 1, "", "getView"], [25, 2, 1, "", "read"], [25, 2, 1, "", "secureWipe"], [25, 2, 1, "", "write"]], "SecureMemoryPool": [[25, 2, 1, "", "allocate"], [25, 2, 1, "", "cleanup"], [25, 2, 1, "", "constructor"], [25, 2, 1, "", "deallocate"], [25, 2, 1, "", "destroy"], [25, 2, 1, "", "getStats"]], "SecurityEvent": [[25, 2, 1, "", "constructor"], [25, 2, 1, "", "toJSON"]], "SecurityValidationError": [[25, 3, 1, "", "code"], [25, 3, 1, "", "message"]]}, "objnames": {"0": ["js", "class", "JavaScript class"], "1": ["js", "data", "JavaScript data"], "2": ["js", "method", "JavaScript method"], "3": ["js", "attribute", "JavaScript attribute"]}, "objtypes": {"0": "js:class", "1": "js:data", "2": "js:method", "3": "js:attribute"}, "terms": {"": [4, 7, 8, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34], "0": [2, 3, 5, 6, 8, 9, 13, 14, 16, 19, 22, 24, 25, 26, 27, 28, 30, 31, 33], "000": 6, "01": [2, 22], "015": 27, "015m": 24, "02": 27, "02m": 24, "05": [27, 31], "05m": 24, "07": 27, "07m": 24, "0x00": [19, 24, 25, 26, 27, 28, 29, 30], "0x00000000": 13, "0x01": [28, 30, 33], "0x02": [13, 28], "0x12345678": [13, 16], "0x3": 28, "0x55": [19, 24, 25, 26, 27, 28, 29], "0x574f5452": 30, "0x87654321": 13, "0xaa": [19, 24, 25, 26, 27, 28, 29], "0xbb": 28, "0xcc": 28, "0xdd": 28, "0xfe": [30, 33], "0xff": [19, 24, 25, 26, 27, 28, 29], "0xffffffff": [6, 16], "0xfffffffffffffffe": 28, "1": [2, 3, 5, 6, 8, 9, 13, 14, 18, 19, 22, 26, 28, 30, 31, 33, 35], "10": [3, 5, 6, 8, 9, 16, 18, 22, 27, 30, 33], "100": [4, 5, 6, 7, 8, 9, 18, 19, 20, 22, 24, 26, 27, 28, 30, 33], "1000": [7, 9, 16, 18, 19, 20, 22, 25, 26, 28, 33], "10000": [6, 7, 28], "100000": [30, 31, 33], "1000000": 30, "100k": [30, 33], "100kb": 7, "100m": [7, 18, 19, 22, 23, 29], "100mb": 5, "1024": [9, 26, 30, 33], "10485760": [19, 20, 22], "104857600": 5, "10kb": [6, 7], "10m": [4, 6, 7], "10mb": [19, 22, 33], "12": [27, 30], "120000": 6, "1234": 31, "12345": 19, "12345678901234567890abcdef": 28, "128": [5, 6, 13, 26], "12m": 24, "13": 27, "14": [13, 17], "140": [18, 19, 20, 22, 23, 29], "1400": 16, "15": [19, 24, 27], "150": [6, 22], "1500": 3, "15000": 5, "150m": [17, 18], "16": [13, 26, 28], "164": 6, "1640995200000": [3, 10, 19], "18": [1, 28], "180000": 5, "1800000": [3, 5, 8, 20, 22], "1kb": [6, 7], "1m": 6, "2": [3, 4, 5, 6, 7, 8, 9, 10, 13, 14, 16, 18, 19, 20, 22, 23, 24, 25, 26, 28, 29, 35], "20": [4, 18, 24, 27, 28], "200": [5, 6, 9, 16], "2000": [16, 22], "2024": [2, 22], "2048": [13, 17, 26], "22": [2, 18, 20, 22, 23, 29], "24": [8, 19, 22], "245": 33, "25": [5, 6, 19, 24, 27], "255": [30, 33], "256": [0, 2, 11, 12, 13, 14, 17, 19, 25, 26, 28, 29], "27001": 20, "2d": [30, 33], "3": [3, 4, 5, 6, 7, 9, 10, 13, 14, 16, 18, 19, 22, 25, 26, 28, 30, 33, 35], "30": [3, 5, 8, 19, 20, 22, 27, 28], "300": 18, "30000": [5, 6, 9, 16], "300000": [3, 4, 5, 8, 22], "32": [5, 6, 13, 14, 19, 20, 24, 25, 26, 28, 30], "3526": [24, 26, 27, 28, 29], "3600000": [9, 19, 20, 22], "4": [5, 6, 9, 13, 14, 16, 18, 19, 26, 28, 30, 33], "40": [22, 24, 27, 31, 33], "40m": 22, "42": [19, 22], "45": [8, 19], "45000": [3, 8], "4th": 30, "5": [3, 5, 6, 8, 9, 14, 18, 19, 22, 26, 28, 30, 31], "50": [5, 6, 8, 9, 18, 19, 20, 22, 30, 36], "500": [16, 18, 20, 22], "5000": [9, 16, 22], "50000": 7, "500m": 16, "50m": [4, 7, 18, 19, 22, 23, 29], "512": [26, 36], "512kb": 5, "5220": [2, 18, 20, 22, 23, 29], "524288": 5, "5242880": [20, 22], "5678": 31, "5kb": 7, "5m": [4, 6, 7], "5mb": [20, 22, 30], "6": [19, 26, 28], "60": 22, "600": [18, 30], "60000": [6, 8, 9], "600000": [3, 5, 8, 9], "60m": 22, "64": [4, 6, 8, 13, 24, 26, 28], "7": [18, 19, 20, 22, 23, 26, 28, 29, 30, 33], "75": 27, "7776000000": [19, 20, 22], "8": [9, 13, 14, 26, 28, 30, 33], "80": [22, 27], "800": 30, "8000": 33, "80m": 22, "85": [19, 22], "86400000": 8, "88": 36, "8kb": 33, "9": [19, 22, 31], "90": [19, 22, 36], "9012": 31, "95": [19, 22, 31, 33], "96": 13, "99": 19, "999": 16, "A": [3, 10, 24, 27], "Be": 19, "By": 30, "For": [5, 9, 17, 18, 19, 22, 23, 24, 25, 26, 27, 28, 29, 34], "IN": [6, 7], "If": 29, "In": 6, "It": 8, "No": [6, 8, 9, 10, 12, 13, 14, 26, 30, 33], "Not": 33, "ONES": 20, "Or": [5, 9], "The": [2, 3, 6, 7, 8, 10, 12, 13, 16, 17, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 34], "Their": [10, 14, 26], "These": [24, 27, 29], "To": 4, "With": [17, 23, 34], "_": 33, "_clearauthst": 26, "_findpools": 26, "_getactivehandl": 22, "_isweakdhkei": 26, "_performvalid": 26, "_resolvecommitconflict": 26, "_securecleanup": 3, "_storesecretsecur": 3, "_tobiginteg": 26, "_validationcach": 26, "a1": 6, "a2": 6, "a3": 6, "a4": 6, "a5": 6, "ab": [26, 28, 33], "abc123": 19, "abort": [4, 5, 25], "abort_r": 3, "abort_sess": 25, "abortmsg": 3, "abortreason": [3, 9], "about": [12, 27, 29, 35], "abstract": [15, 21, 31, 32], "acceler": 17, "accept": [6, 9, 10, 24, 27, 28, 33], "access": [2, 4, 5, 7, 9, 12, 17, 18, 19, 23, 25, 26, 34], "access_level": [5, 8], "access_ok": 9, "accessdeni": [5, 8, 9], "accesslog": 22, "accessrul": 8, "account": [5, 10, 12], "accur": 23, "accuraci": [17, 18, 34], "achiev": [4, 27], "acl": [4, 7, 8], "across": [2, 15, 18, 21, 23, 25, 27, 28, 29, 30, 32, 33, 34], "action": [3, 5, 6, 9, 11, 25, 26, 28], "activ": [2, 3, 14, 17, 22, 28, 29, 34], "activeinst": [25, 28], "activejob": 33, "actor": 18, "actual": [19, 23, 31], "ad": [4, 7, 11, 30], "adapt": [0, 24, 27, 29, 30, 31, 33, 34], "adaptive_lsb": [31, 33], "adaptivelsb": [30, 31, 33], "adaptivelsbalgorithm": 33, "adaptiveset": 31, "add": [1, 2, 5, 9, 12, 19, 26, 28, 30, 33, 36], "addchangelisten": [5, 8], "addit": [3, 6, 10, 14, 17, 20, 23, 26, 31, 34], "address": [9, 24], "adjust": [9, 19, 27, 33], "admin": [5, 8, 9], "admin_access": 8, "admin_consol": 8, "administr": [8, 10, 23], "adminpolicymanag": 9, "advanc": [7, 17, 18, 19, 23, 24, 27, 34], "advancedproof": [20, 22], "advancedstego": 33, "advancedwebotrcli": 5, "adversari": 29, "ae": [0, 2, 11, 12, 13, 14, 17, 18, 26, 29], "aeskei": [13, 14], "after": [5, 13, 14, 16, 19, 20, 22, 24, 27], "aftereach": 22, "afterward": 29, "against": [6, 10, 17, 18, 20, 24, 26, 27, 28, 29, 30, 34], "aggreg": 19, "ago": [19, 22], "ah": 3, "ai": 34, "ak": [2, 4, 7, 10, 24, 25, 26, 27], "ake_initiation_fail": 16, "akecomplet": 16, "akeerror": [13, 16], "akemanag": 16, "akemessagehandl": 16, "akeprogress": 16, "akeprotocolengin": 16, "akeprotocolerror": 13, "akeresult": 16, "akestart": [13, 14, 16], "aketimeout": 16, "al": 7, "alert": [6, 7, 8, 18, 19, 20, 23, 27], "alertsecurityteam": 22, "alertthreshold": 22, "algorithm": [11, 26, 27, 29, 31, 34], "alic": [3, 8, 10, 13, 14, 16], "all": [2, 3, 4, 5, 8, 9, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34], "alloc": [19, 24, 25, 26, 27, 28], "allow": [5, 7, 9, 10, 27, 30], "allowv2": [5, 8, 9, 10], "allowv3": [5, 8, 9, 10], "alpha": [29, 30, 31, 33, 34], "alphaindex": 30, "alreadi": 6, "alt": 3, "altern": [9, 19, 20], "alwai": [14, 26], "amount": 19, "an": [3, 6, 10, 12, 14, 20, 23, 27, 31, 34, 35], "analysi": [2, 6, 9, 23, 24, 28, 29, 30, 31, 33], "analysisen": 31, "analyz": [30, 31, 33], "analyzecov": [30, 33], "analyzeimag": 33, "analyzetimingconsist": 28, "ani": [6, 8, 12, 14, 19, 31], "anon": 8, "anonym": 8, "answer": 12, "anti": [20, 29, 30, 31, 34], "antidetect": [31, 33], "anyon": 8, "api": [1, 2, 4, 5, 7, 11, 13, 24, 26, 27, 28, 29], "app": [5, 6, 8, 9], "appear": [12, 18, 30, 34], "appli": [5, 7, 8, 9, 17, 24, 25, 26, 27, 30, 31, 33], "applic": [6, 7, 8, 9, 16, 19, 22, 25, 27, 29, 33], "applymemorypressur": 19, "applypolicychang": 5, "approach": [17, 23, 28], "appropri": [9, 26], "ar": [4, 5, 6, 7, 8, 9, 12, 13, 17, 19, 23, 24, 27, 28, 29, 35], "architect": [18, 23], "architectur": [2, 24, 27], "area": 27, "argument": 25, "arithmet": 13, "arrai": [5, 9, 10, 20, 25, 26, 31, 33], "arraybuff": [24, 26, 27, 29, 30], "arriv": [6, 9], "ask": 2, "assess": [2, 5, 8, 10], "assesscompli": 8, "assesssecurityimpact": 8, "assur": [2, 18], "asymmetr": 29, "async": [3, 5, 6, 9, 10, 13, 14, 16, 19, 20, 22, 26, 28, 30, 31, 33], "asyncdelet": 22, "asynchron": 16, "attach": [9, 34], "attachtrac": 9, "attack": [2, 3, 4, 5, 7, 9, 10, 13, 14, 16, 17, 18, 19, 24, 25, 26, 27, 28, 29, 34], "attempt": [9, 16, 25, 34], "attent": 22, "audit": [2, 3, 4, 7, 9, 17, 18, 20, 22, 29, 34], "audit_ev": 20, "audit_event_12345": 19, "auditedpolici": 8, "audithandl": 8, "auditlevel": [8, 19, 20, 22], "auditlog": [8, 19, 22], "auditlogg": [3, 8, 22], "auditor": [17, 23, 34], "auditpolici": 22, "auditretent": [19, 20, 22], "audittrail": [16, 19, 20, 22], "audittrailrequir": 22, "audittrailsystem": [2, 18, 19, 23], "auth": [26, 28], "authent": [2, 8, 9, 10, 11, 14, 16, 17, 18, 30, 34], "authstat": 14, "auto": 31, "autodetect": 31, "autom": [23, 27, 28, 34], "automat": [2, 3, 4, 5, 7, 10, 19, 20, 23, 24, 25, 27, 29, 30, 31, 34], "automaticrotationrequir": 22, "autoretri": 16, "autorot": [19, 20, 22], "autoselectcov": [31, 33], "autowip": 25, "avail": [3, 7, 9, 10, 13, 19, 24, 26, 29, 36], "availablework": 33, "aval": 26, "averag": 22, "averageaccesstim": 9, "averageprocessingtim": 9, "averages": 25, "averagesessiontim": 3, "averagetim": [19, 22, 28], "averageverificationtim": [19, 20], "avoid": 33, "await": [3, 5, 6, 8, 9, 10, 13, 14, 16, 19, 20, 22, 26, 28, 30, 31, 33], "awaiting_dh_kei": 26, "awaiting_dhkei": [14, 16], "awaiting_revealsig": [14, 16], "awaiting_sig": [14, 16], "awar": [8, 19, 27], "b": [3, 10, 22, 26, 28], "back": [17, 23, 27], "background": [0, 17, 23, 34], "backup": [5, 8], "backward": [2, 3, 4, 5, 24, 27, 29], "balanc": 8, "bandwidth": 17, "base": [0, 2, 3, 4, 6, 8, 10, 11, 12, 13, 17, 19, 20, 23, 24, 27, 29, 30, 31, 33, 34], "base64": 3, "baselin": 28, "baselinemetr": 28, "baselinepath": 28, "basic": [7, 9, 11, 17, 23, 24, 27, 34], "batch": [20, 26, 27], "batch_validation_fail": 26, "batchvalid": 26, "batchverif": [20, 22], "batchverificationtim": [19, 20], "becom": 9, "been": [13, 24, 27, 29], "befor": [8, 13, 16, 24, 27, 31, 34], "beforeeach": [16, 22], "begin": [2, 4, 12, 14, 19, 35], "behavior": [8, 27, 28, 34], "being": 20, "benchmark": [18, 27, 28], "benchmarkconstanttim": 25, "benchmarkconstanttimeop": 28, "benchmarkerrorrecoveri": 28, "benchmarksecurememori": 28, "benchmarkvalid": 28, "benefit": 24, "best": [1, 2, 17, 18, 23, 24, 26, 27, 34], "beta": [11, 12], "better": [3, 30], "between": [10, 13, 17, 18, 20, 23, 25, 29], "beyond": [19, 29], "big": 13, "bigint": 24, "biginteg": [26, 28], "bind": [13, 17, 18, 29, 30, 33], "bit": [6, 13, 17, 19, 29, 30, 31, 33, 34], "bitcount": [31, 33], "bitindex": 30, "bitsperpixel": [30, 31, 33], "bitstomessag": 33, "blob": [30, 31, 33], "block": [8, 23, 24, 30, 33, 34], "bmp": [31, 33], "bob": [3, 10, 13, 14, 16], "boolean": [3, 8, 14, 20, 25, 31], "both": [5, 10, 12, 13, 14, 17, 29, 35], "bottleneck": [9, 17], "boundari": [18, 24, 27, 28], "boundarytest": 28, "box": 7, "br": [4, 7, 8], "branch": [1, 24, 26, 28], "breach": 29, "breadcrumb": 23, "break": [3, 6, 14, 16, 26], "breakdown": [18, 23], "bring": 27, "browser": [0, 1, 2, 11, 12, 13, 19, 24, 28, 29, 34], "buf": 6, "buffer": [5, 7, 8, 9, 25, 26], "buffer1": 24, "buffer2": 24, "bufferresult": 6, "bug": [1, 2], "build": [3, 4, 7, 23], "built": [7, 23, 24, 26, 27, 29], "busi": 33, "bval": 26, "byte": [13, 14, 19, 20, 25, 26, 28, 30, 31], "bytelength": 30, "c": 10, "cach": [13, 16, 17, 26, 27], "calcul": 33, "calculatecapac": 30, "calculatechecksum": 33, "calculatecomplex": 30, "calculatecompliancemetr": 19, "calculatecrc32": 30, "calculatemean": 28, "calculatenoiselevel": 30, "calculateregress": 28, "calculatestddev": 28, "calculatesuit": 30, "calculatevari": 28, "call": 26, "camouflag": 34, "can": [2, 6, 8, 12, 13, 19, 24, 28, 29, 31], "canabort": [3, 9], "cancel": 3, "cancel_button": 3, "canmodifi": 8, "cannot": [8, 12, 13, 17, 29], "canpaus": [3, 9], "canresum": [3, 9], "canva": [30, 33], "capabl": [3, 4, 5, 7, 9, 18, 22, 27, 30, 31, 34], "capac": [29, 30, 31, 34], "case": [3, 5, 6, 7, 8, 14, 16, 17, 18, 23, 26, 27, 28, 33, 34], "catch": [3, 5, 6, 13, 14, 16, 19, 20, 25, 26, 28, 30, 33], "categori": [3, 4, 5, 9, 28], "cd": 28, "cdn": [30, 33], "cdninstagram": [30, 33], "ce": 7, "ceil": 33, "central": [18, 19], "cfg_in": 7, "cfg_out": 7, "chain": [18, 20, 29], "chainhash": [19, 20], "chang": [1, 5, 8, 9, 11, 19, 23], "changelog": 2, "channel": [2, 11, 13, 19, 24, 27, 29, 30, 31, 33, 34], "characterist": [17, 18, 19, 23, 27, 29, 30, 31, 33, 34], "chart": [15, 17, 18, 21, 23, 32, 34], "chat": [0, 2, 11, 12, 29, 35], "check": [4, 5, 6, 7, 8, 9, 10, 14, 16, 18, 19, 24, 26, 27, 29, 30, 33], "check_access": 9, "check_compat": 9, "check_downgrad": 9, "check_polici": 9, "check_queri": 9, "check_readi": 6, "check_secur": 9, "check_valid": 9, "checkaccess": 8, "checkforhiddenmessag": 33, "checklist": 27, "checkout": 28, "checkreplayattack": 16, "checksecret": 9, "checkstat": 9, "checksum": [30, 31, 33], "checksum_offset": 30, "checktimeout": 9, "checkversiondowngrad": 10, "chrome": 27, "chunk": [30, 31, 33, 34], "chunkdata": 33, "chunkindex": 33, "chunksiz": [30, 31, 33], "ci": 28, "cl1": 7, "cl2": 7, "cl3": 7, "cl4": 7, "claim": [17, 23, 29], "class": [2, 4, 5, 9, 16, 17, 18, 19, 23, 25, 26, 28, 30, 33, 34], "classif": [25, 26], "clean": 25, "cleanup": [3, 5, 6, 7, 13, 19, 24, 25, 26, 27, 28], "cleanupintervalm": [5, 6, 9], "clear": [3, 14, 16, 17, 23, 26, 27, 28, 34], "click": [2, 12, 35, 36], "client": [7, 10], "clone": 1, "cmp": 6, "code": [2, 13, 14, 16, 18, 20, 24, 25, 26, 27, 28, 31], "codebas": 23, "codecov": 28, "coefficientofvari": 28, "collect": [19, 34], "collectcoveragefrom": 28, "com": [5, 10, 30, 31, 33], "combin": [13, 19, 30], "combinechunk": 33, "combinechunkresult": 33, "comfort": 23, "commit": [13, 14, 16, 17, 18, 19, 20, 24, 25, 26, 27, 28, 29], "common": [4, 5, 10, 12, 23, 26, 30, 34], "commons": 26, "commonvers": [9, 10], "commun": [3, 6, 12, 13, 14, 16, 17, 19, 23, 30, 34], "compar": [24, 26, 27, 29], "compareto": 26, "comparison": [2, 24, 26, 27, 28, 29], "compat": [2, 3, 4, 5, 7, 9, 10, 13, 17, 24, 28, 34], "compat_ok": 9, "compet": [2, 24, 25, 26, 27, 28, 29], "competing_dh_commit": [25, 26, 28], "complet": [2, 3, 4, 6, 8, 11, 12, 13, 16, 19, 20, 22, 25, 26, 27, 28, 29, 31], "complex": [8, 9, 17, 23, 30, 31, 33], "complianc": [8, 17, 18, 23, 29, 34], "compliance_check": [19, 20], "compliance_standard": 20, "compliance_viol": 20, "compliancemonitoringdashboard": 22, "compliancereport": 22, "compliancescor": 22, "compliancestandard": [19, 20, 22], "compliancestatu": [8, 20], "complianceviol": [19, 22], "compliant": [18, 19, 20, 23], "compon": [0, 2, 3, 4, 5, 9, 11, 14, 17, 18, 20, 23, 26, 28, 31], "component_initi": 20, "comprehens": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 15, 16, 17, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 32, 33, 34], "compress": [20, 31], "compressionen": 20, "compressionlevel": [30, 31, 33], "compromis": [16, 17, 19, 20, 29], "comput": [13, 14, 17, 26], "computedmac": 25, "computeeventhash": 19, "computemac": 28, "concept": 34, "conclus": 2, "concurr": [13, 16, 17, 18, 34], "condit": [6, 9, 10, 18, 24, 26, 27, 28], "conditionalselect": 24, "confid": 31, "confidenti": [13, 29], "config": [5, 9, 33], "config_migr": 5, "configmap": 5, "configur": [2, 4, 7, 9, 17, 18, 23, 24, 25, 26, 27, 28, 31, 34, 35], "configurepolici": 5, "confirm": [24, 27, 36], "conn": 9, "connect": 9, "connection_lost": 3, "consciou": 26, "consid": [7, 27], "consider": [2, 4, 5, 11, 17, 18, 23, 34], "consist": [0, 13, 17, 18, 19, 23, 24, 25, 26, 28, 34], "consol": [3, 5, 8, 9, 13, 14, 16, 20, 22, 24, 25, 30, 31, 33], "const": [3, 4, 5, 6, 8, 9, 10, 13, 14, 16, 19, 20, 22, 24, 25, 26, 28, 30, 31, 33], "constant": [2, 4, 7, 27, 28, 29], "constanttim": [26, 28], "constanttimecompar": 26, "constanttimeequ": [24, 25, 26, 28], "constanttimeop": [24, 25, 26, 28], "constrain": [5, 6], "constraint": [26, 27, 31], "constructor": [3, 5, 6, 8, 14, 16, 19, 25, 26, 30, 33], "consumpt": 17, "contact": [5, 9], "contactid": 3, "contain": 31, "content": [0, 6, 12, 29], "context": [3, 5, 7, 8, 13, 20, 24, 25, 26, 28, 30], "contextu": 4, "contextualoverrid": 9, "continu": [19, 25, 28, 36], "contribut": [2, 11, 17, 23, 34], "control": [0, 1, 2, 4, 7, 10, 23, 26, 30, 33, 35], "convers": [2, 12, 29], "convert": 33, "coordin": [18, 19, 20, 23], "copi": [23, 25], "core": [2, 3, 4, 5, 7, 11, 17, 18, 20, 26, 28, 29, 33, 34], "correct": [16, 17, 23, 29, 30, 34], "correctli": [19, 28], "correspond": 34, "corrupt": [7, 9, 24, 27], "cost": 27, "could": 13, "count": [5, 19, 20, 23, 29, 33], "counter": [24, 27], "countermeasur": [18, 23, 30, 34], "cover": [2, 9, 17, 18, 23, 31, 33, 34], "coverag": [4, 24, 26, 27, 28], "coveragethreshold": 28, "coverimag": [30, 31, 33], "coverimagedatabas": [30, 31], "coverimagegener": 30, "coverimagemanag": [2, 30, 33, 34], "covermanag": 33, "coverrequir": 33, "covert": [2, 29, 30, 34], "covertyp": 30, "cpu": [9, 17, 19], "crc32": 30, "creat": [1, 3, 5, 6, 7, 8, 9, 10, 12, 13, 14, 16, 19, 25, 31, 33], "createauditev": 19, "createchunkdata": 33, "createcommit": 19, "createcov": 30, "createdat": [19, 20], "createdhcommit": [13, 17], "createdhkei": 13, "createel": [30, 33], "createhead": 30, "createmanifest": 33, "createquerymessag": 10, "createrevealsignatur": 13, "createsess": 16, "createsignatur": 13, "createstegomessag": 30, "creation": [8, 10, 12, 17, 18], "criteria": 30, "critic": [6, 17, 18, 22, 24, 25, 27], "cross": [2, 17, 18, 19, 23, 28, 34], "crypto": [5, 7, 13, 16, 17, 24, 25, 26, 27, 28, 29], "crypto_error": [13, 14, 16], "cryptograph": [2, 3, 7, 11, 16, 18, 19, 20, 23, 24, 25, 26, 27, 34], "cryptographi": [5, 17], "cryptographic_kei": 22, "cryptographicerasur": [19, 20, 22], "cryptographicerror": 13, "cryptographicproof": 19, "cryptographicvalid": 22, "cryptovalid": [24, 25, 26, 28], "csv": 20, "ct": [4, 7], "ctx": [8, 33], "current": [2, 3, 5, 6, 8, 9, 12, 14, 16, 20, 22, 28, 29, 31, 33], "currentconfig": 5, "currentgener": [20, 22], "currentkeyset": 22, "currentsequ": 6, "currentst": 16, "currentus": [5, 8, 9], "custom": [5, 7, 8, 16, 18, 22, 25, 26, 33], "customiz": 26, "customsecurerandom": 22, "customvalid": 8, "cv": 7, "d": 10, "dai": [19, 22], "dailyreport": 22, "dark": 23, "dashboard": [22, 23, 27], "data": [2, 3, 4, 5, 6, 8, 9, 11, 13, 14, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 34], "data1": [25, 28], "data2": [25, 28], "data_migr": 5, "data_volum": 20, "databas": [8, 30, 31], "databasepath": 31, "dataview": 30, "datavolumethreshold": [19, 20, 22], "date": [3, 5, 6, 8, 9, 17, 19, 22, 23, 27, 28, 30, 33, 34], "db": 31, "dct": [31, 34], "dealloc": 25, "debug": [4, 5, 7, 8], "debugconfig": [9, 22], "decis": [7, 18, 26], "decod": 30, "decodemessag": 30, "decrypt": [3, 8, 11, 12, 13, 16, 30, 31], "decryptedmessag": [30, 31], "decryptmessag": 30, "deep": [17, 34], "def456": 19, "default": [4, 7, 8, 14, 16, 17, 19, 20, 22, 23, 25, 26, 27, 31, 33, 34], "defaultconfig": [6, 10], "defaultinstancetag": 16, "defaultmethod": 33, "defaultopt": 19, "defaultvers": 10, "defens": [17, 18, 19, 23, 24, 26, 27, 34], "defin": 16, "definit": 2, "degrad": [7, 24, 25, 26, 27, 29], "delai": [9, 25], "delet": [2, 6, 8, 13, 16, 18, 23, 27, 29], "deletion_complet": 20, "deletion_fail": 20, "deletion_failur": 22, "deletion_pattern": 20, "deletion_start": 20, "deletion_tim": 22, "deletion_verifi": 20, "deletioncomplet": [19, 22], "deletioncontext": 20, "deletioncount": 20, "deletiondata": 20, "deletionev": 22, "deletionfail": 22, "deletionpattern": 19, "deletionpolici": 22, "deletionpromis": 22, "deletionresult": 22, "deletiontim": [19, 20, 22], "deletiontimeout": [19, 20, 22], "deliv": [6, 10], "deliveri": [6, 9], "demand": 19, "demonstr": 34, "deni": [8, 9], "deniabl": [2, 10, 17, 18, 29, 34], "denial": 5, "depart": 19, "depend": [1, 17, 18, 26, 27, 28], "deploi": [5, 7, 23], "deploy": [2, 3, 4, 11, 17, 18], "depth": [23, 26, 34], "deriv": [14, 17, 18, 19, 29], "derivekei": [13, 19], "derivesessionkei": 28, "describ": [6, 8, 10, 13, 16, 22, 28], "descript": 25, "design": [0, 2, 3, 4, 5, 7, 8, 15, 17, 21, 32], "desir": 31, "destroi": [3, 24, 25, 28], "destruct": 28, "detail": [2, 4, 9, 11, 14, 15, 16, 17, 18, 19, 20, 21, 22, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34], "detect": [0, 2, 5, 6, 7, 8, 9, 10, 14, 16, 17, 19, 20, 26, 27, 28, 29, 30, 31, 33, 34], "detectmessagegap": 6, "detectregress": 28, "detectspidermonkei": 19, "detectv8engin": 19, "detectwebkit": 19, "determin": [3, 6, 10], "determinist": [31, 33], "dev": 1, "dev_log": 8, "dev_mobil": 8, "devconfig": 10, "develop": [8, 10, 11], "developmentconfig": 5, "devic": [2, 8, 12, 18, 23, 29, 34], "dh": [13, 14, 16, 17, 18, 24, 25, 26, 27, 28, 29], "dh_commit": [13, 14, 16], "dh_kei": [13, 14, 16], "dh_key_too_larg": 26, "dh_key_too_smal": 26, "dh_modulu": [26, 28], "dh_modulus_minus_2": 26, "dh_weak_kei": 26, "dhcommit": [13, 14, 16], "dhcommitmessag": [13, 14], "dhexchang": 13, "dhkei": 16, "dhkeymessag": 13, "dhkeypair": [13, 16, 28], "dhkeyrespons": [13, 14, 16], "dhpublickei": 25, "dhsharedsecret": 13, "diag": [3, 9], "diagnos": 9, "diagnosi": 22, "diagnost": [4, 7], "diagram": [7, 15, 17, 18, 21, 32, 34], "didthrow": 28, "differ": [0, 3, 5, 7, 8, 17, 18, 19, 23, 24, 26, 28, 34], "diffi": [14, 17, 27, 29], "digit": [0, 11, 17, 18, 29, 34], "direct": [16, 22, 33], "directembed": 30, "directli": 12, "disabl": [5, 22], "discord": [0, 2, 11, 12, 30, 33, 34, 35], "discordapp": [30, 33], "discuss": 2, "displai": 9, "displayhiddenmessag": [30, 33], "displaymessag": 5, "disrupt": 2, "distribut": [29, 30, 31, 34], "distributemessag": 33, "dive": [17, 34], "dm": 11, "do": 12, "document": [1, 4, 5, 7, 11, 14, 15, 20, 21, 25, 26, 27, 28, 30, 31, 32, 33], "dod": [2, 18, 20, 22, 23, 29], "dod_5220_22_m": 20, "dodcompli": [19, 20], "doe": [12, 29], "doesn": [2, 12], "domain": [31, 34], "doubl": [0, 11, 29], "downgrad": [4, 7, 9], "downgradecheck": 10, "download": [2, 30, 34], "downloadhandl": 30, "downloadprocessor": 33, "drawimag": 33, "drive": 34, "dropbox": 34, "dryrun": 8, "dsa": [13, 14, 16, 17, 18, 26], "dsakei": 16, "dsakeypair": [13, 16], "dump": 29, "duplic": [6, 9], "duplicatesdetect": [5, 9], "durat": [3, 16, 25, 26, 28], "dure": [2, 6, 7, 9, 19, 24, 26, 27, 28, 29, 31, 34], "dynam": [29, 30, 34], "e": 10, "e1": [6, 10], "e2": [6, 10], "e3": [6, 10], "e4": [6, 10], "e5": 6, "each": [4, 9, 13, 17, 22, 26, 29], "earli": [13, 19], "earlier": 6, "easi": 18, "eavesdrop": [17, 29], "ecdh": 29, "ecdsa": [13, 17], "ed1": 7, "ed2": 7, "ed25519": [0, 11, 19, 29], "ed3": 7, "ed4": 7, "edg": [27, 36], "effect": [7, 8, 27], "effectiveconfig": 8, "effectiveconfigur": 8, "effectived": 8, "effectivetimeout": 8, "effici": [7, 13, 17, 24, 25, 26, 29, 30, 34], "either": 13, "element": [0, 17, 24, 26, 27, 29, 31, 34], "elimin": [2, 24, 27, 29], "els": [3, 5, 8, 9, 16, 19, 20, 26, 28, 33], "email": [30, 34], "emb": [30, 33], "embed": [29, 31, 33, 34], "embedchunk": 30, "embeddingparam": 33, "embedinchunk": 33, "embedlsb": 30, "embedmessag": 33, "emerg": [19, 20, 27], "emergency_rot": 20, "emergencyreset": 16, "emergencyrespons": 20, "emergencyresult": 20, "emergencyrot": [19, 22], "emergencyrotationen": 22, "emit": [16, 19, 20, 22, 30, 33], "enabl": [2, 3, 4, 5, 8, 9, 12, 13, 16, 19, 20, 25, 26, 29, 30, 31, 34, 35], "enableaccesscontrol": [4, 5, 8, 9], "enableauditlog": [5, 8], "enableautorecoveri": 9, "enablecach": [5, 8, 16], "enabledetailedlog": [3, 5, 8, 9], "enabledplatform": 31, "enableencrypt": 16, "enableenhancedsmp": 5, "enablefallbackcrypto": 16, "enablegaprecoveri": 6, "enablelog": [5, 25, 26], "enablemessageord": 5, "enablemetr": 16, "enablenoiseinject": 26, "enableoptim": [5, 8], "enablepolicymanag": 5, "enablepool": 26, "enablesecurityaudit": [5, 8], "enablesecurityvalid": [3, 5, 8], "enablestatepersist": [3, 4, 5, 8, 9], "enableversionnegoti": 5, "encod": [3, 13], "encrypt": [0, 2, 3, 8, 10, 11, 12, 13, 14, 16, 17, 19, 20, 28, 29, 30, 31, 34], "encryptedgx": [13, 14], "encryptedmessag": 30, "encryptedsignatur": [13, 14], "encryptionen": 20, "encryptionkei": [19, 20], "encryptionrequir": 22, "encryptmessag": 30, "end": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 20, 22, 26, 27, 28, 29], "enddat": [19, 20, 22], "endpixel": 33, "endpoint": 29, "endtim": 16, "enforc": [6, 8, 18, 20, 23], "enforceinstancetag": 16, "engin": [2, 17, 20, 31, 34], "enhanc": [1, 2, 5, 7, 8, 10, 11, 13, 20, 22, 30, 31, 33], "enhancecov": 30, "enhanced_smp_st": 3, "enhancedabort": 3, "enhancederasur": [19, 20], "enhancedotrmessag": 6, "enhancedotrsess": 10, "enhancedsmp": [4, 5, 7, 9], "enhancedst": [3, 9], "enhancedverif": 22, "enhancesecuritybeforeembed": 33, "enough": 33, "ensur": [2, 4, 6, 7, 8, 10, 17, 19, 23, 27, 28, 29, 30, 34], "enterpris": [2, 3, 4, 7, 11, 20, 24, 27, 29], "enterpriseconfig": 5, "enterprisefeatur": [20, 22], "enterpriseintegr": [19, 20, 22], "enterprisepolicymanag": [2, 18, 19, 22, 23], "enterprisesecurechat": 3, "enterprisewebotrconfigur": 8, "entri": [5, 9, 20, 28], "entropi": [19, 20], "enum": [2, 8], "enumer": 8, "env": 8, "env_dev": 8, "env_prod": 8, "environ": [7, 8, 9, 17, 18, 19, 23, 24, 26, 27, 28, 34], "ephemer": [13, 16, 17, 18, 29], "equal": [24, 26, 28, 29], "equival": [24, 27, 29], "er": [4, 7], "er1": 7, "er2": 7, "er3": 7, "er4": 7, "eras": 13, "erasur": [20, 23, 29], "error": [1, 2, 3, 4, 5, 8, 17, 18, 19, 23, 27, 28, 29, 30, 33, 34], "error_typ": [24, 25, 26, 28], "errorcod": 3, "errorcorrect": 33, "errorr": 22, "errorrecoveri": [26, 28], "errorstartak": 10, "errortyp": 3, "es6": 1, "escal": 9, "escalationthreshold": 9, "esmp": [4, 5, 7], "esmp_a": 3, "esmp_b": 3, "especi": 6, "essenti": [10, 13, 17], "establish": [4, 7, 9, 10, 13, 14, 16, 29, 34], "establishak": 13, "estim": [9, 31], "estimatedlength": 31, "etc": [17, 26], "evalu": 27, "even": [2, 12, 19, 24, 29], "event": [2, 3, 4, 6, 8, 9, 14, 23, 24, 25, 27, 29, 33, 34], "event_typ": 20, "eventdata": [19, 20], "eventemitt": [19, 20], "eventtyp": 20, "everi": [6, 28, 30], "everyth": [8, 18], "evict": 6, "evid": [18, 19, 20], "examin": [17, 23, 34], "exampl": [1, 2, 5, 18, 26], "exce": [8, 31], "exceed": 9, "excel": [4, 7, 18, 19, 24, 27, 29], "excess": 9, "exchang": [0, 2, 3, 7, 11, 14, 16, 17, 18], "excludesystem": 8, "execut": [2, 17, 18, 24, 26, 30], "executetask": 33, "exhaust": 3, "exist": [4, 24, 26, 27, 30], "existingconfig": 5, "exp": 6, "expand": 17, "expect": [5, 6, 8, 9, 10, 13, 16, 22, 26, 27, 28, 31], "expectedsequ": 6, "expectedsignatur": 26, "expectedst": 13, "expectedtyp": 24, "expens": 13, "experi": 34, "experiment": [11, 12], "expir": [3, 6, 25], "expiresat": [19, 20], "explor": [4, 27], "exponenti": 13, "export": [4, 22, 26, 28], "exportcompliancereport": 22, "exportdata": 8, "exportpolici": 8, "expos": 19, "exposur": [18, 29], "extend": [3, 6, 8, 9, 10, 14, 16, 19, 20, 26, 30, 31], "extendtimeout": 9, "extens": [0, 2, 6, 7, 10, 11, 12, 17, 18, 23, 29, 34], "extra": 10, "extract": [10, 30, 31, 33, 34], "extracteddata": 33, "extractfromchunk": 33, "extractimagechunk": 33, "extraction_fail": 31, "extractiontim": 33, "extractlsb": 30, "extractmessag": 33, "extractmessagechunk": 33, "extrasymmetrickei": 10, "f": [8, 10, 19], "facebook": [30, 31, 33, 34], "fact": 13, "fail": [3, 5, 8, 9, 13, 14, 16, 19, 20, 22, 25, 26, 28, 31], "faileddelet": 22, "failedverif": [19, 20], "failur": [9, 13, 16, 17, 19, 20, 22, 24, 25, 27], "fall": 27, "fallback": [3, 7, 10, 13, 16, 22, 26, 27, 28], "fallbackpolici": 10, "fals": [3, 5, 6, 8, 9, 10, 16, 19, 20, 22, 25, 28, 30, 33], "faq": 11, "fast": 23, "faster": 16, "featur": [1, 3, 5, 7, 9, 10, 19, 20, 22, 24, 26, 27, 28, 29, 31, 33, 35], "feedback": 23, "feel": 2, "fetch": 30, "fewer": 5, "ffffffffff": 26, "field": [14, 24, 26, 27, 30], "file": [8, 11, 29, 30, 31, 33, 34], "file_size_increase_max": 31, "fileinput": [31, 33], "fill": [6, 26], "filter": [8, 19, 34], "final": [3, 19, 25, 26, 28], "finalstat": 28, "find": [26, 30, 33], "findindex": 33, "findsuit": 30, "fine": [8, 10], "fingerprint": 19, "finish": [14, 16], "fip": [18, 19, 20, 22, 23, 29], "fips_140_2": 20, "fipscompli": [19, 20, 22], "firefox": 27, "first": [2, 5, 10, 33], "firststego": 33, "fisher": 33, "fit": 26, "fix": [1, 9], "fix_access": 9, "fix_queri": 9, "fix_secur": 9, "fix_valid": 9, "flag": [10, 30, 31], "flexibl": 8, "floor": [16, 26, 33], "flow": [2, 3, 4, 18, 34], "flowchart": [6, 7, 9, 17, 18], "focus": 19, "follow": [1, 7, 8, 10, 13, 17, 19, 20, 23, 24, 26, 27, 28, 29, 34], "forc": 19, "foreach": [5, 16, 22, 31], "forens": 20, "forg": 13, "fork": 1, "forkrul": 2, "format": [4, 8, 9, 16, 17, 18, 19, 20, 22, 23, 34], "formatnorm": 33, "formatreport": 19, "forward": [0, 2, 10, 11, 12, 16, 17, 34], "forwardsecreci": [19, 20, 22], "forwardsecrecymanag": [2, 18, 22, 23], "found": [30, 31, 33], "foundat": [2, 4, 7], "four": [17, 18, 19, 23, 29, 34], "fragment": 10, "fragments": 16, "framework": [2, 4, 25, 27], "free": [12, 36], "frequent": [2, 5], "fresh": 29, "friendli": 23, "from": [2, 3, 4, 6, 8, 9, 10, 13, 14, 16, 17, 19, 20, 22, 24, 25, 26, 27, 28, 29, 30, 31, 33, 34], "full": [4, 8, 11, 12, 13, 16, 17, 18, 23, 24, 27, 30], "fulli": [20, 29, 33], "fullkei": 8, "fullmessag": 30, "fullreset": 9, "function": [1, 2, 4, 5, 9, 11, 17, 18, 19, 23, 24, 25, 26, 27, 28, 29, 30, 34], "fundament": 30, "furo": 18, "futur": [2, 6, 29], "fuzz": [24, 27, 28], "fuzzdhkeyvalid": 28, "g": [10, 13], "gantt": 18, "gap": [5, 6, 9], "gaprecoveri": 6, "gapsdetect": [5, 9], "gapsiz": 6, "gaptimeoutm": [5, 6, 9], "garbag": [19, 34], "garbagecollectionhint": 22, "gather": 3, "gc": 19, "gcm": [0, 11, 12, 29], "gdpr": 20, "gener": [0, 2, 3, 8, 9, 10, 11, 13, 14, 16, 17, 18, 20, 22, 23, 25, 28, 29, 30, 31, 33, 34], "generatebyt": 19, "generatecompliancereport": [8, 19, 22], "generatedat": [3, 19, 20], "generatedhkeypair": [13, 16, 28], "generatediagnost": [3, 9], "generatedsakeypair": [14, 16], "generateembeddingposit": 33, "generateeventid": 19, "generateinstancetag": 16, "generatekeyfingerprint": 19, "generatekeyid": 16, "generatekeyset": 19, "generateproofid": 19, "generaterandombiginteg": 28, "generaterandombyt": 13, "generaterecommend": 8, "generatereport": 9, "generatereportid": 19, "generaterotationproof": [19, 22], "generatesessionid": 16, "generatesessionreport": 3, "generatesupportinfo": 9, "generatetaskid": 33, "generatezkproof": 19, "generationtim": 19, "generatoren": 31, "get": [2, 3, 6, 7, 8, 9, 16, 20, 25, 26, 30, 33, 36], "getactivesess": 5, "getadaptivebitposit": 30, "getadaptiveskip": 30, "getauditlog": 8, "getconfigur": [5, 9], "getcontext": [30, 33], "getcurrentgener": 22, "getcurrentplatform": 33, "getdeletionpattern": 19, "getdetailedst": [3, 9], "geteffectivepolici": [5, 8, 9], "getglobalstat": [25, 28], "getimagechunk": 30, "getmessagetohid": [30, 33], "getnextsendsequ": 6, "getorderingstatist": 6, "getorgeneratedhkeypair": 16, "getpolici": [5, 8], "getpool": 26, "getrandomvalu": [25, 26, 28], "getrecenterror": 9, "getsecurityconfigur": 8, "getsecuritystatu": 19, "getsess": [3, 16], "getsessionkei": 16, "getstat": [5, 6, 8, 9, 13, 14, 16, 25], "getsupportedfeatur": 5, "gettracedata": 9, "getvers": [5, 9], "getversionnegotiationdebug": [5, 9], "getview": [25, 28], "git": 1, "github": [2, 11, 28], "given": 31, "global": [2, 19, 24, 26, 27, 28], "globalerrorrecoveri": [25, 26, 28], "globalsecurememorypool": 25, "go": 23, "goal": 2, "goe": [19, 29], "googl": 34, "goplaintext": [13, 16], "got": 13, "grace": [7, 24, 26, 27, 29], "graceful_degrad": 25, "gracefulli": [10, 16, 19, 20], "grade": [2, 3, 4, 8, 12, 18, 19, 24, 27, 29, 30], "gradual": 5, "grain": [8, 10], "grant": 9, "graph": [3, 4, 5, 6, 7, 8, 9, 10], "greatli": 27, "group": [12, 13, 17, 24, 27, 29], "guarante": [17, 18, 29, 30], "guest": 8, "guid": [1, 4, 11, 18, 26, 29, 35], "guidanc": [17, 34], "guidelin": [11, 24, 27], "ha": [24, 26, 27, 29], "handl": [0, 1, 2, 4, 9, 17, 18, 19, 23, 24, 25, 26, 27, 28, 29, 31, 33, 34], "handleabort": [3, 9], "handleakeerror": [24, 25, 26, 28], "handledhcommit": 13, "handledhkei": 13, "handlediscorddownload": 30, "handlediscordupload": 30, "handleerror": [16, 33], "handlefacebookdownload": 30, "handlefacebookupload": 30, "handleimagedownload": 30, "handleimageupload": 30, "handleincomingmessag": [6, 16], "handleinstagramdownload": 30, "handleinstagramupload": 30, "handleoutoford": 6, "handlepolicychang": 5, "handlequeri": 10, "handler": [7, 8, 9, 25], "handlesequencerollov": 6, "handlesmp1": 3, "handlesmp2": 3, "handlesmp3": 3, "handlesmp4": 3, "handlesmpabort": 3, "handlesmpmessag": 3, "handleworkererror": 33, "handleworkermessag": 33, "handshak": [16, 17, 18, 29], "harden": 18, "hardwar": [17, 27], "hasgap": 6, "hash": [13, 14, 19, 26, 27], "hash_bas": 20, "hashgx": [26, 28], "hashofencryptedgx": [13, 14], "hasmessag": 31, "have": [12, 13, 14, 17, 23, 26, 28, 34], "header": [30, 31], "heap": 19, "height": [30, 31, 33], "hellman": [14, 17, 27, 29], "hello": [13, 16, 31, 33], "help": [1, 24, 27, 36], "helper": 28, "here": 9, "hidden": [30, 31, 33, 34], "hiddenmessag": [30, 31, 33], "hide": [2, 29, 30, 31, 33, 34], "hide_message_fail": 33, "hidemessag": [30, 33], "hierarch": [2, 4, 8], "hierarchi": 8, "high": [3, 6, 7, 8, 9, 10, 17, 18, 22, 23, 25, 27, 34], "higher": 22, "highest": 10, "highlight": [2, 18, 23], "highthroughputconfig": 6, "hipaa": [18, 19, 20, 22, 23, 29], "hkdf": [13, 14, 17, 18, 19, 29], "hl1": 7, "hl2": 7, "hl3": 7, "hl4": 7, "hmac": [11, 13, 14, 17, 18, 26, 29], "hmacsha256": 26, "hook": 30, "hour": [8, 9, 19, 22], "how": [7, 12], "htmlimageel": 31, "htmlinputel": 31, "http": 7, "human": 25, "i": [0, 2, 3, 6, 8, 9, 10, 12, 14, 16, 17, 19, 20, 22, 23, 25, 26, 28, 29, 30, 33, 36], "icon": [2, 12, 35], "id": [3, 13, 14, 16, 19, 20, 28, 33], "ident": [3, 8, 13, 16, 17, 28], "identif": 31, "identifi": [9, 10, 31], "idl": 3, "ignor": [16, 25, 26], "ignore_incom": 26, "illustr": [7, 15, 17, 21, 23, 32, 34], "imag": [2, 29, 34], "imageblob": 30, "imagecach": 30, "imagechunk": [30, 33], "imagedata": [30, 31, 33], "imagedatabas": 30, "imagedatatoblob": 30, "images": 33, "imageselector": [30, 33], "imageurl": 30, "img": [30, 33], "imgel": 33, "immedi": [5, 6, 8, 17, 18, 19, 20, 22, 23], "impact": [2, 4, 5, 7, 8, 9, 17, 19, 26, 29, 30], "implement": [0, 1, 2, 3, 7, 8, 11, 21, 25, 28, 29], "implic": 10, "import": [3, 4, 5, 6, 10, 13, 16, 20, 22, 24, 25, 33], "importdata": 8, "importpolici": 8, "importresult": 8, "improv": [1, 9, 11, 23, 27, 30, 31], "in_ord": 6, "in_progress": 3, "incid": 18, "includ": [3, 5, 6, 7, 9, 10, 13, 16, 17, 19, 20, 23, 26, 27, 28, 29, 34], "includemetr": [20, 22], "includerecommend": [20, 22], "includeschema": 8, "incom": [6, 7, 10, 12, 14, 26, 31], "incomingcommit": 26, "incomingmessag": 14, "increas": [9, 22, 31], "independ": 19, "index": [2, 19, 26, 28, 30, 33], "indic": [3, 8, 10], "individu": [5, 17, 18, 23, 28], "industri": [18, 24, 27, 28, 29], "info": [3, 8, 10, 13], "inform": [3, 4, 8, 10, 17, 19, 23, 24, 26, 27, 29, 34], "inherit": 8, "initi": [3, 5, 7, 10, 11, 14, 16, 19, 22, 28, 31, 33], "initializedefaultpolici": 8, "initializeprotocol": 10, "initializework": 33, "initialstat": 28, "initiateenhancedsmp": [3, 9], "initiatesmp": [3, 7], "inject": [0, 17, 24, 27, 29, 30, 31, 33, 34], "injectnois": [30, 33], "injectstatisticalnois": [30, 33], "innoc": [2, 29, 30, 34], "inord": 6, "input": [2, 4, 7, 13, 27, 28, 29, 31, 33], "insid": [2, 30], "inspect": 34, "inspir": [0, 29], "instagram": [30, 31, 33, 34], "instal": [1, 2, 5, 24, 28, 35], "instanc": [2, 3, 4, 6, 9, 10, 13, 14, 16, 17, 18, 24, 27, 28, 29], "instanceof": [13, 14, 20, 26, 28], "instancetag": [10, 13, 14, 16], "instead": 4, "instruct": [5, 16, 17, 22, 23, 33, 34], "insuffici": 8, "insufficient_capac": 31, "integ": 13, "integr": [1, 2, 4, 11, 12, 13, 14, 18, 20, 24, 25, 26, 27, 29, 35], "integrityprotect": 22, "integrityviol": 20, "intellig": [26, 27, 30, 34], "intend": [16, 29], "intens": 31, "interact": [3, 4, 17, 34], "intercept": [12, 31], "interceptdiscordupload": 33, "interceptfacebookupload": 33, "interceptinstagramupload": 33, "interceptupload": 33, "interfac": [0, 2, 3, 5, 7, 11, 12, 14, 20, 31, 34, 35], "intern": 23, "interv": [6, 19], "introduc": 3, "intuit": [23, 34], "invalid": [3, 6, 8, 9, 10, 13, 14, 16, 25, 26, 27, 28, 31], "invalid_dh_kei": [25, 26], "invalid_format": 31, "invalid_imag": 31, "invalid_mac": [25, 26], "invalid_messag": [3, 14, 16], "invalid_message_typ": [25, 26], "invalid_signatur": [3, 16, 25, 26, 28], "invalid_st": [13, 14], "invaliddata": 28, "invalidkei": 28, "invalidmac": 26, "invalidmessag": 16, "invers": 19, "inverse_altern": 20, "investig": 22, "invis": 30, "isallow": 10, "isconsist": 28, "isdowngrad": 10, "isequ": 24, "ishighimpactchang": 8, "isimagefil": [30, 33], "iso": 20, "iso_27001": 20, "ispositionsuit": [30, 33], "issu": [2, 4, 5], "istimingconsist": 28, "isvalid": [8, 28], "isvalidmac": 25, "isvalidmessag": 6, "isversionnegoti": 5, "iter": [25, 26, 28], "its": 20, "iv": [4, 7, 13, 14], "j": [1, 24, 26, 28, 33], "javascript": [1, 13, 19, 24, 27, 28, 29], "jest": 28, "job": 28, "join": [8, 33], "json": [8, 13, 19, 20, 22, 25], "keep": [19, 26], "kei": [0, 2, 3, 5, 6, 8, 9, 10, 11, 12, 14, 16, 20, 24, 25, 26, 27, 28, 30, 31, 33], "key_compromise_suspect": 16, "key_rot": 19, "key_rotation_complet": 19, "key_rotation_trigg": 19, "keycach": 16, "keyderiv": 19, "keyfingerprint": [19, 20, 22], "keygener": [19, 20], "keyid": 16, "keylifetim": 19, "keymateri": [13, 19, 20], "keymemori": 25, "keypair": 16, "keyrot": [19, 20, 22], "keyrotationengin": [2, 18, 19, 22, 23], "keysiz": [19, 20], "keystr": 26, "knowledg": [2, 18, 20, 23, 29], "known": [27, 28], "l1": [4, 6], "l2": [4, 6], "l3": [4, 6], "l4": 4, "languag": [9, 23], "larg": [6, 9, 26, 29, 30, 33, 34], "larger": 34, "last": 8, "lastrot": 20, "lastsequ": 6, "latenc": [6, 7], "later": [5, 6, 13], "latest": [5, 27, 28], "layer": [2, 3, 4, 5, 7, 8, 26, 28, 29, 30, 34], "layout": 23, "lazi": [26, 27], "leak": 28, "learn": [27, 34, 35], "least": [8, 29, 30, 34], "length": [3, 8, 9, 19, 22, 25, 26, 28, 30, 31, 33], "length_offset": 30, "less": [5, 8], "let": [10, 16, 19, 22, 25, 26, 28, 30, 33], "level": [7, 8, 9, 17, 18, 20, 23, 27, 31, 33, 34], "libotr": 2, "librari": [24, 27, 29], "lifecycl": [2, 4, 17, 18, 24, 25, 26, 27, 28, 29], "light": 23, "lightn": 23, "like": [2, 12], "limit": [5, 6, 9, 16, 20, 24, 26, 27, 28, 29, 31, 34], "line": [18, 28], "link": [18, 19, 23], "list": [7, 8, 10, 31], "listen": [8, 20, 22], "ll": 35, "ll1": 7, "ll2": 7, "ll3": 7, "ll4": 7, "load": [17, 18, 30], "loadbaselin": 28, "loadimag": [30, 31, 33], "loadimagedata": 30, "local": [10, 12], "localaccount": 10, "localpolici": 10, "lock": [12, 35], "log": [2, 3, 4, 5, 6, 7, 14, 16, 18, 19, 20, 22, 23, 24, 25, 27, 29, 31, 33], "log_1": 8, "log_2": 8, "log_3": 8, "log_4": 8, "log_ev": 5, "log_fil": 8, "log_pol": 8, "logauthenticationfailur": 16, "logcomplianceviol": 22, "logic": [3, 8, 17, 18, 22, 23, 24, 26, 27, 34], "loginfo": 6, "loglevel": [5, 8, 9, 19, 22], "logpolicychang": 8, "logreplayattempt": [6, 16], "logsecurityev": 16, "logsecurityincid": 16, "logsecuritymileston": 16, "logsessionev": 3, "logwarn": 6, "long": [9, 13, 16, 17, 19, 29], "longer": 8, "look": [2, 12, 29, 30, 34, 35], "lookup": 6, "low": [6, 7, 25, 27, 33], "lower": 10, "lowlatencyconfig": 6, "lr": [4, 6, 7, 8, 9, 10], "lrucach": 30, "lsb": [29, 31, 34], "lsb_alpha": [30, 31, 33], "lsb_rgb": 31, "m": [2, 3, 6, 7, 18, 20, 22, 23, 25, 27, 29], "m1": 6, "m2": 6, "m3": 6, "mac": [2, 14, 19, 24, 25, 26, 27, 28, 29], "mac1": 24, "mac2": 24, "machin": [16, 17, 18, 27, 34], "mackei": [13, 14, 19, 20, 28], "magic": [30, 31], "mai": [6, 12, 22], "main": [13, 17, 20, 23, 30, 31, 34], "maintain": [2, 4, 5, 6, 7, 8, 13, 19, 20, 22, 23, 24, 26, 27, 28, 29, 30, 34], "mainten": [2, 24], "major": [4, 18], "make": [1, 6, 17, 19, 23, 34], "malform": [14, 16, 24, 27, 29], "malleabl": 13, "man": [7, 17], "manag": [0, 2, 4, 5, 6, 7, 11, 13, 16, 17, 18, 20, 23, 27, 28, 29, 31, 34], "manifest": [11, 33], "manifestdata": 33, "manipul": [6, 30], "manual": [3, 19, 22, 29], "manual_request": [19, 20, 22], "manual_reset": 9, "manualrecoveri": 9, "map": [5, 16, 18, 19, 26, 30, 33], "mask": [26, 30, 31], "master": 19, "masterkei": [19, 20], "match": [16, 27, 28], "materi": [13, 19, 20, 29], "math": [6, 16, 22, 26, 27, 28, 30, 33], "max": [20, 22, 26, 28, 30, 33], "max_sequ": 6, "maxbuffers": [4, 5, 6, 8, 9], "maxcapac": 31, "maxchunks": 33, "maxconcurrentsess": 8, "maxfiles": [30, 31], "maxgaps": [6, 8], "maximages": 33, "maximum": [2, 3, 6, 16, 19, 20, 22, 25, 26, 27, 29, 30, 31, 34], "maximumvers": 10, "maxlen": 26, "maxlength": 31, "maxlogs": [5, 8, 20], "maxpausedur": 3, "maxpolicys": 5, "maxpools": 26, "maxretri": [16, 25, 26], "maxretryattempt": [3, 5, 9], "maxrotationinterv": 22, "maxrotationtim": [19, 20], "maxsequencenumb": 6, "maxtim": [19, 22], "mb": 36, "mean": 28, "measur": [9, 17, 18, 20, 23, 26, 27, 28, 29, 30, 31, 33, 34], "measuretim": [26, 28], "mechan": [6, 17, 18, 19, 23, 29, 34], "media": [29, 30, 33, 34], "medium": [25, 27], "meet": [22, 24, 28], "mem_encrypt": 3, "mem_usag": 3, "mem_wip": 3, "membership": [24, 27, 29], "memori": [2, 4, 5, 6, 7, 9, 11, 13, 17, 20, 22, 27, 28, 29, 34], "memoryconstrainedconfig": 6, "memorydiff": [24, 28], "memoryforensicsresist": [19, 20, 22], "memoryleak": 26, "memoryoptim": 22, "memorypools": [5, 8], "memoryregion": 19, "memorysecuritytest": 28, "memorytrack": 22, "memoryusag": [3, 9], "merg": 8, "mergechunkresult": 33, "mergeimagechunk": 30, "mermaid": [18, 23], "messag": [0, 2, 3, 4, 5, 7, 11, 12, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 31, 33, 34, 35], "message_count": 20, "message_processing_fail": 16, "message_too_larg": 31, "message_typ": [13, 14, 16], "messagebit": [30, 33], "messagebuff": [5, 9], "messagechunk": [30, 33], "messagecountthreshold": [19, 20, 22], "messagedata": [30, 31, 33], "messagehidden": 33, "messagelength": [30, 33], "messagemetr": 6, "messageord": [4, 5, 7, 9], "messageprocessingtim": 9, "messagequeu": 16, "messagereceiv": 30, "messagereord": 5, "messagerev": 33, "messages": [30, 31], "messagesprocess": 9, "messagesreord": [5, 9], "messagetimeout": 16, "messagetobit": [30, 33], "messagetosend": 16, "messagetyp": [3, 13, 14, 16], "metadata": [3, 8, 10, 12, 19, 20, 29, 30, 34], "metadatastrip": 33, "method": [4, 14, 17, 18, 23, 27, 29, 33, 34], "methodologi": 2, "metric": [3, 6, 7, 8, 9, 17, 20, 22, 23, 24, 25, 26, 27, 28], "metricscollect": 22, "metricscollector": 22, "metricsretent": 9, "micro": [24, 26, 27], "microsoft": [2, 11, 12, 35, 36], "middl": [7, 17], "migrat": [2, 4, 8], "migrateconfigur": 5, "migrationconfig": 5, "militari": [2, 12, 18, 19, 29, 30], "millionair": [2, 3, 4], "millisecond": [19, 20], "min": [6, 30, 33], "mincapac": 30, "mindimens": [30, 31], "minim": [5, 7, 19, 26, 27, 28, 29, 30], "minimum": [22, 31, 36], "minimumpass": 22, "minimumvers": 10, "minrotationinterv": 22, "minut": [2, 3, 5, 8, 9, 20, 22], "miss": [6, 33], "missingchunk": 33, "missingsequ": 6, "mit": 2, "mitig": 7, "mo": [4, 5, 6, 7, 9], "mo_ev": 5, "mo_handl": 5, "mo_l": 7, "mo_m": 7, "mo_t": 7, "mobil": [18, 23], "mobile_devic": 8, "mobile_perf": 8, "mobileconfig": 5, "mock": 28, "mode": 20, "model": [2, 4, 11, 18, 23, 27, 35], "modern": 27, "modif": 34, "modifi": [0, 30, 31, 33], "modp": [13, 17], "modp_group": 13, "modul": [2, 4, 6, 10, 17, 24, 26, 27, 28], "modular": [0, 13, 19, 26], "modularexponenti": 13, "modulu": [26, 28], "monitor": [2, 4, 6, 7, 8, 16, 17, 18, 20, 23, 24, 25, 26, 27, 29], "monitoringdashboard": 22, "monitormemoryusag": 25, "monthlyaudit": 22, "more": [2, 5, 8, 28], "most": [7, 9, 17, 19, 23, 27, 34], "msg": [4, 5, 6, 7, 9], "msg_in": 7, "msg_out": 7, "msg_valid": 3, "multi": [2, 3, 17, 19, 24, 26, 27, 29, 30, 31, 34], "multiimagedistribut": 33, "multiimagesteganographi": 33, "multiimagesupport": [31, 33], "multilingu": 23, "multipl": [3, 7, 8, 13, 18, 19, 20, 22, 23, 25, 26, 27, 28, 29, 30, 33, 34], "multisessionakemanag": 16, "must": [6, 8], "mutual": [2, 10, 17, 18, 29], "my": 12, "n": [6, 10, 20, 24, 27], "name": [5, 8, 20, 26, 28, 33], "nativ": [2, 13, 27], "natur": 31, "naturalheight": 33, "naturalwidth": 33, "navig": [9, 12, 18, 23, 35], "necessari": [3, 9], "need": [5, 9, 10, 12, 17, 18, 22, 23, 27, 30, 31, 34, 35], "negoti": [2, 4, 5, 7], "negotiatevers": [4, 7, 10], "negotiatewithfallback": 10, "negotiationsuccess": 10, "net": 7, "network": [3, 6, 7, 9, 10, 17, 29], "network_error": 3, "network_failur": 3, "never": [2, 13, 19], "new": [1, 3, 4, 5, 6, 8, 9, 10, 13, 14, 16, 19, 20, 22, 24, 25, 26, 27, 28, 30, 31, 33, 34], "new_val": 8, "newcommit": 19, "newer": 17, "newkei": [5, 19, 20, 22], "newkeycommit": 19, "newkeyfingerprint": 19, "newstat": 28, "newvalu": [5, 8], "next": [2, 6, 16, 19, 33], "nextexpectedsequ": 9, "nextfloat": [30, 33], "nextint": [30, 33], "nextkeymateri": [19, 20], "nextrot": 20, "nextseq": 6, "node": [1, 28], "nois": [26, 29, 30, 31, 33, 34], "noiseinject": [30, 31, 33], "noiseintens": [31, 33], "noiseiter": 26, "noiselevel": [30, 31, 33], "non": [13, 29, 30], "none": [8, 31], "normal": [30, 34], "notabl": 11, "note": [3, 6, 7, 8, 9, 10], "notif": [8, 35], "notifi": [3, 5, 8], "notify_us": 5, "now": [3, 5, 6, 8, 9, 14, 16, 19, 22, 25, 27, 28, 30, 33, 35], "npm": [1, 5, 28], "null": [3, 8, 16, 26, 28, 30, 31, 33], "number": [8, 10, 11, 14, 19, 20, 25, 28, 30, 31], "o": 6, "object": [3, 5, 6, 8, 9, 10, 14, 16, 20, 25, 28, 31], "occur": [19, 20, 25], "off": 2, "offici": 2, "offset": [24, 25, 27, 30], "ok": 9, "old": [13, 19, 20], "old_val": 8, "oldcommit": 19, "older": 7, "oldest": 6, "oldestsequ": 6, "oldkei": [5, 19, 20, 22], "oldkeycommit": 19, "oldvalu": [5, 8], "onaccessdeni": 8, "onc": [22, 35, 36], "one": 35, "onerror": 33, "ones": [19, 20], "onli": [2, 5, 8, 10, 26, 29], "onmessag": 33, "onpolicyset": 8, "onprogress": [30, 33], "ons": 36, "oo": 6, "op": [4, 7, 25], "opaqu": 33, "open": [12, 35], "oper": [0, 2, 3, 6, 7, 8, 9, 13, 14, 16, 17, 18, 19, 23, 27, 28, 29, 30, 33, 34], "operationqueu": 16, "operationspersecond": 28, "opportun": [24, 27], "optim": [2, 4, 6, 7, 9, 10, 18, 19, 22, 23, 24, 27, 29, 31], "optimizedakemanag": 16, "optimizedconfig": 22, "optimizedembed": 30, "optimizeembeddingparamet": 33, "option": [3, 4, 5, 7, 8, 9, 16, 17, 20, 23, 24, 25, 26, 30, 31, 33, 34], "orchestr": 19, "order": [2, 4, 5, 7, 33], "orderingstat": 5, "org_proto": 8, "org_sec": 8, "organ": [8, 11, 17, 23, 34], "organizationconfig": 8, "origin": 33, "originalcrypto": 28, "originallength": 33, "originalmessag": 10, "originalperform": 28, "originals": 33, "other": [11, 13, 17, 29], "otr": [2, 4, 5, 6, 7, 9, 10, 13, 14, 16, 17, 19, 24, 27, 28, 29, 33], "otrl_mem_diff": [24, 26, 27, 29], "otrmessag": [6, 30], "otrsess": [10, 13, 16, 30], "otrstat": [13, 16, 17, 18], "otrsteganographysess": [2, 30, 33, 34], "otrstego": 30, "otrv2": 10, "otrv23": [7, 10], "otrv3": 10, "our": [3, 10, 13, 14, 26], "ourhash": 26, "ourinstancetag": 16, "ourpolici": 10, "ourvers": 9, "out": [2, 3, 4, 7, 9, 14, 16], "out_of_ord": 6, "outgo": [6, 7, 12], "outoford": 6, "output": [6, 7, 20, 31], "over": [3, 4, 6, 7, 8, 9, 10], "overal": [17, 23, 24, 27, 29, 31, 34], "overallcompli": 19, "overallscor": [19, 20, 22], "overflow": 6, "overhead": [4, 5, 7, 17, 24, 26, 27], "overrid": 8, "overridden": 8, "overview": [2, 11, 27], "overwrit": [19, 20, 22, 24, 26], "overwriteexist": 8, "overwritememori": 19, "overwritepass": [19, 20, 22], "p": [13, 24, 26, 28], "page": [0, 2, 12, 27, 31], "pair": [13, 14, 16], "parallel": [13, 17, 19, 30, 33, 34], "parallelprocess": 16, "param": 33, "paramet": [2, 3, 4, 6, 8, 10, 14, 17, 18, 20, 24, 25, 26, 27, 29, 30, 31, 33], "pars": [7, 8, 10, 16, 17], "parsechunkdata": 33, "parsemanifest": 33, "parsemessag": [14, 16], "parseotrmessag": 16, "parsesuccess": 10, "parsevers": 10, "parti": [3, 10, 13, 14, 17, 29], "particip": [3, 6, 7, 8, 9, 10, 12, 13, 35], "pass": [2, 3, 5, 8, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30], "passiv": [17, 29, 34], "password": [30, 31], "past": [6, 17, 19, 29], "path": [26, 31], "pattern": [4, 7, 18, 19, 23, 24, 25, 26, 27, 28, 29], "paus": [3, 5, 9], "paused_st": 3, "pauseresult": 3, "pausesess": [3, 9], "pausesmpsess": 3, "payload": 31, "pdf": 20, "pe": 7, "pend": 6, "pendingcount": 6, "pendingmessag": [6, 9], "per": [4, 6, 7, 19, 24, 27, 31, 33], "percentag": 19, "perf": [3, 5, 8, 9], "perf_1": 8, "perf_2": 8, "perf_3": 8, "perf_4": 8, "perf_pol": 8, "perfect": [2, 10, 16, 17, 18, 29, 34], "perfect_forward_secrecy_achiev": 16, "perform": [1, 2, 3, 8, 14, 18, 20, 25, 29], "performance_metr": 20, "performance_warn": 20, "performanceconfig": 5, "performancedegrad": 22, "performancelog": [19, 22], "performancemetr": [9, 19, 20, 22], "performancemonitor": [9, 22, 26], "performanceoptim": 30, "performanceregressiontest": 28, "performancethreshold": 19, "performfullhandshak": 16, "performsecuredelet": [19, 22], "period": [19, 20], "perm": 8, "permiss": [5, 8, 9], "permit": 27, "persist": [2, 4, 7, 8, 9, 17, 24, 27, 29], "persistedst": 3, "persistenceen": 3, "persistst": 3, "phase": [4, 5, 7, 9, 24], "pipelin": [18, 28, 30], "pixel": [30, 31, 33], "pixelindex": [30, 33], "plaintext": [13, 14, 16, 25, 26, 30, 31], "plan": [5, 17, 23, 27, 34], "platform": [2, 9, 11, 12, 17, 19, 29, 35], "platformintegr": [2, 30, 34], "platformoptim": 33, "platformsteganographi": 33, "plausibl": [13, 18], "plugin": 0, "pm": [4, 5, 7, 8], "pm_l": 7, "pm_m": 7, "pm_t": 7, "png": [30, 31, 33], "point": [4, 26], "pol": 9, "polici": [2, 4, 5, 7, 18, 20, 23], "policiesread": 9, "policiesset": 9, "policy_allow": 9, "policy_categori": 8, "policy_chang": 8, "policy_enforc": 20, "policy_ev": 5, "policy_handl": 5, "policyaccesstim": 9, "policycach": 9, "policyenforc": [19, 20, 22], "policymanag": [4, 5, 7, 9, 22], "policynam": 20, "policystat": 5, "pool": [4, 7, 24, 25, 26, 27, 29], "pooledmemori": 24, "poolsiz": 26, "poor": 9, "pop": 26, "popular": [2, 12], "posit": [29, 30, 31, 33, 34], "possibl": [9, 13, 14, 16, 27], "post": [27, 34], "postmessag": 33, "postur": 27, "potenti": [10, 25, 27], "practic": [1, 2, 17, 18, 23, 24, 26, 27, 34], "pre": 16, "precomput": 17, "precomputekei": 16, "prefer": [8, 10, 31, 35, 36], "preferv3": [5, 8, 9, 10], "prefix": 10, "premier": 2, "prepar": 30, "preparemessag": [30, 33], "prerequisit": 1, "preserv": [29, 34], "pressur": 19, "prevent": [2, 4, 7, 10, 13, 17, 19, 24, 27, 29, 30, 33, 34], "previou": [4, 8, 19, 20], "previous_event_hash": 19, "previousev": 19, "previouskeyfingerprint": 19, "previouskeyset": 22, "primari": [10, 29, 30, 34], "primarypolici": 10, "prime": 13, "primit": [7, 17, 18], "principl": [2, 24, 34], "prioriti": 8, "privaci": 2, "privat": [2, 13, 14, 17], "privatekei": [13, 14, 26, 28], "privileg": 8, "prng": [30, 33], "proc": 6, "procedur": [9, 27], "process": [2, 3, 4, 5, 7, 8, 9, 16, 17, 18, 22, 23, 26, 27, 31], "process_readi": 6, "processchunk": [30, 33], "processdhcommit": [13, 16], "processdhkei": [13, 16, 17], "processdiscordimag": 33, "processdownloadedimag": 33, "processedfil": 33, "processedpixel": 33, "processfacebookimag": 33, "processimagechunk": 33, "processimageprogress": 33, "processinstagramimag": 33, "processmessag": [6, 16], "processqueu": 33, "processrevealsignatur": [13, 16, 26], "processsignatur": [13, 16], "processstegoimag": [30, 33], "prod_timeout": 8, "product": [3, 4, 5, 7, 8, 17, 18, 24, 27, 33, 34], "productionconfig": [16, 22], "productioncontext": 8, "profession": [17, 23, 34], "program": [24, 27], "programmat": 25, "progress": [13, 14, 16, 17, 30, 31, 34], "progressiveprocess": [30, 31, 33], "progressivesteganographi": 33, "project": [24, 27], "promis": [3, 9, 14, 16, 19, 20, 22, 30, 31, 33], "promptforsteganographi": [30, 33], "proof": [18, 20, 22, 23, 27, 29], "proof_fail": 20, "proof_gener": [19, 20], "proof_verifi": [19, 20], "proofbatch": [19, 20], "proofcompress": [20, 22], "proofcontext": 20, "proofdata": 19, "proofgener": 19, "proofid": [19, 20], "proofretent": 20, "proofsiz": 19, "prooftyp": 20, "proper": [4, 16, 17, 19, 22, 26, 27, 28, 33, 34], "properli": [27, 28], "properti": [2, 13, 14, 15, 18, 19, 20, 24, 26, 27, 29, 30, 31, 32, 34], "propos": 8, "protect": [2, 3, 4, 7, 8, 11, 13, 17, 18, 19, 24, 26, 27, 29, 34], "proto": 8, "proto_1": 8, "proto_2": 8, "proto_3": 8, "proto_4": 8, "proto_pol": 8, "protocol": [0, 3, 5, 6, 8, 10, 11, 12, 20, 24, 25, 26, 27, 28, 29, 30, 34], "protocol_error": 3, "protocol_vers": [14, 16], "protocol_viol": [25, 26, 28], "protocolcontext": 25, "protocolerrorrecoveri": [24, 25, 28], "protocoltrac": 9, "protocolvers": [13, 14, 16], "prove": [13, 19], "proven": [17, 24, 29], "provid": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34], "psnr": [31, 33], "psnr_min": 31, "pubkei": 26, "public": [8, 13, 14, 24, 25, 26, 27, 29], "publickei": [13, 14, 24, 28], "pull": 1, "pull_request": 28, "pure": 13, "purpos": [20, 22], "push": [5, 16, 22, 26, 28, 30, 33], "pv": 7, "pv2": 7, "q1": 10, "q2": 10, "q3": 10, "q4": 10, "qualiti": [2, 18, 33], "quality_threshold": 31, "qualityscor": 31, "qualitythreshold": 33, "quantum": [27, 34], "queri": [4, 7, 9, 14, 20], "querymessag": 10, "queryselectoral": 33, "question": [2, 3, 5], "queueitem": 33, "quick": [4, 18], "r": [10, 19], "r1": [6, 10], "r2": [6, 10], "r3": [6, 10], "r4": [6, 10], "r5": 6, "ram": 36, "random": [11, 13, 16, 19, 20, 22, 24, 26, 27, 28, 29, 30, 31, 33], "randomizedmessag": 33, "randomizemessagebit": 33, "randomkei": 28, "randomli": 33, "rang": [6, 8, 19, 24, 26, 27, 28], "rap": [4, 7], "ratchet": [0, 11, 29], "rate": [3, 9], "rawmessag": 16, "re": [3, 12, 16, 17, 23, 34], "reach": 9, "react": [0, 1, 11], "read": [2, 4, 7, 8, 12, 23, 25, 29], "readabl": 25, "readfil": 8, "readi": [4, 6, 7, 9, 16, 19, 22, 23, 24, 27, 31], "readymessag": 6, "real": [17, 18, 23, 29, 30, 34], "realist": 28, "reason": [5, 6, 8, 9, 19, 20, 22], "receiv": [3, 6, 10, 14, 16, 33], "receivedmac": 25, "receivedsequ": 6, "receivemessag": 16, "receiverinstancetag": [13, 14, 16], "receivingaeskei": [13, 14], "receivingmackei": [13, 14], "recent": 26, "recentchang": 8, "recenterror": 9, "recipi": [2, 29], "recommend": [3, 8, 9, 19, 20, 31], "recommendedcapac": 31, "reconstruct": 33, "reconstructmessag": 33, "record": [2, 22], "recorderror": 6, "recordmessag": 6, "recordmetr": 26, "recoveri": [2, 3, 4, 6, 7, 17, 18, 19, 23, 27, 28, 29], "recovery_strategi": [25, 26], "recoveryattempt": 9, "recoveryconfig": 9, "recoveryfail": 9, "recoverysuccess": 9, "reduc": [22, 26, 27], "reduct": 27, "redund": [29, 30, 33, 34], "refer": [4, 5, 11, 24, 26, 27, 28, 29], "referenc": [17, 18, 23, 34], "regardless": [24, 26, 27], "regist": 26, "registri": [24, 26, 27], "regress": [27, 28], "regular": [19, 23], "reject": [6, 9, 10, 16, 27, 28, 33], "relai": 16, "relat": [17, 23], "relationship": [17, 18, 23], "releas": 2, "relev": 19, "relevantev": 19, "reliabl": [6, 8, 29, 30], "reload": 7, "remain": [2, 17, 19, 24, 27, 29, 33], "remot": [7, 10], "remoteaccount": 10, "remoteinstancetag": 16, "remotevers": 10, "reorder": [2, 5, 6], "repeat": 27, "replai": [2, 3, 4, 5, 7, 9, 13, 14, 16, 17, 18, 24, 25, 27, 29], "replay_attack": [14, 25, 26], "replay_check": 6, "replay_detect": [6, 16], "replay_protect": 3, "replaydetect": 5, "replaywindows": [4, 5, 6, 8], "replenish": 16, "replenishkeycach": 16, "report": [2, 7, 9, 18, 20, 23, 26, 29], "reportid": [19, 20], "repositori": [1, 2], "repres": [4, 18, 23], "represent": 25, "reproduc": 9, "repudi": 13, "request": [1, 2, 6, 19, 20], "requesteds": 26, "requestmissingmessag": 6, "requir": [2, 3, 4, 7, 10, 12, 17, 18, 19, 20, 22, 23, 24, 27, 28, 30, 31, 34], "requiredposit": 30, "requireencrypt": [5, 8, 9, 10, 16], "research": 27, "reset": [3, 6, 7, 9, 13, 14, 16, 25, 26, 27], "reset_st": [25, 26], "resetenhancedsmp": 9, "resetmessageord": 9, "resetpolicymanag": 9, "resetst": 9, "resetstat": 25, "resettodefault": 9, "resetversionnegoti": 9, "residualdata": 20, "resist": [2, 3, 17, 24, 25, 26, 27, 28, 29, 34], "resolut": [8, 9, 24, 26, 27, 29], "resolv": [3, 9, 16, 22, 30, 33], "resourc": [3, 9, 22, 23, 34], "resource_error": 3, "resp_tim": 3, "respect": 10, "respond": 3, "respondtoenhancedsmp": 3, "respondtosmp": 3, "respons": [3, 6, 10, 13, 14, 18, 23, 30], "responsetim": 3, "restart": [9, 16, 25, 26], "restart_protocol": [25, 26, 28], "restart_with_incom": 26, "restor": 3, "restrict": [5, 7, 8, 10], "result": [3, 5, 6, 7, 8, 10, 13, 14, 16, 19, 20, 24, 25, 26, 27, 28, 30, 31, 33], "result1": 6, "result2": 6, "result3": 6, "resum": [3, 9], "resumefromst": 3, "resumeresult": 3, "resumesess": [3, 9], "resumesmpsess": 3, "resumpt": 3, "retent": 20, "retentionperiod": [19, 20, 22], "retentionpolici": [19, 20], "retransmiss": 6, "retransmit": 26, "retri": [3, 5, 9, 10, 16, 22, 24, 25, 27], "retriev": [3, 4, 8, 20], "retry_after_failur": 22, "retry_r": 3, "retryattempt": [3, 16], "retrycount": [9, 16], "retrydelai": [25, 26], "retrydelaym": 9, "retryinterv": 16, "retryr": 3, "return": [3, 5, 6, 8, 9, 10, 13, 14, 16, 18, 19, 20, 25, 26, 28, 30, 31, 33], "reus": [13, 25, 26], "reveal": [13, 14, 16, 17, 18, 19, 20, 23, 29, 30, 31, 34], "reveal_message_fail": 33, "reveal_signatur": [13, 14, 16], "revealedkei": [13, 14], "revealmessag": [30, 33], "revealsignaturemessag": 13, "revealsigrespons": [13, 14, 16], "review": [4, 9, 18, 19, 23, 27], "rfc": [24, 26, 27, 28, 29], "rfc3526testvector": 28, "rgb": 31, "rigor": [2, 24, 27, 29], "robust": [2, 3, 4, 6, 17, 18, 23, 24, 25, 27, 29], "robustakemanag": 16, "role": [2, 5, 8, 9], "rollov": 6, "rollover_threshold": 6, "room": 6, "root": 29, "rotat": [2, 18, 23, 29], "rotatekei": 22, "rotatekeysmanu": [19, 22], "rotation_complet": 20, "rotation_fail": 20, "rotation_start": 20, "rotation_tim": 22, "rotation_trigg": 20, "rotationcomplet": [19, 22], "rotationcontext": 20, "rotationcount": 20, "rotationdata": [19, 20], "rotationev": 22, "rotationfail": 22, "rotationfrequ": 22, "rotationinterv": [19, 20, 22], "rotationpolici": 22, "rotationproof": 22, "rotationrequest": 20, "rotationresult": 22, "rotationtim": [19, 20, 22], "rotationtimeout": [19, 20, 22], "rotationtrigg": [19, 22], "rp": 6, "rst": 18, "rule": [8, 9], "run": [1, 27, 28], "runbenchmark": 28, "runtim": 8, "s1": 10, "s2": 10, "s3": 10, "s4": 10, "safari": 27, "salt": 13, "same": [6, 28], "sampl": 26, "sampleinterv": 9, "sanitizememori": 19, "sanitizespidermonkeymemori": 19, "sanitizev8memori": 19, "sanitizewebkitmemori": 19, "save": [3, 5, 8], "savesessionst": 3, "sc": 7, "scalabl": [17, 18, 23, 34], "scaledconfig": 22, "scenario": [2, 3, 10, 27, 28], "scontent": [30, 33], "score": [22, 31], "script": [0, 27], "se": 7, "seamless": [2, 4, 27, 29, 30, 33, 34], "seamlessli": [2, 5, 26], "search": [2, 12, 18, 23, 36], "sec": [3, 6, 7, 8], "sec_1": 8, "sec_2": 8, "sec_3": 8, "sec_4": 8, "sec_5": 8, "sec_audit": 3, "sec_mem": 3, "sec_pol": 8, "sec_stat": 3, "sec_viol": 3, "second": [8, 9, 16, 22], "secondari": 10, "secreci": [0, 2, 10, 11, 12, 16, 17, 34], "secret": [3, 9, 13, 14, 17, 31, 33], "secreterror": 9, "secretok": 9, "section": [4, 11, 23], "secur": [0, 1, 5, 8, 9, 11, 14, 20, 31, 35], "secure_deletion_complet": 19, "secure_deletion_start": 19, "secure_sess": 8, "securechat": 3, "secureclear": 26, "securedelet": [19, 20, 22], "securedeletionmanag": [2, 18, 19, 22, 23], "securememori": [3, 19, 20, 22, 24, 25, 26, 28], "securememorypool": [24, 25, 26], "securemessag": 33, "secureprng": [30, 33], "securerandom": [19, 22], "securestorag": 3, "securestorages": 3, "securewip": [25, 26, 28], "security_alert": 20, "security_audit": 8, "security_error": 3, "security_ev": 20, "security_offic": [5, 8], "security_ok": 9, "security_review": 9, "security_viol": [3, 31], "securityalert": 8, "securityalertsystem": 22, "securityauditlogg": 3, "securityconfig": 26, "securityev": [16, 19, 25], "securityeventhandl": 25, "securityimpact": 8, "securitylevel": [3, 10], "securitylogg": [6, 8, 16], "securitymanag": 33, "securitymetr": 26, "securityperformancebenchmark": 28, "securityvalidationerror": [6, 25, 26, 28], "securityviol": 3, "see": [18, 24, 25, 26, 27, 28, 29, 35], "seed": [30, 33], "seen": 6, "select": [4, 7, 10, 23, 24, 29, 30, 31, 33, 34], "selectcov": 30, "selectedvers": 9, "selectoptimalcov": [30, 33], "self": [24, 27], "send": [3, 6, 7, 8, 10, 14, 16, 22, 31], "sendalert": 8, "sender": [14, 29], "senderinstancetag": [13, 14, 16], "sendingaeskei": [13, 14], "sendingmackei": [13, 14], "sendinterv": 16, "sendmessag": [3, 6, 7, 10, 14, 16], "sendretransmissionrequest": 6, "sendstegomessag": [30, 33], "sensit": [2, 3, 11, 13, 14, 19, 20, 22, 23, 24, 26, 27, 28, 29, 34], "sensitivecontext": 26, "sensitivedata": 24, "sensitivefield": 26, "sensitivekeymateri": 22, "sent": [3, 13], "seq": [6, 9], "seq_check": 6, "sequenc": [5, 7, 10, 17, 18, 24, 25], "sequence_error": [25, 26], "sequencediagram": [3, 6, 7, 8, 9, 10], "sequencenumb": [19, 20], "serial": [8, 16, 25], "serializemanifest": 33, "serializemessag": 16, "server": [1, 12, 29], "servic": [0, 29, 30, 31, 34], "sess_audit": 3, "sess_overrid": 8, "sess_secur": 8, "sess_temp": 8, "sess_tim": 3, "sess_timeout": 3, "sess_valid": 3, "session": [2, 4, 5, 6, 7, 8, 9, 10, 13, 14, 17, 18, 25, 28, 29, 30, 31, 33, 34, 35], "session1": 13, "session2": 13, "session_count": 3, "sessiondur": [3, 9], "sessionid": [3, 5, 9, 16], "sessionkei": [16, 25, 26, 28, 30, 31, 33], "sessionmanag": 3, "sessionmemori": 28, "sessionresumpt": 3, "sessionstart": 3, "sessiontimeout": [5, 8], "sessiontimeoutm": [3, 4, 5, 8, 9], "set": [0, 3, 5, 7, 8, 9, 14, 16, 17, 19, 20, 22, 23, 26, 27, 30, 31, 33], "setcontextualpolici": 8, "setinstancetag": 16, "setpolici": [5, 8, 9], "setsecurityeventhandl": 24, "setsessionkei": 13, "settimeout": [9, 16, 22, 30, 33], "setuint32": 30, "setuint8": 30, "setup": [1, 2, 9, 17, 18, 23, 28, 34], "setupauditlog": 8, "setupchangenotif": 8, "setupenterprisefeatur": 8, "setupenvironmentpolici": 8, "setupeventhandl": 5, "setupplatformhandl": [30, 33], "sever": [0, 8, 22, 25], "sha": 13, "sha256": [11, 13, 17, 19, 29], "share": [3, 10, 11, 13, 14, 17, 29, 30, 34], "sharedsecret": [3, 13, 14, 26, 28], "shift": [26, 33], "shorter": 5, "should": [6, 8, 9, 10, 13, 16, 22, 26, 28], "shouldfail": 28, "shouldhidemessag": [30, 33], "shouldprocessimag": 33, "show": [7, 17, 18, 23, 34], "showerror": 5, "showsecuritynotif": 5, "showsmpdialog": 5, "showsmpresult": 5, "showstatu": 5, "showwarn": 5, "shuffl": 33, "shutdown": [19, 22], "side": [2, 19, 24, 27, 29], "siem": 8, "sign": [13, 14, 19], "signal": [0, 11, 12, 29], "signatur": [0, 11, 14, 16, 17, 18, 19, 20, 24, 25, 26, 27, 29, 30, 31, 34], "signature_data": 19, "signature_verification_fail": 14, "signaturedata": 13, "signev": 19, "signific": [27, 29, 30, 34], "significantli": [24, 27, 29], "sigrespons": [13, 14, 16], "silent": 9, "simpl": [8, 17, 23, 34], "simplic": 8, "simul": 17, "size": [3, 5, 6, 7, 9, 16, 17, 20, 25, 26, 31, 33, 34], "skip": [6, 30], "slack": [0, 2, 11, 12, 35], "slice": [8, 13], "slide": 6, "slow": [9, 19, 22], "slowrot": 22, "sm": [3, 4, 7], "small": [2, 24, 26, 27, 29, 30, 31], "smaller": [5, 8, 30], "smallest": 26, "smp": [2, 4, 5, 7, 10, 24, 26, 27, 29], "smp1": [3, 7], "smp2": 3, "smp3": 3, "smp4": 3, "smp_abc123_def456": 3, "smp_abort": 3, "smp_abort_reason": 3, "smp_event": 5, "smp_handler": 5, "smp_in": 7, "smp_l": 7, "smp_m": 7, "smp_out": 7, "smp_paus": 3, "smp_resum": 3, "smp_t": 7, "smpsession": 9, "smpsessionabort": 5, "smpsessioncomplet": 5, "smpsessiondur": 9, "smpsessionmanag": 3, "smpsessionpaus": 5, "smpsessionstart": 5, "smpstate": 9, "smpstatechang": 5, "snapshot": 3, "so": 7, "social": [29, 30, 33, 34], "socialist": [2, 3, 4], "solut": [2, 5, 22, 27], "sound": [17, 34], "sourc": [5, 8, 12, 19, 20, 30], "sox": [18, 19, 20, 22, 23, 29], "sp": 3, "space": 36, "special": 28, "specif": [5, 9, 10, 11, 13, 14, 17, 18, 19, 20, 23, 24, 25, 27, 28, 29, 31, 34, 35], "specifi": 19, "spectrum": [31, 34], "sphinx": [11, 18], "spike": 9, "split": [29, 30, 33, 34], "splitimagedata": 30, "splitimageintochunk": 33, "splitmessag": [30, 33], "spoof": 17, "spread": [31, 34], "spread_spectrum": 31, "sqrt": 28, "src": [24, 26, 28, 30, 33], "ssid": [13, 14, 16], "ssim": 31, "ssim_min": 31, "ssp": [4, 7], "stack": 4, "stage": [3, 20], "standalon": 22, "standard": [2, 3, 18, 22, 28, 29], "standarddevi": 28, "standardscompli": [19, 20, 22], "start": [3, 6, 7, 9, 10, 12, 13, 14, 17, 18, 20, 25, 28, 34, 36], "startak": [13, 16, 17, 18], "startdat": [19, 20, 22], "startpixel": 33, "startsess": [5, 10], "startswith": [5, 16], "starttim": [6, 8, 16, 19, 22], "startup": 19, "startversionnegoti": 9, "stat": [5, 6, 8, 9, 25], "state": [2, 4, 5, 7, 9, 16, 17, 18, 19, 24, 25, 26, 27, 28, 29, 34], "state_encrypt": 3, "state_error": [3, 9, 16], "state_integr": 3, "state_recoveri": 9, "state_timeout": 3, "statediagram": [3, 6, 9], "stateerror": 9, "statement": 28, "stateok": 9, "statepersist": 3, "static": [9, 26, 28], "statist": [8, 9, 24, 25, 27, 28, 29, 31, 34], "statisticalsecur": [2, 30, 33, 34], "statisticalsecuritymanag": 33, "statu": [3, 5, 9, 20], "steganalysi": [29, 30, 34], "steganograph": 2, "steganographi": [2, 29], "steganographyengin": [2, 30, 33, 34], "steganographymanag": 33, "steganographyworkerpool": 33, "stego": [30, 31, 33], "stego_head": 30, "stego_method": 31, "stegoengin": 33, "stegoerror": 30, "stegofil": 33, "stegoimag": [30, 31, 33], "stegoimagedata": 30, "stegoimagereadi": 30, "stegomanag": 33, "stegos": 33, "step": [2, 9, 10, 13, 16, 17, 18, 23, 28, 34], "still": [12, 28], "storag": [0, 3, 8, 11, 17], "storage_s": 3, "store": [2, 3, 6, 7, 8, 12, 13, 28, 36], "strategi": [2, 4, 9, 17, 18, 23, 24, 25, 27, 28], "strict": [4, 5, 7, 8, 19], "strictconfig": 10, "strictpolici": 8, "string": [3, 8, 10, 14, 20, 24, 25, 31], "stringifi": [8, 13], "structur": [6, 10, 11, 14, 16, 17, 18, 20, 23, 24, 26, 27, 28, 30, 31, 34], "studi": [17, 23, 34], "style": [18, 31], "sub": [18, 23, 29], "subgraph": [3, 4, 5, 6, 7, 8, 9, 10], "subgroup": [2, 24, 27, 29], "submit": 1, "substitut": 29, "subtl": 30, "subtract": [26, 28], "succe": 9, "success": [3, 8, 9, 10, 14, 17, 18, 19, 20, 22, 34], "success_r": 3, "successcount": 19, "successfulli": [8, 14, 16, 19, 20, 27], "successfulverif": [19, 20], "successr": [3, 19], "suggest": 31, "suit": [2, 4, 13, 26], "suitabilityscor": [30, 31, 33], "suitabl": [19, 30, 31, 33], "summari": 2, "super": [3, 6, 8, 16, 26, 30], "support": [0, 4, 8, 11, 12, 17, 18, 19, 23, 24, 26, 27, 29, 34, 35], "supported_format": 31, "supportedformat": 33, "supportedvers": 10, "suspect": 20, "sv": [6, 7], "switch": [3, 6, 14, 16, 26, 33], "sy": 8, "symmetr": [10, 17, 29], "symptom": 9, "sync": [2, 23], "syntact": 23, "syntax": [18, 23], "synthet": [30, 31, 34], "sys_perf": 8, "sys_sec": 8, "system": [2, 4, 6, 8, 10, 11, 14, 16, 20, 22, 25, 26, 28, 29, 31, 33], "system_initi": 20, "t": [2, 12], "t1": [6, 7], "t2": [6, 7], "t3": [6, 7], "t4": 7, "tag": [10, 13, 14, 16, 17, 18, 24, 27, 29], "take": [6, 25], "taken": 6, "talk": 29, "tamper": [7, 18, 19, 20], "task": [23, 33], "taskid": 33, "taskqueu": 33, "tb": [3, 4, 5, 6, 7, 8, 9, 10], "td": [6, 7, 9], "team": [0, 2, 11, 12, 22, 35], "technic": [17, 18, 27, 29, 34], "techniqu": [17, 18, 19, 27, 28, 30, 34], "templat": 18, "temporari": [8, 17], "term": [13, 16, 17, 19, 29], "test": [1, 2, 4, 5, 23, 24, 29, 34], "testabl": 26, "testbackwardcompat": 5, "testboundarycondit": 28, "testcas": 28, "testdata": 28, "testenhancedsmp": 5, "testmemoryleak": 28, "testmessageord": 5, "testpattern": 28, "testpolicymanag": 5, "testvalidationthrow": 28, "testversionnegoti": 5, "text": 23, "textencod": [3, 13], "textur": 31, "thei": 12, "theirhash": 26, "theirinstancetag": 14, "theirpublickei": [13, 14], "theirvers": [9, 10], "them": [12, 19], "theme": [18, 23], "thi": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 25, 26, 27, 28, 30, 31, 32, 33, 34, 35, 36], "third": [13, 17, 29], "those": 9, "thread": [11, 30], "threat": [2, 7, 11, 18, 19, 20, 23], "threat_detect": 20, "threshold": [22, 28, 29], "through": [4, 7, 8, 13, 18, 22, 23, 24, 26, 27, 28, 29, 30, 34, 35], "throughout": 28, "throughput": [6, 7, 17, 19, 22, 23], "throw": [3, 6, 8, 10, 13, 14, 16, 19, 25, 26, 28, 30, 33], "throwsvalidationerror": 28, "tiff": 31, "time": [2, 3, 4, 6, 7, 8, 9, 14, 16, 18, 20, 23, 27, 28, 29, 30, 33, 34], "time_bas": [19, 20, 22], "timeequ": 28, "timeinvalid": 26, "timelin": 18, "timeout": [3, 5, 6, 8, 9, 14, 16, 19, 22], "timeout_r": 3, "timeouterror": 9, "timeoutok": 9, "timer": 22, "timestamp": [3, 5, 6, 8, 9, 10, 14, 19, 20, 30, 31, 33], "timeunequ": 28, "timevalid": 26, "timing_resist": 3, "timinganalysi": 22, "timinganalyz": 28, "timingattackattempt": 26, "timingratio": 26, "tobe": [6, 8, 10, 13, 16, 22, 28], "tobedefin": 28, "tobegreaterthan": [8, 22], "tobelessthan": [16, 22, 26, 28], "tobenul": [26, 28], "toblob": 33, "toequal": 16, "tofix": 25, "togeth": [19, 28], "toggl": 35, "tohavelength": 6, "tojson": 25, "toler": 28, "too": [6, 26, 30, 31], "tool": [4, 34], "toolbar": 12, "toolkit": 9, "topic": [17, 34], "tostr": [26, 28, 33], "total": [6, 7, 31], "totalchunk": 33, "totalev": [19, 20], "totalinst": [25, 28], "totalpixel": [30, 31, 33], "totalpolici": 9, "totalproof": [19, 20], "totals": [25, 28], "totaltim": [19, 28], "tothrow": [10, 16, 28], "trace": 7, "tracedata": 9, "traceenhancedsmp": 9, "tracemessageord": 9, "tracepolicychang": 9, "tracer": 9, "traceversionnegoti": 9, "track": [8, 9, 13, 25, 26, 27, 28], "trackaccess": 25, "tracker": 2, "traffic": 17, "trail": [2, 18, 20, 23, 29], "transform": 27, "transit": [3, 13, 14, 16, 17, 18, 27], "translat": 23, "transmiss": [29, 30, 31, 34], "transmitmessag": 6, "transpar": [6, 10, 33], "transport": [5, 7], "tree": 18, "trigger": [18, 22, 23, 29], "troubleshoot": [2, 4, 11], "true": [3, 4, 5, 6, 8, 9, 10, 14, 16, 19, 20, 22, 25, 26, 28, 30, 31, 33], "truli": [2, 29, 30, 34], "trust": 18, "try": [3, 5, 6, 10, 13, 14, 16, 19, 20, 25, 26, 28, 30, 33], "tune": [8, 18, 22, 27], "tutori": [1, 17, 23, 34], "twitter": 34, "two": [10, 13, 18, 29], "type": [2, 3, 6, 7, 8, 10, 12, 16, 18, 22, 24, 25, 26, 28, 33, 35], "typeof": 26, "typic": [6, 7, 17], "ubuntu": 28, "ui": [0, 5, 7, 30, 33, 34], "ui_upd": 5, "uint8arrai": [13, 14, 20, 24, 25, 26, 28, 30, 31], "unauthor": 19, "unavail": [27, 28], "undefin": [5, 8, 26, 33], "under": [2, 17, 27, 34], "understand": [17, 18, 23, 34, 35], "unequ": 28, "unexpect": 20, "unexpectedli": 9, "uniform": [24, 26, 27], "unit": [1, 2, 13, 17, 18, 23, 26, 27], "unknown": [8, 14, 16, 19, 20, 33], "unload": 27, "unreleas": 2, "unreli": 6, "unsupport": [10, 16, 31], "up": [8, 9, 16, 17, 20, 22, 23, 25, 31, 34], "updat": [1, 2, 5, 6, 8, 9, 13, 14, 19, 27, 33], "update_config": 5, "update_polici": 9, "update_st": 6, "updatecap": 5, "updateinterv": 22, "updatemessagedisplai": 5, "updatepolici": 8, "updatesecuritypolici": 8, "updatesequencetrack": 6, "updatesessiontimeout": 5, "updatesmpui": 5, "updateuiforvers": 5, "upgrad": 4, "upload": [28, 31, 34], "uploadhandl": 30, "uploadintercept": 31, "uploadinterceptor": 33, "upon": [3, 4, 7], "us": [0, 1, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 17, 18, 19, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 34], "usabl": 27, "usablepixel": 31, "usag": [2, 5, 7, 9, 19, 24, 27, 28], "useincom": 26, "user": [0, 1, 3, 5, 7, 8, 9, 10, 11, 17, 23, 24, 27, 30, 34], "user_abort": 3, "user_admin": 8, "user_cancel": 3, "user_ctx": 8, "user_guest": 8, "user_pref": 8, "user_request": 3, "useract": 3, "userag": 9, "userid": 5, "userrol": [5, 8, 9], "usertyp": 8, "usewebwork": [31, 33], "utf8": 8, "util": [17, 19, 28, 34], "v": [5, 6, 26], "v1": [10, 28], "v2": [2, 3, 4, 6, 9, 10, 14, 28], "v2_1": 10, "v2_2": 10, "v2_3": 10, "v2_4": 10, "v3": [2, 4, 5, 7, 9, 10, 13, 14, 16, 24, 27], "v3_1": 10, "v3_2": 10, "v3_3": 10, "v3_4": 10, "v3_5": 10, "valid": [2, 3, 4, 5, 7, 9, 17, 18, 19, 20, 23, 29, 33], "valid_queri": 9, "validatebeforeimport": 8, "validatedeletionrequest": 19, "validatedhpublickei": [24, 25, 26, 28], "validategroupel": 26, "validateimag": 33, "validateincomingmessag": 6, "validatemessag": [16, 33], "validatemigr": 5, "validatepolici": 8, "validatepolicychang": [8, 9], "validateprotocolmessag": 24, "validatereplayprotect": 6, "validatesecurewip": 28, "validatesignatur": 16, "validatesmpgroupel": [24, 26], "validatest": [13, 16], "validatetimeout": 3, "validateversioncompat": 10, "validation_error": 26, "validation_level": [5, 8], "validation_ok": 9, "validationcaches": 9, "validationen": 3, "validationfailur": [9, 26], "validationlevel": [4, 5, 8, 9], "validationsecuritytest": 28, "validationspersecond": 28, "validdata": 28, "validkei": 28, "validmac": 26, "validtransit": 16, "valu": [8, 9, 16, 18, 22, 24, 26, 27, 30, 33], "valuea": 24, "valueb": 24, "varianc": [27, 28], "variou": [6, 10, 16, 17, 18, 19, 22, 24, 26, 31], "vda": [4, 7], "ve": 7, "vector": [14, 17, 18, 26, 27, 28, 34], "ver": 9, "verbos": [8, 9, 19, 22], "verif": [2, 3, 7, 13, 14, 17, 18, 20, 22, 23, 24, 25, 26, 27, 28, 29, 33], "verifi": [2, 3, 9, 13, 16, 17, 18, 20, 23, 26, 27, 28, 29, 30, 33, 34], "verificationen": [19, 20, 22], "verificationpromis": 19, "verificationr": 19, "verificationrequir": 22, "verificationresult": [19, 22], "verificationstag": 20, "verificationtim": [19, 20], "verificationtimeout": [19, 20, 22], "verifiedev": 20, "verifybatchproof": 19, "verifycontact": 3, "verifydelet": 19, "verifyembeddingqu": 33, "verifyhmacsha256": 26, "verifymessag": 33, "verifyoverwrit": 19, "verifyproof": [19, 22], "verifysignatur": 13, "version": [1, 2, 3, 4, 5, 7, 16, 17, 19, 20, 23, 24, 27, 28, 30, 31, 33], "versionnegoti": [4, 5, 7, 9], "versionnegotiationfail": 5, "versionnegotiationstart": 5, "versionpolici": 10, "via": [18, 31], "video": [17, 23, 34], "view": [25, 26, 28, 30], "violat": [2, 3, 10, 19, 20, 22, 24, 25, 26, 27, 28, 29, 31], "violationtyp": 3, "visibl": 12, "visit": [12, 17, 23, 34, 36], "visual": [29, 30, 34], "vn": [4, 5, 7], "vn_a": 10, "vn_b": 10, "vn_event": 5, "vn_handler": 5, "vn_in": 7, "vn_l": 7, "vn_m": 7, "vn_out": 7, "vn_t": 7, "void": 31, "volum": [18, 19, 20, 23, 29], "vulner": [2, 24, 27, 28, 29], "w": [7, 33], "wa": [19, 20, 22, 26, 31], "wait": [5, 6, 14, 16, 22, 35], "walk": 35, "walkthrough": [17, 23], "want": 30, "warn": [5, 8, 9, 16, 22, 33], "wasm": 27, "we": [1, 16], "weak": [24, 26, 27], "web": [0, 2, 11, 12, 13, 24, 26, 27, 28, 29, 30, 31, 34, 36], "webassembli": 27, "webcrypto": 19, "webotr": [0, 4, 7, 8, 10, 11, 12, 13, 14, 15, 16, 17, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36], "webotrconfigur": 8, "webotreventhandl": 5, "webott": [2, 3, 4, 5, 6, 8, 10], "websit": 31, "websocket": 7, "weeklycompli": 22, "welcom": [1, 2, 35], "were": 24, "what": [3, 12], "when": [4, 7, 9, 10, 12, 13, 19, 20, 27, 29], "where": [6, 10, 20, 24, 27, 29], "whether": [17, 23, 31, 34], "which": 12, "while": [3, 4, 5, 7, 8, 12, 19, 24, 27, 30, 33], "whiteliststartak": 10, "whitespac": 10, "who": [10, 12, 29], "width": [30, 31, 33], "win": 26, "window": 6, "windows": 6, "wipe": [2, 3, 24, 25, 26, 27, 28, 29], "wipepattern": [25, 26, 28], "within": [9, 16, 26, 29, 30, 31, 34], "without": [18, 19, 20, 23, 24, 26, 27, 29, 33], "work": [2, 12, 19, 23, 26, 27, 28, 34, 35], "worker": [0, 30, 31, 34], "workercount": 33, "workerembed": 30, "workerpool": 30, "workflow": [2, 11, 23, 28, 34], "workload": 4, "workspac": 11, "world": [13, 16, 17, 31, 33, 34], "wotr": 30, "wrapper": 5, "write": [1, 3, 8, 24, 25, 28], "writefil": 8, "wrong": 8, "x": [13, 33], "x25519": [0, 11, 29], "x3dh": 11, "xor": 26, "xx": 2, "y": [13, 33], "yarn": 5, "yate": 33, "ye": [6, 9, 10, 12], "yet": 9, "yield": [30, 33], "you": [1, 2, 12, 17, 23, 29, 34, 35, 36], "your": [1, 2, 5, 12, 16, 22, 29, 33, 36], "zero": [2, 13, 18, 20, 23, 26, 27, 28, 29], "zeroknowledg": 20, "zeroknowledgeproof": [19, 20, 22], "zeroknowledgeverifi": [2, 18, 19, 22, 23], "zk": 20, "zkverifi": 22}, "titles": ["Architecture Overview", "Contributing to WebOTR", "WebOTR Documentation", "Enhanced SMP", "Protocol Compliance &amp; Advanced Features", "Integration Guide", "Message Ordering", "Protocol Overview", "Policy Manager", "Troubleshooting Guide", "Version Negotiation", "Changelog", "Frequently Asked Questions", "Authenticated Key Exchange (AKE)", "AKE API Reference", "AKE Architecture", "AKE Implementation Guide", "AKE Documentation Summary", "WebOTR Security Documentation Summary", "Forward Secrecy Implementation", "Forward Secrecy API Reference", "Forward Secrecy Architecture", "Forward Secrecy Implementation Guide", "Forward Secrecy Documentation Summary", "libOTR Security Enhancements", "libOTR Enhancements API Reference", "libOTR Enhancements Implementation", "libOTR Security Enhancements Summary", "libOTR Enhancements Testing and Validation", "Security Overview", "Steganography Implementation", "Steganography API Reference", "Steganography Architecture", "Steganography Implementation Guide", "Steganography Documentation Summary", "Getting Started", "Installation Guide"], "titleterms": {"0": 11, "01": 11, "1": 11, "2024": 11, "22": 19, "5220": 19, "abort": 3, "access": 8, "accuraci": 23, "advanc": [2, 3, 4, 5, 6, 10, 16, 20, 22, 29, 33], "ak": [13, 14, 15, 16, 17, 18, 29], "akeerror": 14, "akemessag": 14, "alert": 22, "algorithm": [30, 33], "analysi": [17, 18, 27, 34], "analyzecapac": 31, "analyzecov": 31, "api": [3, 6, 8, 10, 14, 17, 18, 19, 20, 23, 25, 31, 34], "applic": 5, "architectur": [0, 4, 7, 8, 13, 15, 17, 18, 19, 21, 23, 26, 28, 30, 32, 34], "ask": 12, "assess": 27, "assur": [17, 23], "attack": 6, "audienc": 18, "audit": [8, 19, 21, 23], "auditev": 20, "audittrailsystem": 20, "authent": [13, 15, 29], "automat": 9, "basic": [3, 5, 6, 8, 10, 16, 20, 22, 31, 33], "batch": 19, "benchmark": 24, "best": 19, "browser": [21, 26, 27, 30, 32, 33, 36], "buffer": 6, "cansendencrypt": 14, "capabl": 10, "categori": 8, "chain": 19, "changelog": 11, "characterist": [4, 6, 7], "chrome": 36, "chromium": 36, "class": [3, 6, 8, 10, 14, 20, 31], "code": [1, 3, 17, 23, 34], "common": [9, 19, 22], "commun": [2, 29], "compat": [26, 27], "complet": [14, 17, 18, 23, 34], "complianc": [2, 4, 19, 20, 21, 22, 24, 27, 28], "compliancereport": 20, "compon": [7, 13, 15, 16, 19, 21, 22, 24, 30, 32, 33], "comprehens": 18, "conclus": [17, 18, 19, 23, 27, 34], "configur": [5, 6, 8, 10, 16, 19, 20, 22, 33], "consider": [3, 6, 7, 10, 16, 19, 22, 26], "constant": [14, 20, 24, 25, 26, 31], "constructor": [20, 31], "content": [13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 30, 31, 32, 33, 34], "contextu": 8, "contribut": 1, "control": 8, "convers": 35, "core": [6, 13, 14, 16, 19, 22, 24, 30], "cover": 30, "coverag": [17, 18, 23, 34], "coveranalysi": 31, "coverimagemanag": 31, "createdhcommit": 14, "createdhkei": 14, "createrevealsignatur": 14, "createsignatur": 14, "creation": 13, "cryptograph": [0, 13, 14, 15, 17, 29], "custom": 10, "data": [7, 21], "debug": [9, 19, 22], "default": [6, 10], "defens": [15, 21, 32], "definit": [14, 20, 31], "delet": [19, 20, 21, 22], "deletioncomplet": 20, "deletionresult": 20, "deniabl": 13, "deploy": [5, 7, 16, 21, 22, 23, 26, 27, 34], "depth": [21, 32], "deriv": [13, 15], "derivekei": 14, "design": [18, 23, 26, 34], "detail": [3, 6, 8, 10, 13, 23, 30], "detectmessag": 31, "detectplatform": 31, "develop": [1, 2, 5, 17, 18, 23, 34], "dhexchang": 14, "dhkeypair": 14, "diagnost": [3, 9], "diagram": 23, "diffi": 13, "digit": 13, "distribut": 33, "document": [2, 17, 18, 23, 34], "dod": 19, "downgrad": 10, "driven": 17, "dsakeypair": 14, "element": [18, 23], "embed": [30, 32], "embeddingparamet": 31, "emergencyrot": 20, "encrypt": 35, "enforc": 10, "enforcepolici": 20, "engin": [13, 16, 19, 22, 30, 33], "enhanc": [3, 4, 9, 17, 23, 24, 25, 26, 27, 28, 29, 34], "enhancedsmp": 3, "enhancesecuritybeforeembed": 31, "enterpris": [5, 8, 18, 19, 21, 22, 23], "enterprisepolicymanag": 20, "enum": 25, "environ": 5, "error": [6, 7, 9, 10, 13, 14, 15, 16, 20, 22, 24, 25, 26, 31, 32], "event": [5, 16, 17, 19, 20, 22], "exampl": [3, 4, 6, 8, 10, 14, 17, 20, 23, 25, 31, 34], "excel": 23, "exchang": [13, 15, 29], "execut": [27, 28], "exist": 5, "export": 8, "extens": [21, 30, 32, 33, 36], "extract": 32, "featur": [2, 4, 6, 12, 13, 16, 17, 18, 23, 30, 34], "firefox": 36, "first": 35, "flow": [6, 7, 13, 15, 17, 19, 21, 23, 32], "format": [10, 30, 31], "forward": [13, 18, 19, 20, 21, 22, 23, 29], "forwardsecrecyerror": 20, "forwardsecrecymanag": [19, 20], "foundat": [17, 29], "four": 13, "framework": [24, 26, 28], "frequent": 12, "from": 5, "function": [13, 14], "futur": [17, 23, 27, 34], "gener": [12, 19], "generateauditreport": 20, "generatecompliancereport": 20, "generatecov": 31, "generatedeletionproof": 20, "generatedhkeypair": 14, "generateembeddingposit": 31, "generatekeyset": 20, "generaterotationproof": 20, "get": [1, 4, 35], "getauditlog": 20, "getsecuritystatu": 20, "global": 25, "goal": 29, "goencrypt": 14, "goplaintext": 14, "grade": 23, "guid": [2, 5, 9, 16, 17, 22, 23, 24, 33, 34, 36], "guidanc": 18, "guidelin": 19, "handl": [3, 5, 6, 7, 10, 13, 14, 15, 16, 20, 22, 32], "handler": 5, "handshak": [13, 14, 15], "hellman": 13, "hidemessag": 31, "high": [15, 21, 32], "highlight": [24, 27], "imag": [30, 31, 32, 33], "impact": [24, 27], "implement": [4, 5, 13, 15, 16, 17, 18, 19, 22, 23, 24, 26, 27, 30, 32, 33, 34], "import": 8, "improv": [17, 34], "indic": 2, "inform": 9, "initi": 20, "injectstatisticalnois": 31, "input": [24, 25, 26], "instal": [12, 36], "instanc": 25, "integr": [0, 3, 5, 6, 7, 8, 10, 16, 17, 19, 22, 23, 28, 30, 31, 32, 33, 34], "interact": [7, 15, 18, 21, 23, 32], "interceptupload": 31, "isakeinprogress": 14, "issu": [9, 19, 22], "kei": [4, 13, 15, 17, 18, 19, 21, 22, 23, 29, 34], "keyrotationengin": 20, "keyrotationerror": 20, "keyset": 20, "knowledg": [19, 21, 22], "layer": 0, "level": [15, 21, 32], "libotr": [24, 25, 26, 27, 28, 29], "licens": 2, "lifecycl": [3, 8, 21, 23], "log": [8, 9], "logev": 20, "lsb": [30, 32, 33], "m": 19, "machin": 13, "mainten": 23, "manag": [3, 8, 9, 14, 19, 22, 24, 25, 26, 30, 32], "manual": [9, 20], "map": 21, "mechan": 15, "media": 32, "memori": [3, 19, 24, 25, 26, 32], "messag": [6, 9, 10, 13, 14, 15, 16, 30, 32], "messageord": 6, "method": [3, 6, 8, 10, 20, 30, 31], "methodologi": 28, "metric": [15, 19, 31], "migrat": [5, 24, 27], "militari": 23, "mitig": 27, "mobil": 5, "model": [7, 15, 17, 21, 29, 32, 34], "modul": [15, 32], "monitor": [3, 9, 19, 21, 22], "multi": [16, 33], "mutual": 13, "negoti": [9, 10], "next": [4, 35, 36], "number": 6, "oper": [20, 24, 25, 26], "optim": [5, 13, 15, 16, 17, 26, 30, 32, 33, 34], "option": [6, 10, 19], "order": [6, 9], "otr": [30, 31, 34], "otrstat": 14, "otrsteganographysess": 31, "out": 6, "overview": [0, 3, 4, 5, 6, 7, 8, 9, 10, 13, 17, 18, 19, 23, 24, 26, 28, 29, 30, 34], "pattern": [5, 17, 20, 34], "perfect": 13, "perform": [4, 5, 6, 7, 9, 13, 15, 16, 17, 19, 21, 22, 23, 24, 26, 27, 28, 30, 32, 33, 34], "performsecuredelet": 20, "persist": 3, "pipelin": 32, "platform": [0, 30, 31, 32, 33, 34], "platformintegr": 31, "point": 7, "polici": [8, 9, 10, 19, 22], "policymanag": 8, "practic": 19, "prevent": 6, "principl": 26, "privaci": 12, "process": [6, 10, 13, 14, 15, 19, 30, 32, 33, 34], "processdhcommit": 14, "processdhkei": 14, "processdownloadedimag": 31, "processrevealsignatur": 14, "processsignatur": 14, "processstegoimag": 31, "product": [16, 22], "profession": 18, "progress": 33, "proof": [19, 21], "proofdata": 20, "properti": 17, "protect": [6, 10], "protocol": [2, 4, 7, 9, 13, 14, 15, 16, 17, 18], "qualiti": [17, 23, 31, 34], "queri": 10, "question": 12, "quick": [2, 5, 16, 22, 23, 33], "real": [19, 21, 22], "reason": 3, "recoveri": [9, 16, 24, 25, 26], "refer": [2, 3, 6, 8, 10, 14, 17, 18, 19, 20, 23, 25, 31, 34], "replai": 6, "report": [19, 22, 28], "requir": [21, 36], "resourc": [17, 18], "revealmessag": 31, "review": [17, 34], "risk": 27, "rotat": [19, 20, 22], "rotatekei": 20, "rotatekeysmanu": 20, "rotationcomplet": 20, "rotationresult": 20, "rotationtrigg": 20, "sanit": 19, "scalabl": [15, 21], "scale": 22, "scenario": [17, 18, 23, 34], "scope": 18, "secreci": [13, 18, 19, 20, 21, 22, 23, 29], "section": 2, "secur": [2, 3, 4, 6, 7, 10, 12, 13, 15, 16, 17, 18, 19, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 32, 33, 34], "securedeletionerror": 20, "securedeletionmanag": 20, "selectoptimalcov": 31, "sendstegomessag": 31, "sequenc": 6, "session": [3, 16], "sessionkei": 14, "setsessionkei": 14, "setup": 12, "shutdown": 20, "signatur": 13, "smp": [3, 9], "social": 32, "specif": 30, "standard": [1, 17, 19, 20, 21, 23, 24, 27, 34], "start": [1, 2, 4, 5, 16, 22, 23, 33, 35], "startak": 14, "state": [3, 6, 13, 14, 15], "statist": [30, 33], "statisticalsecur": 31, "statu": 4, "steganograph": [29, 30, 31, 32, 34], "steganographi": [30, 31, 32, 33, 34], "steganographyengin": 31, "steganographyerror": 31, "stegomessag": 31, "step": [4, 5, 35, 36], "strategi": [7, 15, 26, 34], "structur": [13, 15, 19, 32], "suit": [18, 28], "summari": [17, 18, 23, 27, 34], "support": [2, 9, 10, 16], "system": [0, 7, 15, 17, 18, 19, 21, 23, 30, 32, 34, 36], "tabl": [2, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 30, 31, 32, 33, 34], "target": 18, "technic": 23, "test": [6, 8, 10, 13, 15, 16, 17, 18, 22, 26, 27, 28, 32], "threat": [15, 17, 21, 29, 34], "time": [17, 19, 21, 22, 24, 25, 26], "tool": 9, "total": 18, "trace": 9, "trail": 19, "transit": 15, "trigger": [19, 20], "troubleshoot": [5, 9, 19, 22], "tune": 6, "type": [1, 14, 19, 20, 31], "unit": [16, 22, 28], "unreleas": 11, "updat": 23, "usag": [3, 4, 12, 14, 17, 18, 20, 23, 25, 31, 34], "user": 2, "valid": [6, 8, 10, 13, 16, 22, 24, 25, 26, 27, 28], "validatecompli": 20, "verif": 19, "verifi": [19, 22], "verifyauditintegr": 20, "verifybatchproof": 20, "verifydelet": 20, "verifyproof": 20, "version": [9, 10, 14], "versionnegoti": 10, "visual": 23, "web": 33, "webotr": [1, 2, 5, 18], "worker": 33, "workflow": 1, "xx": 11, "your": 35, "zero": [19, 21, 22], "zeroknowledgeerror": 20, "zeroknowledgeverifi": 20}})