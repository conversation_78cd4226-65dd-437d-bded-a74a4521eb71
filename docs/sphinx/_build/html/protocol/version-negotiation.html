<!doctype html>
<html class="no-js" lang="en" data-content_root="../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><meta name="viewport" content="width=device-width, initial-scale=1" />
<link rel="index" title="Index" href="../genindex.html" /><link rel="search" title="Search" href="../search.html" /><link rel="next" title="Message Ordering" href="message-ordering.html" /><link rel="prev" title="Protocol Overview" href="overview.html" />

    <link rel="shortcut icon" href="../_static/favicon.svg"/><!-- Generated with Sphinx 7.4.7 and Furo 2024.08.06 -->
        <title>Version Negotiation - WebOTR Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../_static/styles/furo.css?v=354aac6f" />
    <link rel="stylesheet" type="text/css" href="../_static/styles/furo-extensions.css?v=302659d7" />
    <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=21a7f706" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  --color-brand-primary: #6366f1;
  --color-brand-content: #6366f1;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8b5cf6;
  --color-brand-content: #8b5cf6;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8b5cf6;
  --color-brand-content: #8b5cf6;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-with-moon" viewBox="0 0 24 24">
    <title>Auto light/dark, in light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
      class="icon-custom-derived-from-feather-sun-and-tabler-moon">
      <path style="opacity: 50%" d="M 5.411 14.504 C 5.471 14.504 5.532 14.504 5.591 14.504 C 3.639 16.319 4.383 19.569 6.931 20.352 C 7.693 20.586 8.512 20.551 9.25 20.252 C 8.023 23.207 4.056 23.725 2.11 21.184 C 0.166 18.642 1.702 14.949 4.874 14.536 C 5.051 14.512 5.231 14.5 5.411 14.5 L 5.411 14.504 Z"/>
      <line x1="14.5" y1="3.25" x2="14.5" y2="1.25"/>
      <line x1="14.5" y1="15.85" x2="14.5" y2="17.85"/>
      <line x1="10.044" y1="5.094" x2="8.63" y2="3.68"/>
      <line x1="19" y1="14.05" x2="20.414" y2="15.464"/>
      <line x1="8.2" y1="9.55" x2="6.2" y2="9.55"/>
      <line x1="20.8" y1="9.55" x2="22.8" y2="9.55"/>
      <line x1="10.044" y1="14.006" x2="8.63" y2="15.42"/>
      <line x1="19" y1="5.05" x2="20.414" y2="3.636"/>
      <circle cx="14.5" cy="9.55" r="3.6"/>
    </svg>
  </symbol>
  <symbol id="svg-moon-with-sun" viewBox="0 0 24 24">
    <title>Auto light/dark, in dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
      class="icon-custom-derived-from-feather-sun-and-tabler-moon">
      <path d="M 8.282 7.007 C 8.385 7.007 8.494 7.007 8.595 7.007 C 5.18 10.184 6.481 15.869 10.942 17.24 C 12.275 17.648 13.706 17.589 15 17.066 C 12.851 22.236 5.91 23.143 2.505 18.696 C -0.897 14.249 1.791 7.786 7.342 7.063 C 7.652 7.021 7.965 7 8.282 7 L 8.282 7.007 Z"/>
      <line style="opacity: 50%" x1="18" y1="3.705" x2="18" y2="2.5"/>
      <line style="opacity: 50%" x1="18" y1="11.295" x2="18" y2="12.5"/>
      <line style="opacity: 50%" x1="15.316" y1="4.816" x2="14.464" y2="3.964"/>
      <line style="opacity: 50%" x1="20.711" y1="10.212" x2="21.563" y2="11.063"/>
      <line style="opacity: 50%" x1="14.205" y1="7.5" x2="13.001" y2="7.5"/>
      <line style="opacity: 50%" x1="21.795" y1="7.5" x2="23" y2="7.5"/>
      <line style="opacity: 50%" x1="15.316" y1="10.184" x2="14.464" y2="11.036"/>
      <line style="opacity: 50%" x1="20.711" y1="4.789" x2="21.563" y2="3.937"/>
      <circle style="opacity: 50%" cx="18" cy="7.5" r="2.169"/>
    </svg>
  </symbol>
  <symbol id="svg-pencil" viewBox="0 0 24 24">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-pencil-code">
      <path d="M4 20h4l10.5 -10.5a2.828 2.828 0 1 0 -4 -4l-10.5 10.5v4" />
      <path d="M13.5 6.5l4 4" />
      <path d="M20 21l2 -2l-2 -2" />
      <path d="M17 17l-2 2l2 2" />
    </svg>
  </symbol>
  <symbol id="svg-eye" viewBox="0 0 24 24">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-eye-code">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0" />
      <path
        d="M11.11 17.958c-3.209 -.307 -5.91 -2.293 -8.11 -5.958c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6c-.21 .352 -.427 .688 -.647 1.008" />
      <path d="M20 21l2 -2l-2 -2" />
      <path d="M17 17l-2 2l2 2" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>

<a class="skip-to-content muted-link" href="#furo-main-content">Skip to content</a>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../index.html"><div class="brand">WebOTR Documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto-light"><use href="#svg-sun-with-moon"></use></svg>
          <svg class="theme-icon-when-auto-dark"><use href="#svg-moon-with-sun"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><a class="sidebar-brand" href="../index.html">
  
  <div class="sidebar-logo-container">
    <img class="sidebar-logo" src="../_static/logo.svg" alt="Logo"/>
  </div>
  
  <span class="sidebar-brand-text">WebOTR Documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-scroll"><div class="sidebar-tree">
  <p class="caption" role="heading"><span class="caption-text">User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user-guide/installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../user-guide/getting-started.html">Getting Started</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../developer-guide/architecture.html">Architecture Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../developer-guide/contributing.html">Contributing to WebOTR</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Protocol Compliance &amp; Advanced Features</span></p>
<ul class="current">
<li class="toctree-l1 current has-children"><a class="reference internal" href="index.html">Protocol Compliance &amp; Advanced Features</a><input checked="" class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of Protocol Compliance &amp; Advanced Features</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="overview.html">Protocol Overview</a></li>
<li class="toctree-l2 current current-page"><a class="current reference internal" href="#">Version Negotiation</a></li>
<li class="toctree-l2"><a class="reference internal" href="message-ordering.html">Message Ordering</a></li>
<li class="toctree-l2"><a class="reference internal" href="enhanced-smp.html">Enhanced SMP</a></li>
<li class="toctree-l2"><a class="reference internal" href="policy-manager.html">Policy Manager</a></li>
<li class="toctree-l2"><a class="reference internal" href="integration-guide.html">Integration Guide</a></li>
<li class="toctree-l2"><a class="reference internal" href="troubleshooting.html">Troubleshooting Guide</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Security</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../security/overview.html">Security Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/libotr-enhancements-summary.html">libOTR Security Enhancements Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/libotr-enhancements.html">libOTR Security Enhancements</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/libotr-enhancements-implementation.html">libOTR Enhancements Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/libotr-enhancements-api.html">libOTR Enhancements API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/libotr-enhancements-testing.html">libOTR Enhancements Testing and Validation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/forward-secrecy-summary.html">Forward Secrecy Documentation Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/forward-secrecy.html">Forward Secrecy Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/forward-secrecy-architecture.html">Forward Secrecy Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/forward-secrecy-implementation.html">Forward Secrecy Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/forward-secrecy-api.html">Forward Secrecy API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/ake-summary.html">AKE Documentation Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/ake.html">Authenticated Key Exchange (AKE)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/ake-architecture.html">AKE Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/ake-implementation.html">AKE Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/ake-api.html">AKE API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/steganography-summary.html">Steganography Documentation Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/steganography.html">Steganography Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/steganography-architecture.html">Steganography Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/steganography-implementation.html">Steganography Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/steganography-api.html">Steganography API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../reference/faq.html">Frequently Asked Questions</a></li>
<li class="toctree-l1"><a class="reference internal" href="../reference/changelog.html">Changelog</a></li>
</ul>

</div>
</div>

      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          <div class="view-this-page">
  <a class="muted-link" href="https://github.com/forkrul/webOTteR/blob/main/docs/sphinx/protocol/version-negotiation.rst?plain=true" title="View this page">
    <svg><use href="#svg-eye"></use></svg>
    <span class="visually-hidden">View this page</span>
  </a>
</div><div class="edit-this-page">
  <a class="muted-link" href="https://github.com/forkrul/webOTteR/edit/main/docs/sphinx/protocol/version-negotiation.rst" title="Edit this page">
    <svg><use href="#svg-pencil"></use></svg>
    <span class="visually-hidden">Edit this page</span>
  </a>
</div><div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto-light"><use href="#svg-sun-with-moon"></use></svg>
              <svg class="theme-icon-when-auto-dark"><use href="#svg-moon-with-sun"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main" id="furo-main-content">
          <section id="version-negotiation">
<h1>Version Negotiation<a class="headerlink" href="#version-negotiation" title="Link to this heading">¶</a></h1>
<p>The Version Negotiation module provides comprehensive OTR protocol version negotiation, supporting both OTR v2 and v3 with automatic version selection and security validation.</p>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading">¶</a></h2>
<p>Version negotiation is the first step in establishing an OTR session, where both parties determine the optimal protocol version to use based on their capabilities and security policies.</p>
<div class="highlight-mermaid notranslate"><div class="highlight"><pre><span></span>graph LR
    subgraph &quot;Version Negotiation Process&quot;
        A[Client A] --&gt; Q1[Send Query ?OTRv23?]
        Q1 --&gt; B[Client B]
        B --&gt; Q2[Parse Query]
        Q2 --&gt; N[Negotiate Version]
        N --&gt; R[Return v3]
        R --&gt; A
        A --&gt; S[Start AKE with v3]
    end
</pre></div>
</div>
</section>
<section id="supported-versions">
<h2>Supported Versions<a class="headerlink" href="#supported-versions" title="Link to this heading">¶</a></h2>
<p>WebOTR supports the following OTR protocol versions:</p>
<ul class="simple">
<li><p><strong>OTR v2</strong>: Basic OTR protocol with essential security features</p></li>
<li><p><strong>OTR v3</strong>: Enhanced protocol with instance tags and additional security features</p></li>
</ul>
<section id="version-capabilities">
<h3>Version Capabilities<a class="headerlink" href="#version-capabilities" title="Link to this heading">¶</a></h3>
<div class="highlight-mermaid notranslate"><div class="highlight"><pre><span></span>graph TB
    subgraph &quot;OTR v2 Capabilities&quot;
        V2_1[Basic AKE Protocol]
        V2_2[SMP v1]
        V2_3[Message Fragmentation]
        V2_4[Basic Error Handling]
    end

    subgraph &quot;OTR v3 Capabilities&quot;
        V3_1[Enhanced AKE Protocol]
        V3_2[Instance Tags]
        V3_3[Extra Symmetric Key]
        V3_4[SMP v2]
        V3_5[Advanced Error Handling]
    end

    subgraph &quot;Shared Features&quot;
        S1[End-to-End Encryption]
        S2[Perfect Forward Secrecy]
        S3[Deniable Authentication]
        S4[Message Authentication]
    end

    V2_1 -.-&gt; S1
    V2_2 -.-&gt; S2
    V2_3 -.-&gt; S3
    V2_4 -.-&gt; S4

    V3_1 -.-&gt; S1
    V3_2 -.-&gt; S2
    V3_3 -.-&gt; S3
    V3_4 -.-&gt; S4
    V3_5 -.-&gt; S4
</pre></div>
</div>
</section>
</section>
<section id="negotiation-process">
<h2>Negotiation Process<a class="headerlink" href="#negotiation-process" title="Link to this heading">¶</a></h2>
<p>The version negotiation process follows a specific sequence to ensure optimal version selection:</p>
<div class="highlight-mermaid notranslate"><div class="highlight"><pre><span></span>sequenceDiagram
    participant Alice as Alice
    participant VN_A as Version Negotiation A
    participant Network as Network
    participant VN_B as Version Negotiation B
    participant Bob as Bob

    Note over Alice,Bob: Initial Query
    Alice-&gt;&gt;VN_A: createQueryMessage(&quot;<EMAIL>&quot;)
    VN_A-&gt;&gt;VN_A: Check local policies
    VN_A--&gt;&gt;Alice: &quot;?OTRv23? <EMAIL>&quot;
    Alice-&gt;&gt;Network: Send query message

    Note over Alice,Bob: Query Processing
    Network-&gt;&gt;Bob: Deliver query message
    Bob-&gt;&gt;VN_B: parseVersions(&quot;?OTRv23? <EMAIL>&quot;)
    VN_B-&gt;&gt;VN_B: Extract versions [2, 3]
    VN_B--&gt;&gt;Bob: {versions: [2, 3], account: &quot;<EMAIL>&quot;}

    Note over Alice,Bob: Version Negotiation
    Bob-&gt;&gt;VN_B: negotiateVersion([2, 3], localPolicy)
    VN_B-&gt;&gt;VN_B: Check compatibility
    VN_B-&gt;&gt;VN_B: Select optimal version
    VN_B--&gt;&gt;Bob: {version: 3, capabilities: {...}}

    Note over Alice,Bob: Security Validation
    Bob-&gt;&gt;VN_B: checkVersionDowngrade(3, [2, 3], policy)
    VN_B-&gt;&gt;VN_B: Validate no downgrade attack
    VN_B--&gt;&gt;Bob: {isDowngrade: false, isAllowed: true}

    Note over Alice,Bob: Protocol Initialization
    Bob-&gt;&gt;Bob: Initialize OTR v3 protocol
    Bob--&gt;&gt;Alice: Start AKE with version 3
</pre></div>
</div>
</section>
<section id="query-message-format">
<h2>Query Message Format<a class="headerlink" href="#query-message-format" title="Link to this heading">¶</a></h2>
<p>Query messages follow the OTR specification format:</p>
<div class="highlight-mermaid notranslate"><div class="highlight"><pre><span></span>graph TB
    subgraph &quot;Query Message Types&quot;
        Q1[&quot;?OTRv2? - OTR v2 only&quot;]
        Q2[&quot;?OTRv3? - OTR v3 only&quot;]
        Q3[&quot;?OTRv23? - Both v2 and v3&quot;]
        Q4[&quot;?OTR? - Generic query&quot;]
    end

    subgraph &quot;Message Structure&quot;
        PREFIX[Query Prefix]
        VERSION[Version Indicator]
        ACCOUNT[Account Information]
    end

    Q1 --&gt; PREFIX
    Q2 --&gt; PREFIX
    Q3 --&gt; PREFIX
    Q4 --&gt; PREFIX

    PREFIX --&gt; VERSION
    VERSION --&gt; ACCOUNT
</pre></div>
</div>
</section>
<section id="api-reference">
<h2>API Reference<a class="headerlink" href="#api-reference" title="Link to this heading">¶</a></h2>
<section id="versionnegotiation-class">
<h3>VersionNegotiation Class<a class="headerlink" href="#versionnegotiation-class" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">import</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">VersionNegotiation</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;webotter/protocol&#39;</span><span class="p">;</span>

<span class="c1">// Negotiate version with remote party</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">VersionNegotiation</span><span class="p">.</span><span class="nx">negotiateVersion</span><span class="p">(</span>
<span class="w">  </span><span class="p">[</span><span class="mf">2</span><span class="p">,</span><span class="w"> </span><span class="mf">3</span><span class="p">],</span><span class="w">  </span><span class="c1">// Their supported versions</span>
<span class="w">  </span><span class="p">{</span><span class="w">        </span><span class="c1">// Our policy</span>
<span class="w">    </span><span class="nx">allowV2</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nx">allowV3</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nx">preferV3</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">);</span>

<span class="c1">// Create query message</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">query</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">VersionNegotiation</span><span class="p">.</span><span class="nx">createQueryMessage</span><span class="p">(</span>
<span class="w">  </span><span class="s1">&#39;<EMAIL>&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="p">{</span><span class="w"> </span><span class="nx">allowV2</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span><span class="w"> </span><span class="nx">allowV3</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="w"> </span><span class="p">}</span>
<span class="p">);</span>

<span class="c1">// Parse incoming query</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">parsed</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">VersionNegotiation</span><span class="p">.</span><span class="nx">parseVersions</span><span class="p">(</span>
<span class="w">  </span><span class="s1">&#39;?OTRv23? <EMAIL>&#39;</span>
<span class="p">);</span>

<span class="c1">// Validate version compatibility</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">compatible</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">VersionNegotiation</span><span class="p">.</span><span class="nx">validateVersionCompatibility</span><span class="p">(</span>
<span class="w">  </span><span class="mf">3</span><span class="p">,</span><span class="w">  </span><span class="c1">// Version to check</span>
<span class="w">  </span><span class="p">[</span><span class="s1">&#39;instanceTags&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;extraSymmetricKey&#39;</span><span class="p">]</span><span class="w">  </span><span class="c1">// Required features</span>
<span class="p">);</span>
</pre></div>
</div>
</section>
<section id="method-details">
<h3>Method Details<a class="headerlink" href="#method-details" title="Link to this heading">¶</a></h3>
<p><strong>negotiateVersion(theirVersions, ourPolicy)</strong></p>
<p>Negotiates the optimal OTR version between two parties.</p>
<p><em>Parameters:</em>
- <code class="docutils literal notranslate"><span class="pre">theirVersions</span></code> (Array&lt;number&gt;): Versions supported by remote party
- <code class="docutils literal notranslate"><span class="pre">ourPolicy</span></code> (Object): Local version policy configuration</p>
<p><em>Returns:</em> Object with negotiation result</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">version</span><span class="o">:</span><span class="w"> </span><span class="mf">3</span><span class="p">,</span><span class="w">                    </span><span class="c1">// Selected version</span>
<span class="w">  </span><span class="nx">capabilities</span><span class="o">:</span><span class="w"> </span><span class="p">{...},</span><span class="w">           </span><span class="c1">// Version capabilities</span>
<span class="w">  </span><span class="nx">commonVersions</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="mf">2</span><span class="p">,</span><span class="w"> </span><span class="mf">3</span><span class="p">],</span><span class="w">       </span><span class="c1">// Mutually supported versions</span>
<span class="w">  </span><span class="nx">negotiationSuccess</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span><span class="w">      </span><span class="c1">// Success flag</span>
<span class="w">  </span><span class="nx">securityLevel</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;high&#39;</span><span class="w">         </span><span class="c1">// Security assessment</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>createQueryMessage(account, policy)</strong></p>
<p>Creates an OTR query message for version negotiation.</p>
<p><em>Parameters:</em>
- <code class="docutils literal notranslate"><span class="pre">account</span></code> (string): Account identifier
- <code class="docutils literal notranslate"><span class="pre">policy</span></code> (Object): Version policy configuration</p>
<p><em>Returns:</em> Object with query message and metadata</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">message</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;?OTRv23? <EMAIL>&#39;</span><span class="p">,</span><span class="w">  </span><span class="c1">// Query message</span>
<span class="w">  </span><span class="nx">versions</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="mf">2</span><span class="p">,</span><span class="w"> </span><span class="mf">3</span><span class="p">],</span><span class="w">                      </span><span class="c1">// Supported versions</span>
<span class="w">  </span><span class="nx">account</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;<EMAIL>&#39;</span><span class="p">,</span><span class="w">          </span><span class="c1">// Account info</span>
<span class="w">  </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="mf">*************</span><span class="w">               </span><span class="c1">// Creation timestamp</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>parseVersions(message)</strong></p>
<p>Parses an OTR query message to extract version information.</p>
<p><em>Parameters:</em>
- <code class="docutils literal notranslate"><span class="pre">message</span></code> (string): OTR query message</p>
<p><em>Returns:</em> Object with parsed version information</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">versions</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="mf">2</span><span class="p">,</span><span class="w"> </span><span class="mf">3</span><span class="p">],</span><span class="w">                    </span><span class="c1">// Extracted versions</span>
<span class="w">  </span><span class="nx">account</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;<EMAIL>&#39;</span><span class="p">,</span><span class="w">        </span><span class="c1">// Account information</span>
<span class="w">  </span><span class="nx">originalMessage</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;?OTRv23? <EMAIL>&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">parseSuccess</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="w">                   </span><span class="c1">// Parse success flag</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="security-considerations">
<h2>Security Considerations<a class="headerlink" href="#security-considerations" title="Link to this heading">¶</a></h2>
<section id="version-downgrade-protection">
<h3>Version Downgrade Protection<a class="headerlink" href="#version-downgrade-protection" title="Link to this heading">¶</a></h3>
<p>The version negotiation system includes protection against version downgrade attacks:</p>
<div class="highlight-mermaid notranslate"><div class="highlight"><pre><span></span>graph TB
    subgraph &quot;Downgrade Attack Prevention&quot;
        A[Receive Version List]
        B{Check Available Versions}
        C[Detect Potential Downgrade]
        D{Policy Allows Downgrade?}
        E[Accept Lower Version]
        F[Reject Downgrade Attack]
        G[Use Highest Available]
    end

    A --&gt; B
    B --&gt; C
    C --&gt; D
    D --&gt;|Yes| E
    D --&gt;|No| F
    B --&gt;|No Downgrade| G
</pre></div>
</div>
</section>
<section id="policy-enforcement">
<h3>Policy Enforcement<a class="headerlink" href="#policy-enforcement" title="Link to this heading">¶</a></h3>
<p>Version selection respects security policies:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">const</span><span class="w"> </span><span class="nx">policy</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">requireEncryption</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span><span class="w">    </span><span class="c1">// Require encrypted protocols</span>
<span class="w">  </span><span class="nx">allowV2</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span><span class="w">            </span><span class="c1">// Allow OTR v2</span>
<span class="w">  </span><span class="nx">allowV3</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span><span class="w">            </span><span class="c1">// Allow OTR v3</span>
<span class="w">  </span><span class="nx">preferV3</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span><span class="w">           </span><span class="c1">// Prefer v3 when available</span>
<span class="w">  </span><span class="nx">whitelistStartAKE</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span><span class="w">  </span><span class="c1">// Allow whitespace tag AKE start</span>
<span class="w">  </span><span class="nx">errorStartAKE</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="w">       </span><span class="c1">// Allow error message AKE start</span>
<span class="p">};</span>
</pre></div>
</div>
</section>
<section id="error-handling">
<h3>Error Handling<a class="headerlink" href="#error-handling" title="Link to this heading">¶</a></h3>
<p>The system handles various error conditions gracefully:</p>
<div class="highlight-mermaid notranslate"><div class="highlight"><pre><span></span>graph TB
    subgraph &quot;Error Scenarios&quot;
        E1[Invalid Query Format]
        E2[Unsupported Versions]
        E3[Policy Violations]
        E4[Network Errors]
    end

    subgraph &quot;Error Responses&quot;
        R1[Parse Error Response]
        R2[No Compatible Versions]
        R3[Policy Rejection]
        R4[Retry with Fallback]
    end

    E1 --&gt; R1
    E2 --&gt; R2
    E3 --&gt; R3
    E4 --&gt; R4
</pre></div>
</div>
</section>
</section>
<section id="configuration-options">
<h2>Configuration Options<a class="headerlink" href="#configuration-options" title="Link to this heading">¶</a></h2>
<section id="default-configuration">
<h3>Default Configuration<a class="headerlink" href="#default-configuration" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">const</span><span class="w"> </span><span class="nx">defaultConfig</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">supportedVersions</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="mf">2</span><span class="p">,</span><span class="w"> </span><span class="mf">3</span><span class="p">],</span>
<span class="w">  </span><span class="nx">defaultVersion</span><span class="o">:</span><span class="w"> </span><span class="mf">3</span><span class="p">,</span>
<span class="w">  </span><span class="nx">minimumVersion</span><span class="o">:</span><span class="w"> </span><span class="mf">2</span><span class="p">,</span>
<span class="w">  </span><span class="nx">maximumVersion</span><span class="o">:</span><span class="w"> </span><span class="mf">3</span><span class="p">,</span>

<span class="w">  </span><span class="nx">policies</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">requireEncryption</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nx">allowV2</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nx">allowV3</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nx">preferV3</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nx">whitelistStartAKE</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nx">errorStartAKE</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">};</span>
</pre></div>
</div>
</section>
<section id="custom-configuration">
<h3>Custom Configuration<a class="headerlink" href="#custom-configuration" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="c1">// Restrict to OTR v3 only</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">strictConfig</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">policies</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">allowV2</span><span class="o">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span>
<span class="w">    </span><span class="nx">allowV3</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nx">requireEncryption</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">};</span>

<span class="c1">// Development/testing configuration</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">devConfig</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">policies</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">allowV2</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nx">allowV3</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nx">preferV3</span><span class="o">:</span><span class="w"> </span><span class="kc">false</span><span class="w">  </span><span class="c1">// Allow v2 for testing</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">};</span>
</pre></div>
</div>
</section>
</section>
<section id="integration-examples">
<h2>Integration Examples<a class="headerlink" href="#integration-examples" title="Link to this heading">¶</a></h2>
<section id="basic-integration">
<h3>Basic Integration<a class="headerlink" href="#basic-integration" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">import</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">VersionNegotiation</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;webotter/protocol&#39;</span><span class="p">;</span>

<span class="kd">class</span><span class="w"> </span><span class="nx">OTRSession</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">startSession</span><span class="p">(</span><span class="nx">remoteAccount</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Create and send query</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">query</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">VersionNegotiation</span><span class="p">.</span><span class="nx">createQueryMessage</span><span class="p">(</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">localAccount</span><span class="p">,</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">versionPolicy</span>
<span class="w">    </span><span class="p">);</span>

<span class="w">    </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">sendMessage</span><span class="p">(</span><span class="nx">query</span><span class="p">.</span><span class="nx">message</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">handleQuery</span><span class="p">(</span><span class="nx">queryMessage</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Parse incoming query</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">parsed</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">VersionNegotiation</span><span class="p">.</span><span class="nx">parseVersions</span><span class="p">(</span><span class="nx">queryMessage</span><span class="p">);</span>

<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">parsed</span><span class="p">.</span><span class="nx">parseSuccess</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="c1">// Negotiate version</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">VersionNegotiation</span><span class="p">.</span><span class="nx">negotiateVersion</span><span class="p">(</span>
<span class="w">        </span><span class="nx">parsed</span><span class="p">.</span><span class="nx">versions</span><span class="p">,</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">versionPolicy</span>
<span class="w">      </span><span class="p">);</span>

<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">result</span><span class="p">.</span><span class="nx">negotiationSuccess</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Initialize protocol with selected version</span>
<span class="w">        </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">initializeProtocol</span><span class="p">(</span><span class="nx">result</span><span class="p">.</span><span class="nx">version</span><span class="p">);</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="advanced-integration">
<h3>Advanced Integration<a class="headerlink" href="#advanced-integration" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">class</span><span class="w"> </span><span class="nx">EnhancedOTRSession</span><span class="w"> </span><span class="k">extends</span><span class="w"> </span><span class="nx">OTRSession</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">negotiateWithFallback</span><span class="p">(</span><span class="nx">remoteVersions</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">let</span><span class="w"> </span><span class="nx">policy</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">primaryPolicy</span><span class="p">;</span>

<span class="w">    </span><span class="c1">// Try primary policy first</span>
<span class="w">    </span><span class="kd">let</span><span class="w"> </span><span class="nx">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">VersionNegotiation</span><span class="p">.</span><span class="nx">negotiateVersion</span><span class="p">(</span>
<span class="w">      </span><span class="nx">remoteVersions</span><span class="p">,</span>
<span class="w">      </span><span class="nx">policy</span>
<span class="w">    </span><span class="p">);</span>

<span class="w">    </span><span class="c1">// Fallback to secondary policy if needed</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="nx">result</span><span class="p">.</span><span class="nx">negotiationSuccess</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">fallbackPolicy</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">VersionNegotiation</span><span class="p">.</span><span class="nx">negotiateVersion</span><span class="p">(</span>
<span class="w">        </span><span class="nx">remoteVersions</span><span class="p">,</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">fallbackPolicy</span>
<span class="w">      </span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="c1">// Validate security implications</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">result</span><span class="p">.</span><span class="nx">negotiationSuccess</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">downgradeCheck</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">VersionNegotiation</span><span class="p">.</span><span class="nx">checkVersionDowngrade</span><span class="p">(</span>
<span class="w">        </span><span class="nx">result</span><span class="p">.</span><span class="nx">version</span><span class="p">,</span>
<span class="w">        </span><span class="nx">remoteVersions</span><span class="p">,</span>
<span class="w">        </span><span class="nx">policy</span>
<span class="w">      </span><span class="p">);</span>

<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="nx">downgradeCheck</span><span class="p">.</span><span class="nx">isAllowed</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">throw</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="ne">Error</span><span class="p">(</span><span class="s1">&#39;Version downgrade attack detected&#39;</span><span class="p">);</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nx">result</span><span class="p">;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="testing-and-validation">
<h2>Testing and Validation<a class="headerlink" href="#testing-and-validation" title="Link to this heading">¶</a></h2>
<p>The version negotiation system includes comprehensive testing:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="c1">// Test version negotiation</span>
<span class="nx">describe</span><span class="p">(</span><span class="s1">&#39;Version Negotiation&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">test</span><span class="p">(</span><span class="s1">&#39;should negotiate highest common version&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">VersionNegotiation</span><span class="p">.</span><span class="nx">negotiateVersion</span><span class="p">([</span><span class="mf">2</span><span class="p">,</span><span class="w"> </span><span class="mf">3</span><span class="p">]);</span>
<span class="w">    </span><span class="nx">expect</span><span class="p">(</span><span class="nx">result</span><span class="p">.</span><span class="nx">version</span><span class="p">).</span><span class="nx">toBe</span><span class="p">(</span><span class="mf">3</span><span class="p">);</span>
<span class="w">    </span><span class="nx">expect</span><span class="p">(</span><span class="nx">result</span><span class="p">.</span><span class="nx">negotiationSuccess</span><span class="p">).</span><span class="nx">toBe</span><span class="p">(</span><span class="kc">true</span><span class="p">);</span>
<span class="w">  </span><span class="p">});</span>

<span class="w">  </span><span class="nx">test</span><span class="p">(</span><span class="s1">&#39;should detect downgrade attacks&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">expect</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">VersionNegotiation</span><span class="p">.</span><span class="nx">checkVersionDowngrade</span><span class="p">(</span><span class="mf">2</span><span class="p">,</span><span class="w"> </span><span class="p">[</span><span class="mf">2</span><span class="p">,</span><span class="w"> </span><span class="mf">3</span><span class="p">],</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">allowV2</span><span class="o">:</span><span class="w"> </span><span class="kc">false</span>
<span class="w">      </span><span class="p">});</span>
<span class="w">    </span><span class="p">}).</span><span class="nx">toThrow</span><span class="p">(</span><span class="s1">&#39;Version downgrade attack detected&#39;</span><span class="p">);</span>
<span class="w">  </span><span class="p">});</span>
<span class="p">});</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Version negotiation is automatic and transparent to end users, but provides extensive configuration options for administrators and developers who need fine-grained control over protocol selection.</p>
</div>
</section>
</section>

        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          <a class="next-page" href="message-ordering.html">
              <div class="page-info">
                <div class="context">
                  <span>Next</span>
                </div>
                <div class="title">Message Ordering</div>
              </div>
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
            </a>
          <a class="prev-page" href="overview.html">
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
              <div class="page-info">
                <div class="context">
                  <span>Previous</span>
                </div>
                
                <div class="title">Protocol Overview</div>
                
              </div>
            </a>
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 1980, WebOTR Team
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer">
      
      
      <div class="toc-sticky toc-scroll">
        <div class="toc-title-container">
          <span class="toc-title">
            On this page
          </span>
        </div>
        <div class="toc-tree-container">
          <div class="toc-tree">
            <ul>
<li><a class="reference internal" href="#">Version Negotiation</a><ul>
<li><a class="reference internal" href="#overview">Overview</a></li>
<li><a class="reference internal" href="#supported-versions">Supported Versions</a><ul>
<li><a class="reference internal" href="#version-capabilities">Version Capabilities</a></li>
</ul>
</li>
<li><a class="reference internal" href="#negotiation-process">Negotiation Process</a></li>
<li><a class="reference internal" href="#query-message-format">Query Message Format</a></li>
<li><a class="reference internal" href="#api-reference">API Reference</a><ul>
<li><a class="reference internal" href="#versionnegotiation-class">VersionNegotiation Class</a></li>
<li><a class="reference internal" href="#method-details">Method Details</a></li>
</ul>
</li>
<li><a class="reference internal" href="#security-considerations">Security Considerations</a><ul>
<li><a class="reference internal" href="#version-downgrade-protection">Version Downgrade Protection</a></li>
<li><a class="reference internal" href="#policy-enforcement">Policy Enforcement</a></li>
<li><a class="reference internal" href="#error-handling">Error Handling</a></li>
</ul>
</li>
<li><a class="reference internal" href="#configuration-options">Configuration Options</a><ul>
<li><a class="reference internal" href="#default-configuration">Default Configuration</a></li>
<li><a class="reference internal" href="#custom-configuration">Custom Configuration</a></li>
</ul>
</li>
<li><a class="reference internal" href="#integration-examples">Integration Examples</a><ul>
<li><a class="reference internal" href="#basic-integration">Basic Integration</a></li>
<li><a class="reference internal" href="#advanced-integration">Advanced Integration</a></li>
</ul>
</li>
<li><a class="reference internal" href="#testing-and-validation">Testing and Validation</a></li>
</ul>
</li>
</ul>

          </div>
        </div>
      </div>
      
      
    </aside>
  </div>
</div><script src="../_static/documentation_options.js?v=01f34227"></script>
    <script src="../_static/doctools.js?v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/scripts/furo.js?v=5fa4622c"></script>
    <script src="../_static/mermaid-renderer.js?v=b683124c"></script>
    </body>
</html>