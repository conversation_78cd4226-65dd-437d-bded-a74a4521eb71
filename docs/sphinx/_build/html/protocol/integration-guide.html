<!doctype html>
<html class="no-js" lang="en" data-content_root="../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><meta name="viewport" content="width=device-width, initial-scale=1" />
<link rel="index" title="Index" href="../genindex.html" /><link rel="search" title="Search" href="../search.html" /><link rel="next" title="Troubleshooting Guide" href="troubleshooting.html" /><link rel="prev" title="Policy Manager" href="policy-manager.html" />

    <link rel="shortcut icon" href="../_static/favicon.svg"/><!-- Generated with Sphinx 7.4.7 and Furo 2024.08.06 -->
        <title>Integration Guide - WebOTR Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../_static/styles/furo.css?v=354aac6f" />
    <link rel="stylesheet" type="text/css" href="../_static/styles/furo-extensions.css?v=302659d7" />
    <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=21a7f706" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  --color-brand-primary: #6366f1;
  --color-brand-content: #6366f1;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8b5cf6;
  --color-brand-content: #8b5cf6;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8b5cf6;
  --color-brand-content: #8b5cf6;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-with-moon" viewBox="0 0 24 24">
    <title>Auto light/dark, in light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
      class="icon-custom-derived-from-feather-sun-and-tabler-moon">
      <path style="opacity: 50%" d="M 5.411 14.504 C 5.471 14.504 5.532 14.504 5.591 14.504 C 3.639 16.319 4.383 19.569 6.931 20.352 C 7.693 20.586 8.512 20.551 9.25 20.252 C 8.023 23.207 4.056 23.725 2.11 21.184 C 0.166 18.642 1.702 14.949 4.874 14.536 C 5.051 14.512 5.231 14.5 5.411 14.5 L 5.411 14.504 Z"/>
      <line x1="14.5" y1="3.25" x2="14.5" y2="1.25"/>
      <line x1="14.5" y1="15.85" x2="14.5" y2="17.85"/>
      <line x1="10.044" y1="5.094" x2="8.63" y2="3.68"/>
      <line x1="19" y1="14.05" x2="20.414" y2="15.464"/>
      <line x1="8.2" y1="9.55" x2="6.2" y2="9.55"/>
      <line x1="20.8" y1="9.55" x2="22.8" y2="9.55"/>
      <line x1="10.044" y1="14.006" x2="8.63" y2="15.42"/>
      <line x1="19" y1="5.05" x2="20.414" y2="3.636"/>
      <circle cx="14.5" cy="9.55" r="3.6"/>
    </svg>
  </symbol>
  <symbol id="svg-moon-with-sun" viewBox="0 0 24 24">
    <title>Auto light/dark, in dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
      class="icon-custom-derived-from-feather-sun-and-tabler-moon">
      <path d="M 8.282 7.007 C 8.385 7.007 8.494 7.007 8.595 7.007 C 5.18 10.184 6.481 15.869 10.942 17.24 C 12.275 17.648 13.706 17.589 15 17.066 C 12.851 22.236 5.91 23.143 2.505 18.696 C -0.897 14.249 1.791 7.786 7.342 7.063 C 7.652 7.021 7.965 7 8.282 7 L 8.282 7.007 Z"/>
      <line style="opacity: 50%" x1="18" y1="3.705" x2="18" y2="2.5"/>
      <line style="opacity: 50%" x1="18" y1="11.295" x2="18" y2="12.5"/>
      <line style="opacity: 50%" x1="15.316" y1="4.816" x2="14.464" y2="3.964"/>
      <line style="opacity: 50%" x1="20.711" y1="10.212" x2="21.563" y2="11.063"/>
      <line style="opacity: 50%" x1="14.205" y1="7.5" x2="13.001" y2="7.5"/>
      <line style="opacity: 50%" x1="21.795" y1="7.5" x2="23" y2="7.5"/>
      <line style="opacity: 50%" x1="15.316" y1="10.184" x2="14.464" y2="11.036"/>
      <line style="opacity: 50%" x1="20.711" y1="4.789" x2="21.563" y2="3.937"/>
      <circle style="opacity: 50%" cx="18" cy="7.5" r="2.169"/>
    </svg>
  </symbol>
  <symbol id="svg-pencil" viewBox="0 0 24 24">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-pencil-code">
      <path d="M4 20h4l10.5 -10.5a2.828 2.828 0 1 0 -4 -4l-10.5 10.5v4" />
      <path d="M13.5 6.5l4 4" />
      <path d="M20 21l2 -2l-2 -2" />
      <path d="M17 17l-2 2l2 2" />
    </svg>
  </symbol>
  <symbol id="svg-eye" viewBox="0 0 24 24">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-eye-code">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0" />
      <path
        d="M11.11 17.958c-3.209 -.307 -5.91 -2.293 -8.11 -5.958c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6c-.21 .352 -.427 .688 -.647 1.008" />
      <path d="M20 21l2 -2l-2 -2" />
      <path d="M17 17l-2 2l2 2" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>

<a class="skip-to-content muted-link" href="#furo-main-content">Skip to content</a>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../index.html"><div class="brand">WebOTR Documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto-light"><use href="#svg-sun-with-moon"></use></svg>
          <svg class="theme-icon-when-auto-dark"><use href="#svg-moon-with-sun"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><a class="sidebar-brand" href="../index.html">
  
  <div class="sidebar-logo-container">
    <img class="sidebar-logo" src="../_static/logo.svg" alt="Logo"/>
  </div>
  
  <span class="sidebar-brand-text">WebOTR Documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-scroll"><div class="sidebar-tree">
  <p class="caption" role="heading"><span class="caption-text">User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user-guide/installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../user-guide/getting-started.html">Getting Started</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../developer-guide/architecture.html">Architecture Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../developer-guide/contributing.html">Contributing to WebOTR</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Protocol Compliance &amp; Advanced Features</span></p>
<ul class="current">
<li class="toctree-l1 current has-children"><a class="reference internal" href="index.html">Protocol Compliance &amp; Advanced Features</a><input checked="" class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of Protocol Compliance &amp; Advanced Features</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="overview.html">Protocol Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="version-negotiation.html">Version Negotiation</a></li>
<li class="toctree-l2"><a class="reference internal" href="message-ordering.html">Message Ordering</a></li>
<li class="toctree-l2"><a class="reference internal" href="enhanced-smp.html">Enhanced SMP</a></li>
<li class="toctree-l2"><a class="reference internal" href="policy-manager.html">Policy Manager</a></li>
<li class="toctree-l2 current current-page"><a class="current reference internal" href="#">Integration Guide</a></li>
<li class="toctree-l2"><a class="reference internal" href="troubleshooting.html">Troubleshooting Guide</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Security</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../security/overview.html">Security Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/libotr-enhancements-summary.html">libOTR Security Enhancements Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/libotr-enhancements.html">libOTR Security Enhancements</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/libotr-enhancements-implementation.html">libOTR Enhancements Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/libotr-enhancements-api.html">libOTR Enhancements API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/libotr-enhancements-testing.html">libOTR Enhancements Testing and Validation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/forward-secrecy-summary.html">Forward Secrecy Documentation Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/forward-secrecy.html">Forward Secrecy Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/forward-secrecy-architecture.html">Forward Secrecy Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/forward-secrecy-implementation.html">Forward Secrecy Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/forward-secrecy-api.html">Forward Secrecy API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/ake-summary.html">AKE Documentation Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/ake.html">Authenticated Key Exchange (AKE)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/ake-architecture.html">AKE Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/ake-implementation.html">AKE Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/ake-api.html">AKE API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/steganography-summary.html">Steganography Documentation Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/steganography.html">Steganography Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/steganography-architecture.html">Steganography Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/steganography-implementation.html">Steganography Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/steganography-api.html">Steganography API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../reference/faq.html">Frequently Asked Questions</a></li>
<li class="toctree-l1"><a class="reference internal" href="../reference/changelog.html">Changelog</a></li>
</ul>

</div>
</div>

      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          <div class="view-this-page">
  <a class="muted-link" href="https://github.com/forkrul/webOTteR/blob/main/docs/sphinx/protocol/integration-guide.rst?plain=true" title="View this page">
    <svg><use href="#svg-eye"></use></svg>
    <span class="visually-hidden">View this page</span>
  </a>
</div><div class="edit-this-page">
  <a class="muted-link" href="https://github.com/forkrul/webOTteR/edit/main/docs/sphinx/protocol/integration-guide.rst" title="Edit this page">
    <svg><use href="#svg-pencil"></use></svg>
    <span class="visually-hidden">Edit this page</span>
  </a>
</div><div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto-light"><use href="#svg-sun-with-moon"></use></svg>
              <svg class="theme-icon-when-auto-dark"><use href="#svg-moon-with-sun"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main" id="furo-main-content">
          <section id="integration-guide">
<h1>Integration Guide<a class="headerlink" href="#integration-guide" title="Link to this heading">¶</a></h1>
<p>This guide provides comprehensive instructions for integrating Phase 3 protocol features into existing WebOTR applications and new deployments.</p>
<section id="integration-overview">
<h2>Integration Overview<a class="headerlink" href="#integration-overview" title="Link to this heading">¶</a></h2>
<p>Phase 3 features are designed to integrate seamlessly with existing WebOTR implementations while providing enhanced functionality for new deployments.</p>
<div class="highlight-mermaid notranslate"><div class="highlight"><pre><span></span>graph TB
    subgraph &quot;Integration Layers&quot;
        subgraph &quot;Application Layer&quot;
            APP[Your Application]
            UI[User Interface]
            API[Application API]
        end

        subgraph &quot;WebOTR Integration Layer&quot;
            WRAPPER[WebOTR Wrapper]
            CONFIG[Configuration Manager]
            EVENT[Event Handlers]
        end

        subgraph &quot;Phase 3 Protocol Layer&quot;
            VN[Version Negotiation]
            MO[Message Ordering]
            ESMP[Enhanced SMP]
            PM[Policy Manager]
        end

        subgraph &quot;Core WebOTR&quot;
            CORE[Core Protocol]
            CRYPTO[Cryptography]
            TRANSPORT[Transport]
        end
    end

    APP --&gt; WRAPPER
    UI --&gt; CONFIG
    API --&gt; EVENT

    WRAPPER --&gt; VN
    CONFIG --&gt; MO
    EVENT --&gt; ESMP
    CONFIG --&gt; PM

    VN --&gt; CORE
    MO --&gt; CORE
    ESMP --&gt; CORE
    PM --&gt; CRYPTO
</pre></div>
</div>
</section>
<section id="quick-start">
<h2>Quick Start<a class="headerlink" href="#quick-start" title="Link to this heading">¶</a></h2>
<section id="basic-integration">
<h3>Basic Integration<a class="headerlink" href="#basic-integration" title="Link to this heading">¶</a></h3>
<p>For basic integration with minimal configuration:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">import</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">WebOTR</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;webotter&#39;</span><span class="p">;</span>
<span class="k">import</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">PolicyManager</span><span class="p">,</span><span class="w"> </span><span class="nx">MessageOrdering</span><span class="p">,</span><span class="w"> </span><span class="nx">EnhancedSMP</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;webotter/protocol&#39;</span><span class="p">;</span>

<span class="c1">// Initialize WebOTR with Phase 3 features</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">otr</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">WebOTR</span><span class="p">({</span>
<span class="w">  </span><span class="c1">// Enable Phase 3 features</span>
<span class="w">  </span><span class="nx">enableVersionNegotiation</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nx">enableMessageOrdering</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nx">enableEnhancedSMP</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nx">enablePolicyManager</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>

<span class="w">  </span><span class="c1">// Basic configuration</span>
<span class="w">  </span><span class="nx">account</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;<EMAIL>&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">policies</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="s1">&#39;security.requireEncryption&#39;</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;security.allowV3&#39;</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;protocol.maxBufferSize&#39;</span><span class="o">:</span><span class="w"> </span><span class="mf">100</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">});</span>

<span class="c1">// Start OTR session with automatic version negotiation</span>
<span class="k">await</span><span class="w"> </span><span class="nx">otr</span><span class="p">.</span><span class="nx">startSession</span><span class="p">(</span><span class="s1">&#39;<EMAIL>&#39;</span><span class="p">);</span>
</pre></div>
</div>
</section>
<section id="advanced-integration">
<h3>Advanced Integration<a class="headerlink" href="#advanced-integration" title="Link to this heading">¶</a></h3>
<p>For advanced integration with custom configuration:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">import</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">WebOTR</span><span class="p">,</span>
<span class="w">  </span><span class="nx">VersionNegotiation</span><span class="p">,</span>
<span class="w">  </span><span class="nx">MessageOrdering</span><span class="p">,</span>
<span class="w">  </span><span class="nx">EnhancedSMP</span><span class="p">,</span>
<span class="w">  </span><span class="nx">PolicyManager</span><span class="p">,</span>
<span class="w">  </span><span class="nx">VALIDATION_LEVEL</span><span class="p">,</span>
<span class="w">  </span><span class="nx">ACCESS_LEVEL</span>
<span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;webotter/protocol&#39;</span><span class="p">;</span>

<span class="kd">class</span><span class="w"> </span><span class="nx">AdvancedWebOTRClient</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span><span class="nx">options</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{})</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Initialize policy manager first</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">policyManager</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">PolicyManager</span><span class="p">({</span>
<span class="w">      </span><span class="nx">currentUser</span><span class="o">:</span><span class="w"> </span><span class="nx">options</span><span class="p">.</span><span class="nx">userId</span><span class="p">,</span>
<span class="w">      </span><span class="nx">userRoles</span><span class="o">:</span><span class="w"> </span><span class="nx">options</span><span class="p">.</span><span class="nx">userRoles</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;user&#39;</span><span class="p">],</span>
<span class="w">      </span><span class="nx">validationLevel</span><span class="o">:</span><span class="w"> </span><span class="nx">VALIDATION_LEVEL</span><span class="p">.</span><span class="nx">STRICT</span><span class="p">,</span>
<span class="w">      </span><span class="nx">enableAccessControl</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">      </span><span class="nx">enableAuditLogging</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="c1">// Configure policies</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">configurePolicies</span><span class="p">(</span><span class="nx">options</span><span class="p">.</span><span class="nx">environment</span><span class="p">);</span>

<span class="w">    </span><span class="c1">// Initialize message ordering</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">messageOrdering</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">MessageOrdering</span><span class="p">({</span>
<span class="w">      </span><span class="nx">maxBufferSize</span><span class="o">:</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">policyManager</span><span class="p">.</span><span class="nx">getPolicy</span><span class="p">(</span><span class="s1">&#39;protocol.maxBufferSize&#39;</span><span class="p">),</span>
<span class="w">      </span><span class="nx">replayWindowSize</span><span class="o">:</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">policyManager</span><span class="p">.</span><span class="nx">getPolicy</span><span class="p">(</span><span class="s1">&#39;protocol.replayWindowSize&#39;</span><span class="p">),</span>
<span class="w">      </span><span class="nx">gapTimeoutMs</span><span class="o">:</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">policyManager</span><span class="p">.</span><span class="nx">getPolicy</span><span class="p">(</span><span class="s1">&#39;protocol.gapTimeoutMs&#39;</span><span class="p">)</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="c1">// Initialize enhanced SMP</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">enhancedSMP</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">EnhancedSMP</span><span class="p">({</span>
<span class="w">      </span><span class="nx">enableStatePersistence</span><span class="o">:</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">policyManager</span><span class="p">.</span><span class="nx">getPolicy</span><span class="p">(</span><span class="s1">&#39;protocol.enableStatePersistence&#39;</span><span class="p">),</span>
<span class="w">      </span><span class="nx">sessionTimeoutMs</span><span class="o">:</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">policyManager</span><span class="p">.</span><span class="nx">getPolicy</span><span class="p">(</span><span class="s1">&#39;security.sessionTimeoutMs&#39;</span><span class="p">),</span>
<span class="w">      </span><span class="nx">enableDetailedLogging</span><span class="o">:</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">policyManager</span><span class="p">.</span><span class="nx">getPolicy</span><span class="p">(</span><span class="s1">&#39;logging.enableDetailedLogging&#39;</span><span class="p">)</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="c1">// Initialize core WebOTR</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">otr</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">WebOTR</span><span class="p">({</span>
<span class="w">      </span><span class="nx">account</span><span class="o">:</span><span class="w"> </span><span class="nx">options</span><span class="p">.</span><span class="nx">account</span><span class="p">,</span>
<span class="w">      </span><span class="nx">versionNegotiation</span><span class="o">:</span><span class="w"> </span><span class="nx">VersionNegotiation</span><span class="p">,</span>
<span class="w">      </span><span class="nx">messageOrdering</span><span class="o">:</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">messageOrdering</span><span class="p">,</span>
<span class="w">      </span><span class="nx">enhancedSMP</span><span class="o">:</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">enhancedSMP</span><span class="p">,</span>
<span class="w">      </span><span class="nx">policyManager</span><span class="o">:</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">policyManager</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">setupEventHandlers</span><span class="p">();</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">configurePolicies</span><span class="p">(</span><span class="nx">environment</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">context</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">environment</span><span class="o">:</span><span class="w"> </span><span class="nx">environment</span><span class="w"> </span><span class="p">};</span>

<span class="w">    </span><span class="c1">// Set environment-specific policies</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">environment</span><span class="w"> </span><span class="o">===</span><span class="w"> </span><span class="s1">&#39;production&#39;</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">policyManager</span><span class="p">.</span><span class="nx">setPolicy</span><span class="p">(</span><span class="s1">&#39;security.sessionTimeoutMs&#39;</span><span class="p">,</span><span class="w"> </span><span class="mf">1800000</span><span class="p">,</span><span class="w"> </span><span class="nx">context</span><span class="p">);</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">policyManager</span><span class="p">.</span><span class="nx">setPolicy</span><span class="p">(</span><span class="s1">&#39;logging.logLevel&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;warn&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">context</span><span class="p">);</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">policyManager</span><span class="p">.</span><span class="nx">setPolicy</span><span class="p">(</span><span class="s1">&#39;performance.enableOptimizations&#39;</span><span class="p">,</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span><span class="w"> </span><span class="nx">context</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">environment</span><span class="w"> </span><span class="o">===</span><span class="w"> </span><span class="s1">&#39;development&#39;</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">policyManager</span><span class="p">.</span><span class="nx">setPolicy</span><span class="p">(</span><span class="s1">&#39;security.sessionTimeoutMs&#39;</span><span class="p">,</span><span class="w"> </span><span class="mf">300000</span><span class="p">,</span><span class="w"> </span><span class="nx">context</span><span class="p">);</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">policyManager</span><span class="p">.</span><span class="nx">setPolicy</span><span class="p">(</span><span class="s1">&#39;logging.logLevel&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;debug&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">context</span><span class="p">);</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">policyManager</span><span class="p">.</span><span class="nx">setPolicy</span><span class="p">(</span><span class="s1">&#39;logging.enableDetailedLogging&#39;</span><span class="p">,</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span><span class="w"> </span><span class="nx">context</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">setupEventHandlers</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Handle version negotiation events</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">otr</span><span class="p">.</span><span class="nx">on</span><span class="p">(</span><span class="s1">&#39;versionNegotiated&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">event</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;OTR version negotiated:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">event</span><span class="p">.</span><span class="nx">version</span><span class="p">);</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">updateUIForVersion</span><span class="p">(</span><span class="nx">event</span><span class="p">.</span><span class="nx">version</span><span class="p">);</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="c1">// Handle message ordering events</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">otr</span><span class="p">.</span><span class="nx">on</span><span class="p">(</span><span class="s1">&#39;messageReordered&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">event</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;Messages reordered:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">event</span><span class="p">.</span><span class="nx">count</span><span class="p">);</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">updateMessageDisplay</span><span class="p">(</span><span class="nx">event</span><span class="p">.</span><span class="nx">messages</span><span class="p">);</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="c1">// Handle enhanced SMP events</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">otr</span><span class="p">.</span><span class="nx">on</span><span class="p">(</span><span class="s1">&#39;smpStateChanged&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">event</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;SMP state changed:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">event</span><span class="p">.</span><span class="nx">state</span><span class="p">);</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">updateSMPUI</span><span class="p">(</span><span class="nx">event</span><span class="p">);</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="c1">// Handle policy changes</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">policyManager</span><span class="p">.</span><span class="nx">addChangeListener</span><span class="p">((</span><span class="nx">event</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;Policy changed:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">event</span><span class="p">.</span><span class="nx">key</span><span class="p">,</span><span class="w"> </span><span class="nx">event</span><span class="p">.</span><span class="nx">newValue</span><span class="p">);</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">applyPolicyChange</span><span class="p">(</span><span class="nx">event</span><span class="p">);</span>
<span class="w">    </span><span class="p">});</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="migration-from-existing-webotr">
<h2>Migration from Existing WebOTR<a class="headerlink" href="#migration-from-existing-webotr" title="Link to this heading">¶</a></h2>
<p>Migrating from existing WebOTR implementations:</p>
<div class="highlight-mermaid notranslate"><div class="highlight"><pre><span></span>graph TB
    subgraph &quot;Migration Process&quot;
        ASSESS[Assess Current Implementation]
        PLAN[Create Migration Plan]
        BACKUP[Backup Current Configuration]
        INSTALL[Install Phase 3 Features]
        CONFIG[Configure New Features]
        TEST[Test Integration]
        DEPLOY[Deploy to Production]
    end

    subgraph &quot;Migration Considerations&quot;
        COMPAT[Backward Compatibility]
        CONFIG_MIGRATE[Configuration Migration]
        DATA_MIGRATE[Data Migration]
        PERF[Performance Impact]
        SECURITY[Security Enhancements]
    end

    ASSESS --&gt; PLAN
    PLAN --&gt; BACKUP
    BACKUP --&gt; INSTALL
    INSTALL --&gt; CONFIG
    CONFIG --&gt; TEST
    TEST --&gt; DEPLOY

    ASSESS -.-&gt; COMPAT
    PLAN -.-&gt; CONFIG_MIGRATE
    BACKUP -.-&gt; DATA_MIGRATE
    CONFIG -.-&gt; PERF
    TEST -.-&gt; SECURITY
</pre></div>
</div>
<section id="step-by-step-migration">
<h3>Step-by-Step Migration<a class="headerlink" href="#step-by-step-migration" title="Link to this heading">¶</a></h3>
<p><strong>Step 1: Assessment</strong></p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="c1">// Assess current WebOTR usage</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">currentConfig</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">version</span><span class="o">:</span><span class="w"> </span><span class="nx">otr</span><span class="p">.</span><span class="nx">getVersion</span><span class="p">(),</span>
<span class="w">  </span><span class="nx">features</span><span class="o">:</span><span class="w"> </span><span class="nx">otr</span><span class="p">.</span><span class="nx">getSupportedFeatures</span><span class="p">(),</span>
<span class="w">  </span><span class="nx">configuration</span><span class="o">:</span><span class="w"> </span><span class="nx">otr</span><span class="p">.</span><span class="nx">getConfiguration</span><span class="p">(),</span>
<span class="w">  </span><span class="nx">sessions</span><span class="o">:</span><span class="w"> </span><span class="nx">otr</span><span class="p">.</span><span class="nx">getActiveSessions</span><span class="p">()</span>
<span class="p">};</span>

<span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;Current WebOTR configuration:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">currentConfig</span><span class="p">);</span>
</pre></div>
</div>
<p><strong>Step 2: Install Phase 3 Features</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Update WebOTR to include Phase 3 features</span>
npm<span class="w"> </span>install<span class="w"> </span>webotter@latest

<span class="c1"># Or if using yarn</span>
yarn<span class="w"> </span>add<span class="w"> </span>webotter@latest
</pre></div>
</div>
<p><strong>Step 3: Gradual Feature Enablement</strong></p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="c1">// Enable features gradually</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">migrationConfig</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="c1">// Start with version negotiation only</span>
<span class="w">  </span><span class="nx">enableVersionNegotiation</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nx">enableMessageOrdering</span><span class="o">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span><span class="w">  </span><span class="c1">// Enable later</span>
<span class="w">  </span><span class="nx">enableEnhancedSMP</span><span class="o">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span><span class="w">      </span><span class="c1">// Enable later</span>
<span class="w">  </span><span class="nx">enablePolicyManager</span><span class="o">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span><span class="w">    </span><span class="c1">// Enable later</span>

<span class="w">  </span><span class="c1">// Maintain existing configuration</span>
<span class="w">  </span><span class="p">...</span><span class="nx">existingConfig</span>
<span class="p">};</span>

<span class="kd">const</span><span class="w"> </span><span class="nx">otr</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">WebOTR</span><span class="p">(</span><span class="nx">migrationConfig</span><span class="p">);</span>
</pre></div>
</div>
<p><strong>Step 4: Configuration Migration</strong></p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="c1">// Migrate existing configuration to policy manager</span>
<span class="kd">function</span><span class="w"> </span><span class="nx">migrateConfiguration</span><span class="p">(</span><span class="nx">existingConfig</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">policyManager</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">PolicyManager</span><span class="p">();</span>

<span class="w">  </span><span class="c1">// Map existing config to policies</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">configMapping</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="s1">&#39;requireEncryption&#39;</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;security.requireEncryption&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;sessionTimeout&#39;</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;security.sessionTimeoutMs&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;maxBufferSize&#39;</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;protocol.maxBufferSize&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;enableLogging&#39;</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;logging.enableDetailedLogging&#39;</span>
<span class="w">  </span><span class="p">};</span>

<span class="w">  </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="kd">const</span><span class="w"> </span><span class="p">[</span><span class="nx">oldKey</span><span class="p">,</span><span class="w"> </span><span class="nx">newKey</span><span class="p">]</span><span class="w"> </span><span class="k">of</span><span class="w"> </span><span class="nb">Object</span><span class="p">.</span><span class="nx">entries</span><span class="p">(</span><span class="nx">configMapping</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">existingConfig</span><span class="p">[</span><span class="nx">oldKey</span><span class="p">]</span><span class="w"> </span><span class="o">!==</span><span class="w"> </span><span class="kc">undefined</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">policyManager</span><span class="p">.</span><span class="nx">setPolicy</span><span class="p">(</span><span class="nx">newKey</span><span class="p">,</span><span class="w"> </span><span class="nx">existingConfig</span><span class="p">[</span><span class="nx">oldKey</span><span class="p">],</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">source</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;migration&#39;</span><span class="p">,</span>
<span class="w">        </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="nb">Date</span><span class="p">.</span><span class="nx">now</span><span class="p">()</span>
<span class="w">      </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="nx">policyManager</span><span class="p">;</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Step 5: Testing and Validation</strong></p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="c1">// Comprehensive testing after migration</span>
<span class="k">async</span><span class="w"> </span><span class="kd">function</span><span class="w"> </span><span class="nx">validateMigration</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">tests</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="nx">testVersionNegotiation</span><span class="p">,</span>
<span class="w">    </span><span class="nx">testMessageOrdering</span><span class="p">,</span>
<span class="w">    </span><span class="nx">testEnhancedSMP</span><span class="p">,</span>
<span class="w">    </span><span class="nx">testPolicyManager</span><span class="p">,</span>
<span class="w">    </span><span class="nx">testBackwardCompatibility</span>
<span class="w">  </span><span class="p">];</span>

<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">results</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[];</span>
<span class="w">  </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="kd">const</span><span class="w"> </span><span class="nx">test</span><span class="w"> </span><span class="k">of</span><span class="w"> </span><span class="nx">tests</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">test</span><span class="p">();</span>
<span class="w">      </span><span class="nx">results</span><span class="p">.</span><span class="nx">push</span><span class="p">({</span><span class="w"> </span><span class="nx">test</span><span class="o">:</span><span class="w"> </span><span class="nx">test</span><span class="p">.</span><span class="nx">name</span><span class="p">,</span><span class="w"> </span><span class="nx">status</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;PASS&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">result</span><span class="w"> </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">results</span><span class="p">.</span><span class="nx">push</span><span class="p">({</span><span class="w"> </span><span class="nx">test</span><span class="o">:</span><span class="w"> </span><span class="nx">test</span><span class="p">.</span><span class="nx">name</span><span class="p">,</span><span class="w"> </span><span class="nx">status</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;FAIL&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="nx">error</span><span class="p">.</span><span class="nx">message</span><span class="w"> </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="nx">results</span><span class="p">;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="configuration-patterns">
<h2>Configuration Patterns<a class="headerlink" href="#configuration-patterns" title="Link to this heading">¶</a></h2>
<p>Common configuration patterns for different use cases:</p>
<section id="enterprise-deployment">
<h3>Enterprise Deployment<a class="headerlink" href="#enterprise-deployment" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">const</span><span class="w"> </span><span class="nx">enterpriseConfig</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">policyManager</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">validationLevel</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;enterprise&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">enableAccessControl</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nx">enableAuditLogging</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nx">userRoles</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;user&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;admin&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;security_officer&#39;</span><span class="p">]</span>
<span class="w">  </span><span class="p">},</span>

<span class="w">  </span><span class="nx">security</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="s1">&#39;security.requireEncryption&#39;</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;security.allowV2&#39;</span><span class="o">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span><span class="w">  </span><span class="c1">// Only OTR v3</span>
<span class="w">    </span><span class="s1">&#39;security.sessionTimeoutMs&#39;</span><span class="o">:</span><span class="w"> </span><span class="mf">1800000</span><span class="p">,</span><span class="w">  </span><span class="c1">// 30 minutes</span>
<span class="w">    </span><span class="s1">&#39;security.enableSecurityValidation&#39;</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">  </span><span class="p">},</span>

<span class="w">  </span><span class="nx">protocol</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="s1">&#39;protocol.maxBufferSize&#39;</span><span class="o">:</span><span class="w"> </span><span class="mf">200</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;protocol.replayWindowSize&#39;</span><span class="o">:</span><span class="w"> </span><span class="mf">128</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;protocol.enableStatePersistence&#39;</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">  </span><span class="p">},</span>

<span class="w">  </span><span class="nx">logging</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="s1">&#39;logging.enableSecurityAudit&#39;</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;logging.logLevel&#39;</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;warn&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;logging.maxLogSize&#39;</span><span class="o">:</span><span class="w"> </span><span class="mf">104857600</span><span class="w">  </span><span class="c1">// 100MB</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">};</span>
</pre></div>
</div>
</section>
<section id="development-environment">
<h3>Development Environment<a class="headerlink" href="#development-environment" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">const</span><span class="w"> </span><span class="nx">developmentConfig</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">policyManager</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">validationLevel</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;basic&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">enableAccessControl</span><span class="o">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span>
<span class="w">    </span><span class="nx">enableAuditLogging</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">  </span><span class="p">},</span>

<span class="w">  </span><span class="nx">security</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="s1">&#39;security.requireEncryption&#39;</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;security.allowV2&#39;</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span><span class="w">  </span><span class="c1">// Allow both versions</span>
<span class="w">    </span><span class="s1">&#39;security.sessionTimeoutMs&#39;</span><span class="o">:</span><span class="w"> </span><span class="mf">300000</span><span class="p">,</span><span class="w">  </span><span class="c1">// 5 minutes</span>
<span class="w">    </span><span class="s1">&#39;security.enableSecurityValidation&#39;</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">  </span><span class="p">},</span>

<span class="w">  </span><span class="nx">protocol</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="s1">&#39;protocol.maxBufferSize&#39;</span><span class="o">:</span><span class="w"> </span><span class="mf">50</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;protocol.replayWindowSize&#39;</span><span class="o">:</span><span class="w"> </span><span class="mf">32</span>
<span class="w">  </span><span class="p">},</span>

<span class="w">  </span><span class="nx">logging</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="s1">&#39;logging.enableDetailedLogging&#39;</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;logging.logLevel&#39;</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;debug&#39;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">};</span>
</pre></div>
</div>
</section>
<section id="mobile-application">
<h3>Mobile Application<a class="headerlink" href="#mobile-application" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">const</span><span class="w"> </span><span class="nx">mobileConfig</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">policyManager</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">validationLevel</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;strict&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">enableAccessControl</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">  </span><span class="p">},</span>

<span class="w">  </span><span class="nx">security</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="s1">&#39;security.sessionTimeoutMs&#39;</span><span class="o">:</span><span class="w"> </span><span class="mf">600000</span><span class="p">,</span><span class="w">  </span><span class="c1">// 10 minutes</span>
<span class="w">    </span><span class="s1">&#39;security.enableSecurityValidation&#39;</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">  </span><span class="p">},</span>

<span class="w">  </span><span class="nx">protocol</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="s1">&#39;protocol.maxBufferSize&#39;</span><span class="o">:</span><span class="w"> </span><span class="mf">25</span><span class="p">,</span><span class="w">  </span><span class="c1">// Smaller buffer for memory</span>
<span class="w">    </span><span class="s1">&#39;protocol.replayWindowSize&#39;</span><span class="o">:</span><span class="w"> </span><span class="mf">32</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;protocol.gapTimeoutMs&#39;</span><span class="o">:</span><span class="w"> </span><span class="mf">15000</span><span class="w">  </span><span class="c1">// Shorter timeout</span>
<span class="w">  </span><span class="p">},</span>

<span class="w">  </span><span class="nx">performance</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="s1">&#39;performance.enableOptimizations&#39;</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;performance.memoryPoolSize&#39;</span><span class="o">:</span><span class="w"> </span><span class="mf">524288</span><span class="p">,</span><span class="w">  </span><span class="c1">// 512KB</span>
<span class="w">    </span><span class="s1">&#39;performance.enableCaching&#39;</span><span class="o">:</span><span class="w"> </span><span class="kc">false</span><span class="w">  </span><span class="c1">// Save memory</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">};</span>
</pre></div>
</div>
</section>
</section>
<section id="event-handling">
<h2>Event Handling<a class="headerlink" href="#event-handling" title="Link to this heading">¶</a></h2>
<p>Comprehensive event handling for Phase 3 features:</p>
<div class="highlight-mermaid notranslate"><div class="highlight"><pre><span></span>graph TB
    subgraph &quot;Event Categories&quot;
        VN_EVENTS[Version Negotiation Events]
        MO_EVENTS[Message Ordering Events]
        SMP_EVENTS[Enhanced SMP Events]
        POLICY_EVENTS[Policy Manager Events]
    end

    subgraph &quot;Event Handlers&quot;
        VN_HANDLER[Version Handler]
        MO_HANDLER[Ordering Handler]
        SMP_HANDLER[SMP Handler]
        POLICY_HANDLER[Policy Handler]
    end

    subgraph &quot;Application Actions&quot;
        UI_UPDATE[Update UI]
        LOG_EVENT[Log Event]
        NOTIFY_USER[Notify User]
        UPDATE_CONFIG[Update Configuration]
    end

    VN_EVENTS --&gt; VN_HANDLER
    MO_EVENTS --&gt; MO_HANDLER
    SMP_EVENTS --&gt; SMP_HANDLER
    POLICY_EVENTS --&gt; POLICY_HANDLER

    VN_HANDLER --&gt; UI_UPDATE
    MO_HANDLER --&gt; LOG_EVENT
    SMP_HANDLER --&gt; NOTIFY_USER
    POLICY_HANDLER --&gt; UPDATE_CONFIG
</pre></div>
</div>
<section id="event-handler-implementation">
<h3>Event Handler Implementation<a class="headerlink" href="#event-handler-implementation" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">class</span><span class="w"> </span><span class="nx">WebOTREventHandler</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span><span class="nx">otr</span><span class="p">,</span><span class="w"> </span><span class="nx">ui</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">otr</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">otr</span><span class="p">;</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">ui</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">ui</span><span class="p">;</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">setupEventHandlers</span><span class="p">();</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">setupEventHandlers</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Version negotiation events</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">otr</span><span class="p">.</span><span class="nx">on</span><span class="p">(</span><span class="s1">&#39;versionNegotiationStarted&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">event</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">ui</span><span class="p">.</span><span class="nx">showStatus</span><span class="p">(</span><span class="s1">&#39;Negotiating OTR version...&#39;</span><span class="p">);</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">otr</span><span class="p">.</span><span class="nx">on</span><span class="p">(</span><span class="s1">&#39;versionNegotiated&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">event</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">ui</span><span class="p">.</span><span class="nx">showStatus</span><span class="p">(</span><span class="sb">`Using OTR v</span><span class="si">${</span><span class="nx">event</span><span class="p">.</span><span class="nx">version</span><span class="si">}</span><span class="sb">`</span><span class="p">);</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">ui</span><span class="p">.</span><span class="nx">updateCapabilities</span><span class="p">(</span><span class="nx">event</span><span class="p">.</span><span class="nx">capabilities</span><span class="p">);</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">otr</span><span class="p">.</span><span class="nx">on</span><span class="p">(</span><span class="s1">&#39;versionNegotiationFailed&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">event</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">ui</span><span class="p">.</span><span class="nx">showError</span><span class="p">(</span><span class="sb">`Version negotiation failed: </span><span class="si">${</span><span class="nx">event</span><span class="p">.</span><span class="nx">reason</span><span class="si">}</span><span class="sb">`</span><span class="p">);</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="c1">// Message ordering events</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">otr</span><span class="p">.</span><span class="nx">on</span><span class="p">(</span><span class="s1">&#39;messageBuffered&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">event</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">ui</span><span class="p">.</span><span class="nx">showStatus</span><span class="p">(</span><span class="sb">`Message </span><span class="si">${</span><span class="nx">event</span><span class="p">.</span><span class="nx">sequence</span><span class="si">}</span><span class="sb"> buffered (waiting for </span><span class="si">${</span><span class="nx">event</span><span class="p">.</span><span class="nx">expected</span><span class="si">}</span><span class="sb">)`</span><span class="p">);</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">otr</span><span class="p">.</span><span class="nx">on</span><span class="p">(</span><span class="s1">&#39;messagesReordered&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">event</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">ui</span><span class="p">.</span><span class="nx">showStatus</span><span class="p">(</span><span class="sb">`</span><span class="si">${</span><span class="nx">event</span><span class="p">.</span><span class="nx">count</span><span class="si">}</span><span class="sb"> messages reordered`</span><span class="p">);</span>
<span class="w">      </span><span class="nx">event</span><span class="p">.</span><span class="nx">messages</span><span class="p">.</span><span class="nx">forEach</span><span class="p">(</span><span class="nx">msg</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">ui</span><span class="p">.</span><span class="nx">displayMessage</span><span class="p">(</span><span class="nx">msg</span><span class="p">));</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">otr</span><span class="p">.</span><span class="nx">on</span><span class="p">(</span><span class="s1">&#39;replayDetected&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">event</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">ui</span><span class="p">.</span><span class="nx">showWarning</span><span class="p">(</span><span class="sb">`Replay attack detected: message </span><span class="si">${</span><span class="nx">event</span><span class="p">.</span><span class="nx">sequence</span><span class="si">}</span><span class="sb">`</span><span class="p">);</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="c1">// Enhanced SMP events</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">otr</span><span class="p">.</span><span class="nx">on</span><span class="p">(</span><span class="s1">&#39;smpSessionStarted&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">event</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">ui</span><span class="p">.</span><span class="nx">showSMPDialog</span><span class="p">(</span><span class="nx">event</span><span class="p">.</span><span class="nx">sessionId</span><span class="p">,</span><span class="w"> </span><span class="nx">event</span><span class="p">.</span><span class="nx">question</span><span class="p">);</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">otr</span><span class="p">.</span><span class="nx">on</span><span class="p">(</span><span class="s1">&#39;smpSessionPaused&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">event</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">ui</span><span class="p">.</span><span class="nx">showStatus</span><span class="p">(</span><span class="sb">`SMP session paused: </span><span class="si">${</span><span class="nx">event</span><span class="p">.</span><span class="nx">reason</span><span class="si">}</span><span class="sb">`</span><span class="p">);</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">otr</span><span class="p">.</span><span class="nx">on</span><span class="p">(</span><span class="s1">&#39;smpSessionCompleted&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">event</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">ui</span><span class="p">.</span><span class="nx">showSMPResult</span><span class="p">(</span><span class="nx">event</span><span class="p">.</span><span class="nx">result</span><span class="p">,</span><span class="w"> </span><span class="nx">event</span><span class="p">.</span><span class="nx">sessionId</span><span class="p">);</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">otr</span><span class="p">.</span><span class="nx">on</span><span class="p">(</span><span class="s1">&#39;smpSessionAborted&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">event</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">ui</span><span class="p">.</span><span class="nx">showError</span><span class="p">(</span><span class="sb">`SMP aborted: </span><span class="si">${</span><span class="nx">event</span><span class="p">.</span><span class="nx">reason</span><span class="si">}</span><span class="sb">`</span><span class="p">);</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="c1">// Policy manager events</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">otr</span><span class="p">.</span><span class="nx">policyManager</span><span class="p">.</span><span class="nx">addChangeListener</span><span class="p">((</span><span class="nx">event</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">handlePolicyChange</span><span class="p">(</span><span class="nx">event</span><span class="p">);</span>
<span class="w">    </span><span class="p">});</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">handlePolicyChange</span><span class="p">(</span><span class="nx">event</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Handle security policy changes</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">event</span><span class="p">.</span><span class="nx">key</span><span class="p">.</span><span class="nx">startsWith</span><span class="p">(</span><span class="s1">&#39;security.&#39;</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">ui</span><span class="p">.</span><span class="nx">showSecurityNotification</span><span class="p">(</span><span class="sb">`Security policy updated: </span><span class="si">${</span><span class="nx">event</span><span class="p">.</span><span class="nx">key</span><span class="si">}</span><span class="sb">`</span><span class="p">);</span>

<span class="w">      </span><span class="c1">// Apply immediate changes if needed</span>
<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">event</span><span class="p">.</span><span class="nx">key</span><span class="w"> </span><span class="o">===</span><span class="w"> </span><span class="s1">&#39;security.sessionTimeoutMs&#39;</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">otr</span><span class="p">.</span><span class="nx">updateSessionTimeout</span><span class="p">(</span><span class="nx">event</span><span class="p">.</span><span class="nx">newValue</span><span class="p">);</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="c1">// Handle protocol policy changes</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">event</span><span class="p">.</span><span class="nx">key</span><span class="p">.</span><span class="nx">startsWith</span><span class="p">(</span><span class="s1">&#39;protocol.&#39;</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">ui</span><span class="p">.</span><span class="nx">showStatus</span><span class="p">(</span><span class="sb">`Protocol configuration updated: </span><span class="si">${</span><span class="nx">event</span><span class="p">.</span><span class="nx">key</span><span class="si">}</span><span class="sb">`</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="c1">// Log all policy changes</span>
<span class="w">    </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;Policy changed:&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">key</span><span class="o">:</span><span class="w"> </span><span class="nx">event</span><span class="p">.</span><span class="nx">key</span><span class="p">,</span>
<span class="w">      </span><span class="nx">oldValue</span><span class="o">:</span><span class="w"> </span><span class="nx">event</span><span class="p">.</span><span class="nx">oldValue</span><span class="p">,</span>
<span class="w">      </span><span class="nx">newValue</span><span class="o">:</span><span class="w"> </span><span class="nx">event</span><span class="p">.</span><span class="nx">newValue</span><span class="p">,</span>
<span class="w">      </span><span class="nx">user</span><span class="o">:</span><span class="w"> </span><span class="nx">event</span><span class="p">.</span><span class="nx">context</span><span class="p">.</span><span class="nx">user</span><span class="p">,</span>
<span class="w">      </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="nx">event</span><span class="p">.</span><span class="nx">timestamp</span>
<span class="w">    </span><span class="p">});</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="performance-optimization">
<h2>Performance Optimization<a class="headerlink" href="#performance-optimization" title="Link to this heading">¶</a></h2>
<p>Optimizing Phase 3 features for different environments:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="c1">// Performance optimization configuration</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">performanceConfig</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="c1">// Message ordering optimization</span>
<span class="w">  </span><span class="nx">messageOrdering</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">maxBufferSize</span><span class="o">:</span><span class="w"> </span><span class="mf">50</span><span class="p">,</span><span class="w">        </span><span class="c1">// Smaller buffer for memory-constrained environments</span>
<span class="w">    </span><span class="nx">cleanupIntervalMs</span><span class="o">:</span><span class="w"> </span><span class="mf">30000</span><span class="p">,</span><span class="w"> </span><span class="c1">// More frequent cleanup</span>
<span class="w">    </span><span class="nx">gapTimeoutMs</span><span class="o">:</span><span class="w"> </span><span class="mf">15000</span><span class="w">      </span><span class="c1">// Shorter gap timeout</span>
<span class="w">  </span><span class="p">},</span>

<span class="w">  </span><span class="c1">// Enhanced SMP optimization</span>
<span class="w">  </span><span class="nx">enhancedSMP</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">enableStatePersistence</span><span class="o">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span><span class="w">  </span><span class="c1">// Disable for performance</span>
<span class="w">    </span><span class="nx">sessionTimeoutMs</span><span class="o">:</span><span class="w"> </span><span class="mf">180000</span><span class="p">,</span><span class="w">       </span><span class="c1">// Shorter timeout</span>
<span class="w">    </span><span class="nx">maxRetryAttempts</span><span class="o">:</span><span class="w"> </span><span class="mf">2</span><span class="w">             </span><span class="c1">// Fewer retries</span>
<span class="w">  </span><span class="p">},</span>

<span class="w">  </span><span class="c1">// Policy manager optimization</span>
<span class="w">  </span><span class="nx">policyManager</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">validationLevel</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;basic&#39;</span><span class="p">,</span><span class="w">       </span><span class="c1">// Less validation overhead</span>
<span class="w">    </span><span class="nx">enableAuditLogging</span><span class="o">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span><span class="w">      </span><span class="c1">// Disable for performance</span>
<span class="w">    </span><span class="nx">maxPolicySize</span><span class="o">:</span><span class="w"> </span><span class="mf">524288</span><span class="w">           </span><span class="c1">// Smaller policy size limit</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">};</span>
</pre></div>
</div>
</section>
<section id="troubleshooting">
<h2>Troubleshooting<a class="headerlink" href="#troubleshooting" title="Link to this heading">¶</a></h2>
<p>Common integration issues and solutions:</p>
<p><strong>Version Negotiation Issues</strong></p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="c1">// Debug version negotiation</span>
<span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="nx">otr</span><span class="p">.</span><span class="nx">isVersionNegotiated</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">debug</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">otr</span><span class="p">.</span><span class="nx">getVersionNegotiationDebug</span><span class="p">();</span>
<span class="w">  </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;Version negotiation debug:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">debug</span><span class="p">);</span>

<span class="w">  </span><span class="c1">// Check policy restrictions</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">policies</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">otr</span><span class="p">.</span><span class="nx">policyManager</span><span class="p">.</span><span class="nx">getEffectivePolicy</span><span class="p">();</span>
<span class="w">  </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;Version policies:&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">allowV2</span><span class="o">:</span><span class="w"> </span><span class="nx">policies</span><span class="p">.</span><span class="nx">security</span><span class="p">.</span><span class="nx">allowV2</span><span class="p">,</span>
<span class="w">    </span><span class="nx">allowV3</span><span class="o">:</span><span class="w"> </span><span class="nx">policies</span><span class="p">.</span><span class="nx">security</span><span class="p">.</span><span class="nx">allowV3</span><span class="p">,</span>
<span class="w">    </span><span class="nx">preferV3</span><span class="o">:</span><span class="w"> </span><span class="nx">policies</span><span class="p">.</span><span class="nx">security</span><span class="p">.</span><span class="nx">preferV3</span>
<span class="w">  </span><span class="p">});</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Message Ordering Issues</strong></p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="c1">// Debug message ordering</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">orderingStats</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">otr</span><span class="p">.</span><span class="nx">messageOrdering</span><span class="p">.</span><span class="nx">getStats</span><span class="p">();</span>
<span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;Message ordering stats:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">orderingStats</span><span class="p">);</span>

<span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">orderingStats</span><span class="p">.</span><span class="nx">duplicatesDetected</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mf">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">console</span><span class="p">.</span><span class="nx">warn</span><span class="p">(</span><span class="s1">&#39;Replay attacks detected:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">orderingStats</span><span class="p">.</span><span class="nx">duplicatesDetected</span><span class="p">);</span>
<span class="p">}</span>

<span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">orderingStats</span><span class="p">.</span><span class="nx">gapsDetected</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mf">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">console</span><span class="p">.</span><span class="nx">warn</span><span class="p">(</span><span class="s1">&#39;Message gaps detected:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">orderingStats</span><span class="p">.</span><span class="nx">gapsDetected</span><span class="p">);</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Policy Manager Issues</strong></p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="c1">// Debug policy issues</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">policyStats</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">otr</span><span class="p">.</span><span class="nx">policyManager</span><span class="p">.</span><span class="nx">getStats</span><span class="p">();</span>
<span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;Policy manager stats:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">policyStats</span><span class="p">);</span>

<span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">policyStats</span><span class="p">.</span><span class="nx">accessDenials</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mf">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">console</span><span class="p">.</span><span class="nx">warn</span><span class="p">(</span><span class="s1">&#39;Policy access denials:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">policyStats</span><span class="p">.</span><span class="nx">accessDenials</span><span class="p">);</span>

<span class="w">  </span><span class="c1">// Check user roles and permissions</span>
<span class="w">  </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;Current user:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">otr</span><span class="p">.</span><span class="nx">policyManager</span><span class="p">.</span><span class="nx">currentUser</span><span class="p">);</span>
<span class="w">  </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;User roles:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nb">Array</span><span class="p">.</span><span class="kr">from</span><span class="p">(</span><span class="nx">otr</span><span class="p">.</span><span class="nx">policyManager</span><span class="p">.</span><span class="nx">userRoles</span><span class="p">));</span>
<span class="p">}</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This integration guide provides comprehensive instructions for implementing Phase 3 features. For specific use cases or advanced configurations, refer to the individual component documentation and API references.</p>
</div>
</section>
</section>

        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          <a class="next-page" href="troubleshooting.html">
              <div class="page-info">
                <div class="context">
                  <span>Next</span>
                </div>
                <div class="title">Troubleshooting Guide</div>
              </div>
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
            </a>
          <a class="prev-page" href="policy-manager.html">
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
              <div class="page-info">
                <div class="context">
                  <span>Previous</span>
                </div>
                
                <div class="title">Policy Manager</div>
                
              </div>
            </a>
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 1980, WebOTR Team
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer">
      
      
      <div class="toc-sticky toc-scroll">
        <div class="toc-title-container">
          <span class="toc-title">
            On this page
          </span>
        </div>
        <div class="toc-tree-container">
          <div class="toc-tree">
            <ul>
<li><a class="reference internal" href="#">Integration Guide</a><ul>
<li><a class="reference internal" href="#integration-overview">Integration Overview</a></li>
<li><a class="reference internal" href="#quick-start">Quick Start</a><ul>
<li><a class="reference internal" href="#basic-integration">Basic Integration</a></li>
<li><a class="reference internal" href="#advanced-integration">Advanced Integration</a></li>
</ul>
</li>
<li><a class="reference internal" href="#migration-from-existing-webotr">Migration from Existing WebOTR</a><ul>
<li><a class="reference internal" href="#step-by-step-migration">Step-by-Step Migration</a></li>
</ul>
</li>
<li><a class="reference internal" href="#configuration-patterns">Configuration Patterns</a><ul>
<li><a class="reference internal" href="#enterprise-deployment">Enterprise Deployment</a></li>
<li><a class="reference internal" href="#development-environment">Development Environment</a></li>
<li><a class="reference internal" href="#mobile-application">Mobile Application</a></li>
</ul>
</li>
<li><a class="reference internal" href="#event-handling">Event Handling</a><ul>
<li><a class="reference internal" href="#event-handler-implementation">Event Handler Implementation</a></li>
</ul>
</li>
<li><a class="reference internal" href="#performance-optimization">Performance Optimization</a></li>
<li><a class="reference internal" href="#troubleshooting">Troubleshooting</a></li>
</ul>
</li>
</ul>

          </div>
        </div>
      </div>
      
      
    </aside>
  </div>
</div><script src="../_static/documentation_options.js?v=01f34227"></script>
    <script src="../_static/doctools.js?v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/scripts/furo.js?v=5fa4622c"></script>
    <script src="../_static/mermaid-renderer.js?v=b683124c"></script>
    </body>
</html>