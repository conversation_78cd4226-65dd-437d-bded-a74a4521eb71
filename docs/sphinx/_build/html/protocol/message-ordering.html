<!doctype html>
<html class="no-js" lang="en" data-content_root="../">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><meta name="viewport" content="width=device-width, initial-scale=1" />
<link rel="index" title="Index" href="../genindex.html" /><link rel="search" title="Search" href="../search.html" /><link rel="next" title="Enhanced SMP" href="enhanced-smp.html" /><link rel="prev" title="Version Negotiation" href="version-negotiation.html" />

    <link rel="shortcut icon" href="../_static/favicon.svg"/><!-- Generated with Sphinx 7.4.7 and Furo 2024.08.06 -->
        <title>Message Ordering - WebOTR Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../_static/styles/furo.css?v=354aac6f" />
    <link rel="stylesheet" type="text/css" href="../_static/styles/furo-extensions.css?v=302659d7" />
    <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=21a7f706" />
    
    


<style>
  body {
    --color-code-background: #eeffcc;
  --color-code-foreground: black;
  --color-brand-primary: #6366f1;
  --color-brand-content: #6366f1;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8b5cf6;
  --color-brand-content: #8b5cf6;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #272822;
  --color-code-foreground: #f8f8f2;
  --color-brand-primary: #8b5cf6;
  --color-brand-content: #8b5cf6;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-with-moon" viewBox="0 0 24 24">
    <title>Auto light/dark, in light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
      class="icon-custom-derived-from-feather-sun-and-tabler-moon">
      <path style="opacity: 50%" d="M 5.411 14.504 C 5.471 14.504 5.532 14.504 5.591 14.504 C 3.639 16.319 4.383 19.569 6.931 20.352 C 7.693 20.586 8.512 20.551 9.25 20.252 C 8.023 23.207 4.056 23.725 2.11 21.184 C 0.166 18.642 1.702 14.949 4.874 14.536 C 5.051 14.512 5.231 14.5 5.411 14.5 L 5.411 14.504 Z"/>
      <line x1="14.5" y1="3.25" x2="14.5" y2="1.25"/>
      <line x1="14.5" y1="15.85" x2="14.5" y2="17.85"/>
      <line x1="10.044" y1="5.094" x2="8.63" y2="3.68"/>
      <line x1="19" y1="14.05" x2="20.414" y2="15.464"/>
      <line x1="8.2" y1="9.55" x2="6.2" y2="9.55"/>
      <line x1="20.8" y1="9.55" x2="22.8" y2="9.55"/>
      <line x1="10.044" y1="14.006" x2="8.63" y2="15.42"/>
      <line x1="19" y1="5.05" x2="20.414" y2="3.636"/>
      <circle cx="14.5" cy="9.55" r="3.6"/>
    </svg>
  </symbol>
  <symbol id="svg-moon-with-sun" viewBox="0 0 24 24">
    <title>Auto light/dark, in dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
      class="icon-custom-derived-from-feather-sun-and-tabler-moon">
      <path d="M 8.282 7.007 C 8.385 7.007 8.494 7.007 8.595 7.007 C 5.18 10.184 6.481 15.869 10.942 17.24 C 12.275 17.648 13.706 17.589 15 17.066 C 12.851 22.236 5.91 23.143 2.505 18.696 C -0.897 14.249 1.791 7.786 7.342 7.063 C 7.652 7.021 7.965 7 8.282 7 L 8.282 7.007 Z"/>
      <line style="opacity: 50%" x1="18" y1="3.705" x2="18" y2="2.5"/>
      <line style="opacity: 50%" x1="18" y1="11.295" x2="18" y2="12.5"/>
      <line style="opacity: 50%" x1="15.316" y1="4.816" x2="14.464" y2="3.964"/>
      <line style="opacity: 50%" x1="20.711" y1="10.212" x2="21.563" y2="11.063"/>
      <line style="opacity: 50%" x1="14.205" y1="7.5" x2="13.001" y2="7.5"/>
      <line style="opacity: 50%" x1="21.795" y1="7.5" x2="23" y2="7.5"/>
      <line style="opacity: 50%" x1="15.316" y1="10.184" x2="14.464" y2="11.036"/>
      <line style="opacity: 50%" x1="20.711" y1="4.789" x2="21.563" y2="3.937"/>
      <circle style="opacity: 50%" cx="18" cy="7.5" r="2.169"/>
    </svg>
  </symbol>
  <symbol id="svg-pencil" viewBox="0 0 24 24">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-pencil-code">
      <path d="M4 20h4l10.5 -10.5a2.828 2.828 0 1 0 -4 -4l-10.5 10.5v4" />
      <path d="M13.5 6.5l4 4" />
      <path d="M20 21l2 -2l-2 -2" />
      <path d="M17 17l-2 2l2 2" />
    </svg>
  </symbol>
  <symbol id="svg-eye" viewBox="0 0 24 24">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-eye-code">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0" />
      <path
        d="M11.11 17.958c-3.209 -.307 -5.91 -2.293 -8.11 -5.958c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6c-.21 .352 -.427 .688 -.647 1.008" />
      <path d="M20 21l2 -2l-2 -2" />
      <path d="M17 17l-2 2l2 2" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>

<a class="skip-to-content muted-link" href="#furo-main-content">Skip to content</a>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="../index.html"><div class="brand">WebOTR Documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto-light"><use href="#svg-sun-with-moon"></use></svg>
          <svg class="theme-icon-when-auto-dark"><use href="#svg-moon-with-sun"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><a class="sidebar-brand" href="../index.html">
  
  <div class="sidebar-logo-container">
    <img class="sidebar-logo" src="../_static/logo.svg" alt="Logo"/>
  </div>
  
  <span class="sidebar-brand-text">WebOTR Documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="../search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-scroll"><div class="sidebar-tree">
  <p class="caption" role="heading"><span class="caption-text">User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user-guide/installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../user-guide/getting-started.html">Getting Started</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../developer-guide/architecture.html">Architecture Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../developer-guide/contributing.html">Contributing to WebOTR</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Protocol Compliance &amp; Advanced Features</span></p>
<ul class="current">
<li class="toctree-l1 current has-children"><a class="reference internal" href="index.html">Protocol Compliance &amp; Advanced Features</a><input checked="" class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" role="switch" type="checkbox"/><label for="toctree-checkbox-1"><div class="visually-hidden">Toggle navigation of Protocol Compliance &amp; Advanced Features</div><i class="icon"><svg><use href="#svg-arrow-right"></use></svg></i></label><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="overview.html">Protocol Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="version-negotiation.html">Version Negotiation</a></li>
<li class="toctree-l2 current current-page"><a class="current reference internal" href="#">Message Ordering</a></li>
<li class="toctree-l2"><a class="reference internal" href="enhanced-smp.html">Enhanced SMP</a></li>
<li class="toctree-l2"><a class="reference internal" href="policy-manager.html">Policy Manager</a></li>
<li class="toctree-l2"><a class="reference internal" href="integration-guide.html">Integration Guide</a></li>
<li class="toctree-l2"><a class="reference internal" href="troubleshooting.html">Troubleshooting Guide</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Security</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../security/overview.html">Security Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/libotr-enhancements-summary.html">libOTR Security Enhancements Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/libotr-enhancements.html">libOTR Security Enhancements</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/libotr-enhancements-implementation.html">libOTR Enhancements Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/libotr-enhancements-api.html">libOTR Enhancements API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/libotr-enhancements-testing.html">libOTR Enhancements Testing and Validation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/forward-secrecy-summary.html">Forward Secrecy Documentation Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/forward-secrecy.html">Forward Secrecy Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/forward-secrecy-architecture.html">Forward Secrecy Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/forward-secrecy-implementation.html">Forward Secrecy Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/forward-secrecy-api.html">Forward Secrecy API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/ake-summary.html">AKE Documentation Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/ake.html">Authenticated Key Exchange (AKE)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/ake-architecture.html">AKE Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/ake-implementation.html">AKE Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/ake-api.html">AKE API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/steganography-summary.html">Steganography Documentation Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/steganography.html">Steganography Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/steganography-architecture.html">Steganography Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/steganography-implementation.html">Steganography Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../security/steganography-api.html">Steganography API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../reference/faq.html">Frequently Asked Questions</a></li>
<li class="toctree-l1"><a class="reference internal" href="../reference/changelog.html">Changelog</a></li>
</ul>

</div>
</div>

      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          <div class="view-this-page">
  <a class="muted-link" href="https://github.com/forkrul/webOTteR/blob/main/docs/sphinx/protocol/message-ordering.rst?plain=true" title="View this page">
    <svg><use href="#svg-eye"></use></svg>
    <span class="visually-hidden">View this page</span>
  </a>
</div><div class="edit-this-page">
  <a class="muted-link" href="https://github.com/forkrul/webOTteR/edit/main/docs/sphinx/protocol/message-ordering.rst" title="Edit this page">
    <svg><use href="#svg-pencil"></use></svg>
    <span class="visually-hidden">Edit this page</span>
  </a>
</div><div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto-light"><use href="#svg-sun-with-moon"></use></svg>
              <svg class="theme-icon-when-auto-dark"><use href="#svg-moon-with-sun"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main" id="furo-main-content">
          <section id="message-ordering">
<h1>Message Ordering<a class="headerlink" href="#message-ordering" title="Link to this heading">¶</a></h1>
<p>The Message Ordering module provides robust handling of out-of-order messages, replay protection, and sequence validation to ensure reliable and secure message delivery in OTR sessions.</p>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading">¶</a></h2>
<p>Message ordering is critical for maintaining the integrity and security of OTR communications, especially in unreliable network conditions where messages may arrive out of order or be duplicated.</p>
<div class="highlight-mermaid notranslate"><div class="highlight"><pre><span></span>graph TB
    subgraph &quot;Message Ordering System&quot;
        IN[Incoming Message]
        SV[Sequence Validation]
        RP[Replay Protection]
        OO[Out-of-Order Handling]
        BUF[Message Buffer]
        PROC[Process Message]
        OUT[Ordered Output]
    end

    IN --&gt; SV
    SV --&gt; RP
    RP --&gt; OO
    OO --&gt; BUF
    BUF --&gt; PROC
    PROC --&gt; OUT
</pre></div>
</div>
</section>
<section id="core-features">
<h2>Core Features<a class="headerlink" href="#core-features" title="Link to this heading">¶</a></h2>
<section id="sequence-number-validation">
<h3>Sequence Number Validation<a class="headerlink" href="#sequence-number-validation" title="Link to this heading">¶</a></h3>
<p>Every message includes a sequence number that must be validated:</p>
<div class="highlight-mermaid notranslate"><div class="highlight"><pre><span></span>graph LR
    subgraph &quot;Sequence Validation&quot;
        MSG[Message with Seq N]
        EXP[Expected Seq M]
        CMP{N vs M}
        IN_ORDER[N = M: In Order]
        FUTURE[N &gt; M: Future Message]
        PAST[N &lt; M: Past Message]
    end

    MSG --&gt; CMP
    EXP --&gt; CMP
    CMP --&gt; IN_ORDER
    CMP --&gt; FUTURE
    CMP --&gt; PAST
</pre></div>
</div>
</section>
<section id="replay-protection">
<h3>Replay Protection<a class="headerlink" href="#replay-protection" title="Link to this heading">¶</a></h3>
<p>The system uses a sliding window to detect and prevent replay attacks:</p>
<div class="highlight-mermaid notranslate"><div class="highlight"><pre><span></span>graph TB
    subgraph &quot;Sliding Window Replay Protection&quot;
        subgraph &quot;Window State&quot;
            BASE[Window Base: 100]
            SIZE[Window Size: 64]
            END[Window End: 164]
        end

        subgraph &quot;Message Validation&quot;
            MSG[Message Seq: 150]
            CHECK{In Window?}
            SEEN{Already Seen?}
            ACCEPT[Accept Message]
            REJECT[Reject Replay]
        end
    end

    MSG --&gt; CHECK
    CHECK --&gt;|Yes| SEEN
    CHECK --&gt;|No| REJECT
    SEEN --&gt;|No| ACCEPT
    SEEN --&gt;|Yes| REJECT
</pre></div>
</div>
</section>
<section id="out-of-order-message-handling">
<h3>Out-of-Order Message Handling<a class="headerlink" href="#out-of-order-message-handling" title="Link to this heading">¶</a></h3>
<p>Messages arriving out of order are buffered and reordered:</p>
<div class="highlight-mermaid notranslate"><div class="highlight"><pre><span></span>sequenceDiagram
    participant Network as Network
    participant MO as Message Ordering
    participant Buffer as Message Buffer
    participant App as Application

    Note over Network,App: Messages Arrive Out of Order
    Network-&gt;&gt;MO: Message Seq 5
    MO-&gt;&gt;Buffer: Buffer message 5
    MO--&gt;&gt;Network: Waiting for 3, 4

    Network-&gt;&gt;MO: Message Seq 3
    MO-&gt;&gt;MO: Process message 3
    MO-&gt;&gt;App: Deliver message 3

    Network-&gt;&gt;MO: Message Seq 4
    MO-&gt;&gt;MO: Process message 4
    MO-&gt;&gt;App: Deliver message 4
    MO-&gt;&gt;Buffer: Check for ready messages
    Buffer--&gt;&gt;MO: Message 5 ready
    MO-&gt;&gt;App: Deliver message 5
</pre></div>
</div>
</section>
</section>
<section id="message-flow-states">
<h2>Message Flow States<a class="headerlink" href="#message-flow-states" title="Link to this heading">¶</a></h2>
<p>Messages can be in various states during processing:</p>
<div class="highlight-mermaid notranslate"><div class="highlight"><pre><span></span>stateDiagram-v2
    [*] --&gt; Received
    Received --&gt; Validating: Check sequence
    Validating --&gt; InOrder: Seq = Expected
    Validating --&gt; OutOfOrder: Seq &gt; Expected
    Validating --&gt; Duplicate: Seq &lt; Expected

    InOrder --&gt; Processing: Immediate process
    OutOfOrder --&gt; Buffered: Store for later
    Duplicate --&gt; Rejected: Replay detected

    Buffered --&gt; Ready: Gap filled
    Ready --&gt; Processing: Process in order
    Processing --&gt; Delivered: Send to application

    Rejected --&gt; [*]
    Delivered --&gt; [*]
</pre></div>
</div>
</section>
<section id="api-reference">
<h2>API Reference<a class="headerlink" href="#api-reference" title="Link to this heading">¶</a></h2>
<section id="messageordering-class">
<h3>MessageOrdering Class<a class="headerlink" href="#messageordering-class" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">import</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">MessageOrdering</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;webotter/protocol&#39;</span><span class="p">;</span>

<span class="c1">// Create message ordering instance</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">ordering</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">MessageOrdering</span><span class="p">({</span>
<span class="w">  </span><span class="nx">maxBufferSize</span><span class="o">:</span><span class="w"> </span><span class="mf">100</span><span class="p">,</span><span class="w">      </span><span class="c1">// Maximum buffered messages</span>
<span class="w">  </span><span class="nx">maxGapSize</span><span class="o">:</span><span class="w"> </span><span class="mf">10</span><span class="p">,</span><span class="w">          </span><span class="c1">// Maximum sequence gap</span>
<span class="w">  </span><span class="nx">replayWindowSize</span><span class="o">:</span><span class="w"> </span><span class="mf">64</span><span class="p">,</span><span class="w">    </span><span class="c1">// Replay protection window</span>
<span class="w">  </span><span class="nx">gapTimeoutMs</span><span class="o">:</span><span class="w"> </span><span class="mf">30000</span><span class="p">,</span><span class="w">     </span><span class="c1">// Gap timeout</span>
<span class="w">  </span><span class="nx">cleanupIntervalMs</span><span class="o">:</span><span class="w"> </span><span class="mf">60000</span><span class="w"> </span><span class="c1">// Cleanup interval</span>
<span class="p">});</span>

<span class="c1">// Validate incoming message</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">ordering</span><span class="p">.</span><span class="nx">validateIncomingMessage</span><span class="p">({</span>
<span class="w">  </span><span class="nx">sequence</span><span class="o">:</span><span class="w"> </span><span class="mf">5</span><span class="p">,</span>
<span class="w">  </span><span class="nx">data</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;message content&#39;</span>
<span class="p">});</span>

<span class="c1">// Handle out-of-order message</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">bufferResult</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">ordering</span><span class="p">.</span><span class="nx">handleOutOfOrder</span><span class="p">(</span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="nx">currentSequence</span><span class="p">);</span>

<span class="c1">// Get next sequence for outgoing message</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">nextSeq</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">ordering</span><span class="p">.</span><span class="nx">getNextSendSequence</span><span class="p">();</span>
</pre></div>
</div>
</section>
<section id="method-details">
<h3>Method Details<a class="headerlink" href="#method-details" title="Link to this heading">¶</a></h3>
<p><strong>validateIncomingMessage(message, expectedSequence)</strong></p>
<p>Validates an incoming message and determines processing action.</p>
<p><em>Parameters:</em>
- <code class="docutils literal notranslate"><span class="pre">message</span></code> (Object): Message with sequence number
- <code class="docutils literal notranslate"><span class="pre">expectedSequence</span></code> (number): Expected sequence number (optional)</p>
<p><em>Returns:</em> Validation result object</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">action</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;PROCESS&#39;</span><span class="p">,</span><span class="w">           </span><span class="c1">// Action to take</span>
<span class="w">  </span><span class="nx">reason</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;In order message&#39;</span><span class="p">,</span><span class="w">  </span><span class="c1">// Reason for action</span>
<span class="w">  </span><span class="nx">state</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;PROCESSED&#39;</span><span class="p">,</span><span class="w">          </span><span class="c1">// Message state</span>
<span class="w">  </span><span class="nx">sequence</span><span class="o">:</span><span class="w"> </span><span class="mf">5</span><span class="p">,</span><span class="w">                 </span><span class="c1">// Message sequence</span>
<span class="w">  </span><span class="nx">readyMessages</span><span class="o">:</span><span class="w"> </span><span class="p">[]</span><span class="w">            </span><span class="c1">// Additional ready messages</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>handleOutOfOrder(message, currentSequence)</strong></p>
<p>Handles out-of-order message buffering and reordering.</p>
<p><em>Parameters:</em>
- <code class="docutils literal notranslate"><span class="pre">message</span></code> (Object): Out-of-order message
- <code class="docutils literal notranslate"><span class="pre">currentSequence</span></code> (number): Current sequence number</p>
<p><em>Returns:</em> Buffer operation result</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">action</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;BUFFERED&#39;</span><span class="p">,</span><span class="w">          </span><span class="c1">// Buffer action taken</span>
<span class="w">  </span><span class="nx">reason</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;Out of order message buffered&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">readyMessages</span><span class="o">:</span><span class="w"> </span><span class="p">[],</span><span class="w">           </span><span class="c1">// Messages now ready</span>
<span class="w">  </span><span class="nx">pendingCount</span><span class="o">:</span><span class="w"> </span><span class="mf">3</span><span class="w">              </span><span class="c1">// Total pending messages</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>detectMessageGaps(receivedSequence, expectedSequence)</strong></p>
<p>Detects gaps in message sequence.</p>
<p><em>Parameters:</em>
- <code class="docutils literal notranslate"><span class="pre">receivedSequence</span></code> (number): Received sequence number
- <code class="docutils literal notranslate"><span class="pre">expectedSequence</span></code> (number): Expected sequence number</p>
<p><em>Returns:</em> Gap detection result</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nx">hasGap</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span><span class="w">                </span><span class="c1">// Gap detected</span>
<span class="w">  </span><span class="nx">gapSize</span><span class="o">:</span><span class="w"> </span><span class="mf">3</span><span class="p">,</span><span class="w">                  </span><span class="c1">// Size of gap</span>
<span class="w">  </span><span class="nx">missingSequences</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="mf">2</span><span class="p">,</span><span class="w"> </span><span class="mf">3</span><span class="p">,</span><span class="w"> </span><span class="mf">4</span><span class="p">]</span><span class="w">  </span><span class="c1">// Missing sequence numbers</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="configuration-options">
<h2>Configuration Options<a class="headerlink" href="#configuration-options" title="Link to this heading">¶</a></h2>
<section id="default-configuration">
<h3>Default Configuration<a class="headerlink" href="#default-configuration" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">const</span><span class="w"> </span><span class="nx">defaultConfig</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">maxBufferSize</span><span class="o">:</span><span class="w"> </span><span class="mf">100</span><span class="p">,</span><span class="w">        </span><span class="c1">// Maximum out-of-order messages to buffer</span>
<span class="w">  </span><span class="nx">maxGapSize</span><span class="o">:</span><span class="w"> </span><span class="mf">10</span><span class="p">,</span><span class="w">           </span><span class="c1">// Maximum gap in sequence numbers</span>
<span class="w">  </span><span class="nx">replayWindowSize</span><span class="o">:</span><span class="w"> </span><span class="mf">64</span><span class="p">,</span><span class="w">     </span><span class="c1">// Size of replay protection window</span>
<span class="w">  </span><span class="nx">maxSequenceNumber</span><span class="o">:</span><span class="w"> </span><span class="mh">0xFFFFFFFF</span><span class="p">,</span><span class="w"> </span><span class="c1">// Maximum sequence number (32-bit)</span>
<span class="w">  </span><span class="nx">gapTimeoutMs</span><span class="o">:</span><span class="w"> </span><span class="mf">30000</span><span class="p">,</span><span class="w">      </span><span class="c1">// Timeout for waiting for missing messages</span>
<span class="w">  </span><span class="nx">cleanupIntervalMs</span><span class="o">:</span><span class="w"> </span><span class="mf">60000</span><span class="w">  </span><span class="c1">// Interval for cleanup operations</span>
<span class="p">};</span>
</pre></div>
</div>
</section>
<section id="performance-tuning">
<h3>Performance Tuning<a class="headerlink" href="#performance-tuning" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="c1">// High-throughput configuration</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">highThroughputConfig</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">maxBufferSize</span><span class="o">:</span><span class="w"> </span><span class="mf">200</span><span class="p">,</span>
<span class="w">  </span><span class="nx">replayWindowSize</span><span class="o">:</span><span class="w"> </span><span class="mf">128</span><span class="p">,</span>
<span class="w">  </span><span class="nx">cleanupIntervalMs</span><span class="o">:</span><span class="w"> </span><span class="mf">30000</span>
<span class="p">};</span>

<span class="c1">// Low-latency configuration</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">lowLatencyConfig</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">maxBufferSize</span><span class="o">:</span><span class="w"> </span><span class="mf">50</span><span class="p">,</span>
<span class="w">  </span><span class="nx">maxGapSize</span><span class="o">:</span><span class="w"> </span><span class="mf">5</span><span class="p">,</span>
<span class="w">  </span><span class="nx">gapTimeoutMs</span><span class="o">:</span><span class="w"> </span><span class="mf">10000</span>
<span class="p">};</span>

<span class="c1">// Memory-constrained configuration</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">memoryConstrainedConfig</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">maxBufferSize</span><span class="o">:</span><span class="w"> </span><span class="mf">25</span><span class="p">,</span>
<span class="w">  </span><span class="nx">replayWindowSize</span><span class="o">:</span><span class="w"> </span><span class="mf">32</span><span class="p">,</span>
<span class="w">  </span><span class="nx">cleanupIntervalMs</span><span class="o">:</span><span class="w"> </span><span class="mf">120000</span>
<span class="p">};</span>
</pre></div>
</div>
</section>
</section>
<section id="message-processing-flow">
<h2>Message Processing Flow<a class="headerlink" href="#message-processing-flow" title="Link to this heading">¶</a></h2>
<p>The complete message processing flow:</p>
<div class="highlight-mermaid notranslate"><div class="highlight"><pre><span></span>flowchart TD
    START[Receive Message] --&gt; VALIDATE[Validate Structure]
    VALIDATE --&gt; SEQ_CHECK[Check Sequence Number]

    SEQ_CHECK --&gt; IN_ORDER{In Order?}
    IN_ORDER --&gt;|Yes| REPLAY_CHECK[Check Replay Protection]
    IN_ORDER --&gt;|No| OUT_OF_ORDER{Future Message?}

    OUT_OF_ORDER --&gt;|Yes| BUFFER[Buffer Message]
    OUT_OF_ORDER --&gt;|No| DUPLICATE[Handle Duplicate]

    REPLAY_CHECK --&gt; VALID{Valid?}
    VALID --&gt;|Yes| PROCESS[Process Message]
    VALID --&gt;|No| REJECT[Reject Message]

    BUFFER --&gt; CHECK_READY[Check Ready Messages]
    CHECK_READY --&gt; READY{Messages Ready?}
    READY --&gt;|Yes| PROCESS_READY[Process Ready Messages]
    READY --&gt;|No| WAIT[Wait for Gap Fill]

    PROCESS --&gt; UPDATE_STATE[Update Sequence State]
    PROCESS_READY --&gt; UPDATE_STATE
    UPDATE_STATE --&gt; DELIVER[Deliver to Application]

    DUPLICATE --&gt; LOG[Log Duplicate]
    REJECT --&gt; LOG
    WAIT --&gt; TIMEOUT{Timeout?}
    TIMEOUT --&gt;|Yes| CLEANUP[Cleanup Expired]
    TIMEOUT --&gt;|No| WAIT

    DELIVER --&gt; END[Complete]
    LOG --&gt; END
    CLEANUP --&gt; END
</pre></div>
</div>
</section>
<section id="error-handling">
<h2>Error Handling<a class="headerlink" href="#error-handling" title="Link to this heading">¶</a></h2>
<p>The system handles various error conditions:</p>
<div class="highlight-mermaid notranslate"><div class="highlight"><pre><span></span>graph TB
    subgraph &quot;Error Types&quot;
        E1[Invalid Message Structure]
        E2[Sequence Out of Range]
        E3[Buffer Overflow]
        E4[Gap Too Large]
        E5[Replay Attack]
    end

    subgraph &quot;Error Responses&quot;
        R1[Validation Error]
        R2[Range Error]
        R3[Buffer Management]
        R4[Gap Rejection]
        R5[Security Alert]
    end

    subgraph &quot;Recovery Actions&quot;
        A1[Request Retransmission]
        A2[Reset Sequence State]
        A3[Evict Oldest Messages]
        A4[Skip Missing Messages]
        A5[Log Security Event]
    end

    E1 --&gt; R1 --&gt; A1
    E2 --&gt; R2 --&gt; A2
    E3 --&gt; R3 --&gt; A3
    E4 --&gt; R4 --&gt; A4
    E5 --&gt; R5 --&gt; A5
</pre></div>
</div>
</section>
<section id="performance-characteristics">
<h2>Performance Characteristics<a class="headerlink" href="#performance-characteristics" title="Link to this heading">¶</a></h2>
<p>The message ordering system is optimized for performance:</p>
<div class="highlight-mermaid notranslate"><div class="highlight"><pre><span></span>graph LR
    subgraph &quot;Performance Metrics&quot;
        subgraph &quot;Latency&quot;
            L1[In-order: &lt; 1ms]
            L2[Out-of-order: &lt; 10ms]
            L3[Gap detection: &lt; 5ms]
        end

        subgraph &quot;Throughput&quot;
            T1[10,000+ messages/sec]
            T2[1,000+ out-of-order/sec]
            T3[100+ gaps/sec]
        end

        subgraph &quot;Memory&quot;
            M1[&lt; 10KB buffer typical]
            M2[&lt; 1KB per message]
            M3[O(1) lookup time]
        end
    end
</pre></div>
</div>
</section>
<section id="security-considerations">
<h2>Security Considerations<a class="headerlink" href="#security-considerations" title="Link to this heading">¶</a></h2>
<section id="replay-attack-prevention">
<h3>Replay Attack Prevention<a class="headerlink" href="#replay-attack-prevention" title="Link to this heading">¶</a></h3>
<p>The sliding window mechanism prevents replay attacks:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="c1">// Replay protection validation</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">isValidMessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">ordering</span><span class="p">.</span><span class="nx">validateReplayProtection</span><span class="p">(</span><span class="nx">message</span><span class="p">,</span><span class="w"> </span><span class="nx">windowSize</span><span class="p">);</span>

<span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="nx">isValidMessage</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="c1">// Message is a replay - reject and log</span>
<span class="w">  </span><span class="nx">securityLogger</span><span class="p">.</span><span class="nx">logReplayAttempt</span><span class="p">(</span><span class="nx">message</span><span class="p">);</span>
<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="sequence-number-security">
<h3>Sequence Number Security<a class="headerlink" href="#sequence-number-security" title="Link to this heading">¶</a></h3>
<p>Sequence numbers are validated to prevent manipulation:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="c1">// Validate sequence number range</span>
<span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">sequence</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mf">0</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="nx">sequence</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="nx">MAX_SEQUENCE</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">throw</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">SecurityValidationError</span><span class="p">(</span><span class="s1">&#39;Sequence number out of range&#39;</span><span class="p">);</span>
<span class="p">}</span>

<span class="c1">// Check for sequence number rollover</span>
<span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">sequence</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="nx">lastSequence</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="p">(</span><span class="nx">lastSequence</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="nx">sequence</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="nx">ROLLOVER_THRESHOLD</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="c1">// Handle sequence number rollover</span>
<span class="w">  </span><span class="nx">handleSequenceRollover</span><span class="p">();</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="buffer-security">
<h3>Buffer Security<a class="headerlink" href="#buffer-security" title="Link to this heading">¶</a></h3>
<p>Message buffers are protected against overflow attacks:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="c1">// Enforce buffer size limits</span>
<span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">pendingMessages</span><span class="p">.</span><span class="nx">size</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="nx">maxBufferSize</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="c1">// Evict oldest message to make room</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">oldestSequence</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">Math</span><span class="p">.</span><span class="nx">min</span><span class="p">(...</span><span class="nx">pendingMessages</span><span class="p">.</span><span class="nx">keys</span><span class="p">());</span>
<span class="w">  </span><span class="nx">pendingMessages</span><span class="p">.</span><span class="ow">delete</span><span class="p">(</span><span class="nx">oldestSequence</span><span class="p">);</span>
<span class="w">  </span><span class="nx">stats</span><span class="p">.</span><span class="nx">timeouts</span><span class="o">++</span><span class="p">;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="integration-examples">
<h2>Integration Examples<a class="headerlink" href="#integration-examples" title="Link to this heading">¶</a></h2>
<section id="basic-integration">
<h3>Basic Integration<a class="headerlink" href="#basic-integration" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="k">import</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">MessageOrdering</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;webotter/protocol&#39;</span><span class="p">;</span>

<span class="kd">class</span><span class="w"> </span><span class="nx">OTRMessaging</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">ordering</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">MessageOrdering</span><span class="p">({</span>
<span class="w">      </span><span class="nx">maxBufferSize</span><span class="o">:</span><span class="w"> </span><span class="mf">100</span><span class="p">,</span>
<span class="w">      </span><span class="nx">replayWindowSize</span><span class="o">:</span><span class="w"> </span><span class="mf">64</span>
<span class="w">    </span><span class="p">});</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">handleIncomingMessage</span><span class="p">(</span><span class="nx">message</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">ordering</span><span class="p">.</span><span class="nx">validateIncomingMessage</span><span class="p">(</span><span class="nx">message</span><span class="p">);</span>

<span class="w">    </span><span class="k">switch</span><span class="w"> </span><span class="p">(</span><span class="nx">result</span><span class="p">.</span><span class="nx">action</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">case</span><span class="w"> </span><span class="s1">&#39;PROCESS&#39;</span><span class="o">:</span>
<span class="w">        </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">processMessage</span><span class="p">(</span><span class="nx">message</span><span class="p">);</span>
<span class="w">        </span><span class="c1">// Process any ready buffered messages</span>
<span class="w">        </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="kd">const</span><span class="w"> </span><span class="nx">ready</span><span class="w"> </span><span class="k">of</span><span class="w"> </span><span class="nx">result</span><span class="p">.</span><span class="nx">readyMessages</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">processMessage</span><span class="p">(</span><span class="nx">ready</span><span class="p">.</span><span class="nx">message</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">        </span><span class="k">break</span><span class="p">;</span>

<span class="w">      </span><span class="k">case</span><span class="w"> </span><span class="s1">&#39;BUFFER&#39;</span><span class="o">:</span>
<span class="w">        </span><span class="c1">// Message buffered, waiting for earlier messages</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">logInfo</span><span class="p">(</span><span class="sb">`Message </span><span class="si">${</span><span class="nx">message</span><span class="p">.</span><span class="nx">sequence</span><span class="si">}</span><span class="sb"> buffered`</span><span class="p">);</span>
<span class="w">        </span><span class="k">break</span><span class="p">;</span>

<span class="w">      </span><span class="k">case</span><span class="w"> </span><span class="s1">&#39;REJECT&#39;</span><span class="o">:</span>
<span class="w">        </span><span class="c1">// Replay or invalid message</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">logWarning</span><span class="p">(</span><span class="sb">`Message </span><span class="si">${</span><span class="nx">message</span><span class="p">.</span><span class="nx">sequence</span><span class="si">}</span><span class="sb"> rejected: </span><span class="si">${</span><span class="nx">result</span><span class="p">.</span><span class="nx">reason</span><span class="si">}</span><span class="sb">`</span><span class="p">);</span>
<span class="w">        </span><span class="k">break</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">sendMessage</span><span class="p">(</span><span class="nx">content</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">sequence</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">ordering</span><span class="p">.</span><span class="nx">getNextSendSequence</span><span class="p">();</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">message</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">sequence</span><span class="o">:</span><span class="w"> </span><span class="nx">sequence</span><span class="p">,</span>
<span class="w">      </span><span class="nx">content</span><span class="o">:</span><span class="w"> </span><span class="nx">content</span><span class="p">,</span>
<span class="w">      </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="nb">Date</span><span class="p">.</span><span class="nx">now</span><span class="p">()</span>
<span class="w">    </span><span class="p">};</span>

<span class="w">    </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">transmitMessage</span><span class="p">(</span><span class="nx">message</span><span class="p">);</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">ordering</span><span class="p">.</span><span class="nx">updateSequenceTracking</span><span class="p">(</span><span class="nx">sequence</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="advanced-integration">
<h3>Advanced Integration<a class="headerlink" href="#advanced-integration" title="Link to this heading">¶</a></h3>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">class</span><span class="w"> </span><span class="nx">EnhancedOTRMessaging</span><span class="w"> </span><span class="k">extends</span><span class="w"> </span><span class="nx">OTRMessaging</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span><span class="nx">options</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{})</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">super</span><span class="p">();</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">gapRecovery</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">options</span><span class="p">.</span><span class="nx">enableGapRecovery</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">metrics</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">MessageMetrics</span><span class="p">();</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">handleIncomingMessage</span><span class="p">(</span><span class="nx">message</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">startTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">performance</span><span class="p">.</span><span class="nx">now</span><span class="p">();</span>

<span class="w">    </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">super</span><span class="p">.</span><span class="nx">handleIncomingMessage</span><span class="p">(</span><span class="nx">message</span><span class="p">);</span>

<span class="w">      </span><span class="c1">// Handle gap recovery</span>
<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">result</span><span class="p">.</span><span class="nx">action</span><span class="w"> </span><span class="o">===</span><span class="w"> </span><span class="s1">&#39;BUFFER&#39;</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">gapRecovery</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">requestMissingMessages</span><span class="p">(</span><span class="nx">result</span><span class="p">.</span><span class="nx">gap</span><span class="p">);</span>
<span class="w">      </span><span class="p">}</span>

<span class="w">      </span><span class="c1">// Update metrics</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">metrics</span><span class="p">.</span><span class="nx">recordMessage</span><span class="p">(</span><span class="nx">result</span><span class="p">.</span><span class="nx">action</span><span class="p">,</span><span class="w"> </span><span class="nx">performance</span><span class="p">.</span><span class="nx">now</span><span class="p">()</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="nx">startTime</span><span class="p">);</span>

<span class="w">      </span><span class="k">return</span><span class="w"> </span><span class="nx">result</span><span class="p">;</span>

<span class="w">    </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">metrics</span><span class="p">.</span><span class="nx">recordError</span><span class="p">(</span><span class="nx">error</span><span class="p">);</span>
<span class="w">      </span><span class="k">throw</span><span class="w"> </span><span class="nx">error</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">requestMissingMessages</span><span class="p">(</span><span class="nx">gap</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">gap</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="nx">gap</span><span class="p">.</span><span class="nx">missingSequences</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="kd">const</span><span class="w"> </span><span class="nx">seq</span><span class="w"> </span><span class="k">of</span><span class="w"> </span><span class="nx">gap</span><span class="p">.</span><span class="nx">missingSequences</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">sendRetransmissionRequest</span><span class="p">(</span><span class="nx">seq</span><span class="p">);</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">getOrderingStatistics</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="p">...</span><span class="k">this</span><span class="p">.</span><span class="nx">ordering</span><span class="p">.</span><span class="nx">getStats</span><span class="p">(),</span>
<span class="w">      </span><span class="p">...</span><span class="k">this</span><span class="p">.</span><span class="nx">metrics</span><span class="p">.</span><span class="nx">getStats</span><span class="p">()</span>
<span class="w">    </span><span class="p">};</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="testing-and-validation">
<h2>Testing and Validation<a class="headerlink" href="#testing-and-validation" title="Link to this heading">¶</a></h2>
<p>Comprehensive testing ensures reliability:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="nx">describe</span><span class="p">(</span><span class="s1">&#39;Message Ordering&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">test</span><span class="p">(</span><span class="s1">&#39;should handle out-of-order messages&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">ordering</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">MessageOrdering</span><span class="p">();</span>

<span class="w">    </span><span class="c1">// Send messages out of order: 0, 2, 1</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">result1</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">ordering</span><span class="p">.</span><span class="nx">validateIncomingMessage</span><span class="p">({</span><span class="w"> </span><span class="nx">sequence</span><span class="o">:</span><span class="w"> </span><span class="mf">0</span><span class="w"> </span><span class="p">});</span>
<span class="w">    </span><span class="nx">expect</span><span class="p">(</span><span class="nx">result1</span><span class="p">.</span><span class="nx">action</span><span class="p">).</span><span class="nx">toBe</span><span class="p">(</span><span class="s1">&#39;PROCESS&#39;</span><span class="p">);</span>

<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">result2</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">ordering</span><span class="p">.</span><span class="nx">validateIncomingMessage</span><span class="p">({</span><span class="w"> </span><span class="nx">sequence</span><span class="o">:</span><span class="w"> </span><span class="mf">2</span><span class="w"> </span><span class="p">});</span>
<span class="w">    </span><span class="nx">expect</span><span class="p">(</span><span class="nx">result2</span><span class="p">.</span><span class="nx">action</span><span class="p">).</span><span class="nx">toBe</span><span class="p">(</span><span class="s1">&#39;BUFFER&#39;</span><span class="p">);</span>

<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">result3</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">ordering</span><span class="p">.</span><span class="nx">validateIncomingMessage</span><span class="p">({</span><span class="w"> </span><span class="nx">sequence</span><span class="o">:</span><span class="w"> </span><span class="mf">1</span><span class="w"> </span><span class="p">});</span>
<span class="w">    </span><span class="nx">expect</span><span class="p">(</span><span class="nx">result3</span><span class="p">.</span><span class="nx">action</span><span class="p">).</span><span class="nx">toBe</span><span class="p">(</span><span class="s1">&#39;PROCESS&#39;</span><span class="p">);</span>
<span class="w">    </span><span class="nx">expect</span><span class="p">(</span><span class="nx">result3</span><span class="p">.</span><span class="nx">readyMessages</span><span class="p">).</span><span class="nx">toHaveLength</span><span class="p">(</span><span class="mf">1</span><span class="p">);</span>
<span class="w">    </span><span class="nx">expect</span><span class="p">(</span><span class="nx">result3</span><span class="p">.</span><span class="nx">readyMessages</span><span class="p">[</span><span class="mf">0</span><span class="p">].</span><span class="nx">sequence</span><span class="p">).</span><span class="nx">toBe</span><span class="p">(</span><span class="mf">2</span><span class="p">);</span>
<span class="w">  </span><span class="p">});</span>

<span class="w">  </span><span class="nx">test</span><span class="p">(</span><span class="s1">&#39;should detect replay attacks&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">ordering</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">MessageOrdering</span><span class="p">();</span>

<span class="w">    </span><span class="c1">// Process message</span>
<span class="w">    </span><span class="nx">ordering</span><span class="p">.</span><span class="nx">validateIncomingMessage</span><span class="p">({</span><span class="w"> </span><span class="nx">sequence</span><span class="o">:</span><span class="w"> </span><span class="mf">0</span><span class="w"> </span><span class="p">});</span>

<span class="w">    </span><span class="c1">// Try to replay same message</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">ordering</span><span class="p">.</span><span class="nx">validateIncomingMessage</span><span class="p">({</span><span class="w"> </span><span class="nx">sequence</span><span class="o">:</span><span class="w"> </span><span class="mf">0</span><span class="w"> </span><span class="p">});</span>
<span class="w">    </span><span class="nx">expect</span><span class="p">(</span><span class="nx">result</span><span class="p">.</span><span class="nx">action</span><span class="p">).</span><span class="nx">toBe</span><span class="p">(</span><span class="s1">&#39;REJECT&#39;</span><span class="p">);</span>
<span class="w">    </span><span class="nx">expect</span><span class="p">(</span><span class="nx">result</span><span class="p">.</span><span class="nx">reason</span><span class="p">).</span><span class="nx">toBe</span><span class="p">(</span><span class="s1">&#39;REPLAY_DETECTED&#39;</span><span class="p">);</span>
<span class="w">  </span><span class="p">});</span>
<span class="p">});</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Message ordering is transparent to applications but provides extensive monitoring and configuration options for performance tuning and security analysis.</p>
</div>
</section>
</section>

        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          <a class="next-page" href="enhanced-smp.html">
              <div class="page-info">
                <div class="context">
                  <span>Next</span>
                </div>
                <div class="title">Enhanced SMP</div>
              </div>
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
            </a>
          <a class="prev-page" href="version-negotiation.html">
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
              <div class="page-info">
                <div class="context">
                  <span>Previous</span>
                </div>
                
                <div class="title">Version Negotiation</div>
                
              </div>
            </a>
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 1980, WebOTR Team
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer">
      
      
      <div class="toc-sticky toc-scroll">
        <div class="toc-title-container">
          <span class="toc-title">
            On this page
          </span>
        </div>
        <div class="toc-tree-container">
          <div class="toc-tree">
            <ul>
<li><a class="reference internal" href="#">Message Ordering</a><ul>
<li><a class="reference internal" href="#overview">Overview</a></li>
<li><a class="reference internal" href="#core-features">Core Features</a><ul>
<li><a class="reference internal" href="#sequence-number-validation">Sequence Number Validation</a></li>
<li><a class="reference internal" href="#replay-protection">Replay Protection</a></li>
<li><a class="reference internal" href="#out-of-order-message-handling">Out-of-Order Message Handling</a></li>
</ul>
</li>
<li><a class="reference internal" href="#message-flow-states">Message Flow States</a></li>
<li><a class="reference internal" href="#api-reference">API Reference</a><ul>
<li><a class="reference internal" href="#messageordering-class">MessageOrdering Class</a></li>
<li><a class="reference internal" href="#method-details">Method Details</a></li>
</ul>
</li>
<li><a class="reference internal" href="#configuration-options">Configuration Options</a><ul>
<li><a class="reference internal" href="#default-configuration">Default Configuration</a></li>
<li><a class="reference internal" href="#performance-tuning">Performance Tuning</a></li>
</ul>
</li>
<li><a class="reference internal" href="#message-processing-flow">Message Processing Flow</a></li>
<li><a class="reference internal" href="#error-handling">Error Handling</a></li>
<li><a class="reference internal" href="#performance-characteristics">Performance Characteristics</a></li>
<li><a class="reference internal" href="#security-considerations">Security Considerations</a><ul>
<li><a class="reference internal" href="#replay-attack-prevention">Replay Attack Prevention</a></li>
<li><a class="reference internal" href="#sequence-number-security">Sequence Number Security</a></li>
<li><a class="reference internal" href="#buffer-security">Buffer Security</a></li>
</ul>
</li>
<li><a class="reference internal" href="#integration-examples">Integration Examples</a><ul>
<li><a class="reference internal" href="#basic-integration">Basic Integration</a></li>
<li><a class="reference internal" href="#advanced-integration">Advanced Integration</a></li>
</ul>
</li>
<li><a class="reference internal" href="#testing-and-validation">Testing and Validation</a></li>
</ul>
</li>
</ul>

          </div>
        </div>
      </div>
      
      
    </aside>
  </div>
</div><script src="../_static/documentation_options.js?v=01f34227"></script>
    <script src="../_static/doctools.js?v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/scripts/furo.js?v=5fa4622c"></script>
    <script src="../_static/mermaid-renderer.js?v=b683124c"></script>
    </body>
</html>