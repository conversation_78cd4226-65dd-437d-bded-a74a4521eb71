#!/bin/bash

# WebOTR Sphinx Documentation Build Script
# This script builds the Sphinx documentation with Forward Secrecy content

set -e

echo "🔐 Building WebOTR Security Documentation"
echo "=========================================="

# Check if we're in the right directory
if [ ! -f "conf.py" ]; then
    echo "❌ Error: conf.py not found. Please run this script from the docs/sphinx directory."
    exit 1
fi

# Check if Sphinx is installed
if ! command -v sphinx-build &> /dev/null; then
    echo "❌ Error: sphinx-build not found. Please install Sphinx:"
    echo "   pip install -r requirements.txt"
    exit 1
fi

# Clean previous build
echo "🧹 Cleaning previous build..."
rm -rf _build/

# Create build directory
mkdir -p _build

# Build HTML documentation
echo "📚 Building HTML documentation..."
sphinx-build -b html . _build/html

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "✅ Documentation built successfully!"
    echo ""
    echo "📖 Documentation available at:"
    echo "   file://$(pwd)/_build/html/index.html"
    echo ""
    echo "🔐 Security Documentation includes:"
    echo "   • libOTR Security Enhancements (NEW)"
    echo "     - Timing Attack Resistance"
    echo "     - Comprehensive Input Validation"
    echo "     - Secure Memory Management"
    echo "     - Enhanced Error Recovery"
    echo "   • Forward Secrecy Implementation"
    echo "   • Authenticated Key Exchange (AKE)"
    echo "   • Steganographic Communication"
    echo "   • Technical Overviews & Architecture"
    echo "   • Implementation Guides"
    echo "   • Complete API References"
    echo "   • Testing & Validation Guides"
    echo "   • Interactive Mermaid Diagrams"
    echo ""
    echo "🚀 To serve locally:"
    echo "   cd _build/html && python -m http.server 8000"
else
    echo "❌ Documentation build failed!"
    exit 1
fi
