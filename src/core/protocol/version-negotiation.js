/**
 * OTR Version Negotiation Module
 * 
 * Implements comprehensive OTR version negotiation supporting v2 and v3 protocols
 * with automatic version detection, query message handling, and protocol selection.
 * 
 * Based on OTR Protocol Specification and libOTR implementation patterns.
 */

import { PROTOCOL_VERSION, MESSAGE_TYPE, MESSAGE_PREFIX } from './constants.js';
import { CryptoValidation, SecurityValidationError } from '../security/validation.js';
import { globalErrorRecovery, ERROR_TYPES } from '../security/error-recovery.js';

/**
 * OTR Version Negotiation Manager
 */
export class VersionNegotiation {
  /**
   * Supported OTR protocol versions
   */
  static SUPPORTED_VERSIONS = [2, 3];
  static DEFAULT_VERSION = 3;
  static MINIMUM_VERSION = 2;
  static MAXIMUM_VERSION = 3;

  /**
   * Version-specific capabilities
   */
  static VERSION_CAPABILITIES = {
    2: {
      instanceTags: false,
      extraSymmetricKey: false,
      smpV1: true,
      fragmentationV2: true
    },
    3: {
      instanceTags: true,
      extraSymmetricKey: true,
      smpV1: true,
      fragmentationV2: true,
      smpV2: true
    }
  };

  /**
   * Query message patterns for different versions
   */
  static QUERY_PATTERNS = {
    2: '?OTRv2?',
    3: '?OTRv3?',
    23: '?OTRv23?',
    ANY: '?OTR?'
  };

  /**
   * Version negotiation policies
   */
  static POLICIES = {
    REQUIRE_ENCRYPTION: 'require_encryption',
    ALLOW_V2: 'allow_v2',
    ALLOW_V3: 'allow_v3',
    PREFER_V3: 'prefer_v3',
    WHITESPACE_START_AKE: 'whitespace_start_ake',
    ERROR_START_AKE: 'error_start_ake'
  };

  /**
   * Default policy configuration
   */
  static DEFAULT_POLICY = {
    [VersionNegotiation.POLICIES.REQUIRE_ENCRYPTION]: true,
    [VersionNegotiation.POLICIES.ALLOW_V2]: true,
    [VersionNegotiation.POLICIES.ALLOW_V3]: true,
    [VersionNegotiation.POLICIES.PREFER_V3]: true,
    [VersionNegotiation.POLICIES.WHITESPACE_START_AKE]: true,
    [VersionNegotiation.POLICIES.ERROR_START_AKE]: true
  };

  /**
   * Negotiate optimal OTR version between parties
   * 
   * @param {Array<number>} theirVersions - Versions supported by remote party
   * @param {Object} ourPolicy - Our version policy configuration
   * @returns {Object} Negotiation result with selected version and capabilities
   */
  static negotiateVersion(theirVersions, ourPolicy = VersionNegotiation.DEFAULT_POLICY) {
    try {
      // Validate input parameters
      if (!Array.isArray(theirVersions) || theirVersions.length === 0) {
        throw new SecurityValidationError('Invalid version list provided', 'INVALID_VERSION_LIST');
      }

      // Get our supported versions based on policy
      const ourVersions = this._getOurSupportedVersions(ourPolicy);
      
      // Find common versions
      const commonVersions = theirVersions.filter(v => ourVersions.includes(v));
      
      if (commonVersions.length === 0) {
        throw new SecurityValidationError('No compatible OTR versions found', 'NO_COMPATIBLE_VERSIONS');
      }

      // Select optimal version (prefer highest version)
      const selectedVersion = Math.max(...commonVersions);
      
      // Validate selected version
      this._validateSelectedVersion(selectedVersion, ourPolicy);
      
      // Get capabilities for selected version
      const capabilities = this.VERSION_CAPABILITIES[selectedVersion];
      
      return {
        version: selectedVersion,
        capabilities: capabilities,
        commonVersions: commonVersions,
        negotiationSuccess: true,
        securityLevel: this._getSecurityLevel(selectedVersion)
      };
      
    } catch (error) {
      // Handle negotiation errors with recovery system
      const recovery = globalErrorRecovery.handleAKEError(error, {
        id: 'version-negotiation',
        versions: { theirs: theirVersions, ours: ourPolicy }
      });
      
      return {
        version: null,
        capabilities: null,
        negotiationSuccess: false,
        error: error.message,
        recovery: recovery
      };
    }
  }

  /**
   * Create OTR query message for version negotiation
   * 
   * @param {string} account - Account identifier
   * @param {Object} policy - Version policy configuration
   * @returns {string} OTR query message
   */
  static createQueryMessage(account, policy = VersionNegotiation.DEFAULT_POLICY) {
    try {
      // Validate account parameter
      if (typeof account !== 'string' || account.length === 0) {
        throw new SecurityValidationError('Invalid account identifier', 'INVALID_ACCOUNT');
      }

      // Get supported versions based on policy
      const supportedVersions = this._getOurSupportedVersions(policy);
      
      if (supportedVersions.length === 0) {
        throw new SecurityValidationError('No versions enabled in policy', 'NO_VERSIONS_ENABLED');
      }

      // Generate appropriate query message
      let queryMessage;
      
      if (supportedVersions.includes(2) && supportedVersions.includes(3)) {
        // Support both v2 and v3
        queryMessage = this.QUERY_PATTERNS[23];
      } else if (supportedVersions.includes(3)) {
        // Support only v3
        queryMessage = this.QUERY_PATTERNS[3];
      } else if (supportedVersions.includes(2)) {
        // Support only v2
        queryMessage = this.QUERY_PATTERNS[2];
      } else {
        // Fallback to generic query
        queryMessage = this.QUERY_PATTERNS.ANY;
      }

      // Add account information if needed
      const fullQuery = `${queryMessage} ${account}`;
      
      return {
        message: fullQuery,
        versions: supportedVersions,
        account: account,
        timestamp: Date.now()
      };
      
    } catch (error) {
      throw new SecurityValidationError(
        `Failed to create query message: ${error.message}`,
        'QUERY_CREATION_FAILED'
      );
    }
  }

  /**
   * Parse OTR query message to extract version information
   * 
   * @param {string} message - Incoming OTR query message
   * @returns {Object} Parsed version information
   */
  static parseVersions(message) {
    try {
      // Validate message parameter
      if (typeof message !== 'string') {
        throw new SecurityValidationError('Invalid message format', 'INVALID_MESSAGE_FORMAT');
      }

      const trimmedMessage = message.trim();
      const versions = [];
      let account = null;

      // Parse different query patterns
      if (trimmedMessage.startsWith(this.QUERY_PATTERNS[23])) {
        versions.push(2, 3);
        account = this._extractAccount(trimmedMessage, this.QUERY_PATTERNS[23]);
      } else if (trimmedMessage.startsWith(this.QUERY_PATTERNS[3])) {
        versions.push(3);
        account = this._extractAccount(trimmedMessage, this.QUERY_PATTERNS[3]);
      } else if (trimmedMessage.startsWith(this.QUERY_PATTERNS[2])) {
        versions.push(2);
        account = this._extractAccount(trimmedMessage, this.QUERY_PATTERNS[2]);
      } else if (trimmedMessage.startsWith(this.QUERY_PATTERNS.ANY)) {
        // Generic query - assume all versions
        versions.push(...this.SUPPORTED_VERSIONS);
        account = this._extractAccount(trimmedMessage, this.QUERY_PATTERNS.ANY);
      } else {
        throw new SecurityValidationError('Unrecognized query format', 'UNRECOGNIZED_QUERY');
      }

      // Validate extracted versions
      const validVersions = versions.filter(v => this.SUPPORTED_VERSIONS.includes(v));
      
      if (validVersions.length === 0) {
        throw new SecurityValidationError('No valid versions in query', 'NO_VALID_VERSIONS');
      }

      return {
        versions: validVersions,
        account: account,
        originalMessage: message,
        parseSuccess: true
      };
      
    } catch (error) {
      return {
        versions: [],
        account: null,
        originalMessage: message,
        parseSuccess: false,
        error: error.message
      };
    }
  }

  /**
   * Validate version compatibility with features
   * 
   * @param {number} version - OTR protocol version
   * @param {Array<string>} requiredFeatures - Required protocol features
   * @returns {boolean} True if version supports all required features
   */
  static validateVersionCompatibility(version, requiredFeatures = []) {
    try {
      // Validate version parameter
      if (!this.SUPPORTED_VERSIONS.includes(version)) {
        throw new SecurityValidationError(`Unsupported version: ${version}`, 'UNSUPPORTED_VERSION');
      }

      const capabilities = this.VERSION_CAPABILITIES[version];
      
      // Check if all required features are supported
      for (const feature of requiredFeatures) {
        if (!(feature in capabilities) || !capabilities[feature]) {
          return false;
        }
      }

      return true;
      
    } catch (error) {
      throw new SecurityValidationError(
        `Version compatibility check failed: ${error.message}`,
        'COMPATIBILITY_CHECK_FAILED'
      );
    }
  }

  /**
   * Check for version downgrade attacks
   * 
   * @param {number} negotiatedVersion - Negotiated version
   * @param {Array<number>} availableVersions - All available versions
   * @param {Object} policy - Version policy
   * @returns {Object} Downgrade analysis result
   */
  static checkVersionDowngrade(negotiatedVersion, availableVersions, policy) {
    try {
      const maxAvailable = Math.max(...availableVersions);
      const isDowngrade = negotiatedVersion < maxAvailable;
      
      if (isDowngrade) {
        // Check if downgrade is policy-compliant
        const isAllowed = this._isDowngradeAllowed(negotiatedVersion, maxAvailable, policy);
        
        if (!isAllowed) {
          throw new SecurityValidationError(
            `Version downgrade attack detected: ${maxAvailable} -> ${negotiatedVersion}`,
            'VERSION_DOWNGRADE_ATTACK'
          );
        }
        
        return {
          isDowngrade: true,
          isAllowed: true,
          fromVersion: maxAvailable,
          toVersion: negotiatedVersion,
          securityImpact: this._assessDowngradeImpact(maxAvailable, negotiatedVersion)
        };
      }
      
      return {
        isDowngrade: false,
        isAllowed: true,
        fromVersion: negotiatedVersion,
        toVersion: negotiatedVersion,
        securityImpact: 'none'
      };
      
    } catch (error) {
      throw new SecurityValidationError(
        `Downgrade check failed: ${error.message}`,
        'DOWNGRADE_CHECK_FAILED'
      );
    }
  }

  /**
   * Get our supported versions based on policy
   * 
   * @private
   * @param {Object} policy - Version policy configuration
   * @returns {Array<number>} Supported versions
   */
  static _getOurSupportedVersions(policy) {
    const versions = [];
    
    if (policy[this.POLICIES.ALLOW_V2]) {
      versions.push(2);
    }
    
    if (policy[this.POLICIES.ALLOW_V3]) {
      versions.push(3);
    }
    
    return versions;
  }

  /**
   * Validate selected version against policy
   * 
   * @private
   * @param {number} version - Selected version
   * @param {Object} policy - Version policy
   */
  static _validateSelectedVersion(version, policy) {
    if (version === 2 && !policy[this.POLICIES.ALLOW_V2]) {
      throw new SecurityValidationError('OTR v2 not allowed by policy', 'V2_NOT_ALLOWED');
    }
    
    if (version === 3 && !policy[this.POLICIES.ALLOW_V3]) {
      throw new SecurityValidationError('OTR v3 not allowed by policy', 'V3_NOT_ALLOWED');
    }
  }

  /**
   * Extract account information from query message
   * 
   * @private
   * @param {string} message - Query message
   * @param {string} pattern - Query pattern to remove
   * @returns {string|null} Extracted account or null
   */
  static _extractAccount(message, pattern) {
    const remainder = message.substring(pattern.length).trim();
    return remainder.length > 0 ? remainder : null;
  }

  /**
   * Get security level for version
   * 
   * @private
   * @param {number} version - OTR version
   * @returns {string} Security level assessment
   */
  static _getSecurityLevel(version) {
    switch (version) {
      case 3: return 'high';
      case 2: return 'medium';
      default: return 'low';
    }
  }

  /**
   * Check if version downgrade is allowed by policy
   * 
   * @private
   * @param {number} toVersion - Target version
   * @param {number} fromVersion - Original version
   * @param {Object} policy - Version policy
   * @returns {boolean} True if downgrade is allowed
   */
  static _isDowngradeAllowed(toVersion, fromVersion, policy) {
    // Never allow downgrade if require encryption is set and target version doesn't support it
    if (policy[this.POLICIES.REQUIRE_ENCRYPTION] && toVersion < 2) {
      return false;
    }
    
    // Check specific version policies
    if (toVersion === 2 && !policy[this.POLICIES.ALLOW_V2]) {
      return false;
    }
    
    return true;
  }

  /**
   * Assess security impact of version downgrade
   * 
   * @private
   * @param {number} fromVersion - Original version
   * @param {number} toVersion - Target version
   * @returns {string} Security impact assessment
   */
  static _assessDowngradeImpact(fromVersion, toVersion) {
    if (fromVersion === 3 && toVersion === 2) {
      return 'medium'; // Loss of instance tags and extra symmetric key
    }
    
    return 'low';
  }
}

/**
 * Version-specific protocol factory
 */
export class ProtocolVersionFactory {
  /**
   * Create version-specific protocol handler
   * 
   * @param {number} version - OTR protocol version
   * @param {Object} options - Protocol options
   * @returns {Object} Version-specific protocol handler
   */
  static createProtocolHandler(version, options = {}) {
    switch (version) {
      case 2:
        return this._createV2Handler(options);
      case 3:
        return this._createV3Handler(options);
      default:
        throw new SecurityValidationError(`Unsupported protocol version: ${version}`, 'UNSUPPORTED_VERSION');
    }
  }

  /**
   * Create OTR v2 protocol handler
   * 
   * @private
   * @param {Object} options - Protocol options
   * @returns {Object} OTR v2 handler
   */
  static _createV2Handler(options) {
    return {
      version: 2,
      capabilities: VersionNegotiation.VERSION_CAPABILITIES[2],
      messageHandler: null, // Will be implemented in v2 support module
      akeHandler: null,
      smpHandler: null
    };
  }

  /**
   * Create OTR v3 protocol handler
   * 
   * @private
   * @param {Object} options - Protocol options
   * @returns {Object} OTR v3 handler
   */
  static _createV3Handler(options) {
    return {
      version: 3,
      capabilities: VersionNegotiation.VERSION_CAPABILITIES[3],
      messageHandler: null, // Use existing v3 handlers
      akeHandler: null,
      smpHandler: null
    };
  }
}
