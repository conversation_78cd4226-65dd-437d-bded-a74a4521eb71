/**
 * Protocol Tracer - Detailed Protocol Flow Tracing
 * 
 * Provides detailed tracing capabilities for WebOTR protocol flows with:
 * - Message flow visualization
 * - Protocol sequence analysis
 * - Timing and performance tracking
 * - Flow diagram generation
 * - Interactive trace exploration
 * 
 * Specialized component for analyzing protocol message flows and sequences.
 */

import { ProtocolDebugger, PROTOCOL_EVENT, DEBUG_LEVEL } from './protocol-debugger.js';

/**
 * Trace event types
 */
export const TRACE_EVENT = {
  FLOW_START: 'flow_start',
  FLOW_END: 'flow_end',
  MESSAGE_FLOW: 'message_flow',
  STATE_TRANSITION: 'state_transition',
  TIMING_POINT: 'timing_point',
  CHECKPOINT: 'checkpoint',
  BRANCH: 'branch',
  MERGE: 'merge'
};

/**
 * Flow analysis types
 */
export const FLOW_ANALYSIS = {
  SEQUENCE: 'sequence',
  TIMING: 'timing',
  DEPENDENCIES: 'dependencies',
  CRITICAL_PATH: 'critical_path',
  BOTTLENECKS: 'bottlenecks',
  PATTERNS: 'patterns'
};

/**
 * Trace levels
 */
export const TRACE_LEVEL = {
  BASIC: 'basic',
  DETAILED: 'detailed',
  VERBOSE: 'verbose',
  DEBUG: 'debug'
};

/**
 * Trace visualization formats
 */
export const TRACE_FORMAT = {
  SEQUENCE_DIAGRAM: 'sequence_diagram',
  FLOW_CHART: 'flow_chart',
  TIMELINE: 'timeline',
  NETWORK_GRAPH: 'network_graph',
  MERMAID: 'mermaid',
  JSON: 'json',
  TEXT: 'text'
};

/**
 * Protocol Tracer Implementation
 */
export class ProtocolTracer {
  /**
   * Create protocol tracer
   *
   * @param {ProtocolDebugger} protocolDebugger - Protocol debugger instance
   * @param {Object} options - Tracer options
   */
  constructor(protocolDebugger, options = {}) {
    this.debugger = protocolDebugger;
    this.options = {
      enableFlowTracking: true,
      enableTimingAnalysis: true,
      enableDependencyTracking: true,
      maxFlowDepth: 100,
      timingPrecision: 'milliseconds',
      autoGenerateDiagrams: true,
      ...options
    };
    
    // Flow tracking
    this.activeFlows = new Map();
    this.completedFlows = [];
    this.flowSequences = new Map();
    
    // Timing analysis
    this.timingPoints = new Map();
    this.performanceMetrics = new Map();
    
    // Dependencies
    this.dependencies = new Map();
    this.dependencyGraph = new Map();
    
    // Visualization data
    this.sequenceDiagrams = new Map();
    this.flowCharts = new Map();
    
    // Statistics
    this.stats = {
      flowsTracked: 0,
      sequencesGenerated: 0,
      timingPointsRecorded: 0,
      dependenciesTracked: 0,
      diagramsGenerated: 0
    };
    
    // Initialize tracer
    this._initializeTracer();
  }

  /**
   * Start flow tracing
   * 
   * @param {string} flowId - Flow identifier
   * @param {string} flowType - Type of flow
   * @param {Object} context - Flow context
   */
  startFlow(flowId, flowType, context = {}) {
    if (!this.options.enableFlowTracking) return;
    
    const flow = {
      id: flowId,
      type: flowType,
      startTime: performance.now(),
      context: context,
      events: [],
      timingPoints: [],
      dependencies: [],
      state: 'active'
    };
    
    this.activeFlows.set(flowId, flow);
    this.stats.flowsTracked++;
    
    // Log flow start
    this.debugger.logEvent(TRACE_EVENT.FLOW_START, {
      flowId: flowId,
      flowType: flowType,
      context: context
    });
    
    // Initialize sequence tracking
    if (!this.flowSequences.has(flowId)) {
      this.flowSequences.set(flowId, []);
    }
  }

  /**
   * End flow tracing
   * 
   * @param {string} flowId - Flow identifier
   * @param {Object} result - Flow result
   */
  endFlow(flowId, result = {}) {
    const flow = this.activeFlows.get(flowId);
    if (!flow) return;
    
    flow.endTime = performance.now();
    flow.duration = flow.endTime - flow.startTime;
    flow.result = result;
    flow.state = 'completed';
    
    // Move to completed flows
    this.completedFlows.push(flow);
    this.activeFlows.delete(flowId);
    
    // Log flow end
    this.debugger.logEvent(TRACE_EVENT.FLOW_END, {
      flowId: flowId,
      duration: flow.duration,
      result: result
    });
    
    // Generate diagrams if enabled
    if (this.options.autoGenerateDiagrams) {
      this._generateFlowDiagrams(flow);
    }
  }

  /**
   * Add timing point
   * 
   * @param {string} flowId - Flow identifier
   * @param {string} pointName - Timing point name
   * @param {Object} data - Additional data
   */
  addTimingPoint(flowId, pointName, data = {}) {
    if (!this.options.enableTimingAnalysis) return;
    
    const timestamp = performance.now();
    const timingPoint = {
      flowId: flowId,
      name: pointName,
      timestamp: timestamp,
      data: data
    };
    
    // Add to flow if active
    const flow = this.activeFlows.get(flowId);
    if (flow) {
      flow.timingPoints.push(timingPoint);
    }
    
    // Store in timing points map
    if (!this.timingPoints.has(flowId)) {
      this.timingPoints.set(flowId, []);
    }
    this.timingPoints.get(flowId).push(timingPoint);
    
    this.stats.timingPointsRecorded++;
    
    // Log timing point
    this.debugger.logEvent(TRACE_EVENT.TIMING_POINT, timingPoint);
  }

  /**
   * Track message in flow
   * 
   * @param {string} flowId - Flow identifier
   * @param {string} direction - 'sent' or 'received'
   * @param {Object} message - Protocol message
   * @param {Object} metadata - Message metadata
   */
  traceMessage(flowId, direction, message, metadata = {}) {
    const flow = this.activeFlows.get(flowId);
    if (!flow) return;
    
    const messageEvent = {
      type: 'message',
      direction: direction,
      message: this._sanitizeMessage(message),
      metadata: metadata,
      timestamp: performance.now(),
      sequenceNumber: flow.events.length
    };
    
    flow.events.push(messageEvent);
    
    // Add to sequence
    const sequence = this.flowSequences.get(flowId);
    if (sequence) {
      sequence.push({
        type: 'message',
        direction: direction,
        data: messageEvent,
        timestamp: messageEvent.timestamp
      });
    }
    
    // Log message flow
    this.debugger.logEvent(TRACE_EVENT.MESSAGE_FLOW, {
      flowId: flowId,
      direction: direction,
      messageType: message.type || 'unknown',
      sequenceNumber: messageEvent.sequenceNumber
    });
  }

  /**
   * Track state transition in flow
   * 
   * @param {string} flowId - Flow identifier
   * @param {string} component - Component name
   * @param {string} fromState - Previous state
   * @param {string} toState - New state
   * @param {Object} context - Transition context
   */
  traceStateTransition(flowId, component, fromState, toState, context = {}) {
    const flow = this.activeFlows.get(flowId);
    if (!flow) return;
    
    const transitionEvent = {
      type: 'state_transition',
      component: component,
      fromState: fromState,
      toState: toState,
      context: context,
      timestamp: performance.now(),
      sequenceNumber: flow.events.length
    };
    
    flow.events.push(transitionEvent);
    
    // Add to sequence
    const sequence = this.flowSequences.get(flowId);
    if (sequence) {
      sequence.push({
        type: 'state_transition',
        data: transitionEvent,
        timestamp: transitionEvent.timestamp
      });
    }
    
    // Log state transition
    this.debugger.logEvent(TRACE_EVENT.STATE_TRANSITION, {
      flowId: flowId,
      component: component,
      transition: `${fromState} -> ${toState}`,
      sequenceNumber: transitionEvent.sequenceNumber
    });
  }

  /**
   * Add dependency between flows
   * 
   * @param {string} dependentFlowId - Dependent flow ID
   * @param {string} dependencyFlowId - Dependency flow ID
   * @param {string} dependencyType - Type of dependency
   */
  addDependency(dependentFlowId, dependencyFlowId, dependencyType = 'requires') {
    if (!this.options.enableDependencyTracking) return;
    
    const dependency = {
      dependent: dependentFlowId,
      dependency: dependencyFlowId,
      type: dependencyType,
      timestamp: Date.now()
    };
    
    // Store dependency
    if (!this.dependencies.has(dependentFlowId)) {
      this.dependencies.set(dependentFlowId, []);
    }
    this.dependencies.get(dependentFlowId).push(dependency);
    
    // Update dependency graph
    this._updateDependencyGraph(dependency);
    
    this.stats.dependenciesTracked++;
  }

  /**
   * Analyze flow performance
   * 
   * @param {string} flowId - Flow identifier
   * @returns {Object} Performance analysis
   */
  analyzeFlowPerformance(flowId) {
    const flow = this._getFlow(flowId);
    if (!flow) return null;
    
    const timingPoints = this.timingPoints.get(flowId) || [];
    const analysis = {
      flowId: flowId,
      totalDuration: flow.duration,
      eventCount: flow.events.length,
      timingPointCount: timingPoints.length,
      phases: this._analyzeFlowPhases(flow, timingPoints),
      bottlenecks: this._identifyFlowBottlenecks(flow, timingPoints),
      efficiency: this._calculateFlowEfficiency(flow)
    };
    
    return analysis;
  }

  /**
   * Generate sequence diagram
   * 
   * @param {string} flowId - Flow identifier
   * @param {string} format - Diagram format
   * @returns {Object} Sequence diagram data
   */
  generateSequenceDiagram(flowId, format = TRACE_FORMAT.MERMAID) {
    const sequence = this.flowSequences.get(flowId);
    if (!sequence) return null;
    
    const diagram = {
      flowId: flowId,
      format: format,
      timestamp: Date.now(),
      data: this._buildSequenceDiagram(sequence, format)
    };
    
    this.sequenceDiagrams.set(flowId, diagram);
    this.stats.diagramsGenerated++;
    
    return diagram;
  }

  /**
   * Generate flow chart
   * 
   * @param {string} flowId - Flow identifier
   * @param {string} format - Chart format
   * @returns {Object} Flow chart data
   */
  generateFlowChart(flowId, format = TRACE_FORMAT.MERMAID) {
    const flow = this._getFlow(flowId);
    if (!flow) return null;
    
    const chart = {
      flowId: flowId,
      format: format,
      timestamp: Date.now(),
      data: this._buildFlowChart(flow, format)
    };
    
    this.flowCharts.set(flowId, chart);
    this.stats.diagramsGenerated++;
    
    return chart;
  }

  /**
   * Analyze flow patterns
   * 
   * @param {Array} flowIds - Flow IDs to analyze
   * @returns {Object} Pattern analysis
   */
  analyzeFlowPatterns(flowIds = null) {
    const flows = flowIds 
      ? flowIds.map(id => this._getFlow(id)).filter(f => f)
      : [...this.completedFlows, ...this.activeFlows.values()];
    
    const patterns = {
      commonSequences: this._findCommonSequences(flows),
      timingPatterns: this._analyzeTimingPatterns(flows),
      errorPatterns: this._analyzeErrorPatterns(flows),
      performancePatterns: this._analyzePerformancePatterns(flows)
    };
    
    return patterns;
  }

  /**
   * Export trace data
   * 
   * @param {Object} options - Export options
   * @returns {Object} Exported trace data
   */
  exportTraceData(options = {}) {
    const {
      includeActiveFlows = true,
      includeCompletedFlows = true,
      includeTimingData = true,
      includeDependencies = true,
      includeDiagrams = true,
      flowIds = null
    } = options;
    
    const exportData = {
      timestamp: Date.now(),
      options: this.options,
      statistics: this.stats
    };
    
    if (includeActiveFlows) {
      exportData.activeFlows = flowIds
        ? Array.from(this.activeFlows.values()).filter(f => flowIds.includes(f.id))
        : Array.from(this.activeFlows.values());
    }
    
    if (includeCompletedFlows) {
      exportData.completedFlows = flowIds
        ? this.completedFlows.filter(f => flowIds.includes(f.id))
        : this.completedFlows;
    }
    
    if (includeTimingData) {
      exportData.timingPoints = flowIds
        ? Object.fromEntries(
            Array.from(this.timingPoints.entries())
              .filter(([id]) => flowIds.includes(id))
          )
        : Object.fromEntries(this.timingPoints);
    }
    
    if (includeDependencies) {
      exportData.dependencies = Object.fromEntries(this.dependencies);
      exportData.dependencyGraph = Object.fromEntries(this.dependencyGraph);
    }
    
    if (includeDiagrams) {
      exportData.sequenceDiagrams = Object.fromEntries(this.sequenceDiagrams);
      exportData.flowCharts = Object.fromEntries(this.flowCharts);
    }
    
    return exportData;
  }

  /**
   * Get trace statistics
   * 
   * @returns {Object} Trace statistics
   */
  getStats() {
    return {
      ...this.stats,
      activeFlowsCount: this.activeFlows.size,
      completedFlowsCount: this.completedFlows.length,
      totalFlowsCount: this.activeFlows.size + this.completedFlows.length,
      averageFlowDuration: this._calculateAverageFlowDuration(),
      longestFlow: this._findLongestFlow(),
      shortestFlow: this._findShortestFlow()
    };
  }

  /**
   * Clear trace data
   *
   * @param {Object} options - Clear options
   */
  clearTraceData(options = {}) {
    const {
      clearActiveFlows = false,
      clearCompletedFlows = true,
      clearTimingData = true,
      clearDependencies = true,
      clearDiagrams = true
    } = options;

    if (clearActiveFlows) {
      this.activeFlows.clear();
    }

    if (clearCompletedFlows) {
      this.completedFlows.length = 0;
    }

    if (clearTimingData) {
      this.timingPoints.clear();
      this.performanceMetrics.clear();
    }

    if (clearDependencies) {
      this.dependencies.clear();
      this.dependencyGraph.clear();
    }

    if (clearDiagrams) {
      this.sequenceDiagrams.clear();
      this.flowCharts.clear();
    }
  }

  /**
   * Start trace for session
   *
   * @param {string} sessionId - Session identifier
   * @param {Object} options - Trace options
   */
  startTrace(sessionId, options = {}) {
    this.startFlow(sessionId, 'session', { sessionId, ...options });
  }

  /**
   * Stop trace for session
   *
   * @param {string} sessionId - Session identifier
   * @param {Object} result - Trace result
   */
  stopTrace(sessionId, result = {}) {
    this.endFlow(sessionId, result);
  }

  /**
   * Get trace data for session
   *
   * @param {string} sessionId - Session identifier
   * @returns {Object} Trace data
   */
  getTraceData(sessionId) {
    const flow = this._getFlow(sessionId);
    const sequence = this.flowSequences.get(sessionId) || [];
    const timingPoints = this.timingPoints.get(sessionId) || [];

    return {
      flow: flow,
      events: flow ? flow.events : [],
      sequence: sequence,
      timingPoints: timingPoints,
      dependencies: this.dependencies.get(sessionId) || [],
      diagrams: {
        sequence: this.sequenceDiagrams.get(sessionId),
        flowChart: this.flowCharts.get(sessionId)
      }
    };
  }

  /**
   * Export trace for session
   *
   * @param {string} sessionId - Session identifier
   * @param {Object} options - Export options
   * @returns {Object} Exported trace data
   */
  exportTrace(sessionId, options = {}) {
    const traceData = this.getTraceData(sessionId);
    const { format = TRACE_FORMAT.JSON } = options;

    return {
      sessionId: sessionId,
      format: format,
      timestamp: Date.now(),
      data: traceData,
      statistics: this.getStats()
    };
  }

  /**
   * Initialize tracer
   *
   * @private
   */
  _initializeTracer() {
    // Set up debugger event listeners
    this.debuggerListenerId = this.debugger.addEventListener('*', (entry) => {
      this._handleDebuggerEvent(entry);
    });
  }

  /**
   * Handle debugger events
   *
   * @private
   * @param {Object} entry - Debug entry
   */
  _handleDebuggerEvent(entry) {
    // Auto-trace certain events if flow is active
    if (entry.data.sessionId) {
      const flowId = entry.data.sessionId;

      switch (entry.type) {
        case PROTOCOL_EVENT.MESSAGE_SENT:
        case PROTOCOL_EVENT.MESSAGE_RECEIVED:
          if (this.activeFlows.has(flowId)) {
            this.traceMessage(
              flowId,
              entry.type === PROTOCOL_EVENT.MESSAGE_SENT ? 'sent' : 'received',
              entry.data.message,
              entry.data.metadata
            );
          }
          break;

        case PROTOCOL_EVENT.STATE_CHANGE:
          if (this.activeFlows.has(flowId)) {
            this.traceStateTransition(
              flowId,
              entry.data.component,
              entry.data.oldState,
              entry.data.newState,
              entry.data.context
            );
          }
          break;
      }
    }
  }

  /**
   * Get flow by ID
   *
   * @private
   * @param {string} flowId - Flow ID
   * @returns {Object|null} Flow object
   */
  _getFlow(flowId) {
    return this.activeFlows.get(flowId) ||
           this.completedFlows.find(f => f.id === flowId) ||
           null;
  }

  /**
   * Sanitize message for tracing
   *
   * @private
   * @param {Object} message - Message to sanitize
   * @returns {Object} Sanitized message
   */
  _sanitizeMessage(message) {
    // Create a copy and remove sensitive data
    const sanitized = JSON.parse(JSON.stringify(message));

    const sensitiveFields = ['privateKey', 'secretKey', 'password', 'token'];

    const sanitizeObject = (obj) => {
      if (typeof obj !== 'object' || obj === null) return obj;

      for (const [key, value] of Object.entries(obj)) {
        if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
          obj[key] = '[REDACTED]';
        } else if (typeof value === 'object') {
          sanitizeObject(value);
        }
      }
    };

    sanitizeObject(sanitized);
    return sanitized;
  }

  /**
   * Generate flow diagrams
   *
   * @private
   * @param {Object} flow - Flow object
   */
  _generateFlowDiagrams(flow) {
    try {
      // Generate sequence diagram
      this.generateSequenceDiagram(flow.id);

      // Generate flow chart
      this.generateFlowChart(flow.id);
    } catch (error) {
      console.error('Error generating flow diagrams:', error);
    }
  }

  /**
   * Build sequence diagram
   *
   * @private
   * @param {Array} sequence - Sequence events
   * @param {string} format - Diagram format
   * @returns {string} Diagram data
   */
  _buildSequenceDiagram(sequence, format) {
    if (format === TRACE_FORMAT.MERMAID) {
      return this._buildMermaidSequenceDiagram(sequence);
    }

    // Default to simple text format
    return this._buildTextSequenceDiagram(sequence);
  }

  /**
   * Build Mermaid sequence diagram
   *
   * @private
   * @param {Array} sequence - Sequence events
   * @returns {string} Mermaid diagram
   */
  _buildMermaidSequenceDiagram(sequence) {
    let mermaid = 'sequenceDiagram\n';
    mermaid += '    participant Client\n';
    mermaid += '    participant Server\n';
    mermaid += '    participant Protocol\n\n';

    for (const event of sequence) {
      switch (event.type) {
        case 'message':
          const from = event.data.direction === 'sent' ? 'Client' : 'Server';
          const to = event.data.direction === 'sent' ? 'Server' : 'Client';
          const messageType = event.data.message.type || 'Message';
          mermaid += `    ${from}->>+${to}: ${messageType}\n`;
          break;

        case 'state_transition':
          mermaid += `    Note over Protocol: ${event.data.component}: ${event.data.fromState} -> ${event.data.toState}\n`;
          break;
      }
    }

    return mermaid;
  }

  /**
   * Build text sequence diagram
   *
   * @private
   * @param {Array} sequence - Sequence events
   * @returns {string} Text diagram
   */
  _buildTextSequenceDiagram(sequence) {
    let text = 'Protocol Sequence:\n';
    text += '='.repeat(50) + '\n\n';

    for (let i = 0; i < sequence.length; i++) {
      const event = sequence[i];
      const timestamp = new Date(event.timestamp).toISOString();

      text += `${i + 1}. [${timestamp}] `;

      switch (event.type) {
        case 'message':
          text += `${event.data.direction.toUpperCase()}: ${event.data.message.type || 'Message'}\n`;
          break;

        case 'state_transition':
          text += `STATE: ${event.data.component} ${event.data.fromState} -> ${event.data.toState}\n`;
          break;

        default:
          text += `${event.type.toUpperCase()}\n`;
      }
    }

    return text;
  }

  /**
   * Build flow chart
   *
   * @private
   * @param {Object} flow - Flow object
   * @param {string} format - Chart format
   * @returns {string} Flow chart data
   */
  _buildFlowChart(flow, format) {
    if (format === TRACE_FORMAT.MERMAID) {
      return this._buildMermaidFlowChart(flow);
    }

    return this._buildTextFlowChart(flow);
  }

  /**
   * Build Mermaid flow chart
   *
   * @private
   * @param {Object} flow - Flow object
   * @returns {string} Mermaid flow chart
   */
  _buildMermaidFlowChart(flow) {
    let mermaid = 'flowchart TD\n';
    mermaid += `    Start([Flow Start: ${flow.type}])\n`;

    for (let i = 0; i < flow.events.length; i++) {
      const event = flow.events[i];
      const nodeId = `E${i}`;

      switch (event.type) {
        case 'message':
          mermaid += `    ${nodeId}[${event.direction}: ${event.message.type || 'Message'}]\n`;
          break;

        case 'state_transition':
          mermaid += `    ${nodeId}{${event.component}: ${event.toState}}\n`;
          break;

        default:
          mermaid += `    ${nodeId}[${event.type}]\n`;
      }

      if (i === 0) {
        mermaid += `    Start --> ${nodeId}\n`;
      } else {
        mermaid += `    E${i-1} --> ${nodeId}\n`;
      }
    }

    if (flow.state === 'completed') {
      mermaid += `    End([Flow End])\n`;
      if (flow.events.length > 0) {
        mermaid += `    E${flow.events.length - 1} --> End\n`;
      } else {
        mermaid += `    Start --> End\n`;
      }
    }

    return mermaid;
  }

  /**
   * Analyze flow phases
   *
   * @private
   * @param {Object} flow - Flow object
   * @param {Array} timingPoints - Timing points
   * @returns {Array} Flow phases
   */
  _analyzeFlowPhases(flow, timingPoints) {
    const phases = [];

    if (timingPoints.length < 2) {
      return [{
        name: 'Complete Flow',
        startTime: flow.startTime,
        endTime: flow.endTime || performance.now(),
        duration: flow.duration || (performance.now() - flow.startTime)
      }];
    }

    for (let i = 0; i < timingPoints.length - 1; i++) {
      const start = timingPoints[i];
      const end = timingPoints[i + 1];

      phases.push({
        name: `${start.name} -> ${end.name}`,
        startTime: start.timestamp,
        endTime: end.timestamp,
        duration: end.timestamp - start.timestamp
      });
    }

    return phases;
  }

  /**
   * Identify flow bottlenecks
   *
   * @private
   * @param {Object} flow - Flow object
   * @param {Array} timingPoints - Timing points
   * @returns {Array} Bottlenecks
   */
  _identifyFlowBottlenecks(flow, timingPoints) {
    const phases = this._analyzeFlowPhases(flow, timingPoints);
    if (phases.length === 0) return [];

    const avgDuration = phases.reduce((sum, phase) => sum + phase.duration, 0) / phases.length;
    const threshold = avgDuration * 2; // Phases taking more than 2x average

    return phases
      .filter(phase => phase.duration > threshold)
      .map(phase => ({
        ...phase,
        severity: phase.duration > avgDuration * 5 ? 'critical' : 'warning',
        impact: (phase.duration / flow.duration) * 100
      }));
  }

  /**
   * Calculate flow efficiency
   *
   * @private
   * @param {Object} flow - Flow object
   * @returns {Object} Efficiency metrics
   */
  _calculateFlowEfficiency(flow) {
    const messageEvents = flow.events.filter(e => e.type === 'message');
    const stateEvents = flow.events.filter(e => e.type === 'state_transition');

    return {
      messageEfficiency: messageEvents.length / (flow.duration || 1),
      stateEfficiency: stateEvents.length / (flow.duration || 1),
      overallEfficiency: flow.events.length / (flow.duration || 1),
      eventDensity: flow.events.length / Math.max(1, flow.duration / 1000) // Events per second
    };
  }

  /**
   * Update dependency graph
   *
   * @private
   * @param {Object} dependency - Dependency object
   */
  _updateDependencyGraph(dependency) {
    if (!this.dependencyGraph.has(dependency.dependent)) {
      this.dependencyGraph.set(dependency.dependent, new Set());
    }
    this.dependencyGraph.get(dependency.dependent).add(dependency.dependency);
  }

  /**
   * Calculate average flow duration
   *
   * @private
   * @returns {number} Average duration
   */
  _calculateAverageFlowDuration() {
    const completedFlows = this.completedFlows.filter(f => f.duration);
    if (completedFlows.length === 0) return 0;

    const totalDuration = completedFlows.reduce((sum, flow) => sum + flow.duration, 0);
    return totalDuration / completedFlows.length;
  }

  /**
   * Find longest flow
   *
   * @private
   * @returns {Object|null} Longest flow
   */
  _findLongestFlow() {
    const completedFlows = this.completedFlows.filter(f => f.duration);
    if (completedFlows.length === 0) return null;

    return completedFlows.reduce((longest, flow) =>
      flow.duration > (longest?.duration || 0) ? flow : longest
    );
  }

  /**
   * Find shortest flow
   *
   * @private
   * @returns {Object|null} Shortest flow
   */
  _findShortestFlow() {
    const completedFlows = this.completedFlows.filter(f => f.duration);
    if (completedFlows.length === 0) return null;

    return completedFlows.reduce((shortest, flow) =>
      flow.duration < (shortest?.duration || Infinity) ? flow : shortest
    );
  }

  /**
   * Build text flow chart
   *
   * @private
   * @param {Object} flow - Flow object
   * @returns {string} Text flow chart
   */
  _buildTextFlowChart(flow) {
    let text = `Flow Chart: ${flow.type}\n`;
    text += '='.repeat(50) + '\n\n';
    text += `Start: ${new Date(flow.startTime).toISOString()}\n`;

    for (let i = 0; i < flow.events.length; i++) {
      const event = flow.events[i];
      text += `  ${i + 1}. ${event.type}: `;

      switch (event.type) {
        case 'message':
          text += `${event.direction} ${event.message.type || 'Message'}\n`;
          break;
        case 'state_transition':
          text += `${event.component} ${event.fromState} -> ${event.toState}\n`;
          break;
        default:
          text += `${event.type}\n`;
      }
    }

    if (flow.endTime) {
      text += `End: ${new Date(flow.endTime).toISOString()}\n`;
      text += `Duration: ${flow.duration}ms\n`;
    }

    return text;
  }

  /**
   * Find common sequences
   *
   * @private
   * @param {Array} flows - Flow objects
   * @returns {Array} Common sequences
   */
  _findCommonSequences(flows) {
    // Simple implementation - find sequences that appear in multiple flows
    const sequences = new Map();

    for (const flow of flows) {
      const eventTypes = flow.events.map(e => e.type);
      for (let i = 0; i < eventTypes.length - 1; i++) {
        const sequence = eventTypes.slice(i, i + 2).join(' -> ');
        sequences.set(sequence, (sequences.get(sequence) || 0) + 1);
      }
    }

    return Array.from(sequences.entries())
      .filter(([seq, count]) => count > 1)
      .map(([sequence, count]) => ({ sequence, count }))
      .sort((a, b) => b.count - a.count);
  }

  /**
   * Analyze timing patterns
   *
   * @private
   * @param {Array} flows - Flow objects
   * @returns {Object} Timing patterns
   */
  _analyzeTimingPatterns(flows) {
    const durations = flows.filter(f => f.duration).map(f => f.duration);
    if (durations.length === 0) return { patterns: [] };

    const avg = durations.reduce((sum, d) => sum + d, 0) / durations.length;
    const variance = durations.reduce((sum, d) => sum + Math.pow(d - avg, 2), 0) / durations.length;

    return {
      averageDuration: avg,
      variance: variance,
      patterns: [
        { type: 'fast', threshold: avg * 0.5, count: durations.filter(d => d < avg * 0.5).length },
        { type: 'normal', threshold: avg, count: durations.filter(d => d >= avg * 0.5 && d <= avg * 1.5).length },
        { type: 'slow', threshold: avg * 1.5, count: durations.filter(d => d > avg * 1.5).length }
      ]
    };
  }

  /**
   * Analyze error patterns
   *
   * @private
   * @param {Array} flows - Flow objects
   * @returns {Object} Error patterns
   */
  _analyzeErrorPatterns(flows) {
    const errorFlows = flows.filter(f => f.result && f.result.error);
    const errorTypes = new Map();

    for (const flow of errorFlows) {
      const errorType = flow.result.error.name || 'Unknown';
      errorTypes.set(errorType, (errorTypes.get(errorType) || 0) + 1);
    }

    return {
      totalErrors: errorFlows.length,
      errorRate: errorFlows.length / flows.length,
      errorTypes: Array.from(errorTypes.entries()).map(([type, count]) => ({ type, count }))
    };
  }

  /**
   * Analyze performance patterns
   *
   * @private
   * @param {Array} flows - Flow objects
   * @returns {Object} Performance patterns
   */
  _analyzePerformancePatterns(flows) {
    const completedFlows = flows.filter(f => f.duration);
    if (completedFlows.length === 0) return { patterns: [] };

    const durations = completedFlows.map(f => f.duration);
    const eventCounts = completedFlows.map(f => f.events.length);

    return {
      durationTrend: this._calculateTrend(durations),
      eventCountTrend: this._calculateTrend(eventCounts),
      efficiency: {
        avgEventsPerMs: eventCounts.reduce((sum, c) => sum + c, 0) / durations.reduce((sum, d) => sum + d, 0),
        patterns: []
      }
    };
  }

  /**
   * Calculate trend
   *
   * @private
   * @param {Array} values - Values to analyze
   * @returns {string} Trend direction
   */
  _calculateTrend(values) {
    if (values.length < 2) return 'stable';

    const firstHalf = values.slice(0, Math.floor(values.length / 2));
    const secondHalf = values.slice(Math.floor(values.length / 2));

    const firstAvg = firstHalf.reduce((sum, v) => sum + v, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, v) => sum + v, 0) / secondHalf.length;

    const change = (secondAvg - firstAvg) / firstAvg;

    if (change > 0.1) return 'increasing';
    if (change < -0.1) return 'decreasing';
    return 'stable';
  }

  /**
   * Cleanup resources
   */
  destroy() {
    if (this.debuggerListenerId) {
      this.debugger.removeEventListener('*', this.debuggerListenerId);
    }

    this.activeFlows.clear();
    this.completedFlows.length = 0;
    this.flowSequences.clear();
    this.timingPoints.clear();
    this.dependencies.clear();
    this.dependencyGraph.clear();
    this.sequenceDiagrams.clear();
    this.flowCharts.clear();
  }
}
