/**
 * Configuration Management System - Policy Manager
 * 
 * Implements enterprise-grade configuration and policy management with:
 * - Hierarchical policy management
 * - Context-aware configuration
 * - Runtime policy updates
 * - Comprehensive validation
 * - Security controls and access management
 * 
 * Based on enterprise configuration management patterns.
 */

import { CryptoValidation, SecurityValidationError } from '../security/validation.js';
import { SecureMemory } from '../security/secure-memory.js';
import { ConstantTimeOps } from '../security/constant-time.js';
import { globalErrorRecovery, ERROR_TYPES } from '../security/error-recovery.js';

/**
 * Policy categories and types
 */
export const POLICY_CATEGORY = {
  SECURITY: 'security',
  PROTOCOL: 'protocol',
  PERFORMANCE: 'performance',
  LOGGING: 'logging',
  NETWORK: 'network',
  USER: 'user'
};

/**
 * Policy validation levels
 */
export const VALIDATION_LEVEL = {
  NONE: 'none',           // No validation
  BASIC: 'basic',         // Basic type checking
  STRICT: 'strict',       // Comprehensive validation
  ENTERPRISE: 'enterprise' // Full enterprise validation
};

/**
 * Policy access levels
 */
export const ACCESS_LEVEL = {
  PUBLIC: 'public',       // Anyone can read/write
  PROTECTED: 'protected', // Authenticated users can read/write
  RESTRICTED: 'restricted', // Admin users only
  SYSTEM: 'system'        // System-level policies only
};

/**
 * Default policy schemas for validation
 */
export const DEFAULT_POLICY_SCHEMAS = {
  [POLICY_CATEGORY.SECURITY]: {
    requireEncryption: { type: 'boolean', default: true, access: ACCESS_LEVEL.RESTRICTED },
    allowV2: { type: 'boolean', default: true, access: ACCESS_LEVEL.PROTECTED },
    allowV3: { type: 'boolean', default: true, access: ACCESS_LEVEL.PROTECTED },
    preferV3: { type: 'boolean', default: true, access: ACCESS_LEVEL.PROTECTED },
    sessionTimeoutMs: { type: 'number', min: 30000, max: 3600000, default: 300000, access: ACCESS_LEVEL.PROTECTED },
    maxRetryAttempts: { type: 'number', min: 1, max: 10, default: 3, access: ACCESS_LEVEL.PROTECTED },
    enableSecurityValidation: { type: 'boolean', default: true, access: ACCESS_LEVEL.RESTRICTED }
  },
  
  [POLICY_CATEGORY.PROTOCOL]: {
    maxBufferSize: { type: 'number', min: 10, max: 1000, default: 100, access: ACCESS_LEVEL.PROTECTED },
    maxGapSize: { type: 'number', min: 1, max: 50, default: 10, access: ACCESS_LEVEL.PROTECTED },
    replayWindowSize: { type: 'number', min: 8, max: 256, default: 64, access: ACCESS_LEVEL.PROTECTED },
    enableStatePersistence: { type: 'boolean', default: true, access: ACCESS_LEVEL.PROTECTED },
    cleanupIntervalMs: { type: 'number', min: 10000, max: 300000, default: 60000, access: ACCESS_LEVEL.PROTECTED }
  },
  
  [POLICY_CATEGORY.PERFORMANCE]: {
    enableOptimizations: { type: 'boolean', default: true, access: ACCESS_LEVEL.PUBLIC },
    maxConcurrentSessions: { type: 'number', min: 1, max: 100, default: 10, access: ACCESS_LEVEL.PROTECTED },
    memoryPoolSize: { type: 'number', min: 1024, max: 104857600, default: 1048576, access: ACCESS_LEVEL.PROTECTED },
    enableCaching: { type: 'boolean', default: true, access: ACCESS_LEVEL.PUBLIC }
  },
  
  [POLICY_CATEGORY.LOGGING]: {
    enableDetailedLogging: { type: 'boolean', default: false, access: ACCESS_LEVEL.PROTECTED },
    logLevel: { type: 'string', enum: ['error', 'warn', 'info', 'debug'], default: 'info', access: ACCESS_LEVEL.PROTECTED },
    enableSecurityAudit: { type: 'boolean', default: true, access: ACCESS_LEVEL.RESTRICTED },
    maxLogSize: { type: 'number', min: 1024, max: 104857600, default: 10485760, access: ACCESS_LEVEL.PROTECTED }
  }
};

/**
 * Enterprise Configuration and Policy Manager
 */
export class PolicyManager {
  /**
   * Create policy manager
   * 
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      validationLevel: VALIDATION_LEVEL.STRICT,
      enableAccessControl: true,
      enableAuditLogging: true,
      enableEncryption: true,
      maxPolicySize: 1048576, // 1MB
      ...options
    };
    
    // Policy storage
    this.policies = new Map();
    this.policySchemas = new Map();
    this.contextualPolicies = new Map();
    this.policyHistory = new Map();
    
    // Access control
    this.accessControl = new Map();
    this.currentUser = options.currentUser || 'anonymous';
    this.userRoles = new Set(options.userRoles || ['user']);
    
    // Audit and monitoring
    this.auditLog = [];
    this.changeListeners = new Set();
    this.validationCache = new Map();
    
    // Statistics
    this.stats = {
      policiesSet: 0,
      policiesRead: 0,
      validationFailures: 0,
      accessDenials: 0,
      contextualOverrides: 0
    };
    
    // Initialize with default schemas
    this._initializeDefaultSchemas();
    
    // Set up cleanup
    this.cleanupTimer = setInterval(() => this._performCleanup(), 300000); // 5 minutes
  }

  /**
   * Set policy value with validation and access control
   * 
   * @param {string} key - Policy key (category.name format)
   * @param {*} value - Policy value
   * @param {Object} context - Context for policy application
   * @returns {boolean} True if policy was set successfully
   */
  setPolicy(key, value, context = {}) {
    try {
      // Validate key format
      if (!this._validatePolicyKey(key)) {
        throw new SecurityValidationError(`Invalid policy key format: ${key}`, 'INVALID_POLICY_KEY');
      }
      
      // Check access permissions
      if (!this._checkAccess(key, 'write', context)) {
        this.stats.accessDenials++;
        throw new SecurityValidationError(`Access denied for policy: ${key}`, 'ACCESS_DENIED');
      }
      
      // Validate policy value
      if (!this._validatePolicyValue(key, value)) {
        this.stats.validationFailures++;
        throw new SecurityValidationError(`Invalid policy value for: ${key}`, 'INVALID_POLICY_VALUE');
      }
      
      // Store previous value for rollback
      const previousValue = this.policies.get(key);
      
      // Set the policy
      this.policies.set(key, {
        value: value,
        timestamp: Date.now(),
        user: this.currentUser,
        context: { ...context },
        version: this._getNextVersion(key)
      });
      
      // Update history
      this._updatePolicyHistory(key, previousValue, value, context);
      
      // Audit log
      this._auditPolicyChange('SET', key, previousValue?.value, value, context);
      
      // Notify listeners
      this._notifyPolicyChange(key, value, previousValue?.value, context);
      
      // Update statistics
      this.stats.policiesSet++;
      
      return true;
      
    } catch (error) {
      this._handleError(error, 'setPolicy', { key, value, context });
      return false;
    }
  }

  /**
   * Get policy value with context awareness
   * 
   * @param {string} key - Policy key
   * @param {Object} context - Context for policy resolution
   * @returns {*} Policy value or undefined
   */
  getPolicy(key, context = {}) {
    try {
      // Check access permissions
      if (!this._checkAccess(key, 'read', context)) {
        this.stats.accessDenials++;
        return undefined;
      }
      
      // Check for contextual override first
      const contextualValue = this._getContextualPolicy(key, context);
      if (contextualValue !== undefined) {
        this.stats.contextualOverrides++;
        return contextualValue;
      }
      
      // Get base policy
      const policy = this.policies.get(key);
      const value = policy ? policy.value : this._getDefaultValue(key);
      
      // Update statistics
      this.stats.policiesRead++;
      
      return value;
      
    } catch (error) {
      this._handleError(error, 'getPolicy', { key, context });
      return undefined;
    }
  }

  /**
   * Get effective policy configuration for context
   * 
   * @param {Object} context - Context for policy resolution
   * @returns {Object} Effective policy configuration
   */
  getEffectivePolicy(context = {}) {
    const effectivePolicy = {};
    
    // Get all policy schemas
    for (const [category, schema] of this.policySchemas) {
      effectivePolicy[category] = {};
      
      for (const [name, config] of Object.entries(schema)) {
        const key = `${category}.${name}`;
        const value = this.getPolicy(key, context);
        
        if (value !== undefined) {
          effectivePolicy[category][name] = value;
        }
      }
    }
    
    return effectivePolicy;
  }

  /**
   * Validate policy against schema
   * 
   * @param {string} key - Policy key
   * @param {*} value - Policy value
   * @param {Object} schema - Policy schema (optional)
   * @returns {boolean} True if policy is valid
   */
  validatePolicy(key, value, schema = null) {
    try {
      if (this.options.validationLevel === VALIDATION_LEVEL.NONE) {
        return true;
      }
      
      // Check validation cache
      const cacheKey = `${key}:${JSON.stringify(value)}`;
      if (this.validationCache.has(cacheKey)) {
        return this.validationCache.get(cacheKey);
      }
      
      const result = this._validatePolicyValue(key, value, schema);
      
      // Cache result
      this.validationCache.set(cacheKey, result);
      
      return result;
      
    } catch (error) {
      this._handleError(error, 'validatePolicy', { key, value });
      return false;
    }
  }

  /**
   * Validate policy change before applying
   * 
   * @param {string} key - Policy key
   * @param {*} oldValue - Current value
   * @param {*} newValue - New value
   * @returns {Object} Validation result
   */
  validatePolicyChange(key, oldValue, newValue) {
    try {
      const validation = {
        isValid: true,
        warnings: [],
        errors: [],
        securityImpact: 'none'
      };
      
      // Basic validation
      if (!this.validatePolicy(key, newValue)) {
        validation.isValid = false;
        validation.errors.push('Policy value validation failed');
      }
      
      // Security impact assessment
      const securityImpact = this._assessSecurityImpact(key, oldValue, newValue);
      validation.securityImpact = securityImpact.level;
      
      if (securityImpact.warnings.length > 0) {
        validation.warnings.push(...securityImpact.warnings);
      }
      
      // Enterprise-level validation
      if (this.options.validationLevel === VALIDATION_LEVEL.ENTERPRISE) {
        const enterpriseValidation = this._performEnterpriseValidation(key, oldValue, newValue);
        if (!enterpriseValidation.isValid) {
          validation.isValid = false;
          validation.errors.push(...enterpriseValidation.errors);
        }
      }
      
      return validation;
      
    } catch (error) {
      return {
        isValid: false,
        errors: [error.message],
        warnings: [],
        securityImpact: 'unknown'
      };
    }
  }

  /**
   * Apply contextual policies for specific context
   * 
   * @param {Object} context - Context object
   * @param {Object} basePolicies - Base policies to override
   * @returns {Object} Policies with contextual overrides applied
   */
  applyContextualPolicies(context, basePolicies = {}) {
    const contextualPolicies = { ...basePolicies };
    
    // Apply context-specific overrides
    for (const [contextKey, overrides] of this.contextualPolicies) {
      if (this._matchesContext(context, contextKey)) {
        Object.assign(contextualPolicies, overrides);
      }
    }
    
    return contextualPolicies;
  }

  /**
   * Inherit policies from parent context
   * 
   * @param {Object} parentContext - Parent context
   * @param {Object} childContext - Child context
   * @returns {Object} Inherited policy configuration
   */
  inheritPolicies(parentContext, childContext) {
    const parentPolicies = this.getEffectivePolicy(parentContext);
    const childPolicies = this.getEffectivePolicy(childContext);
    
    // Merge with child taking precedence
    const inheritedPolicies = {};
    
    for (const category of Object.keys(parentPolicies)) {
      inheritedPolicies[category] = {
        ...parentPolicies[category],
        ...childPolicies[category]
      };
    }
    
    return inheritedPolicies;
  }

  /**
   * Update policy at runtime
   * 
   * @param {string} key - Policy key
   * @param {*} value - New policy value
   * @param {boolean} immediate - Apply immediately or queue for next cycle
   * @returns {boolean} True if update was successful
   */
  updatePolicy(key, value, immediate = false) {
    if (immediate) {
      return this.setPolicy(key, value, { immediate: true });
    } else {
      // Queue for next update cycle
      this._queuePolicyUpdate(key, value);
      return true;
    }
  }

  /**
   * Reload policies from source
   * 
   * @param {Object} source - Policy source (file, database, etc.)
   * @returns {Promise<boolean>} True if reload was successful
   */
  async reloadPolicies(source) {
    try {
      if (typeof source === 'object' && source !== null) {
        // Load from object
        for (const [key, value] of Object.entries(source)) {
          this.setPolicy(key, value, { source: 'reload' });
        }
        return true;
      }
      
      // Could implement file/database loading here
      return false;
      
    } catch (error) {
      this._handleError(error, 'reloadPolicies', { source });
      return false;
    }
  }

  /**
   * Get policy statistics and metrics
   * 
   * @returns {Object} Policy statistics
   */
  getStats() {
    return {
      ...this.stats,
      totalPolicies: this.policies.size,
      totalSchemas: this.policySchemas.size,
      contextualPolicies: this.contextualPolicies.size,
      auditLogSize: this.auditLog.length,
      validationCacheSize: this.validationCache.size,
      changeListeners: this.changeListeners.size
    };
  }

  /**
   * Add policy change listener
   * 
   * @param {Function} listener - Change listener function
   */
  addChangeListener(listener) {
    if (typeof listener === 'function') {
      this.changeListeners.add(listener);
    }
  }

  /**
   * Remove policy change listener
   * 
   * @param {Function} listener - Change listener function
   */
  removeChangeListener(listener) {
    this.changeListeners.delete(listener);
  }

  /**
   * Export policies for backup/transfer
   * 
   * @param {Object} options - Export options
   * @returns {Object} Exported policy data
   */
  exportPolicies(options = {}) {
    const exported = {
      version: '1.0',
      timestamp: Date.now(),
      policies: {},
      schemas: {},
      metadata: {
        totalPolicies: this.policies.size,
        exportedBy: this.currentUser,
        options: options
      }
    };
    
    // Export policies
    for (const [key, policy] of this.policies) {
      if (!options.excludeSystem || !key.startsWith('system.')) {
        exported.policies[key] = {
          value: policy.value,
          timestamp: policy.timestamp,
          version: policy.version
        };
      }
    }
    
    // Export schemas if requested
    if (options.includeSchemas) {
      for (const [category, schema] of this.policySchemas) {
        exported.schemas[category] = schema;
      }
    }
    
    return exported;
  }

  /**
   * Import policies from backup/transfer
   * 
   * @param {Object} data - Imported policy data
   * @param {Object} options - Import options
   * @returns {boolean} True if import was successful
   */
  importPolicies(data, options = {}) {
    try {
      if (!data || !data.policies) {
        throw new SecurityValidationError('Invalid import data', 'INVALID_IMPORT_DATA');
      }
      
      let imported = 0;
      let failed = 0;
      
      for (const [key, policy] of Object.entries(data.policies)) {
        try {
          if (this.setPolicy(key, policy.value, { source: 'import', ...options })) {
            imported++;
          } else {
            failed++;
          }
        } catch (error) {
          failed++;
        }
      }
      
      this._auditPolicyChange('IMPORT', 'bulk', null, { imported, failed }, options);
      
      return failed === 0;
      
    } catch (error) {
      this._handleError(error, 'importPolicies', { data, options });
      return false;
    }
  }

  /**
   * Reset policies to defaults
   * 
   * @param {Array<string>} categories - Categories to reset (optional)
   */
  resetToDefaults(categories = null) {
    const categoriesToReset = categories || Array.from(this.policySchemas.keys());
    
    for (const category of categoriesToReset) {
      const schema = this.policySchemas.get(category);
      if (schema) {
        for (const [name, config] of Object.entries(schema)) {
          const key = `${category}.${name}`;
          if (config.default !== undefined) {
            this.setPolicy(key, config.default, { source: 'reset' });
          }
        }
      }
    }
  }

  /**
   * Cleanup resources
   */
  destroy() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
    
    this.policies.clear();
    this.policySchemas.clear();
    this.contextualPolicies.clear();
    this.policyHistory.clear();
    this.accessControl.clear();
    this.auditLog.length = 0;
    this.changeListeners.clear();
    this.validationCache.clear();
  }

  /**
   * Initialize default policy schemas
   * 
   * @private
   */
  _initializeDefaultSchemas() {
    for (const [category, schema] of Object.entries(DEFAULT_POLICY_SCHEMAS)) {
      this.policySchemas.set(category, schema);
    }
  }

  /**
   * Validate policy key format
   *
   * @private
   * @param {string} key - Policy key to validate
   * @returns {boolean} True if key format is valid
   */
  _validatePolicyKey(key) {
    if (typeof key !== 'string' || key.length === 0) return false;

    // Key format: category.name
    const parts = key.split('.');
    return parts.length === 2 && parts[0].length > 0 && parts[1].length > 0;
  }

  /**
   * Check access permissions for policy operation
   *
   * @private
   * @param {string} key - Policy key
   * @param {string} operation - Operation (read/write)
   * @param {Object} context - Operation context
   * @returns {boolean} True if access is allowed
   */
  _checkAccess(key, operation, context) {
    if (!this.options.enableAccessControl) return true;

    const [category, name] = key.split('.');
    const schema = this.policySchemas.get(category);

    if (!schema || !schema[name]) return true; // Allow if no schema

    const accessLevel = schema[name].access || ACCESS_LEVEL.PUBLIC;

    switch (accessLevel) {
      case ACCESS_LEVEL.PUBLIC:
        return true;
      case ACCESS_LEVEL.PROTECTED:
        return this.userRoles.has('user') || this.userRoles.has('admin');
      case ACCESS_LEVEL.RESTRICTED:
        return this.userRoles.has('admin');
      case ACCESS_LEVEL.SYSTEM:
        return this.userRoles.has('system') || context.system === true;
      default:
        return false;
    }
  }

  /**
   * Validate policy value against schema
   *
   * @private
   * @param {string} key - Policy key
   * @param {*} value - Policy value
   * @param {Object} schema - Policy schema (optional)
   * @returns {boolean} True if value is valid
   */
  _validatePolicyValue(key, value, schema = null) {
    const [category, name] = key.split('.');
    const policySchema = schema || this.policySchemas.get(category);

    if (!policySchema || !policySchema[name]) {
      return this.options.validationLevel === VALIDATION_LEVEL.NONE;
    }

    const config = policySchema[name];

    // Type validation
    if (config.type && typeof value !== config.type) {
      return false;
    }

    // Enum validation
    if (config.enum && !config.enum.includes(value)) {
      return false;
    }

    // Range validation for numbers
    if (config.type === 'number') {
      if (config.min !== undefined && value < config.min) return false;
      if (config.max !== undefined && value > config.max) return false;
    }

    // String length validation
    if (config.type === 'string') {
      if (config.minLength !== undefined && value.length < config.minLength) return false;
      if (config.maxLength !== undefined && value.length > config.maxLength) return false;
    }

    return true;
  }

  /**
   * Get contextual policy override
   *
   * @private
   * @param {string} key - Policy key
   * @param {Object} context - Context object
   * @returns {*} Contextual value or undefined
   */
  _getContextualPolicy(key, context) {
    for (const [contextKey, overrides] of this.contextualPolicies) {
      if (this._matchesContext(context, contextKey) && overrides[key] !== undefined) {
        return overrides[key];
      }
    }
    return undefined;
  }

  /**
   * Get default value for policy
   *
   * @private
   * @param {string} key - Policy key
   * @returns {*} Default value or undefined
   */
  _getDefaultValue(key) {
    const [category, name] = key.split('.');
    const schema = this.policySchemas.get(category);
    return schema && schema[name] ? schema[name].default : undefined;
  }

  /**
   * Get next version number for policy
   *
   * @private
   * @param {string} key - Policy key
   * @returns {number} Next version number
   */
  _getNextVersion(key) {
    const current = this.policies.get(key);
    return current ? (current.version || 0) + 1 : 1;
  }

  /**
   * Update policy history
   *
   * @private
   * @param {string} key - Policy key
   * @param {*} oldValue - Previous value
   * @param {*} newValue - New value
   * @param {Object} context - Change context
   */
  _updatePolicyHistory(key, oldValue, newValue, context) {
    if (!this.policyHistory.has(key)) {
      this.policyHistory.set(key, []);
    }

    const history = this.policyHistory.get(key);
    history.push({
      timestamp: Date.now(),
      oldValue: oldValue?.value,
      newValue: newValue,
      user: this.currentUser,
      context: { ...context }
    });

    // Keep only last 100 entries
    if (history.length > 100) {
      history.splice(0, history.length - 100);
    }
  }

  /**
   * Audit policy change
   *
   * @private
   * @param {string} action - Action performed
   * @param {string} key - Policy key
   * @param {*} oldValue - Previous value
   * @param {*} newValue - New value
   * @param {Object} context - Change context
   */
  _auditPolicyChange(action, key, oldValue, newValue, context) {
    if (!this.options.enableAuditLogging) return;

    this.auditLog.push({
      timestamp: Date.now(),
      action: action,
      key: key,
      oldValue: oldValue,
      newValue: newValue,
      user: this.currentUser,
      context: { ...context }
    });

    // Keep only last 1000 entries
    if (this.auditLog.length > 1000) {
      this.auditLog.splice(0, this.auditLog.length - 1000);
    }
  }

  /**
   * Notify policy change listeners
   *
   * @private
   * @param {string} key - Policy key
   * @param {*} newValue - New value
   * @param {*} oldValue - Previous value
   * @param {Object} context - Change context
   */
  _notifyPolicyChange(key, newValue, oldValue, context) {
    const changeEvent = {
      key: key,
      newValue: newValue,
      oldValue: oldValue,
      timestamp: Date.now(),
      context: { ...context }
    };

    for (const listener of this.changeListeners) {
      try {
        listener(changeEvent);
      } catch (error) {
        console.error('Policy change listener error:', error);
      }
    }
  }

  /**
   * Check if context matches pattern
   *
   * @private
   * @param {Object} context - Context to check
   * @param {string} pattern - Context pattern
   * @returns {boolean} True if context matches
   */
  _matchesContext(context, pattern) {
    // Simple pattern matching - could be enhanced
    if (pattern === '*') return true;

    const parts = pattern.split('.');
    let current = context;

    for (const part of parts) {
      if (current[part] === undefined) return false;
      current = current[part];
    }

    return true;
  }

  /**
   * Assess security impact of policy change
   *
   * @private
   * @param {string} key - Policy key
   * @param {*} oldValue - Current value
   * @param {*} newValue - New value
   * @returns {Object} Security impact assessment
   */
  _assessSecurityImpact(key, oldValue, newValue) {
    const impact = {
      level: 'none',
      warnings: []
    };

    // Security-related policy changes
    if (key.startsWith('security.')) {
      impact.level = 'medium';

      if (key === 'security.requireEncryption' && newValue === false) {
        impact.level = 'high';
        impact.warnings.push('Disabling encryption requirement reduces security');
      }

      if (key === 'security.enableSecurityValidation' && newValue === false) {
        impact.level = 'high';
        impact.warnings.push('Disabling security validation increases risk');
      }
    }

    return impact;
  }

  /**
   * Perform enterprise-level validation
   *
   * @private
   * @param {string} key - Policy key
   * @param {*} oldValue - Current value
   * @param {*} newValue - New value
   * @returns {Object} Enterprise validation result
   */
  _performEnterpriseValidation(key, oldValue, newValue) {
    const validation = {
      isValid: true,
      errors: []
    };

    // Enterprise-specific validation rules
    if (key.startsWith('security.') && !this.userRoles.has('admin')) {
      validation.isValid = false;
      validation.errors.push('Security policy changes require admin privileges');
    }

    return validation;
  }

  /**
   * Queue policy update for batch processing
   *
   * @private
   * @param {string} key - Policy key
   * @param {*} value - Policy value
   */
  _queuePolicyUpdate(key, value) {
    // Simple implementation - could be enhanced with proper queue
    setTimeout(() => {
      this.setPolicy(key, value, { queued: true });
    }, 0);
  }

  /**
   * Perform periodic cleanup
   *
   * @private
   */
  _performCleanup() {
    // Clear validation cache
    if (this.validationCache.size > 1000) {
      this.validationCache.clear();
    }

    // Trim audit log
    if (this.auditLog.length > 1000) {
      this.auditLog.splice(0, this.auditLog.length - 1000);
    }
  }

  /**
   * Handle errors with recovery
   *
   * @private
   * @param {Error} error - Error to handle
   * @param {string} operation - Operation that failed
   * @param {Object} context - Error context
   */
  _handleError(error, operation, context) {
    if (this.options.enableAuditLogging) {
      this.auditLog.push({
        timestamp: Date.now(),
        action: 'ERROR',
        operation: operation,
        error: error.message,
        context: context,
        user: this.currentUser
      });
    }

    // Use global error recovery if available
    if (typeof globalErrorRecovery !== 'undefined') {
      globalErrorRecovery.handleAKEError(error, {
        id: 'policy-manager',
        operation: operation,
        context: context
      });
    }
  }
}
