/**
 * Enhanced Socialist Millionaire Protocol (SMP) Module
 * 
 * Extends the base SMP implementation with advanced features:
 * - Comprehensive state persistence and recovery
 * - Advanced abort handling with reason codes
 * - SMP session resumption capabilities
 * - Enhanced debugging and monitoring
 * - Robust error recovery and validation
 * 
 * Based on libOTR patterns and OTR specification.
 */

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, SMP_MESSAGE_TYPE, SMP_RESULT, SMPState } from './smp.js';
import { CryptoValidation, SecurityValidationError } from '../security/validation.js';
import { SecureMemory } from '../security/secure-memory.js';
import { ConstantTimeOps } from '../security/constant-time.js';
import { globalErrorRecovery, ERROR_TYPES } from '../security/error-recovery.js';

/**
 * Enhanced SMP abort reason codes
 */
export const SMP_ABORT_REASON = {
  USER_ABORT: 'user_abort',           // User manually aborted
  PROTOCOL_ERROR: 'protocol_error',   // Protocol violation detected
  TIMEOUT: 'timeout',                 // Session timed out
  INVALID_MESSAGE: 'invalid_message', // Invalid message received
  STATE_ERROR: 'state_error',         // Invalid state transition
  SECURITY_ERROR: 'security_error',   // Security validation failed
  NETWORK_ERROR: 'network_error',     // Network communication failed
  RESOURCE_ERROR: 'resource_error'    // Resource exhaustion
};

/**
 * Enhanced SMP session states
 */
export const ENHANCED_SMP_STATE = {
  IDLE: 'idle',                       // No SMP session active
  INITIATING: 'initiating',           // Initiating SMP
  RESPONDING: 'responding',           // Responding to SMP
  IN_PROGRESS: 'in_progress',         // SMP exchange in progress
  PAUSED: 'paused',                   // Session temporarily paused
  COMPLETING: 'completing',           // Final verification
  COMPLETED: 'completed',             // SMP completed successfully
  FAILED: 'failed',                   // SMP failed
  ABORTED: 'aborted',                 // SMP aborted
  ERROR: 'error'                      // Error state
};

/**
 * Enhanced SMP configuration options
 */
export const ENHANCED_SMP_CONFIG = {
  sessionTimeoutMs: 300000,           // 5 minutes default timeout
  maxRetryAttempts: 3,                // Maximum retry attempts
  retryDelayMs: 5000,                 // Delay between retries
  enableStatePersistence: true,       // Enable state persistence
  enableDetailedLogging: false,       // Enable detailed logging
  enableSecurityValidation: true,     // Enable security validation
  maxConcurrentSessions: 1,           // Maximum concurrent sessions
  cleanupIntervalMs: 60000           // Cleanup interval
};

/**
 * Enhanced Socialist Millionaire Protocol Handler
 */
export class EnhancedSMP extends SMPHandler {
  /**
   * Create enhanced SMP handler
   * 
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super(options);
    
    this.config = { ...ENHANCED_SMP_CONFIG, ...options };
    this.enhancedState = ENHANCED_SMP_STATE.IDLE;
    this.sessionId = null;
    this.startTime = null;
    this.lastActivity = null;
    this.retryCount = 0;
    this.abortReason = null;
    this.persistedState = null;
    this.secureStorage = new Map(); // For sensitive data
    
    // Enhanced statistics
    this.enhancedStats = {
      sessionsStarted: 0,
      sessionsCompleted: 0,
      sessionsFailed: 0,
      sessionsAborted: 0,
      retryAttempts: 0,
      averageSessionTime: 0,
      securityViolations: 0
    };
    
    // Session management
    this.sessionTimer = null;
    this.cleanupTimer = null;
    
    this._startCleanupTimer();
  }

  /**
   * Initiate enhanced SMP with comprehensive options
   * 
   * @param {string} secret - Shared secret
   * @param {Object} options - Enhanced options
   * @returns {Promise<Object>} SMP1 message with session info
   */
  async initiateEnhancedSMP(secret, options = {}) {
    try {
      // Validate inputs
      if (!secret || typeof secret !== 'string') {
        throw new SecurityValidationError('Invalid secret provided', 'INVALID_SECRET');
      }

      // Check if we can start a new session
      if (!this._canStartNewSession()) {
        throw new SecurityValidationError('Cannot start new SMP session', 'SESSION_LIMIT_EXCEEDED');
      }

      // Reset and initialize session
      this._initializeSession(secret, options);
      
      // Store secret securely
      this._storeSecretSecurely(secret);
      
      // Call base implementation
      const smp1Message = await super.initiateSMP(secret, options.question);
      
      // Enhance message with session information
      const enhancedMessage = {
        ...smp1Message,
        sessionId: this.sessionId,
        timestamp: Date.now(),
        version: '1.0',
        capabilities: this._getCapabilities()
      };
      
      // Update enhanced state
      this.enhancedState = ENHANCED_SMP_STATE.INITIATING;
      this.enhancedStats.sessionsStarted++;
      
      // Set session timeout
      this._setSessionTimeout();
      
      // Persist state if enabled
      if (this.config.enableStatePersistence) {
        await this.persistState();
      }
      
      return enhancedMessage;
      
    } catch (error) {
      this._handleError(error, 'initiate');
      throw error;
    }
  }

  /**
   * Respond to enhanced SMP with validation
   * 
   * @param {string} secret - Shared secret
   * @param {Object} options - Response options
   * @returns {Promise<Object>} SMP2 message with validation
   */
  async respondToEnhancedSMP(secret, options = {}) {
    try {
      // Validate state
      if (this.enhancedState !== ENHANCED_SMP_STATE.IDLE) {
        throw new SecurityValidationError('Invalid state for SMP response', 'INVALID_STATE');
      }

      // Validate secret
      if (!secret || typeof secret !== 'string') {
        throw new SecurityValidationError('Invalid secret provided', 'INVALID_SECRET');
      }

      // Initialize response session
      this._initializeResponseSession(secret, options);
      
      // Store secret securely
      this._storeSecretSecurely(secret);
      
      // Call base implementation
      const smp2Message = await super.respondToSMP(secret);
      
      // Enhance message
      const enhancedMessage = {
        ...smp2Message,
        sessionId: this.sessionId,
        timestamp: Date.now(),
        responseTime: Date.now() - this.startTime
      };
      
      // Update enhanced state
      this.enhancedState = ENHANCED_SMP_STATE.RESPONDING;
      
      // Set session timeout
      this._setSessionTimeout();
      
      // Persist state if enabled
      if (this.config.enableStatePersistence) {
        await this.persistState();
      }
      
      return enhancedMessage;
      
    } catch (error) {
      this._handleError(error, 'respond');
      throw error;
    }
  }

  /**
   * Handle enhanced abort with detailed reason
   * 
   * @param {string} reason - Abort reason code
   * @param {Object} context - Additional context
   * @returns {Object} Enhanced abort message
   */
  handleAbort(reason = SMP_ABORT_REASON.USER_ABORT, context = {}) {
    try {
      // Validate reason
      if (!Object.values(SMP_ABORT_REASON).includes(reason)) {
        reason = SMP_ABORT_REASON.USER_ABORT;
      }

      // Store abort information
      this.abortReason = reason;
      this.enhancedState = ENHANCED_SMP_STATE.ABORTED;
      this.enhancedStats.sessionsAborted++;
      
      // Clear session timeout
      this._clearSessionTimeout();
      
      // Secure cleanup
      this._secureCleanup();
      
      // Create enhanced abort message
      const abortMessage = {
        type: SMP_MESSAGE_TYPE.SMP_ABORT,
        sessionId: this.sessionId,
        reason: reason,
        timestamp: Date.now(),
        context: this._sanitizeContext(context)
      };
      
      // Call base abort
      super.abortSMP();
      
      // Log abort for debugging
      if (this.config.enableDetailedLogging) {
        this._logEvent('abort', { reason, context });
      }
      
      return abortMessage;
      
    } catch (error) {
      this._handleError(error, 'abort');
      return super.createAbortMessage();
    }
  }

  /**
   * Persist current SMP state securely
   * 
   * @param {Object} storage - Optional storage interface
   * @returns {Promise<Object>} Serialized state
   */
  async persistState(storage = null) {
    try {
      if (!this.config.enableStatePersistence) {
        return null;
      }

      // Create state snapshot
      const stateSnapshot = {
        sessionId: this.sessionId,
        enhancedState: this.enhancedState,
        stage: this.state.stage,
        initiator: this.state.initiator,
        startTime: this.startTime,
        lastActivity: this.lastActivity,
        retryCount: this.retryCount,
        question: this.state.question,
        receivedQuestion: this.state.receivedQuestion,
        timestamp: Date.now(),
        version: '1.0'
      };
      
      // Encrypt sensitive state data
      const encryptedState = await this._encryptStateData(stateSnapshot);
      
      // Store in provided storage or internal storage
      if (storage && typeof storage.store === 'function') {
        await storage.store(this.sessionId, encryptedState);
      } else {
        this.persistedState = encryptedState;
      }
      
      return encryptedState;
      
    } catch (error) {
      this._handleError(error, 'persist');
      return null;
    }
  }

  /**
   * Resume SMP session from persisted state
   * 
   * @param {Object} state - Persisted state data
   * @param {Object} validation - Validation parameters
   * @returns {Promise<boolean>} True if resumed successfully
   */
  async resumeFromState(state, validation = {}) {
    try {
      // Validate state data
      if (!state || typeof state !== 'object') {
        throw new SecurityValidationError('Invalid state data', 'INVALID_STATE_DATA');
      }

      // Decrypt state data
      const decryptedState = await this._decryptStateData(state);
      
      // Validate state integrity
      if (!this._validateStateIntegrity(decryptedState, validation)) {
        throw new SecurityValidationError('State integrity validation failed', 'STATE_INTEGRITY_FAILED');
      }
      
      // Check if state is not too old
      const stateAge = Date.now() - decryptedState.timestamp;
      if (stateAge > this.config.sessionTimeoutMs) {
        throw new SecurityValidationError('State too old to resume', 'STATE_EXPIRED');
      }
      
      // Restore state
      this.sessionId = decryptedState.sessionId;
      this.enhancedState = decryptedState.enhancedState;
      this.state.stage = decryptedState.stage;
      this.state.initiator = decryptedState.initiator;
      this.startTime = decryptedState.startTime;
      this.lastActivity = decryptedState.lastActivity;
      this.retryCount = decryptedState.retryCount;
      this.state.question = decryptedState.question;
      this.state.receivedQuestion = decryptedState.receivedQuestion;
      
      // Set session timeout
      this._setSessionTimeout();
      
      // Log resumption
      if (this.config.enableDetailedLogging) {
        this._logEvent('resume', { sessionId: this.sessionId, stateAge });
      }
      
      return true;
      
    } catch (error) {
      this._handleError(error, 'resume');
      return false;
    }
  }

  /**
   * Pause SMP session temporarily
   * 
   * @param {string} reason - Pause reason
   * @returns {Object} Pause confirmation
   */
  pauseSession(reason = 'user_request') {
    try {
      if (this.enhancedState === ENHANCED_SMP_STATE.IN_PROGRESS) {
        this.enhancedState = ENHANCED_SMP_STATE.PAUSED;
        this.lastActivity = Date.now();
        
        // Clear session timeout while paused
        this._clearSessionTimeout();
        
        if (this.config.enableDetailedLogging) {
          this._logEvent('pause', { reason });
        }
        
        return {
          success: true,
          sessionId: this.sessionId,
          reason: reason,
          timestamp: Date.now()
        };
      }
      
      return {
        success: false,
        reason: 'Invalid state for pause'
      };
      
    } catch (error) {
      this._handleError(error, 'pause');
      return { success: false, error: error.message };
    }
  }

  /**
   * Resume paused SMP session
   * 
   * @param {Object} validation - Validation parameters
   * @returns {Object} Resume confirmation
   */
  resumeSession(validation = {}) {
    try {
      if (this.enhancedState === ENHANCED_SMP_STATE.PAUSED) {
        // Validate session can be resumed
        if (!this._validateSessionResumption(validation)) {
          throw new SecurityValidationError('Session resumption validation failed', 'RESUMPTION_FAILED');
        }
        
        this.enhancedState = ENHANCED_SMP_STATE.IN_PROGRESS;
        this.lastActivity = Date.now();
        
        // Restore session timeout
        this._setSessionTimeout();
        
        if (this.config.enableDetailedLogging) {
          this._logEvent('resume_session', { sessionId: this.sessionId });
        }
        
        return {
          success: true,
          sessionId: this.sessionId,
          timestamp: Date.now()
        };
      }
      
      return {
        success: false,
        reason: 'Invalid state for resume'
      };
      
    } catch (error) {
      this._handleError(error, 'resume_session');
      return { success: false, error: error.message };
    }
  }

  /**
   * Validate current SMP state against expected state
   * 
   * @param {string} expectedState - Expected SMP state
   * @param {string} currentState - Current SMP state (optional)
   * @returns {boolean} True if state is valid
   */
  validateSMPState(expectedState, currentState = null) {
    try {
      const current = currentState || this.enhancedState;
      
      // Direct state comparison
      if (current === expectedState) {
        return true;
      }
      
      // Check valid state transitions
      const validTransitions = this._getValidStateTransitions(current);
      return validTransitions.includes(expectedState);
      
    } catch (error) {
      this._handleError(error, 'validate_state');
      return false;
    }
  }

  /**
   * Get detailed SMP state information
   * 
   * @returns {Object} Detailed state information
   */
  getDetailedState() {
    return {
      // Base state information
      baseState: super.getState(),
      enhancedState: this.enhancedState,
      
      // Session information
      sessionId: this.sessionId,
      startTime: this.startTime,
      lastActivity: this.lastActivity,
      sessionDuration: this.startTime ? Date.now() - this.startTime : 0,
      
      // Configuration
      config: { ...this.config },
      
      // Statistics
      stats: { ...this.enhancedStats },
      
      // State validation
      isValid: this._validateCurrentState(),
      canAbort: this._canAbort(),
      canPause: this._canPause(),
      canResume: this._canResume(),
      
      // Security information
      retryCount: this.retryCount,
      abortReason: this.abortReason,
      hasPersistedState: this.persistedState !== null
    };
  }

  /**
   * Generate comprehensive diagnostics
   * 
   * @returns {Object} Diagnostic information
   */
  generateDiagnostics() {
    const state = this.getDetailedState();
    
    return {
      ...state,
      
      // Performance metrics
      performance: {
        averageSessionTime: this.enhancedStats.averageSessionTime,
        successRate: this._calculateSuccessRate(),
        retryRate: this._calculateRetryRate()
      },
      
      // Security metrics
      security: {
        securityViolations: this.enhancedStats.securityViolations,
        validationEnabled: this.config.enableSecurityValidation,
        persistenceEnabled: this.config.enableStatePersistence
      },
      
      // Resource usage
      resources: {
        secureStorageSize: this.secureStorage.size,
        memoryUsage: this._estimateMemoryUsage()
      },
      
      // Recommendations
      recommendations: this._generateRecommendations()
    };
  }

  /**
   * Enhanced cleanup with secure memory wiping
   */
  destroy() {
    try {
      // Clear timeouts
      this._clearSessionTimeout();
      this._clearCleanupTimer();
      
      // Secure cleanup
      this._secureCleanup();
      
      // Call base destroy
      super.destroy();
      
    } catch (error) {
      console.error('Error during enhanced SMP cleanup:', error);
    }
  }

  /**
   * Initialize new SMP session
   * 
   * @private
   * @param {string} secret - Shared secret
   * @param {Object} options - Session options
   */
  _initializeSession(secret, options) {
    this.sessionId = this._generateSessionId();
    this.startTime = Date.now();
    this.lastActivity = Date.now();
    this.retryCount = 0;
    this.abortReason = null;
    this.enhancedState = ENHANCED_SMP_STATE.IDLE;
    
    // Store session options
    if (options.timeout) {
      this.config.sessionTimeoutMs = options.timeout;
    }
  }

  /**
   * Initialize response session
   * 
   * @private
   * @param {string} secret - Shared secret
   * @param {Object} options - Response options
   */
  _initializeResponseSession(secret, options) {
    this._initializeSession(secret, options);
    this.enhancedState = ENHANCED_SMP_STATE.IDLE;
  }

  /**
   * Store secret securely in memory
   * 
   * @private
   * @param {string} secret - Secret to store
   */
  _storeSecretSecurely(secret) {
    if (this.sessionId) {
      const secureMemory = new SecureMemory(secret.length * 2);
      secureMemory.write(new TextEncoder().encode(secret));
      this.secureStorage.set(this.sessionId, secureMemory);
    }
  }

  /**
   * Generate unique session ID
   * 
   * @private
   * @returns {string} Session ID
   */
  _generateSessionId() {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2);
    return `smp_${timestamp}_${random}`;
  }

  /**
   * Get SMP capabilities
   * 
   * @private
   * @returns {Object} Capabilities object
   */
  _getCapabilities() {
    return {
      statePersistence: this.config.enableStatePersistence,
      sessionResumption: true,
      enhancedAbort: true,
      detailedLogging: this.config.enableDetailedLogging,
      securityValidation: this.config.enableSecurityValidation
    };
  }

  /**
   * Check if new session can be started
   * 
   * @private
   * @returns {boolean} True if new session can be started
   */
  _canStartNewSession() {
    return this.enhancedState === ENHANCED_SMP_STATE.IDLE ||
           this.enhancedState === ENHANCED_SMP_STATE.COMPLETED ||
           this.enhancedState === ENHANCED_SMP_STATE.FAILED ||
           this.enhancedState === ENHANCED_SMP_STATE.ABORTED;
  }

  /**
   * Set session timeout
   * 
   * @private
   */
  _setSessionTimeout() {
    this._clearSessionTimeout();
    this.sessionTimer = setTimeout(() => {
      this.handleAbort(SMP_ABORT_REASON.TIMEOUT, {
        sessionDuration: Date.now() - this.startTime
      });
    }, this.config.sessionTimeoutMs);
  }

  /**
   * Clear session timeout
   * 
   * @private
   */
  _clearSessionTimeout() {
    if (this.sessionTimer) {
      clearTimeout(this.sessionTimer);
      this.sessionTimer = null;
    }
  }

  /**
   * Start cleanup timer
   * 
   * @private
   */
  _startCleanupTimer() {
    this.cleanupTimer = setInterval(() => {
      this._performCleanup();
    }, this.config.cleanupIntervalMs);
  }

  /**
   * Clear cleanup timer
   * 
   * @private
   */
  _clearCleanupTimer() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
  }

  /**
   * Perform periodic cleanup
   * 
   * @private
   */
  _performCleanup() {
    // Clean up expired secure storage
    for (const [sessionId, secureMemory] of this.secureStorage) {
      if (sessionId !== this.sessionId) {
        secureMemory.destroy();
        this.secureStorage.delete(sessionId);
      }
    }
  }

  /**
   * Secure cleanup of sensitive data
   * 
   * @private
   */
  _secureCleanup() {
    // Clear secure storage
    for (const [sessionId, secureMemory] of this.secureStorage) {
      secureMemory.destroy();
    }
    this.secureStorage.clear();
    
    // Clear persisted state
    this.persistedState = null;
  }

  /**
   * Handle errors with recovery
   * 
   * @private
   * @param {Error} error - Error to handle
   * @param {string} operation - Operation that failed
   */
  _handleError(error, operation) {
    this.enhancedStats.securityViolations++;
    
    if (this.config.enableDetailedLogging) {
      this._logEvent('error', { operation, error: error.message });
    }
    
    // Use global error recovery
    const recovery = globalErrorRecovery.handleAKEError(error, {
      id: 'enhanced-smp',
      operation: operation,
      sessionId: this.sessionId
    });
    
    // Apply recovery strategy if available
    if (recovery && recovery.strategy === 'RESET_STATE') {
      this.reset();
    }
  }

  /**
   * Log events for debugging
   * 
   * @private
   * @param {string} event - Event name
   * @param {Object} data - Event data
   */
  _logEvent(event, data) {
    if (this.config.enableDetailedLogging) {
      console.log(`[EnhancedSMP] ${event}:`, {
        sessionId: this.sessionId,
        timestamp: Date.now(),
        ...data
      });
    }
  }

  /**
   * Encrypt state data for persistence
   *
   * @private
   * @param {Object} stateData - State data to encrypt
   * @returns {Promise<Object>} Encrypted state data
   */
  async _encryptStateData(stateData) {
    // Simple encryption for demo - in production would use proper encryption
    const serialized = JSON.stringify(stateData);
    const encoded = btoa(serialized);
    return { encrypted: encoded, timestamp: Date.now() };
  }

  /**
   * Decrypt state data from persistence
   *
   * @private
   * @param {Object} encryptedData - Encrypted state data
   * @returns {Promise<Object>} Decrypted state data
   */
  async _decryptStateData(encryptedData) {
    try {
      const decoded = atob(encryptedData.encrypted);
      return JSON.parse(decoded);
    } catch (error) {
      throw new SecurityValidationError('Failed to decrypt state data', 'DECRYPTION_FAILED');
    }
  }

  /**
   * Validate state integrity
   *
   * @private
   * @param {Object} state - State to validate
   * @param {Object} validation - Validation parameters
   * @returns {boolean} True if state is valid
   */
  _validateStateIntegrity(state, validation) {
    if (!state || typeof state !== 'object') return false;
    if (!state.sessionId || !state.enhancedState) return false;
    if (!state.timestamp || typeof state.timestamp !== 'number') return false;
    return true;
  }

  /**
   * Validate session resumption
   *
   * @private
   * @param {Object} validation - Validation parameters
   * @returns {boolean} True if session can be resumed
   */
  _validateSessionResumption(validation) {
    const timeSincePause = Date.now() - this.lastActivity;
    return timeSincePause < this.config.sessionTimeoutMs;
  }

  /**
   * Get valid state transitions
   *
   * @private
   * @param {string} currentState - Current state
   * @returns {Array<string>} Valid next states
   */
  _getValidStateTransitions(currentState) {
    const transitions = {
      [ENHANCED_SMP_STATE.IDLE]: [ENHANCED_SMP_STATE.INITIATING, ENHANCED_SMP_STATE.RESPONDING],
      [ENHANCED_SMP_STATE.INITIATING]: [ENHANCED_SMP_STATE.IN_PROGRESS, ENHANCED_SMP_STATE.ABORTED],
      [ENHANCED_SMP_STATE.RESPONDING]: [ENHANCED_SMP_STATE.IN_PROGRESS, ENHANCED_SMP_STATE.ABORTED],
      [ENHANCED_SMP_STATE.IN_PROGRESS]: [ENHANCED_SMP_STATE.PAUSED, ENHANCED_SMP_STATE.COMPLETING, ENHANCED_SMP_STATE.ABORTED],
      [ENHANCED_SMP_STATE.PAUSED]: [ENHANCED_SMP_STATE.IN_PROGRESS, ENHANCED_SMP_STATE.ABORTED],
      [ENHANCED_SMP_STATE.COMPLETING]: [ENHANCED_SMP_STATE.COMPLETED, ENHANCED_SMP_STATE.FAILED],
      [ENHANCED_SMP_STATE.COMPLETED]: [ENHANCED_SMP_STATE.IDLE],
      [ENHANCED_SMP_STATE.FAILED]: [ENHANCED_SMP_STATE.IDLE],
      [ENHANCED_SMP_STATE.ABORTED]: [ENHANCED_SMP_STATE.IDLE]
    };
    return transitions[currentState] || [];
  }

  /**
   * Validate current state
   *
   * @private
   * @returns {boolean} True if current state is valid
   */
  _validateCurrentState() {
    return Object.values(ENHANCED_SMP_STATE).includes(this.enhancedState);
  }

  /**
   * Check if SMP can be aborted
   *
   * @private
   * @returns {boolean} True if can abort
   */
  _canAbort() {
    return ![ENHANCED_SMP_STATE.IDLE, ENHANCED_SMP_STATE.COMPLETED, ENHANCED_SMP_STATE.ABORTED].includes(this.enhancedState);
  }

  /**
   * Check if SMP can be paused
   *
   * @private
   * @returns {boolean} True if can pause
   */
  _canPause() {
    return this.enhancedState === ENHANCED_SMP_STATE.IN_PROGRESS;
  }

  /**
   * Check if SMP can be resumed
   *
   * @private
   * @returns {boolean} True if can resume
   */
  _canResume() {
    return this.enhancedState === ENHANCED_SMP_STATE.PAUSED;
  }

  /**
   * Calculate success rate
   *
   * @private
   * @returns {number} Success rate percentage
   */
  _calculateSuccessRate() {
    const total = this.enhancedStats.sessionsCompleted + this.enhancedStats.sessionsFailed;
    return total > 0 ? (this.enhancedStats.sessionsCompleted / total) * 100 : 0;
  }

  /**
   * Calculate retry rate
   *
   * @private
   * @returns {number} Retry rate percentage
   */
  _calculateRetryRate() {
    const total = this.enhancedStats.sessionsStarted;
    return total > 0 ? (this.enhancedStats.retryAttempts / total) * 100 : 0;
  }

  /**
   * Estimate memory usage
   *
   * @private
   * @returns {number} Estimated memory usage in bytes
   */
  _estimateMemoryUsage() {
    let usage = 0;
    for (const [sessionId, secureMemory] of this.secureStorage) {
      usage += secureMemory.size || 0;
    }
    return usage;
  }

  /**
   * Generate recommendations
   *
   * @private
   * @returns {Array<string>} Recommendations
   */
  _generateRecommendations() {
    const recommendations = [];

    if (this._calculateSuccessRate() < 80) {
      recommendations.push('Consider investigating frequent SMP failures');
    }

    if (this._calculateRetryRate() > 20) {
      recommendations.push('High retry rate detected - check network stability');
    }

    if (this.enhancedStats.securityViolations > 0) {
      recommendations.push('Security violations detected - review security configuration');
    }

    return recommendations;
  }

  /**
   * Sanitize context for logging
   *
   * @private
   * @param {Object} context - Context to sanitize
   * @returns {Object} Sanitized context
   */
  _sanitizeContext(context) {
    const sanitized = { ...context };

    // Remove sensitive fields
    delete sanitized.secret;
    delete sanitized.privateKey;
    delete sanitized.sessionKey;

    return sanitized;
  }
}
