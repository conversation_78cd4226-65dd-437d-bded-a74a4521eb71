/**
 * Enhanced Message Ordering Module
 * 
 * Implements comprehensive message ordering, replay protection, and out-of-order
 * message handling with sequence validation based on libOTR patterns.
 * 
 * Features:
 * - Sequence number validation and tracking
 * - Out-of-order message buffering and reordering
 * - Replay attack detection and prevention
 * - Message gap detection and recovery
 * - Sliding window protocol for efficiency
 */

import { CryptoValidation, SecurityValidationError } from '../security/validation.js';
import { ConstantTimeOps } from '../security/constant-time.js';
import { globalErrorRecovery, ERROR_TYPES } from '../security/error-recovery.js';

/**
 * Message ordering and replay protection manager
 */
export class MessageOrdering {
  /**
   * Default configuration values
   */
  static DEFAULT_CONFIG = {
    maxBufferSize: 100,        // Maximum out-of-order messages to buffer
    maxGapSize: 10,           // Maximum gap in sequence numbers to tolerate
    replayWindowSize: 64,     // Size of replay protection window
    maxSequenceNumber: 0xFFFFFFFF, // Maximum sequence number (32-bit)
    gapTimeoutMs: 30000,      // Timeout for waiting for missing messages
    cleanupIntervalMs: 60000  // Interval for cleanup operations
  };

  /**
   * Message states for tracking
   */
  static MESSAGE_STATE = {
    PENDING: 'pending',       // Waiting for earlier messages
    PROCESSED: 'processed',   // Successfully processed
    EXPIRED: 'expired',       // Timed out waiting for dependencies
    DUPLICATE: 'duplicate'    // Detected as replay/duplicate
  };

  /**
   * Create a new message ordering manager
   * 
   * @param {Object} config - Configuration options
   */
  constructor(config = {}) {
    this.config = { ...MessageOrdering.DEFAULT_CONFIG, ...config };
    
    // Sequence tracking
    this.nextExpectedSequence = 0;
    this.lastProcessedSequence = -1;
    this.nextSendSequence = 0;
    
    // Out-of-order message buffer
    this.pendingMessages = new Map(); // sequence -> { message, timestamp, attempts }
    
    // Replay protection window (sliding window of recent sequence numbers)
    this.replayWindow = new Set();
    this.replayWindowBase = 0;
    
    // Gap tracking
    this.detectedGaps = new Map(); // sequence -> { timestamp, notified }
    
    // Statistics
    this.stats = {
      messagesProcessed: 0,
      messagesReordered: 0,
      duplicatesDetected: 0,
      gapsDetected: 0,
      timeouts: 0
    };
    
    // Cleanup timer
    this.cleanupTimer = null;
    this._startCleanupTimer();
  }

  /**
   * Validate incoming message sequence and handle ordering
   * 
   * @param {Object} message - Incoming message with sequence number
   * @param {number} expectedSequence - Expected sequence number (optional)
   * @returns {Object} Validation result with processing instructions
   */
  validateIncomingMessage(message, expectedSequence = null) {
    try {
      // Validate message structure
      if (!message || typeof message.sequence !== 'number') {
        throw new SecurityValidationError('Invalid message structure', 'INVALID_MESSAGE_STRUCTURE');
      }

      const sequence = message.sequence;
      
      // Validate sequence number range
      if (sequence < 0 || sequence > this.config.maxSequenceNumber) {
        throw new SecurityValidationError(
          `Sequence number out of range: ${sequence}`,
          'SEQUENCE_OUT_OF_RANGE'
        );
      }

      // Use provided expected sequence or our tracked value
      const expected = expectedSequence !== null ? expectedSequence : this.nextExpectedSequence;
      
      // Check for replay attacks
      const replayCheck = this._checkReplayProtection(sequence);
      if (!replayCheck.isValid) {
        this.stats.duplicatesDetected++;
        return {
          action: 'REJECT',
          reason: 'REPLAY_DETECTED',
          state: MessageOrdering.MESSAGE_STATE.DUPLICATE,
          sequence: sequence,
          expected: expected,
          replayInfo: replayCheck
        };
      }

      // Check message ordering
      if (sequence === expected) {
        // Perfect order - process immediately
        return this._handleInOrderMessage(message, sequence);
      } else if (sequence > expected) {
        // Future message - buffer for later processing
        return this._handleOutOfOrderMessage(message, sequence, expected);
      } else {
        // Past message - likely duplicate or very delayed
        return this._handlePastMessage(message, sequence, expected);
      }
      
    } catch (error) {
      // Handle validation errors with recovery system
      const recovery = globalErrorRecovery.handleAKEError(error, {
        id: 'message-ordering',
        message: { sequence: message?.sequence },
        expected: expectedSequence
      });
      
      return {
        action: 'ERROR',
        reason: error.message,
        state: 'error',
        recovery: recovery
      };
    }
  }

  /**
   * Handle out-of-order messages with buffering and reordering
   * 
   * @param {Object} message - Out-of-order message
   * @param {number} currentSequence - Current sequence number
   * @returns {Object} Processing result with any ready messages
   */
  handleOutOfOrder(message, currentSequence) {
    try {
      const sequence = message.sequence;
      
      // Check if we already have this message
      if (this.pendingMessages.has(sequence)) {
        return {
          action: 'DUPLICATE',
          reason: 'Already buffered',
          readyMessages: []
        };
      }

      // Check buffer capacity
      if (this.pendingMessages.size >= this.config.maxBufferSize) {
        // Remove oldest pending message to make room
        const oldestSequence = Math.min(...this.pendingMessages.keys());
        this.pendingMessages.delete(oldestSequence);
        this.stats.timeouts++;
      }

      // Buffer the message
      this.pendingMessages.set(sequence, {
        message: message,
        timestamp: Date.now(),
        attempts: 0
      });

      // Check for any messages that are now ready to process
      const readyMessages = this._checkForReadyMessages();
      
      // Detect gaps in sequence
      this._detectMessageGaps(currentSequence);
      
      return {
        action: 'BUFFERED',
        reason: 'Out of order message buffered',
        readyMessages: readyMessages,
        pendingCount: this.pendingMessages.size
      };
      
    } catch (error) {
      throw new SecurityValidationError(
        `Out-of-order handling failed: ${error.message}`,
        'OUT_OF_ORDER_HANDLING_FAILED'
      );
    }
  }

  /**
   * Detect and handle message gaps in sequence
   * 
   * @param {number} receivedSequence - Received sequence number
   * @param {number} expectedSequence - Expected sequence number
   * @returns {Object} Gap detection result
   */
  detectMessageGaps(receivedSequence, expectedSequence) {
    try {
      const gap = receivedSequence - expectedSequence;
      
      if (gap <= 0) {
        return { hasGap: false, gapSize: 0 };
      }

      if (gap > this.config.maxGapSize) {
        throw new SecurityValidationError(
          `Message gap too large: ${gap} (max: ${this.config.maxGapSize})`,
          'MESSAGE_GAP_TOO_LARGE'
        );
      }

      // Record the gap
      for (let seq = expectedSequence; seq < receivedSequence; seq++) {
        if (!this.detectedGaps.has(seq)) {
          this.detectedGaps.set(seq, {
            timestamp: Date.now(),
            notified: false
          });
          this.stats.gapsDetected++;
        }
      }

      return {
        hasGap: true,
        gapSize: gap,
        missingSequences: Array.from(
          { length: gap },
          (_, i) => expectedSequence + i
        )
      };
      
    } catch (error) {
      throw new SecurityValidationError(
        `Gap detection failed: ${error.message}`,
        'GAP_DETECTION_FAILED'
      );
    }
  }

  /**
   * Generate next sequence number for outgoing messages
   * 
   * @returns {number} Next sequence number
   */
  getNextSendSequence() {
    const sequence = this.nextSendSequence;
    this.nextSendSequence = (this.nextSendSequence + 1) % (this.config.maxSequenceNumber + 1);
    return sequence;
  }

  /**
   * Validate replay protection using sliding window
   * 
   * @param {Object} message - Message to validate
   * @param {number} windowSize - Size of replay window (optional)
   * @returns {boolean} True if message is not a replay
   */
  validateReplayProtection(message, windowSize = null) {
    const window = windowSize || this.config.replayWindowSize;
    return this._checkReplayProtection(message.sequence, window).isValid;
  }

  /**
   * Update sequence tracking after successful message processing
   * 
   * @param {number} sequence - Processed sequence number
   */
  updateSequenceTracking(sequence) {
    // Update last processed sequence
    this.lastProcessedSequence = Math.max(this.lastProcessedSequence, sequence);
    
    // Update next expected sequence if this was the next in line
    if (sequence === this.nextExpectedSequence) {
      this.nextExpectedSequence = sequence + 1;
      
      // Check if we can advance further with buffered messages
      while (this.pendingMessages.has(this.nextExpectedSequence)) {
        this.nextExpectedSequence++;
      }
    }
    
    // Update replay protection window
    this._updateReplayWindow(sequence);
    
    // Remove from pending messages if it was buffered
    this.pendingMessages.delete(sequence);
    
    // Remove from detected gaps
    this.detectedGaps.delete(sequence);
    
    // Update statistics
    this.stats.messagesProcessed++;
  }

  /**
   * Get current ordering statistics
   * 
   * @returns {Object} Statistics object
   */
  getStats() {
    return {
      ...this.stats,
      nextExpectedSequence: this.nextExpectedSequence,
      lastProcessedSequence: this.lastProcessedSequence,
      nextSendSequence: this.nextSendSequence,
      pendingMessages: this.pendingMessages.size,
      detectedGaps: this.detectedGaps.size,
      replayWindowSize: this.replayWindow.size
    };
  }

  /**
   * Reset ordering state (for new sessions)
   */
  reset() {
    this.nextExpectedSequence = 0;
    this.lastProcessedSequence = -1;
    this.nextSendSequence = 0;
    this.pendingMessages.clear();
    this.replayWindow.clear();
    this.replayWindowBase = 0;
    this.detectedGaps.clear();
    
    // Reset statistics
    this.stats = {
      messagesProcessed: 0,
      messagesReordered: 0,
      duplicatesDetected: 0,
      gapsDetected: 0,
      timeouts: 0
    };
  }

  /**
   * Cleanup resources
   */
  destroy() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
    
    this.pendingMessages.clear();
    this.replayWindow.clear();
    this.detectedGaps.clear();
  }

  /**
   * Handle in-order message processing
   * 
   * @private
   * @param {Object} message - Message to process
   * @param {number} sequence - Sequence number
   * @returns {Object} Processing result
   */
  _handleInOrderMessage(message, sequence) {
    this.updateSequenceTracking(sequence);
    
    // Check for any buffered messages that are now ready
    const readyMessages = this._checkForReadyMessages();
    
    return {
      action: 'PROCESS',
      reason: 'In order message',
      state: MessageOrdering.MESSAGE_STATE.PROCESSED,
      sequence: sequence,
      readyMessages: readyMessages
    };
  }

  /**
   * Handle out-of-order message buffering
   * 
   * @private
   * @param {Object} message - Message to buffer
   * @param {number} sequence - Sequence number
   * @param {number} expected - Expected sequence number
   * @returns {Object} Processing result
   */
  _handleOutOfOrderMessage(message, sequence, expected) {
    const gap = sequence - expected;
    
    if (gap > this.config.maxGapSize) {
      throw new SecurityValidationError(
        `Message gap too large: ${gap}`,
        'MESSAGE_GAP_TOO_LARGE'
      );
    }
    
    // Buffer the message
    const bufferResult = this.handleOutOfOrder(message, sequence);
    
    return {
      action: 'BUFFER',
      reason: `Future message (gap: ${gap})`,
      state: MessageOrdering.MESSAGE_STATE.PENDING,
      sequence: sequence,
      expected: expected,
      gap: gap,
      ...bufferResult
    };
  }

  /**
   * Handle past/duplicate message
   * 
   * @private
   * @param {Object} message - Message to check
   * @param {number} sequence - Sequence number
   * @param {number} expected - Expected sequence number
   * @returns {Object} Processing result
   */
  _handlePastMessage(message, sequence, expected) {
    // Check if this is within our replay window
    if (sequence > this.lastProcessedSequence - this.config.replayWindowSize) {
      // Might be a legitimate retransmission
      return {
        action: 'IGNORE',
        reason: 'Possible retransmission',
        state: MessageOrdering.MESSAGE_STATE.DUPLICATE,
        sequence: sequence,
        expected: expected
      };
    } else {
      // Definitely too old
      this.stats.duplicatesDetected++;
      return {
        action: 'REJECT',
        reason: 'Message too old',
        state: MessageOrdering.MESSAGE_STATE.DUPLICATE,
        sequence: sequence,
        expected: expected
      };
    }
  }

  /**
   * Check for replay attacks using sliding window
   * 
   * @private
   * @param {number} sequence - Sequence number to check
   * @param {number} windowSize - Window size (optional)
   * @returns {Object} Replay check result
   */
  _checkReplayProtection(sequence, windowSize = null) {
    const window = windowSize || this.config.replayWindowSize;
    
    // Check if sequence is within current window
    const windowStart = Math.max(0, this.replayWindowBase);
    const windowEnd = windowStart + window;
    
    if (sequence < windowStart) {
      return {
        isValid: false,
        reason: 'Sequence too old',
        windowStart: windowStart,
        windowEnd: windowEnd
      };
    }
    
    if (this.replayWindow.has(sequence)) {
      return {
        isValid: false,
        reason: 'Duplicate sequence number',
        windowStart: windowStart,
        windowEnd: windowEnd
      };
    }
    
    return {
      isValid: true,
      reason: 'Valid sequence',
      windowStart: windowStart,
      windowEnd: windowEnd
    };
  }

  /**
   * Update replay protection window
   * 
   * @private
   * @param {number} sequence - Processed sequence number
   */
  _updateReplayWindow(sequence) {
    this.replayWindow.add(sequence);
    
    // Slide window if necessary
    if (sequence >= this.replayWindowBase + this.config.replayWindowSize) {
      const newBase = sequence - this.config.replayWindowSize + 1;
      
      // Remove old entries
      for (let seq = this.replayWindowBase; seq < newBase; seq++) {
        this.replayWindow.delete(seq);
      }
      
      this.replayWindowBase = newBase;
    }
  }

  /**
   * Check for buffered messages that are now ready to process
   * 
   * @private
   * @returns {Array} Array of ready messages in order
   */
  _checkForReadyMessages() {
    const readyMessages = [];
    
    // Check for consecutive messages starting from next expected
    let checkSequence = this.nextExpectedSequence;
    
    while (this.pendingMessages.has(checkSequence)) {
      const bufferedMessage = this.pendingMessages.get(checkSequence);
      readyMessages.push({
        sequence: checkSequence,
        message: bufferedMessage.message,
        bufferedTime: Date.now() - bufferedMessage.timestamp
      });
      
      this.pendingMessages.delete(checkSequence);
      this.nextExpectedSequence = checkSequence + 1;
      checkSequence++;
      this.stats.messagesReordered++;
    }
    
    return readyMessages;
  }

  /**
   * Detect gaps in message sequence
   * 
   * @private
   * @param {number} currentSequence - Current sequence number
   */
  _detectMessageGaps(currentSequence) {
    const expected = this.nextExpectedSequence;
    
    if (currentSequence > expected) {
      for (let seq = expected; seq < currentSequence; seq++) {
        if (!this.detectedGaps.has(seq) && !this.pendingMessages.has(seq)) {
          this.detectedGaps.set(seq, {
            timestamp: Date.now(),
            notified: false
          });
          this.stats.gapsDetected++;
        }
      }
    }
  }

  /**
   * Start cleanup timer for expired messages and gaps
   * 
   * @private
   */
  _startCleanupTimer() {
    this.cleanupTimer = setInterval(() => {
      this._cleanupExpiredMessages();
      this._cleanupExpiredGaps();
    }, this.config.cleanupIntervalMs);
  }

  /**
   * Clean up expired pending messages
   * 
   * @private
   */
  _cleanupExpiredMessages() {
    const now = Date.now();
    const expiredSequences = [];
    
    for (const [sequence, data] of this.pendingMessages) {
      if (now - data.timestamp > this.config.gapTimeoutMs) {
        expiredSequences.push(sequence);
      }
    }
    
    for (const sequence of expiredSequences) {
      this.pendingMessages.delete(sequence);
      this.stats.timeouts++;
    }
  }

  /**
   * Clean up expired gap tracking
   * 
   * @private
   */
  _cleanupExpiredGaps() {
    const now = Date.now();
    const expiredSequences = [];
    
    for (const [sequence, data] of this.detectedGaps) {
      if (now - data.timestamp > this.config.gapTimeoutMs) {
        expiredSequences.push(sequence);
      }
    }
    
    for (const sequence of expiredSequences) {
      this.detectedGaps.delete(sequence);
    }
  }
}
