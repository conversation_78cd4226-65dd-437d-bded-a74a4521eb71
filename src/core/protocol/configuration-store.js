/**
 * Configuration Store - Persistent Configuration Management
 * 
 * Provides secure, persistent storage for WebOTR configuration with:
 * - Encrypted configuration storage
 * - Multi-layer configuration hierarchy
 * - Automatic backup and recovery
 * - Configuration versioning and migration
 * - Cross-browser compatibility
 * 
 * Integrates with Policy Manager for comprehensive configuration management.
 */

import { CryptoValidation, SecurityValidationError } from '../security/validation.js';
import { SecureMemory } from '../security/secure-memory.js';
import { ConstantTimeOps } from '../security/constant-time.js';
import { globalErrorRecovery, ERROR_TYPES } from '../security/error-recovery.js';

/**
 * Storage backends supported by the configuration store
 */
export const STORAGE_BACKEND = {
  LOCALSTORAGE: 'localStorage',
  INDEXEDDB: 'indexedDB',
  MEMORY: 'memory',
  CUSTOM: 'custom'
};

/**
 * Configuration layers in order of precedence (highest to lowest)
 */
export const CONFIG_LAYER = {
  RUNTIME: 'runtime',        // Temporary runtime overrides
  USER: 'user',             // User-specific settings
  ORGANIZATION: 'organization', // Organization-wide policies
  SYSTEM: 'system',         // System defaults
  DEFAULT: 'default'        // Built-in defaults
};

/**
 * Configuration encryption modes
 */
export const ENCRYPTION_MODE = {
  NONE: 'none',             // No encryption (not recommended)
  BASIC: 'basic',           // Basic encryption with user key
  ADVANCED: 'advanced',     // Advanced encryption with key derivation
  ENTERPRISE: 'enterprise'  // Enterprise-grade encryption with HSM support
};

/**
 * Configuration Store Implementation
 */
export class ConfigurationStore {
  /**
   * Create configuration store
   * 
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      backend: STORAGE_BACKEND.INDEXEDDB,
      encryptionMode: ENCRYPTION_MODE.ADVANCED,
      enableVersioning: true,
      enableBackup: true,
      maxVersions: 10,
      backupInterval: 3600000, // 1 hour
      compressionEnabled: true,
      ...options
    };
    
    // Storage layers
    this.layers = new Map();
    this.layerOrder = [
      CONFIG_LAYER.RUNTIME,
      CONFIG_LAYER.USER,
      CONFIG_LAYER.ORGANIZATION,
      CONFIG_LAYER.SYSTEM,
      CONFIG_LAYER.DEFAULT
    ];
    
    // Storage backend
    this.backend = null;
    this.encryptionKey = null;
    
    // Configuration cache
    this.cache = new Map();
    this.cacheTimestamps = new Map();
    this.cacheTTL = 300000; // 5 minutes
    
    // Version management
    this.currentVersion = '1.0';
    this.versionHistory = [];
    
    // Statistics
    this.stats = {
      reads: 0,
      writes: 0,
      cacheHits: 0,
      cacheMisses: 0,
      encryptionOperations: 0,
      backupsCreated: 0,
      migrationsPerformed: 0
    };
    
    // Initialize storage backend
    this._initializeBackend();
    
    // Set up automatic backup
    if (this.options.enableBackup) {
      this.backupTimer = setInterval(() => this._performBackup(), this.options.backupInterval);
    }
  }

  /**
   * Initialize configuration layer with default values
   * 
   * @param {string} layer - Configuration layer
   * @param {Object} config - Default configuration
   */
  initializeLayer(layer, config = {}) {
    if (!this.layerOrder.includes(layer)) {
      throw new SecurityValidationError(`Invalid configuration layer: ${layer}`, 'INVALID_LAYER');
    }
    
    this.layers.set(layer, {
      config: { ...config },
      timestamp: Date.now(),
      version: this.currentVersion,
      checksum: this._calculateChecksum(config)
    });
    
    // Clear cache for affected keys
    this._invalidateCache();
  }

  /**
   * Set configuration value in specific layer
   * 
   * @param {string} layer - Configuration layer
   * @param {string} key - Configuration key
   * @param {*} value - Configuration value
   * @param {Object} metadata - Additional metadata
   * @returns {Promise<boolean>} Success indicator
   */
  async setConfig(layer, key, value, metadata = {}) {
    try {
      if (!this.layerOrder.includes(layer)) {
        throw new SecurityValidationError(`Invalid configuration layer: ${layer}`, 'INVALID_LAYER');
      }
      
      // Get or create layer
      let layerData = this.layers.get(layer) || {
        config: {},
        timestamp: Date.now(),
        version: this.currentVersion,
        checksum: null
      };
      
      // Set configuration value
      layerData.config[key] = value;
      layerData.timestamp = Date.now();
      layerData.metadata = { ...layerData.metadata, [key]: metadata };
      layerData.checksum = this._calculateChecksum(layerData.config);
      
      // Update layer
      this.layers.set(layer, layerData);
      
      // Persist to storage backend
      await this._persistLayer(layer, layerData);
      
      // Clear cache for this key
      this._invalidateCacheKey(key);
      
      // Update statistics
      this.stats.writes++;
      
      return true;
      
    } catch (error) {
      this._handleError(error, 'setConfig', { layer, key, value });
      return false;
    }
  }

  /**
   * Get configuration value with layer resolution
   * 
   * @param {string} key - Configuration key
   * @param {Object} options - Retrieval options
   * @returns {*} Configuration value
   */
  async getConfig(key, options = {}) {
    try {
      const {
        layer = null,           // Specific layer to check
        useCache = true,        // Use cached values
        includeMetadata = false // Include metadata in response
      } = options;
      
      // Check cache first
      if (useCache && this._isCacheValid(key)) {
        this.stats.cacheHits++;
        const cached = this.cache.get(key);
        return includeMetadata ? cached : cached.value;
      }
      
      this.stats.cacheMisses++;
      
      // Resolve value through layer hierarchy
      let resolvedValue = undefined;
      let resolvedLayer = null;
      let resolvedMetadata = {};
      
      const layersToCheck = layer ? [layer] : this.layerOrder;
      
      for (const currentLayer of layersToCheck) {
        const layerData = this.layers.get(currentLayer);
        if (layerData && layerData.config.hasOwnProperty(key)) {
          resolvedValue = layerData.config[key];
          resolvedLayer = currentLayer;
          resolvedMetadata = layerData.metadata?.[key] || {};
          break;
        }
      }
      
      // Cache the result
      if (useCache && resolvedValue !== undefined) {
        this.cache.set(key, {
          value: resolvedValue,
          layer: resolvedLayer,
          metadata: resolvedMetadata,
          timestamp: Date.now()
        });
        this.cacheTimestamps.set(key, Date.now());
      }
      
      // Update statistics
      this.stats.reads++;
      
      if (includeMetadata) {
        return {
          value: resolvedValue,
          layer: resolvedLayer,
          metadata: resolvedMetadata
        };
      }
      
      return resolvedValue;
      
    } catch (error) {
      this._handleError(error, 'getConfig', { key, options });
      return undefined;
    }
  }

  /**
   * Get complete configuration with layer resolution
   * 
   * @param {Object} options - Retrieval options
   * @returns {Object} Complete configuration
   */
  async getCompleteConfig(options = {}) {
    const {
      includeMetadata = false,
      layerInfo = false
    } = options;
    
    const completeConfig = {};
    const configMetadata = {};
    const layerInfo_data = {};
    
    // Collect all keys from all layers
    const allKeys = new Set();
    for (const layerData of this.layers.values()) {
      Object.keys(layerData.config).forEach(key => allKeys.add(key));
    }
    
    // Resolve each key through layer hierarchy
    for (const key of allKeys) {
      const result = await this.getConfig(key, { includeMetadata: true });
      if (result && result.value !== undefined) {
        completeConfig[key] = result.value;
        if (includeMetadata) {
          configMetadata[key] = result.metadata;
        }
        if (layerInfo) {
          layerInfo_data[key] = result.layer;
        }
      }
    }
    
    const response = { config: completeConfig };
    if (includeMetadata) response.metadata = configMetadata;
    if (layerInfo) response.layers = layerInfo_data;
    
    return response;
  }

  /**
   * Remove configuration value from layer
   * 
   * @param {string} layer - Configuration layer
   * @param {string} key - Configuration key
   * @returns {Promise<boolean>} Success indicator
   */
  async removeConfig(layer, key) {
    try {
      const layerData = this.layers.get(layer);
      if (!layerData || !layerData.config.hasOwnProperty(key)) {
        return false;
      }
      
      delete layerData.config[key];
      if (layerData.metadata) {
        delete layerData.metadata[key];
      }
      
      layerData.timestamp = Date.now();
      layerData.checksum = this._calculateChecksum(layerData.config);
      
      // Persist changes
      await this._persistLayer(layer, layerData);
      
      // Clear cache
      this._invalidateCacheKey(key);
      
      return true;
      
    } catch (error) {
      this._handleError(error, 'removeConfig', { layer, key });
      return false;
    }
  }

  /**
   * Clear entire configuration layer
   * 
   * @param {string} layer - Configuration layer
   * @returns {Promise<boolean>} Success indicator
   */
  async clearLayer(layer) {
    try {
      if (!this.layerOrder.includes(layer)) {
        throw new SecurityValidationError(`Invalid configuration layer: ${layer}`, 'INVALID_LAYER');
      }
      
      this.layers.delete(layer);
      await this._removePersistedLayer(layer);
      this._invalidateCache();
      
      return true;
      
    } catch (error) {
      this._handleError(error, 'clearLayer', { layer });
      return false;
    }
  }

  /**
   * Export configuration for backup or migration
   * 
   * @param {Object} options - Export options
   * @returns {Object} Exported configuration data
   */
  async exportConfig(options = {}) {
    const {
      includeLayers = this.layerOrder,
      includeMetadata = true,
      includeVersionHistory = false,
      encrypt = true
    } = options;
    
    const exportData = {
      version: this.currentVersion,
      timestamp: Date.now(),
      layers: {},
      metadata: {
        exportOptions: options,
        stats: { ...this.stats }
      }
    };
    
    // Export specified layers
    for (const layer of includeLayers) {
      const layerData = this.layers.get(layer);
      if (layerData) {
        exportData.layers[layer] = {
          config: { ...layerData.config },
          timestamp: layerData.timestamp,
          version: layerData.version,
          checksum: layerData.checksum
        };
        
        if (includeMetadata && layerData.metadata) {
          exportData.layers[layer].metadata = { ...layerData.metadata };
        }
      }
    }
    
    // Include version history if requested
    if (includeVersionHistory) {
      exportData.versionHistory = [...this.versionHistory];
    }
    
    // Encrypt if requested
    if (encrypt && this.encryptionKey) {
      exportData.encrypted = true;
      exportData.data = await this._encryptData(JSON.stringify(exportData));
      // Remove unencrypted data
      delete exportData.layers;
      delete exportData.versionHistory;
    }
    
    return exportData;
  }

  /**
   * Import configuration from backup or migration
   * 
   * @param {Object} importData - Configuration data to import
   * @param {Object} options - Import options
   * @returns {Promise<boolean>} Success indicator
   */
  async importConfig(importData, options = {}) {
    try {
      const {
        overwriteExisting = false,
        validateChecksums = true,
        performMigration = true
      } = options;
      
      let configData = importData;
      
      // Decrypt if necessary
      if (importData.encrypted && importData.data) {
        const decryptedData = await this._decryptData(importData.data);
        configData = JSON.parse(decryptedData);
      }
      
      // Validate import data
      if (!configData.version || !configData.layers) {
        throw new SecurityValidationError('Invalid import data format', 'INVALID_IMPORT_DATA');
      }
      
      // Perform migration if needed
      if (performMigration && configData.version !== this.currentVersion) {
        configData = await this._migrateConfig(configData);
      }
      
      // Import layers
      for (const [layer, layerData] of Object.entries(configData.layers)) {
        if (!this.layerOrder.includes(layer)) {
          console.warn(`Skipping unknown layer: ${layer}`);
          continue;
        }
        
        // Validate checksum if requested
        if (validateChecksums && layerData.checksum) {
          const calculatedChecksum = this._calculateChecksum(layerData.config);
          if (!ConstantTimeOps.constantTimeEquals(layerData.checksum, calculatedChecksum)) {
            throw new SecurityValidationError(`Checksum validation failed for layer: ${layer}`, 'CHECKSUM_MISMATCH');
          }
        }
        
        // Import layer data
        if (overwriteExisting || !this.layers.has(layer)) {
          this.layers.set(layer, {
            config: { ...layerData.config },
            timestamp: layerData.timestamp || Date.now(),
            version: layerData.version || this.currentVersion,
            checksum: layerData.checksum,
            metadata: layerData.metadata ? { ...layerData.metadata } : {}
          });
          
          // Persist imported layer
          await this._persistLayer(layer, this.layers.get(layer));
        }
      }
      
      // Clear cache after import
      this._invalidateCache();
      
      // Update statistics
      this.stats.migrationsPerformed++;
      
      return true;
      
    } catch (error) {
      this._handleError(error, 'importConfig', { importData, options });
      return false;
    }
  }

  /**
   * Get configuration store statistics
   * 
   * @returns {Object} Statistics and metrics
   */
  getStats() {
    return {
      ...this.stats,
      layers: this.layers.size,
      cacheSize: this.cache.size,
      currentVersion: this.currentVersion,
      versionHistory: this.versionHistory.length,
      backend: this.options.backend,
      encryptionMode: this.options.encryptionMode
    };
  }

  /**
   * Cleanup resources and stop background tasks
   */
  destroy() {
    if (this.backupTimer) {
      clearInterval(this.backupTimer);
      this.backupTimer = null;
    }
    
    this.cache.clear();
    this.cacheTimestamps.clear();
    this.layers.clear();
    
    if (this.backend && typeof this.backend.destroy === 'function') {
      this.backend.destroy();
    }
  }

  /**
   * Initialize storage backend
   * 
   * @private
   */
  async _initializeBackend() {
    switch (this.options.backend) {
      case STORAGE_BACKEND.LOCALSTORAGE:
        this.backend = new LocalStorageBackend(this.options);
        break;
      case STORAGE_BACKEND.INDEXEDDB:
        this.backend = new IndexedDBBackend(this.options);
        break;
      case STORAGE_BACKEND.MEMORY:
        this.backend = new MemoryBackend(this.options);
        break;
      case STORAGE_BACKEND.CUSTOM:
        if (this.options.customBackend) {
          this.backend = this.options.customBackend;
        } else {
          throw new Error('Custom backend specified but not provided');
        }
        break;
      default:
        throw new Error(`Unsupported storage backend: ${this.options.backend}`);
    }
    
    await this.backend.initialize();
    
    // Initialize encryption if enabled
    if (this.options.encryptionMode !== ENCRYPTION_MODE.NONE) {
      await this._initializeEncryption();
    }
    
    // Load existing configuration
    await this._loadExistingConfig();
  }

  /**
   * Initialize encryption system
   *
   * @private
   */
  async _initializeEncryption() {
    if (this.options.encryptionMode === ENCRYPTION_MODE.NONE) return;

    // Generate or derive encryption key
    if (this.options.encryptionKey) {
      this.encryptionKey = this.options.encryptionKey;
    } else {
      // Generate key from user context or system entropy
      this.encryptionKey = await this._deriveEncryptionKey();
    }
  }

  /**
   * Derive encryption key from available entropy
   *
   * @private
   * @returns {CryptoKey} Derived encryption key
   */
  async _deriveEncryptionKey() {
    const keyMaterial = new TextEncoder().encode(
      `webotter-config-${Date.now()}-${Math.random()}`
    );

    const key = await crypto.subtle.importKey(
      'raw',
      keyMaterial,
      { name: 'PBKDF2' },
      false,
      ['deriveKey']
    );

    const salt = crypto.getRandomValues(new Uint8Array(16));

    return await crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt: salt,
        iterations: 100000,
        hash: 'SHA-256'
      },
      key,
      { name: 'AES-GCM', length: 256 },
      false,
      ['encrypt', 'decrypt']
    );
  }

  /**
   * Encrypt configuration data
   *
   * @private
   * @param {string} data - Data to encrypt
   * @returns {string} Encrypted data
   */
  async _encryptData(data) {
    if (!this.encryptionKey) return data;

    const iv = crypto.getRandomValues(new Uint8Array(12));
    const encodedData = new TextEncoder().encode(data);

    const encrypted = await crypto.subtle.encrypt(
      { name: 'AES-GCM', iv: iv },
      this.encryptionKey,
      encodedData
    );

    // Combine IV and encrypted data
    const combined = new Uint8Array(iv.length + encrypted.byteLength);
    combined.set(iv);
    combined.set(new Uint8Array(encrypted), iv.length);

    this.stats.encryptionOperations++;

    return btoa(String.fromCharCode(...combined));
  }

  /**
   * Decrypt configuration data
   *
   * @private
   * @param {string} encryptedData - Encrypted data
   * @returns {string} Decrypted data
   */
  async _decryptData(encryptedData) {
    if (!this.encryptionKey) return encryptedData;

    const combined = new Uint8Array(
      atob(encryptedData).split('').map(char => char.charCodeAt(0))
    );

    const iv = combined.slice(0, 12);
    const encrypted = combined.slice(12);

    const decrypted = await crypto.subtle.decrypt(
      { name: 'AES-GCM', iv: iv },
      this.encryptionKey,
      encrypted
    );

    this.stats.encryptionOperations++;

    return new TextDecoder().decode(decrypted);
  }

  /**
   * Calculate checksum for configuration data
   *
   * @private
   * @param {Object} config - Configuration object
   * @returns {string} Checksum
   */
  _calculateChecksum(config) {
    const configString = JSON.stringify(config, Object.keys(config).sort());
    const encoder = new TextEncoder();
    const data = encoder.encode(configString);

    // Simple hash for checksum (in production, use crypto.subtle.digest)
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      hash = ((hash << 5) - hash + data[i]) & 0xffffffff;
    }
    return hash.toString(16);
  }

  /**
   * Check if cache entry is valid
   *
   * @private
   * @param {string} key - Cache key
   * @returns {boolean} True if cache is valid
   */
  _isCacheValid(key) {
    const timestamp = this.cacheTimestamps.get(key);
    return timestamp && (Date.now() - timestamp) < this.cacheTTL;
  }

  /**
   * Invalidate entire cache
   *
   * @private
   */
  _invalidateCache() {
    this.cache.clear();
    this.cacheTimestamps.clear();
  }

  /**
   * Invalidate specific cache key
   *
   * @private
   * @param {string} key - Key to invalidate
   */
  _invalidateCacheKey(key) {
    this.cache.delete(key);
    this.cacheTimestamps.delete(key);
  }

  /**
   * Persist layer to storage backend
   *
   * @private
   * @param {string} layer - Layer name
   * @param {Object} layerData - Layer data
   */
  async _persistLayer(layer, layerData) {
    if (!this.backend) return;

    const serializedData = JSON.stringify(layerData);
    const dataToStore = this.options.encryptionMode !== ENCRYPTION_MODE.NONE
      ? await this._encryptData(serializedData)
      : serializedData;

    await this.backend.setItem(`config_layer_${layer}`, dataToStore);
  }

  /**
   * Remove persisted layer from storage
   *
   * @private
   * @param {string} layer - Layer name
   */
  async _removePersistedLayer(layer) {
    if (!this.backend) return;
    await this.backend.removeItem(`config_layer_${layer}`);
  }

  /**
   * Load existing configuration from storage
   *
   * @private
   */
  async _loadExistingConfig() {
    if (!this.backend) return;

    for (const layer of this.layerOrder) {
      try {
        const storedData = await this.backend.getItem(`config_layer_${layer}`);
        if (storedData) {
          const decryptedData = this.options.encryptionMode !== ENCRYPTION_MODE.NONE
            ? await this._decryptData(storedData)
            : storedData;

          const layerData = JSON.parse(decryptedData);
          this.layers.set(layer, layerData);
        }
      } catch (error) {
        console.warn(`Failed to load configuration layer ${layer}:`, error);
      }
    }
  }

  /**
   * Perform automatic backup
   *
   * @private
   */
  async _performBackup() {
    try {
      const backupData = await this.exportConfig({
        includeMetadata: true,
        includeVersionHistory: true,
        encrypt: true
      });

      const backupKey = `config_backup_${Date.now()}`;
      await this.backend.setItem(backupKey, JSON.stringify(backupData));

      this.stats.backupsCreated++;

      // Clean up old backups
      await this._cleanupOldBackups();

    } catch (error) {
      console.error('Failed to create configuration backup:', error);
    }
  }

  /**
   * Clean up old backup files
   *
   * @private
   */
  async _cleanupOldBackups() {
    // Implementation would depend on backend capabilities
    // Keep only the most recent backups based on maxVersions
  }

  /**
   * Migrate configuration to current version
   *
   * @private
   * @param {Object} configData - Configuration data to migrate
   * @returns {Object} Migrated configuration data
   */
  async _migrateConfig(configData) {
    // Implement version-specific migration logic
    const migrations = {
      '1.0': (data) => data, // No migration needed for current version
      // Add future migration functions here
    };

    let migratedData = { ...configData };

    // Apply migrations in sequence if needed
    if (migrations[configData.version]) {
      migratedData = migrations[configData.version](migratedData);
      migratedData.version = this.currentVersion;
    }

    return migratedData;
  }

  /**
   * Handle errors with recovery
   *
   * @private
   * @param {Error} error - Error to handle
   * @param {string} operation - Operation that failed
   * @param {Object} context - Error context
   */
  _handleError(error, operation, context) {
    console.error(`Configuration store error in ${operation}:`, error);

    // Use global error recovery if available
    if (typeof globalErrorRecovery !== 'undefined') {
      globalErrorRecovery.handleAKEError(error, {
        id: 'configuration-store',
        operation: operation,
        context: context
      });
    }
  }
}

/**
 * Memory Storage Backend
 */
class MemoryBackend {
  constructor(options = {}) {
    this.storage = new Map();
  }

  async initialize() {
    // No initialization needed for memory backend
  }

  async getItem(key) {
    return this.storage.get(key);
  }

  async setItem(key, value) {
    this.storage.set(key, value);
  }

  async removeItem(key) {
    this.storage.delete(key);
  }

  destroy() {
    this.storage.clear();
  }
}

/**
 * LocalStorage Backend
 */
class LocalStorageBackend {
  constructor(options = {}) {
    this.prefix = options.prefix || 'webotter_config_';
  }

  async initialize() {
    if (typeof localStorage === 'undefined') {
      throw new Error('localStorage not available');
    }
  }

  async getItem(key) {
    return localStorage.getItem(this.prefix + key);
  }

  async setItem(key, value) {
    localStorage.setItem(this.prefix + key, value);
  }

  async removeItem(key) {
    localStorage.removeItem(this.prefix + key);
  }

  destroy() {
    // Clean up all items with our prefix
    const keysToRemove = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(this.prefix)) {
        keysToRemove.push(key);
      }
    }
    keysToRemove.forEach(key => localStorage.removeItem(key));
  }
}

/**
 * IndexedDB Backend (simplified implementation)
 */
class IndexedDBBackend {
  constructor(options = {}) {
    this.dbName = options.dbName || 'webotter_config';
    this.version = options.version || 1;
    this.db = null;
  }

  async initialize() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        if (!db.objectStoreNames.contains('config')) {
          db.createObjectStore('config');
        }
      };
    });
  }

  async getItem(key) {
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction(['config'], 'readonly');
      const store = transaction.objectStore('config');
      const request = store.get(key);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
    });
  }

  async setItem(key, value) {
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction(['config'], 'readwrite');
      const store = transaction.objectStore('config');
      const request = store.put(value, key);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();
    });
  }

  async removeItem(key) {
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction(['config'], 'readwrite');
      const store = transaction.objectStore('config');
      const request = store.delete(key);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();
    });
  }

  destroy() {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}
