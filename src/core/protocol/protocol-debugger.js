/**
 * Protocol Debugger - Comprehensive Protocol Debugging and Tracing
 * 
 * Provides comprehensive debugging capabilities for WebOTR protocol with:
 * - Real-time protocol message tracing
 * - State inspection and visualization
 * - Performance monitoring and profiling
 * - Error tracking and analysis
 * - Interactive debugging interface
 * 
 * Essential for development, troubleshooting, and protocol compliance validation.
 */

import { CryptoValidation, SecurityValidationError } from '../security/validation.js';
import { ConstantTimeOps } from '../security/constant-time.js';
import { globalErrorRecovery, ERROR_TYPES } from '../security/error-recovery.js';

/**
 * Debug levels for filtering debug output
 */
export const DEBUG_LEVEL = {
  TRACE: 0,     // Detailed trace information
  DEBUG: 1,     // Debug information
  INFO: 2,      // General information
  WARN: 3,      // Warning messages
  ERROR: 4,     // Error messages
  FATAL: 5      // Fatal errors
};

/**
 * Protocol event types for tracing
 */
export const PROTOCOL_EVENT = {
  MESSAGE_SENT: 'message_sent',
  MESSAGE_RECEIVED: 'message_received',
  STATE_CHANGE: 'state_change',
  KEY_EXCHANGE: 'key_exchange',
  ENCRYPTION: 'encryption',
  DECRYPTION: 'decryption',
  VALIDATION: 'validation',
  ERROR: 'error',
  PERFORMANCE: 'performance',
  SMP_EVENT: 'smp_event',
  VERSION_NEGOTIATION: 'version_negotiation'
};

/**
 * Debug output formats
 */
export const DEBUG_FORMAT = {
  JSON: 'json',
  TEXT: 'text',
  HTML: 'html',
  BINARY: 'binary'
};

/**
 * Protocol Debugger Implementation
 */
export class ProtocolDebugger {
  /**
   * Create protocol debugger
   * 
   * @param {Object} options - Debugger options
   */
  constructor(options = {}) {
    this.options = {
      enabled: true,
      level: DEBUG_LEVEL.INFO,
      maxTraceEntries: 10000,
      enablePerformanceMonitoring: true,
      enableStateTracking: true,
      enableMessageCapture: true,
      enableErrorTracking: true,
      outputFormat: DEBUG_FORMAT.JSON,
      realTimeUpdates: true,
      ...options
    };
    
    // Trace storage
    this.traceEntries = [];
    this.stateHistory = [];
    this.performanceMetrics = new Map();
    this.errorLog = [];
    
    // Active sessions tracking
    this.activeSessions = new Map();
    this.sessionStates = new Map();
    
    // Event listeners
    this.eventListeners = new Map();
    
    // Performance monitoring
    this.performanceTimers = new Map();
    this.performanceCounters = new Map();
    
    // Statistics
    this.stats = {
      totalEvents: 0,
      messagesSent: 0,
      messagesReceived: 0,
      errorsLogged: 0,
      stateChanges: 0,
      performanceEvents: 0,
      sessionsTracked: 0
    };
    
    // Initialize debugger
    this._initializeDebugger();
  }

  /**
   * Start debugging session
   * 
   * @param {string} sessionId - Session identifier
   * @param {Object} sessionInfo - Session information
   */
  startSession(sessionId, sessionInfo = {}) {
    if (!this.options.enabled) return;
    
    const session = {
      id: sessionId,
      startTime: Date.now(),
      info: sessionInfo,
      events: [],
      states: [],
      performance: {
        startTime: performance.now(),
        events: []
      }
    };
    
    this.activeSessions.set(sessionId, session);
    this.stats.sessionsTracked++;
    
    this._logEvent(PROTOCOL_EVENT.STATE_CHANGE, {
      sessionId: sessionId,
      event: 'session_started',
      info: sessionInfo
    });
  }

  /**
   * End debugging session
   * 
   * @param {string} sessionId - Session identifier
   * @param {Object} endInfo - Session end information
   */
  endSession(sessionId, endInfo = {}) {
    if (!this.options.enabled) return;
    
    const session = this.activeSessions.get(sessionId);
    if (session) {
      session.endTime = Date.now();
      session.duration = session.endTime - session.startTime;
      session.endInfo = endInfo;
      
      this._logEvent(PROTOCOL_EVENT.STATE_CHANGE, {
        sessionId: sessionId,
        event: 'session_ended',
        duration: session.duration,
        info: endInfo
      });
      
      // Move to completed sessions
      this.activeSessions.delete(sessionId);
    }
  }

  /**
   * Log protocol message
   * 
   * @param {string} direction - 'sent' or 'received'
   * @param {Object} message - Protocol message
   * @param {string} sessionId - Session identifier
   * @param {Object} metadata - Additional metadata
   */
  logMessage(direction, message, sessionId, metadata = {}) {
    if (!this.options.enabled || !this.options.enableMessageCapture) return;
    
    const eventType = direction === 'sent' ? PROTOCOL_EVENT.MESSAGE_SENT : PROTOCOL_EVENT.MESSAGE_RECEIVED;
    
    const messageData = {
      sessionId: sessionId,
      direction: direction,
      message: this._sanitizeMessage(message),
      timestamp: Date.now(),
      size: this._calculateMessageSize(message),
      metadata: metadata
    };
    
    this._logEvent(eventType, messageData);
    
    // Update statistics
    if (direction === 'sent') {
      this.stats.messagesSent++;
    } else {
      this.stats.messagesReceived++;
    }
    
    // Add to session events
    const session = this.activeSessions.get(sessionId);
    if (session) {
      session.events.push({
        type: eventType,
        data: messageData,
        timestamp: Date.now()
      });
    }
  }

  /**
   * Log state change
   * 
   * @param {string} sessionId - Session identifier
   * @param {string} component - Component name
   * @param {string} oldState - Previous state
   * @param {string} newState - New state
   * @param {Object} context - State change context
   */
  logStateChange(sessionId, component, oldState, newState, context = {}) {
    if (!this.options.enabled || !this.options.enableStateTracking) return;
    
    const stateData = {
      sessionId: sessionId,
      component: component,
      oldState: oldState,
      newState: newState,
      context: context,
      timestamp: Date.now()
    };
    
    this._logEvent(PROTOCOL_EVENT.STATE_CHANGE, stateData);
    this.stats.stateChanges++;
    
    // Add to state history
    this.stateHistory.push(stateData);
    
    // Add to session states
    const session = this.activeSessions.get(sessionId);
    if (session) {
      session.states.push(stateData);
    }
    
    // Update current session state
    if (!this.sessionStates.has(sessionId)) {
      this.sessionStates.set(sessionId, {});
    }
    this.sessionStates.get(sessionId)[component] = newState;
  }

  /**
   * Log performance event
   * 
   * @param {string} operation - Operation name
   * @param {number} duration - Operation duration in milliseconds
   * @param {string} sessionId - Session identifier
   * @param {Object} details - Performance details
   */
  logPerformance(operation, duration, sessionId, details = {}) {
    if (!this.options.enabled || !this.options.enablePerformanceMonitoring) return;
    
    const perfData = {
      operation: operation,
      duration: duration,
      sessionId: sessionId,
      details: details,
      timestamp: Date.now()
    };
    
    this._logEvent(PROTOCOL_EVENT.PERFORMANCE, perfData);
    this.stats.performanceEvents++;
    
    // Update performance metrics
    if (!this.performanceMetrics.has(operation)) {
      this.performanceMetrics.set(operation, {
        count: 0,
        totalDuration: 0,
        minDuration: Infinity,
        maxDuration: 0,
        avgDuration: 0
      });
    }
    
    const metrics = this.performanceMetrics.get(operation);
    metrics.count++;
    metrics.totalDuration += duration;
    metrics.minDuration = Math.min(metrics.minDuration, duration);
    metrics.maxDuration = Math.max(metrics.maxDuration, duration);
    metrics.avgDuration = metrics.totalDuration / metrics.count;
    
    // Add to session performance
    const session = this.activeSessions.get(sessionId);
    if (session) {
      session.performance.events.push(perfData);
    }
  }

  /**
   * Start performance timer
   * 
   * @param {string} operation - Operation name
   * @param {string} sessionId - Session identifier
   * @returns {string} Timer ID
   */
  startTimer(operation, sessionId) {
    if (!this.options.enabled || !this.options.enablePerformanceMonitoring) return null;
    
    const timerId = `${operation}_${sessionId}_${Date.now()}`;
    this.performanceTimers.set(timerId, {
      operation: operation,
      sessionId: sessionId,
      startTime: performance.now()
    });
    
    return timerId;
  }

  /**
   * End performance timer
   * 
   * @param {string} timerId - Timer ID
   * @param {Object} details - Additional details
   */
  endTimer(timerId, details = {}) {
    if (!this.options.enabled || !timerId) return;
    
    const timer = this.performanceTimers.get(timerId);
    if (timer) {
      const duration = performance.now() - timer.startTime;
      this.logPerformance(timer.operation, duration, timer.sessionId, details);
      this.performanceTimers.delete(timerId);
    }
  }

  /**
   * Log error event
   * 
   * @param {Error} error - Error object
   * @param {string} sessionId - Session identifier
   * @param {Object} context - Error context
   */
  logError(error, sessionId, context = {}) {
    if (!this.options.enabled || !this.options.enableErrorTracking) return;
    
    const errorData = {
      message: error.message,
      stack: error.stack,
      name: error.name,
      sessionId: sessionId,
      context: context,
      timestamp: Date.now()
    };
    
    this._logEvent(PROTOCOL_EVENT.ERROR, errorData);
    this.errorLog.push(errorData);
    this.stats.errorsLogged++;
    
    // Add to session events
    const session = this.activeSessions.get(sessionId);
    if (session) {
      session.events.push({
        type: PROTOCOL_EVENT.ERROR,
        data: errorData,
        timestamp: Date.now()
      });
    }
  }

  /**
   * Get current session state
   * 
   * @param {string} sessionId - Session identifier
   * @returns {Object} Current session state
   */
  getSessionState(sessionId) {
    const session = this.activeSessions.get(sessionId);
    const currentState = this.sessionStates.get(sessionId) || {};
    
    return {
      session: session,
      currentState: currentState,
      isActive: this.activeSessions.has(sessionId)
    };
  }

  /**
   * Get trace entries with filtering
   * 
   * @param {Object} filters - Filter options
   * @returns {Array} Filtered trace entries
   */
  getTraceEntries(filters = {}) {
    let entries = [...this.traceEntries];
    
    // Apply filters
    if (filters.sessionId) {
      entries = entries.filter(entry => entry.data.sessionId === filters.sessionId);
    }
    
    if (filters.eventType) {
      entries = entries.filter(entry => entry.type === filters.eventType);
    }
    
    if (filters.level !== undefined) {
      entries = entries.filter(entry => entry.level >= filters.level);
    }
    
    if (filters.startTime) {
      entries = entries.filter(entry => entry.timestamp >= filters.startTime);
    }
    
    if (filters.endTime) {
      entries = entries.filter(entry => entry.timestamp <= filters.endTime);
    }
    
    // Apply limit
    if (filters.limit) {
      entries = entries.slice(-filters.limit);
    }
    
    return entries;
  }

  /**
   * Get performance summary
   * 
   * @param {string} sessionId - Optional session filter
   * @returns {Object} Performance summary
   */
  getPerformanceSummary(sessionId = null) {
    const summary = {
      operations: {},
      totalOperations: 0,
      averageResponseTime: 0,
      sessionMetrics: {}
    };
    
    // Aggregate performance metrics
    for (const [operation, metrics] of this.performanceMetrics) {
      summary.operations[operation] = { ...metrics };
      summary.totalOperations += metrics.count;
    }
    
    // Calculate overall average
    if (summary.totalOperations > 0) {
      const totalDuration = Object.values(summary.operations)
        .reduce((sum, op) => sum + op.totalDuration, 0);
      summary.averageResponseTime = totalDuration / summary.totalOperations;
    }
    
    // Session-specific metrics
    if (sessionId) {
      const session = this.activeSessions.get(sessionId);
      if (session && session.performance) {
        summary.sessionMetrics = this._calculateSessionMetrics(session);
      }
    }
    
    return summary;
  }

  /**
   * Export debug data
   *
   * @param {Object} options - Export options
   * @returns {Object} Exported debug data
   */
  exportDebugData(options = {}) {
    const {
      includeTraceEntries = true,
      includeStateHistory = true,
      includePerformanceMetrics = true,
      includeErrorLog = true,
      sessionId = null,
      format = this.options.outputFormat
    } = options;

    const exportData = {
      timestamp: Date.now(),
      debuggerOptions: this.options,
      statistics: this.stats
    };

    if (includeTraceEntries) {
      exportData.traceEntries = sessionId
        ? this.getTraceEntries({ sessionId })
        : this.traceEntries;
    }

    if (includeStateHistory) {
      exportData.stateHistory = sessionId
        ? this.stateHistory.filter(state => state.sessionId === sessionId)
        : this.stateHistory;
    }

    if (includePerformanceMetrics) {
      exportData.performanceMetrics = Object.fromEntries(this.performanceMetrics);
      exportData.performanceSummary = this.getPerformanceSummary(sessionId);
    }

    if (includeErrorLog) {
      exportData.errorLog = sessionId
        ? this.errorLog.filter(error => error.sessionId === sessionId)
        : this.errorLog;
    }

    // Include session data
    if (sessionId) {
      exportData.sessionData = this.getSessionState(sessionId);
    } else {
      exportData.activeSessions = Array.from(this.activeSessions.values());
    }

    return this._formatOutput(exportData, format);
  }

  /**
   * Clear debug data
   *
   * @param {Object} options - Clear options
   */
  clearDebugData(options = {}) {
    const {
      clearTraceEntries = true,
      clearStateHistory = true,
      clearPerformanceMetrics = true,
      clearErrorLog = true,
      clearActiveSessions = false
    } = options;

    if (clearTraceEntries) {
      this.traceEntries = [];
    }

    if (clearStateHistory) {
      this.stateHistory = [];
    }

    if (clearPerformanceMetrics) {
      this.performanceMetrics.clear();
      this.performanceTimers.clear();
    }

    if (clearErrorLog) {
      this.errorLog = [];
    }

    if (clearActiveSessions) {
      this.activeSessions.clear();
      this.sessionStates.clear();
    }

    // Reset statistics
    this.stats = {
      totalEvents: 0,
      messagesSent: 0,
      messagesReceived: 0,
      errorsLogged: 0,
      stateChanges: 0,
      performanceEvents: 0,
      sessionsTracked: this.stats.sessionsTracked
    };
  }

  /**
   * Add event listener
   *
   * @param {string} eventType - Event type to listen for
   * @param {Function} callback - Callback function
   * @returns {string} Listener ID
   */
  addEventListener(eventType, callback) {
    const listenerId = `listener_${Date.now()}_${Math.random()}`;

    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, new Map());
    }

    this.eventListeners.get(eventType).set(listenerId, callback);
    return listenerId;
  }

  /**
   * Remove event listener
   *
   * @param {string} eventType - Event type
   * @param {string} listenerId - Listener ID
   */
  removeEventListener(eventType, listenerId) {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      listeners.delete(listenerId);
      if (listeners.size === 0) {
        this.eventListeners.delete(eventType);
      }
    }
  }

  /**
   * Get debug statistics
   *
   * @returns {Object} Debug statistics
   */
  getStats() {
    return {
      ...this.stats,
      traceEntriesCount: this.traceEntries.length,
      stateHistoryCount: this.stateHistory.length,
      errorLogCount: this.errorLog.length,
      activeSessionsCount: this.activeSessions.size,
      performanceMetricsCount: this.performanceMetrics.size,
      eventListenersCount: Array.from(this.eventListeners.values())
        .reduce((sum, listeners) => sum + listeners.size, 0)
    };
  }

  /**
   * Enable/disable debugging
   *
   * @param {boolean} enabled - Enable state
   */
  setEnabled(enabled) {
    this.options.enabled = enabled;
  }

  /**
   * Set debug level
   *
   * @param {number} level - Debug level
   */
  setDebugLevel(level) {
    this.options.level = level;
  }

  /**
   * Cleanup resources
   */
  destroy() {
    this.clearDebugData({ clearActiveSessions: true });
    this.eventListeners.clear();
    this.performanceTimers.clear();
    this.performanceCounters.clear();
  }

  /**
   * Initialize debugger
   *
   * @private
   */
  _initializeDebugger() {
    // Set up automatic cleanup
    if (this.options.maxTraceEntries > 0) {
      setInterval(() => {
        if (this.traceEntries.length > this.options.maxTraceEntries) {
          const excess = this.traceEntries.length - this.options.maxTraceEntries;
          this.traceEntries.splice(0, excess);
        }
      }, 60000); // Check every minute
    }
  }

  /**
   * Log debug event
   *
   * @private
   * @param {string} eventType - Event type
   * @param {Object} data - Event data
   * @param {number} level - Debug level
   */
  _logEvent(eventType, data, level = DEBUG_LEVEL.INFO) {
    if (!this.options.enabled || level < this.options.level) return;

    const entry = {
      id: `event_${Date.now()}_${Math.random()}`,
      type: eventType,
      level: level,
      data: data,
      timestamp: Date.now(),
      formattedTime: new Date().toISOString()
    };

    this.traceEntries.push(entry);
    this.stats.totalEvents++;

    // Notify event listeners
    this._notifyEventListeners(eventType, entry);

    // Real-time updates
    if (this.options.realTimeUpdates) {
      this._handleRealTimeUpdate(entry);
    }
  }

  /**
   * Notify event listeners
   *
   * @private
   * @param {string} eventType - Event type
   * @param {Object} entry - Event entry
   */
  _notifyEventListeners(eventType, entry) {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      for (const callback of listeners.values()) {
        try {
          callback(entry);
        } catch (error) {
          console.error('Error in debug event listener:', error);
        }
      }
    }

    // Also notify global listeners
    const globalListeners = this.eventListeners.get('*');
    if (globalListeners) {
      for (const callback of globalListeners.values()) {
        try {
          callback(entry);
        } catch (error) {
          console.error('Error in global debug event listener:', error);
        }
      }
    }
  }

  /**
   * Handle real-time updates
   *
   * @private
   * @param {Object} entry - Event entry
   */
  _handleRealTimeUpdate(entry) {
    // Emit to console if in development mode
    if (typeof window !== 'undefined' && window.location?.hostname === 'localhost') {
      const levelNames = ['TRACE', 'DEBUG', 'INFO', 'WARN', 'ERROR', 'FATAL'];
      const levelName = levelNames[entry.level] || 'UNKNOWN';
      console.log(`[WebOTR-${levelName}] ${entry.type}:`, entry.data);
    }
  }

  /**
   * Sanitize message for logging
   *
   * @private
   * @param {Object} message - Message to sanitize
   * @returns {Object} Sanitized message
   */
  _sanitizeMessage(message) {
    // Create a deep copy and remove sensitive data
    const sanitized = JSON.parse(JSON.stringify(message));

    // Remove or mask sensitive fields
    const sensitiveFields = ['privateKey', 'secretKey', 'password', 'token'];

    const sanitizeObject = (obj) => {
      if (typeof obj !== 'object' || obj === null) return obj;

      for (const [key, value] of Object.entries(obj)) {
        if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
          obj[key] = '[REDACTED]';
        } else if (typeof value === 'object') {
          sanitizeObject(value);
        }
      }
    };

    sanitizeObject(sanitized);
    return sanitized;
  }

  /**
   * Calculate message size
   *
   * @private
   * @param {Object} message - Message object
   * @returns {number} Message size in bytes
   */
  _calculateMessageSize(message) {
    try {
      return new Blob([JSON.stringify(message)]).size;
    } catch (error) {
      return 0;
    }
  }

  /**
   * Calculate session metrics
   *
   * @private
   * @param {Object} session - Session object
   * @returns {Object} Session metrics
   */
  _calculateSessionMetrics(session) {
    const metrics = {
      duration: session.endTime ? session.endTime - session.startTime : Date.now() - session.startTime,
      eventCount: session.events.length,
      stateChangeCount: session.states.length,
      performanceEventCount: session.performance.events.length,
      errorCount: session.events.filter(e => e.type === PROTOCOL_EVENT.ERROR).length
    };

    // Calculate performance statistics
    if (session.performance.events.length > 0) {
      const durations = session.performance.events.map(e => e.duration);
      metrics.averageOperationTime = durations.reduce((sum, d) => sum + d, 0) / durations.length;
      metrics.minOperationTime = Math.min(...durations);
      metrics.maxOperationTime = Math.max(...durations);
    }

    return metrics;
  }

  /**
   * Format output according to specified format
   *
   * @private
   * @param {Object} data - Data to format
   * @param {string} format - Output format
   * @returns {*} Formatted data
   */
  _formatOutput(data, format) {
    switch (format) {
      case DEBUG_FORMAT.JSON:
        return data;

      case DEBUG_FORMAT.TEXT:
        return this._formatAsText(data);

      case DEBUG_FORMAT.HTML:
        return this._formatAsHTML(data);

      case DEBUG_FORMAT.BINARY:
        return new Uint8Array(JSON.stringify(data).split('').map(c => c.charCodeAt(0)));

      default:
        return data;
    }
  }

  /**
   * Format data as text
   *
   * @private
   * @param {Object} data - Data to format
   * @returns {string} Text representation
   */
  _formatAsText(data) {
    let text = `WebOTR Debug Report - ${new Date(data.timestamp).toISOString()}\n`;
    text += '='.repeat(60) + '\n\n';

    text += `Statistics:\n`;
    text += `- Total Events: ${data.statistics.totalEvents}\n`;
    text += `- Messages Sent: ${data.statistics.messagesSent}\n`;
    text += `- Messages Received: ${data.statistics.messagesReceived}\n`;
    text += `- Errors Logged: ${data.statistics.errorsLogged}\n`;
    text += `- State Changes: ${data.statistics.stateChanges}\n\n`;

    if (data.traceEntries) {
      text += `Trace Entries (${data.traceEntries.length}):\n`;
      text += '-'.repeat(40) + '\n';

      for (const entry of data.traceEntries.slice(-10)) {
        text += `[${entry.formattedTime}] ${entry.type}: ${JSON.stringify(entry.data, null, 2)}\n\n`;
      }
    }

    return text;
  }

  /**
   * Format data as HTML
   *
   * @private
   * @param {Object} data - Data to format
   * @returns {string} HTML representation
   */
  _formatAsHTML(data) {
    let html = `
      <div class="webottr-debug-report">
        <h1>WebOTR Debug Report</h1>
        <p><strong>Generated:</strong> ${new Date(data.timestamp).toISOString()}</p>

        <h2>Statistics</h2>
        <ul>
          <li>Total Events: ${data.statistics.totalEvents}</li>
          <li>Messages Sent: ${data.statistics.messagesSent}</li>
          <li>Messages Received: ${data.statistics.messagesReceived}</li>
          <li>Errors Logged: ${data.statistics.errorsLogged}</li>
          <li>State Changes: ${data.statistics.stateChanges}</li>
        </ul>
    `;

    if (data.traceEntries) {
      html += `
        <h2>Recent Trace Entries</h2>
        <div class="trace-entries">
      `;

      for (const entry of data.traceEntries.slice(-10)) {
        html += `
          <div class="trace-entry level-${entry.level}">
            <div class="timestamp">${entry.formattedTime}</div>
            <div class="type">${entry.type}</div>
            <div class="data"><pre>${JSON.stringify(entry.data, null, 2)}</pre></div>
          </div>
        `;
      }

      html += `</div>`;
    }

    html += `</div>`;
    return html;
  }
}
