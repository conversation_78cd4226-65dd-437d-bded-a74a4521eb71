/**
 * Configuration Validator - Comprehensive Configuration Validation
 * 
 * Provides comprehensive validation for WebOTR configuration with:
 * - Schema-based validation
 * - Cross-field dependency validation
 * - Security constraint enforcement
 * - Performance impact assessment
 * - Custom validation rules
 * 
 * Integrates with Policy Manager and Configuration Store.
 */

import { CryptoValidation, SecurityValidationError } from '../security/validation.js';
import { ConstantTimeOps } from '../security/constant-time.js';

/**
 * Validation severity levels
 */
export const VALIDATION_SEVERITY = {
  ERROR: 'error',       // Validation failure - blocks operation
  WARNING: 'warning',   // Potential issue - allows operation with warning
  INFO: 'info',         // Informational - no impact on operation
  DEBUG: 'debug'        // Debug information - for development only
};

/**
 * Validation rule types
 */
export const VALIDATION_RULE_TYPE = {
  TYPE: 'type',                 // Data type validation
  RANGE: 'range',              // Numeric range validation
  ENUM: 'enum',                // Enumeration validation
  PATTERN: 'pattern',          // Regular expression validation
  CUSTOM: 'custom',            // Custom validation function
  DEPENDENCY: 'dependency',    // Cross-field dependency validation
  SECURITY: 'security',        // Security constraint validation
  PERFORMANCE: 'performance'   // Performance impact validation
};

/**
 * Default validation schemas for WebOTR configuration
 */
export const DEFAULT_VALIDATION_SCHEMAS = {
  security: {
    requireEncryption: {
      type: 'boolean',
      required: true,
      default: true,
      security: {
        impact: 'critical',
        description: 'Disabling encryption removes all security protections'
      }
    },
    allowV2: {
      type: 'boolean',
      default: true,
      dependencies: ['allowV3'],
      validation: {
        custom: (value, config) => {
          if (!value && !config.allowV3) {
            return { valid: false, message: 'At least one OTR version must be allowed' };
          }
          return { valid: true };
        }
      }
    },
    allowV3: {
      type: 'boolean',
      default: true,
      dependencies: ['allowV2']
    },
    sessionTimeoutMs: {
      type: 'number',
      min: 30000,      // 30 seconds minimum
      max: 86400000,   // 24 hours maximum
      default: 300000, // 5 minutes
      performance: {
        impact: 'low',
        description: 'Very short timeouts may impact user experience'
      }
    },
    maxRetryAttempts: {
      type: 'number',
      min: 1,
      max: 10,
      default: 3,
      performance: {
        impact: 'medium',
        description: 'High retry counts may impact performance under load'
      }
    }
  },
  
  protocol: {
    maxBufferSize: {
      type: 'number',
      min: 10,
      max: 1000,
      default: 100,
      performance: {
        impact: 'high',
        description: 'Large buffers increase memory usage'
      }
    },
    maxGapSize: {
      type: 'number',
      min: 1,
      max: 50,
      default: 10,
      dependencies: ['maxBufferSize'],
      validation: {
        custom: (value, config) => {
          if (value > config.maxBufferSize / 2) {
            return { 
              valid: false, 
              message: 'Gap size should not exceed half of buffer size' 
            };
          }
          return { valid: true };
        }
      }
    },
    replayWindowSize: {
      type: 'number',
      min: 8,
      max: 256,
      default: 64,
      validation: {
        custom: (value) => {
          // Must be power of 2 for efficient bit operations
          if ((value & (value - 1)) !== 0) {
            return { 
              valid: false, 
              message: 'Replay window size must be a power of 2' 
            };
          }
          return { valid: true };
        }
      }
    }
  },
  
  performance: {
    enableOptimizations: {
      type: 'boolean',
      default: true
    },
    maxConcurrentSessions: {
      type: 'number',
      min: 1,
      max: 100,
      default: 10,
      performance: {
        impact: 'critical',
        description: 'High session counts significantly impact memory and CPU usage'
      }
    },
    memoryPoolSize: {
      type: 'number',
      min: 1024,        // 1KB minimum
      max: 104857600,   // 100MB maximum
      default: 1048576, // 1MB default
      performance: {
        impact: 'high',
        description: 'Large memory pools improve performance but increase memory usage'
      }
    }
  },
  
  logging: {
    enableDetailedLogging: {
      type: 'boolean',
      default: false,
      performance: {
        impact: 'medium',
        description: 'Detailed logging impacts performance and may expose sensitive information'
      },
      security: {
        impact: 'medium',
        description: 'Detailed logging may expose sensitive protocol information'
      }
    },
    logLevel: {
      type: 'string',
      enum: ['error', 'warn', 'info', 'debug'],
      default: 'info'
    },
    maxLogSize: {
      type: 'number',
      min: 1024,
      max: 104857600,
      default: 10485760 // 10MB
    }
  }
};

/**
 * Configuration Validator Implementation
 */
export class ConfigurationValidator {
  /**
   * Create configuration validator
   * 
   * @param {Object} options - Validator options
   */
  constructor(options = {}) {
    this.options = {
      strictMode: true,
      enablePerformanceValidation: true,
      enableSecurityValidation: true,
      enableDependencyValidation: true,
      maxValidationTime: 5000, // 5 seconds
      ...options
    };
    
    // Validation schemas
    this.schemas = new Map();
    this.customValidators = new Map();
    
    // Validation cache
    this.validationCache = new Map();
    this.cacheTimeout = 60000; // 1 minute
    
    // Statistics
    this.stats = {
      validationsPerformed: 0,
      validationErrors: 0,
      validationWarnings: 0,
      cacheHits: 0,
      cacheMisses: 0,
      averageValidationTime: 0
    };
    
    // Initialize default schemas
    this._initializeDefaultSchemas();
  }

  /**
   * Register validation schema for configuration category
   * 
   * @param {string} category - Configuration category
   * @param {Object} schema - Validation schema
   */
  registerSchema(category, schema) {
    this.schemas.set(category, schema);
    this._clearValidationCache(category);
  }

  /**
   * Register custom validator function
   * 
   * @param {string} name - Validator name
   * @param {Function} validator - Validator function
   */
  registerCustomValidator(name, validator) {
    if (typeof validator !== 'function') {
      throw new Error('Custom validator must be a function');
    }
    this.customValidators.set(name, validator);
  }

  /**
   * Validate single configuration value
   * 
   * @param {string} category - Configuration category
   * @param {string} key - Configuration key
   * @param {*} value - Value to validate
   * @param {Object} context - Validation context
   * @returns {Object} Validation result
   */
  validateValue(category, key, value, context = {}) {
    const startTime = performance.now();
    
    try {
      // Check cache first
      const cacheKey = `${category}.${key}:${JSON.stringify(value)}`;
      if (this._isCacheValid(cacheKey)) {
        this.stats.cacheHits++;
        return this.validationCache.get(cacheKey);
      }
      
      this.stats.cacheMisses++;
      
      const schema = this.schemas.get(category);
      if (!schema || !schema[key]) {
        return this._createValidationResult(true, [], 'No schema found for validation');
      }
      
      const fieldSchema = schema[key];
      const result = this._validateField(key, value, fieldSchema, context);
      
      // Cache result
      this.validationCache.set(cacheKey, result);
      setTimeout(() => this.validationCache.delete(cacheKey), this.cacheTimeout);
      
      // Update statistics
      this.stats.validationsPerformed++;
      if (!result.valid) this.stats.validationErrors++;
      if (result.warnings.length > 0) this.stats.validationWarnings++;
      
      const validationTime = performance.now() - startTime;
      this.stats.averageValidationTime = 
        (this.stats.averageValidationTime * (this.stats.validationsPerformed - 1) + validationTime) / 
        this.stats.validationsPerformed;
      
      return result;
      
    } catch (error) {
      return this._createValidationResult(false, [{
        severity: VALIDATION_SEVERITY.ERROR,
        message: `Validation error: ${error.message}`,
        field: key,
        value: value
      }]);
    }
  }

  /**
   * Validate complete configuration object
   * 
   * @param {Object} config - Configuration object to validate
   * @param {Object} options - Validation options
   * @returns {Object} Comprehensive validation result
   */
  validateConfiguration(config, options = {}) {
    const {
      categories = Array.from(this.schemas.keys()),
      includeWarnings = true,
      includePerformanceAnalysis = this.options.enablePerformanceValidation,
      includeSecurityAnalysis = this.options.enableSecurityValidation
    } = options;
    
    const validationResult = {
      valid: true,
      errors: [],
      warnings: [],
      info: [],
      fieldResults: {},
      summary: {
        totalFields: 0,
        validFields: 0,
        errorFields: 0,
        warningFields: 0
      }
    };
    
    // Validate each category
    for (const category of categories) {
      const categoryConfig = config[category] || {};
      const categoryResult = this._validateCategory(category, categoryConfig, config);
      
      // Merge results
      validationResult.errors.push(...categoryResult.errors);
      validationResult.warnings.push(...categoryResult.warnings);
      validationResult.info.push(...categoryResult.info);
      validationResult.fieldResults[category] = categoryResult.fieldResults;
      
      // Update summary
      validationResult.summary.totalFields += categoryResult.summary.totalFields;
      validationResult.summary.validFields += categoryResult.summary.validFields;
      validationResult.summary.errorFields += categoryResult.summary.errorFields;
      validationResult.summary.warningFields += categoryResult.summary.warningFields;
      
      if (!categoryResult.valid) {
        validationResult.valid = false;
      }
    }
    
    // Perform cross-category validation
    if (this.options.enableDependencyValidation) {
      const dependencyResult = this._validateDependencies(config);
      validationResult.errors.push(...dependencyResult.errors);
      validationResult.warnings.push(...dependencyResult.warnings);
      if (!dependencyResult.valid) {
        validationResult.valid = false;
      }
    }
    
    // Add performance analysis
    if (includePerformanceAnalysis) {
      validationResult.performanceAnalysis = this._analyzePerformanceImpact(config);
    }
    
    // Add security analysis
    if (includeSecurityAnalysis) {
      validationResult.securityAnalysis = this._analyzeSecurityImpact(config);
    }
    
    return validationResult;
  }

  /**
   * Validate configuration change impact
   * 
   * @param {Object} currentConfig - Current configuration
   * @param {Object} newConfig - New configuration
   * @returns {Object} Change impact analysis
   */
  validateConfigurationChange(currentConfig, newConfig) {
    const changeAnalysis = {
      valid: true,
      changes: [],
      impacts: {
        security: [],
        performance: [],
        compatibility: []
      },
      recommendations: []
    };
    
    // Identify changes
    const changes = this._identifyChanges(currentConfig, newConfig);
    changeAnalysis.changes = changes;
    
    // Analyze impact of each change
    for (const change of changes) {
      const impact = this._analyzeChangeImpact(change, currentConfig, newConfig);
      
      if (impact.security.length > 0) {
        changeAnalysis.impacts.security.push(...impact.security);
      }
      
      if (impact.performance.length > 0) {
        changeAnalysis.impacts.performance.push(...impact.performance);
      }
      
      if (impact.compatibility.length > 0) {
        changeAnalysis.impacts.compatibility.push(...impact.compatibility);
      }
      
      if (impact.recommendations.length > 0) {
        changeAnalysis.recommendations.push(...impact.recommendations);
      }
      
      if (impact.blocksChange) {
        changeAnalysis.valid = false;
      }
    }
    
    return changeAnalysis;
  }

  /**
   * Get validation statistics
   * 
   * @returns {Object} Validation statistics
   */
  getStats() {
    return {
      ...this.stats,
      schemasRegistered: this.schemas.size,
      customValidators: this.customValidators.size,
      cacheSize: this.validationCache.size,
      cacheHitRate: this.stats.cacheHits / (this.stats.cacheHits + this.stats.cacheMisses) || 0
    };
  }

  /**
   * Clear validation cache
   * 
   * @param {string} category - Optional category to clear
   */
  clearCache(category = null) {
    if (category) {
      this._clearValidationCache(category);
    } else {
      this.validationCache.clear();
    }
  }

  /**
   * Initialize default validation schemas
   * 
   * @private
   */
  _initializeDefaultSchemas() {
    for (const [category, schema] of Object.entries(DEFAULT_VALIDATION_SCHEMAS)) {
      this.registerSchema(category, schema);
    }
  }

  /**
   * Validate individual field against schema
   *
   * @private
   * @param {string} key - Field key
   * @param {*} value - Field value
   * @param {Object} schema - Field schema
   * @param {Object} context - Validation context
   * @returns {Object} Validation result
   */
  _validateField(key, value, schema, context) {
    const issues = [];

    // Required field validation
    if (schema.required && (value === undefined || value === null)) {
      issues.push({
        severity: VALIDATION_SEVERITY.ERROR,
        message: `Required field '${key}' is missing`,
        field: key,
        ruleType: VALIDATION_RULE_TYPE.TYPE
      });
      return this._createValidationResult(false, issues);
    }

    // Skip further validation if value is undefined and not required
    if (value === undefined || value === null) {
      return this._createValidationResult(true, []);
    }

    // Type validation
    if (schema.type && typeof value !== schema.type) {
      issues.push({
        severity: VALIDATION_SEVERITY.ERROR,
        message: `Field '${key}' must be of type ${schema.type}, got ${typeof value}`,
        field: key,
        value: value,
        ruleType: VALIDATION_RULE_TYPE.TYPE
      });
    }

    // Range validation for numbers
    if (schema.type === 'number' && typeof value === 'number') {
      if (schema.min !== undefined && value < schema.min) {
        issues.push({
          severity: VALIDATION_SEVERITY.ERROR,
          message: `Field '${key}' must be at least ${schema.min}, got ${value}`,
          field: key,
          value: value,
          ruleType: VALIDATION_RULE_TYPE.RANGE
        });
      }

      if (schema.max !== undefined && value > schema.max) {
        issues.push({
          severity: VALIDATION_SEVERITY.ERROR,
          message: `Field '${key}' must be at most ${schema.max}, got ${value}`,
          field: key,
          value: value,
          ruleType: VALIDATION_RULE_TYPE.RANGE
        });
      }
    }

    // Enumeration validation
    if (schema.enum && !schema.enum.includes(value)) {
      issues.push({
        severity: VALIDATION_SEVERITY.ERROR,
        message: `Field '${key}' must be one of [${schema.enum.join(', ')}], got '${value}'`,
        field: key,
        value: value,
        ruleType: VALIDATION_RULE_TYPE.ENUM
      });
    }

    // Pattern validation for strings
    if (schema.pattern && typeof value === 'string') {
      const regex = new RegExp(schema.pattern);
      if (!regex.test(value)) {
        issues.push({
          severity: VALIDATION_SEVERITY.ERROR,
          message: `Field '${key}' does not match required pattern`,
          field: key,
          value: value,
          ruleType: VALIDATION_RULE_TYPE.PATTERN
        });
      }
    }

    // Custom validation
    if (schema.validation && schema.validation.custom) {
      try {
        const customResult = schema.validation.custom(value, context);
        if (!customResult.valid) {
          issues.push({
            severity: VALIDATION_SEVERITY.ERROR,
            message: customResult.message || `Custom validation failed for field '${key}'`,
            field: key,
            value: value,
            ruleType: VALIDATION_RULE_TYPE.CUSTOM
          });
        }
      } catch (error) {
        issues.push({
          severity: VALIDATION_SEVERITY.ERROR,
          message: `Custom validation error for field '${key}': ${error.message}`,
          field: key,
          value: value,
          ruleType: VALIDATION_RULE_TYPE.CUSTOM
        });
      }
    }

    // Security validation
    if (this.options.enableSecurityValidation && schema.security) {
      const securityIssues = this._validateSecurityConstraints(key, value, schema.security);
      issues.push(...securityIssues);
    }

    // Performance validation
    if (this.options.enablePerformanceValidation && schema.performance) {
      const performanceIssues = this._validatePerformanceConstraints(key, value, schema.performance);
      issues.push(...performanceIssues);
    }

    const hasErrors = issues.some(issue => issue.severity === VALIDATION_SEVERITY.ERROR);
    return this._createValidationResult(!hasErrors, issues);
  }

  /**
   * Validate category configuration
   *
   * @private
   * @param {string} category - Category name
   * @param {Object} categoryConfig - Category configuration
   * @param {Object} fullConfig - Complete configuration for context
   * @returns {Object} Category validation result
   */
  _validateCategory(category, categoryConfig, fullConfig) {
    const schema = this.schemas.get(category);
    if (!schema) {
      return this._createValidationResult(true, [], `No schema for category: ${category}`);
    }

    const result = {
      valid: true,
      errors: [],
      warnings: [],
      info: [],
      fieldResults: {},
      summary: {
        totalFields: 0,
        validFields: 0,
        errorFields: 0,
        warningFields: 0
      }
    };

    // Validate each field in the schema
    for (const [key, fieldSchema] of Object.entries(schema)) {
      const value = categoryConfig[key];
      const fieldResult = this._validateField(key, value, fieldSchema, fullConfig);

      result.fieldResults[key] = fieldResult;
      result.summary.totalFields++;

      if (fieldResult.valid) {
        result.summary.validFields++;
      } else {
        result.valid = false;
        result.summary.errorFields++;
      }

      // Categorize issues
      for (const issue of fieldResult.issues) {
        switch (issue.severity) {
          case VALIDATION_SEVERITY.ERROR:
            result.errors.push(issue);
            break;
          case VALIDATION_SEVERITY.WARNING:
            result.warnings.push(issue);
            result.summary.warningFields++;
            break;
          case VALIDATION_SEVERITY.INFO:
            result.info.push(issue);
            break;
        }
      }
    }

    return result;
  }

  /**
   * Validate cross-field dependencies
   *
   * @private
   * @param {Object} config - Complete configuration
   * @returns {Object} Dependency validation result
   */
  _validateDependencies(config) {
    const issues = [];

    // Check dependencies defined in schemas
    for (const [category, schema] of this.schemas) {
      const categoryConfig = config[category] || {};

      for (const [key, fieldSchema] of Object.entries(schema)) {
        if (fieldSchema.dependencies) {
          for (const depKey of fieldSchema.dependencies) {
            const depValue = this._getConfigValue(config, depKey);

            // Custom dependency validation if defined
            if (fieldSchema.validation && fieldSchema.validation.custom) {
              try {
                const result = fieldSchema.validation.custom(categoryConfig[key], config);
                if (!result.valid) {
                  issues.push({
                    severity: VALIDATION_SEVERITY.ERROR,
                    message: result.message,
                    field: `${category}.${key}`,
                    dependency: depKey,
                    ruleType: VALIDATION_RULE_TYPE.DEPENDENCY
                  });
                }
              } catch (error) {
                issues.push({
                  severity: VALIDATION_SEVERITY.ERROR,
                  message: `Dependency validation error: ${error.message}`,
                  field: `${category}.${key}`,
                  dependency: depKey,
                  ruleType: VALIDATION_RULE_TYPE.DEPENDENCY
                });
              }
            }
          }
        }
      }
    }

    const hasErrors = issues.some(issue => issue.severity === VALIDATION_SEVERITY.ERROR);
    return this._createValidationResult(!hasErrors, issues);
  }

  /**
   * Validate security constraints
   *
   * @private
   * @param {string} key - Field key
   * @param {*} value - Field value
   * @param {Object} securityConstraints - Security constraints
   * @returns {Array} Security validation issues
   */
  _validateSecurityConstraints(key, value, securityConstraints) {
    const issues = [];

    if (securityConstraints.impact === 'critical') {
      // For critical security settings, add warnings about changes
      if (key === 'requireEncryption' && value === false) {
        issues.push({
          severity: VALIDATION_SEVERITY.WARNING,
          message: 'Disabling encryption removes all security protections',
          field: key,
          value: value,
          ruleType: VALIDATION_RULE_TYPE.SECURITY
        });
      }
    }

    return issues;
  }

  /**
   * Validate performance constraints
   *
   * @private
   * @param {string} key - Field key
   * @param {*} value - Field value
   * @param {Object} performanceConstraints - Performance constraints
   * @returns {Array} Performance validation issues
   */
  _validatePerformanceConstraints(key, value, performanceConstraints) {
    const issues = [];

    if (performanceConstraints.impact === 'high' || performanceConstraints.impact === 'critical') {
      // Add warnings for high-impact performance settings
      issues.push({
        severity: VALIDATION_SEVERITY.INFO,
        message: performanceConstraints.description || 'This setting may impact performance',
        field: key,
        value: value,
        ruleType: VALIDATION_RULE_TYPE.PERFORMANCE
      });
    }

    return issues;
  }

  /**
   * Analyze performance impact of configuration
   *
   * @private
   * @param {Object} config - Configuration to analyze
   * @returns {Object} Performance analysis
   */
  _analyzePerformanceImpact(config) {
    const analysis = {
      overallImpact: 'low',
      memoryImpact: 'low',
      cpuImpact: 'low',
      networkImpact: 'low',
      recommendations: []
    };

    // Analyze memory impact
    const maxSessions = config.performance?.maxConcurrentSessions || 10;
    const bufferSize = config.protocol?.maxBufferSize || 100;
    const memoryPool = config.performance?.memoryPoolSize || 1048576;

    const estimatedMemoryUsage = (maxSessions * bufferSize * 1024) + memoryPool;

    if (estimatedMemoryUsage > 50 * 1024 * 1024) { // 50MB
      analysis.memoryImpact = 'high';
      analysis.recommendations.push('Consider reducing maxConcurrentSessions or maxBufferSize to reduce memory usage');
    }

    // Analyze CPU impact
    if (config.logging?.enableDetailedLogging) {
      analysis.cpuImpact = 'medium';
      analysis.recommendations.push('Detailed logging increases CPU usage');
    }

    // Determine overall impact
    const impacts = [analysis.memoryImpact, analysis.cpuImpact, analysis.networkImpact];
    if (impacts.includes('high')) {
      analysis.overallImpact = 'high';
    } else if (impacts.includes('medium')) {
      analysis.overallImpact = 'medium';
    }

    return analysis;
  }

  /**
   * Analyze security impact of configuration
   *
   * @private
   * @param {Object} config - Configuration to analyze
   * @returns {Object} Security analysis
   */
  _analyzeSecurityImpact(config) {
    const analysis = {
      overallSecurity: 'high',
      risks: [],
      recommendations: []
    };

    // Check critical security settings
    if (config.security?.requireEncryption === false) {
      analysis.overallSecurity = 'none';
      analysis.risks.push('Encryption is disabled - no security protection');
      analysis.recommendations.push('Enable encryption for security protection');
    }

    if (!config.security?.allowV3 && !config.security?.allowV2) {
      analysis.risks.push('No OTR versions are enabled');
      analysis.recommendations.push('Enable at least one OTR version');
    }

    if (config.logging?.enableDetailedLogging) {
      analysis.risks.push('Detailed logging may expose sensitive information');
      analysis.recommendations.push('Disable detailed logging in production');
    }

    return analysis;
  }

  /**
   * Identify changes between configurations
   *
   * @private
   * @param {Object} currentConfig - Current configuration
   * @param {Object} newConfig - New configuration
   * @returns {Array} List of changes
   */
  _identifyChanges(currentConfig, newConfig) {
    const changes = [];

    // Simple deep comparison (in production, use a proper diff library)
    const allKeys = new Set([
      ...Object.keys(currentConfig || {}),
      ...Object.keys(newConfig || {})
    ]);

    for (const key of allKeys) {
      const currentValue = currentConfig?.[key];
      const newValue = newConfig?.[key];

      if (JSON.stringify(currentValue) !== JSON.stringify(newValue)) {
        changes.push({
          key: key,
          oldValue: currentValue,
          newValue: newValue,
          type: currentValue === undefined ? 'added' :
                newValue === undefined ? 'removed' : 'modified'
        });
      }
    }

    return changes;
  }

  /**
   * Analyze impact of a specific change
   *
   * @private
   * @param {Object} change - Configuration change
   * @param {Object} currentConfig - Current configuration
   * @param {Object} newConfig - New configuration
   * @returns {Object} Change impact analysis
   */
  _analyzeChangeImpact(change, currentConfig, newConfig) {
    const impact = {
      security: [],
      performance: [],
      compatibility: [],
      recommendations: [],
      blocksChange: false
    };

    // Analyze security impact
    if (change.key.startsWith('security.')) {
      if (change.key === 'security.requireEncryption' && change.newValue === false) {
        impact.security.push('Disabling encryption removes all security protections');
        impact.recommendations.push('Consider the security implications before disabling encryption');
      }
    }

    return impact;
  }

  /**
   * Get configuration value by path
   *
   * @private
   * @param {Object} config - Configuration object
   * @param {string} path - Configuration path (e.g., 'category.key')
   * @returns {*} Configuration value
   */
  _getConfigValue(config, path) {
    const parts = path.split('.');
    let value = config;

    for (const part of parts) {
      if (value && typeof value === 'object') {
        value = value[part];
      } else {
        return undefined;
      }
    }

    return value;
  }

  /**
   * Create validation result object
   *
   * @private
   * @param {boolean} valid - Validation success
   * @param {Array} issues - Validation issues
   * @param {string} message - Optional message
   * @returns {Object} Validation result
   */
  _createValidationResult(valid, issues = [], message = null) {
    return {
      valid: valid,
      issues: issues,
      message: message,
      timestamp: Date.now()
    };
  }

  /**
   * Check if cache entry is valid
   *
   * @private
   * @param {string} key - Cache key
   * @returns {boolean} True if cache is valid
   */
  _isCacheValid(key) {
    const entry = this.validationCache.get(key);
    return entry && (Date.now() - entry.timestamp) < this.cacheTimeout;
  }

  /**
   * Clear validation cache for category
   *
   * @private
   * @param {string} category - Category to clear
   */
  _clearValidationCache(category) {
    for (const [key] of this.validationCache) {
      if (key.startsWith(`${category}.`)) {
        this.validationCache.delete(key);
      }
    }
  }
}
