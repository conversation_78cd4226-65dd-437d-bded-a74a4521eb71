/**
 * Protocol Inspector - Interactive Protocol State Inspection
 * 
 * Provides interactive inspection capabilities for WebOTR protocol with:
 * - Real-time state visualization
 * - Interactive debugging interface
 * - Protocol flow analysis
 * - State manipulation for testing
 * - Visual protocol diagrams
 * 
 * Complements the Protocol Debugger with interactive capabilities.
 */

import { ProtocolDebugger, DEBUG_LEVEL, PROTOCOL_EVENT } from './protocol-debugger.js';
import { CryptoValidation } from '../security/validation.js';

/**
 * Inspector view types
 */
export const INSPECTOR_VIEW = {
  OVERVIEW: 'overview',
  MESSAGES: 'messages',
  STATES: 'states',
  PERFORMANCE: 'performance',
  ERRORS: 'errors',
  FLOW: 'flow',
  NETWORK: 'network'
};

/**
 * State visualization modes
 */
export const VISUALIZATION_MODE = {
  TABLE: 'table',
  TREE: 'tree',
  GRAPH: 'graph',
  TIMELINE: 'timeline',
  FLOW_CHART: 'flow_chart'
};

/**
 * Protocol Inspector Implementation
 */
export class ProtocolInspector {
  /**
   * Create protocol inspector
   *
   * @param {ProtocolDebugger} protocolDebugger - Protocol debugger instance
   * @param {Object} options - Inspector options
   */
  constructor(protocolDebugger, options = {}) {
    this.debugger = protocolDebugger;
    this.options = {
      enableInteractiveMode: true,
      enableStateManipulation: false, // Dangerous - only for testing
      enableVisualization: true,
      updateInterval: 1000, // 1 second
      maxDisplayEntries: 1000,
      defaultView: INSPECTOR_VIEW.OVERVIEW,
      visualizationMode: VISUALIZATION_MODE.TABLE,
      ...options
    };
    
    // Inspector state
    this.currentView = this.options.defaultView;
    this.selectedSession = null;
    this.filters = {};
    this.isRunning = false;
    
    // UI elements (if in browser environment)
    this.container = null;
    this.views = new Map();
    
    // Data caches for performance
    this.cachedData = new Map();
    this.lastUpdate = 0;
    
    // Event listeners
    this.updateListeners = new Set();
    
    // Initialize inspector
    this._initializeInspector();
  }

  /**
   * Start inspector
   * 
   * @param {HTMLElement} container - Container element for UI
   */
  start(container = null) {
    this.isRunning = true;
    this.container = container;
    
    if (container && this.options.enableVisualization) {
      this._createUI();
    }
    
    // Start update loop
    this._startUpdateLoop();
    
    // Listen to debugger events
    this._setupDebuggerListeners();
  }

  /**
   * Stop inspector
   */
  stop() {
    this.isRunning = false;
    this._stopUpdateLoop();
    this._cleanupDebuggerListeners();
    
    if (this.container) {
      this.container.innerHTML = '';
    }
  }

  /**
   * Set current view
   * 
   * @param {string} view - View type
   */
  setView(view) {
    if (Object.values(INSPECTOR_VIEW).includes(view)) {
      this.currentView = view;
      this._updateView();
    }
  }

  /**
   * Set selected session
   * 
   * @param {string} sessionId - Session ID
   */
  setSelectedSession(sessionId) {
    this.selectedSession = sessionId;
    this._updateView();
  }

  /**
   * Set filters
   * 
   * @param {Object} filters - Filter options
   */
  setFilters(filters) {
    this.filters = { ...this.filters, ...filters };
    this._updateView();
  }

  /**
   * Get current inspector data
   * 
   * @returns {Object} Inspector data
   */
  getInspectorData() {
    const data = {
      overview: this._getOverviewData(),
      sessions: this._getSessionsData(),
      messages: this._getMessagesData(),
      states: this._getStatesData(),
      performance: this._getPerformanceData(),
      errors: this._getErrorsData(),
      flow: this._getFlowData()
    };
    
    return data;
  }

  /**
   * Export inspector report
   * 
   * @param {Object} options - Export options
   * @returns {Object} Inspector report
   */
  exportReport(options = {}) {
    const {
      includeVisualization = true,
      includeRawData = false,
      format = 'json'
    } = options;
    
    const report = {
      timestamp: Date.now(),
      inspector: {
        currentView: this.currentView,
        selectedSession: this.selectedSession,
        filters: this.filters,
        options: this.options
      },
      data: this.getInspectorData(),
      debuggerStats: this.debugger.getStats()
    };
    
    if (includeRawData) {
      report.rawData = this.debugger.exportDebugData();
    }
    
    if (includeVisualization && this.container) {
      report.visualization = this._captureVisualization();
    }
    
    return report;
  }

  /**
   * Manipulate session state (testing only)
   * 
   * @param {string} sessionId - Session ID
   * @param {string} component - Component name
   * @param {string} newState - New state
   * @param {Object} context - State context
   */
  manipulateState(sessionId, component, newState, context = {}) {
    if (!this.options.enableStateManipulation) {
      throw new Error('State manipulation is disabled');
    }
    
    // Log the manipulation
    this.debugger.logStateChange(
      sessionId, 
      component, 
      'UNKNOWN', 
      newState, 
      { ...context, manipulated: true, inspector: true }
    );
    
    // Emit manipulation event
    this._emitUpdate('state_manipulated', {
      sessionId,
      component,
      newState,
      context
    });
  }

  /**
   * Inject test message (testing only)
   * 
   * @param {string} sessionId - Session ID
   * @param {Object} message - Test message
   * @param {string} direction - 'sent' or 'received'
   */
  injectMessage(sessionId, message, direction = 'received') {
    if (!this.options.enableStateManipulation) {
      throw new Error('Message injection is disabled');
    }
    
    // Log the injected message
    this.debugger.logMessage(
      direction,
      { ...message, injected: true, inspector: true },
      sessionId,
      { injected: true, timestamp: Date.now() }
    );
    
    // Emit injection event
    this._emitUpdate('message_injected', {
      sessionId,
      message,
      direction
    });
  }

  /**
   * Add update listener
   * 
   * @param {Function} callback - Update callback
   * @returns {Function} Unsubscribe function
   */
  addUpdateListener(callback) {
    this.updateListeners.add(callback);
    return () => this.updateListeners.delete(callback);
  }

  /**
   * Get visualization data for current view
   * 
   * @returns {Object} Visualization data
   */
  getVisualizationData() {
    switch (this.currentView) {
      case INSPECTOR_VIEW.OVERVIEW:
        return this._getOverviewVisualization();
      
      case INSPECTOR_VIEW.MESSAGES:
        return this._getMessagesVisualization();
      
      case INSPECTOR_VIEW.STATES:
        return this._getStatesVisualization();
      
      case INSPECTOR_VIEW.PERFORMANCE:
        return this._getPerformanceVisualization();
      
      case INSPECTOR_VIEW.ERRORS:
        return this._getErrorsVisualization();
      
      case INSPECTOR_VIEW.FLOW:
        return this._getFlowVisualization();
      
      default:
        return {};
    }
  }

  /**
   * Initialize inspector
   * 
   * @private
   */
  _initializeInspector() {
    // Set up data caching
    this.cachedData.set('overview', null);
    this.cachedData.set('sessions', null);
    this.cachedData.set('messages', null);
    this.cachedData.set('states', null);
    this.cachedData.set('performance', null);
    this.cachedData.set('errors', null);
  }

  /**
   * Create UI elements
   * 
   * @private
   */
  _createUI() {
    if (!this.container) return;
    
    this.container.innerHTML = `
      <div class="protocol-inspector">
        <div class="inspector-header">
          <h2>WebOTR Protocol Inspector</h2>
          <div class="inspector-controls">
            <select class="view-selector">
              <option value="${INSPECTOR_VIEW.OVERVIEW}">Overview</option>
              <option value="${INSPECTOR_VIEW.MESSAGES}">Messages</option>
              <option value="${INSPECTOR_VIEW.STATES}">States</option>
              <option value="${INSPECTOR_VIEW.PERFORMANCE}">Performance</option>
              <option value="${INSPECTOR_VIEW.ERRORS}">Errors</option>
              <option value="${INSPECTOR_VIEW.FLOW}">Flow</option>
            </select>
            <select class="session-selector">
              <option value="">All Sessions</option>
            </select>
            <button class="refresh-btn">Refresh</button>
            <button class="export-btn">Export</button>
          </div>
        </div>
        <div class="inspector-content">
          <div class="inspector-view" id="view-content"></div>
        </div>
      </div>
    `;
    
    // Add event listeners
    this._setupUIEventListeners();
    
    // Initial update
    this._updateView();
  }

  /**
   * Setup UI event listeners
   * 
   * @private
   */
  _setupUIEventListeners() {
    if (!this.container) return;
    
    const viewSelector = this.container.querySelector('.view-selector');
    const sessionSelector = this.container.querySelector('.session-selector');
    const refreshBtn = this.container.querySelector('.refresh-btn');
    const exportBtn = this.container.querySelector('.export-btn');
    
    if (viewSelector) {
      viewSelector.addEventListener('change', (e) => {
        this.setView(e.target.value);
      });
    }
    
    if (sessionSelector) {
      sessionSelector.addEventListener('change', (e) => {
        this.setSelectedSession(e.target.value || null);
      });
    }
    
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => {
        this._clearCache();
        this._updateView();
      });
    }
    
    if (exportBtn) {
      exportBtn.addEventListener('click', () => {
        const report = this.exportReport();
        this._downloadReport(report);
      });
    }
  }

  /**
   * Setup debugger event listeners
   * 
   * @private
   */
  _setupDebuggerListeners() {
    // Listen for all debugger events
    this.debuggerListenerId = this.debugger.addEventListener('*', (entry) => {
      this._handleDebuggerEvent(entry);
    });
  }

  /**
   * Cleanup debugger event listeners
   * 
   * @private
   */
  _cleanupDebuggerListeners() {
    if (this.debuggerListenerId) {
      this.debugger.removeEventListener('*', this.debuggerListenerId);
    }
  }

  /**
   * Handle debugger events
   * 
   * @private
   * @param {Object} entry - Debug entry
   */
  _handleDebuggerEvent(entry) {
    // Clear relevant caches
    this._clearCacheForEvent(entry.type);
    
    // Update UI if running
    if (this.isRunning && this.container) {
      this._scheduleUpdate();
    }
    
    // Notify listeners
    this._emitUpdate('debugger_event', entry);
  }

  /**
   * Start update loop
   * 
   * @private
   */
  _startUpdateLoop() {
    this.updateInterval = setInterval(() => {
      if (this.isRunning) {
        this._updateView();
      }
    }, this.options.updateInterval);
  }

  /**
   * Stop update loop
   * 
   * @private
   */
  _stopUpdateLoop() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }

  /**
   * Schedule UI update
   * 
   * @private
   */
  _scheduleUpdate() {
    if (this.scheduledUpdate) return;
    
    this.scheduledUpdate = setTimeout(() => {
      this._updateView();
      this.scheduledUpdate = null;
    }, 100); // Debounce updates
  }

  /**
   * Update current view
   * 
   * @private
   */
  _updateView() {
    if (!this.container) return;
    
    const viewContent = this.container.querySelector('#view-content');
    if (!viewContent) return;
    
    try {
      const visualizationData = this.getVisualizationData();
      const html = this._renderVisualization(visualizationData);
      viewContent.innerHTML = html;
      
      // Update session selector
      this._updateSessionSelector();
      
      this.lastUpdate = Date.now();
    } catch (error) {
      console.error('Error updating inspector view:', error);
      viewContent.innerHTML = `<div class="error">Error updating view: ${error.message}</div>`;
    }
  }

  /**
   * Get overview data
   *
   * @private
   * @returns {Object} Overview data
   */
  _getOverviewData() {
    const cached = this._getCachedData('overview');
    if (cached) return cached;

    const stats = this.debugger.getStats();
    const sessions = Array.from(this.debugger.activeSessions.values());
    const recentErrors = this.debugger.errorLog.slice(-5);

    const data = {
      stats: stats,
      activeSessions: sessions.length,
      totalSessions: stats.sessionsTracked,
      recentErrors: recentErrors,
      systemHealth: this._calculateSystemHealth(stats),
      uptime: Date.now() - (this.debugger.startTime || Date.now())
    };

    this._setCachedData('overview', data);
    return data;
  }

  /**
   * Get sessions data
   *
   * @private
   * @returns {Object} Sessions data
   */
  _getSessionsData() {
    const cached = this._getCachedData('sessions');
    if (cached) return cached;

    const activeSessions = Array.from(this.debugger.activeSessions.values());
    const sessionStates = Object.fromEntries(this.debugger.sessionStates);

    const data = {
      active: activeSessions,
      states: sessionStates,
      count: activeSessions.length
    };

    this._setCachedData('sessions', data);
    return data;
  }

  /**
   * Get messages data
   *
   * @private
   * @returns {Object} Messages data
   */
  _getMessagesData() {
    const cached = this._getCachedData('messages');
    if (cached) return cached;

    const filters = {
      ...this.filters,
      sessionId: this.selectedSession,
      limit: this.options.maxDisplayEntries
    };

    // Get message events from trace entries
    const allEvents = this.debugger.getTraceEntries ? this.debugger.getTraceEntries(filters) : [];
    const messageEvents = allEvents.filter(event =>
      event.type === 'MESSAGE_SENT' || event.type === 'MESSAGE_RECEIVED'
    );

    const data = {
      messages: messageEvents,
      totalSent: this.debugger.stats?.messagesSent || 0,
      totalReceived: this.debugger.stats?.messagesReceived || 0,
      recentActivity: this._analyzeRecentActivity(messageEvents)
    };

    this._setCachedData('messages', data);
    return data;
  }

  /**
   * Get states data
   *
   * @private
   * @returns {Object} States data
   */
  _getStatesData() {
    const cached = this._getCachedData('states');
    if (cached) return cached;

    let stateHistory = this.debugger.stateHistory || [];

    if (this.selectedSession) {
      stateHistory = stateHistory.filter(state => state.sessionId === this.selectedSession);
    }

    const data = {
      history: stateHistory.slice(-this.options.maxDisplayEntries),
      currentStates: Object.fromEntries(this.debugger.sessionStates || new Map()),
      stateTransitions: this._analyzeStateTransitions(stateHistory)
    };

    this._setCachedData('states', data);
    return data;
  }

  /**
   * Get performance data
   *
   * @private
   * @returns {Object} Performance data
   */
  _getPerformanceData() {
    const cached = this._getCachedData('performance');
    if (cached) return cached;

    const summary = this.debugger.getPerformanceSummary ?
      this.debugger.getPerformanceSummary(this.selectedSession) : {};

    const allEvents = this.debugger.getTraceEntries ?
      this.debugger.getTraceEntries({
        sessionId: this.selectedSession,
        limit: this.options.maxDisplayEntries
      }) : [];

    const recentPerformanceEvents = allEvents.filter(event =>
      event.type === 'PERFORMANCE'
    );

    const data = {
      summary: summary,
      recentEvents: recentPerformanceEvents,
      trends: this._analyzePerformanceTrends(recentPerformanceEvents),
      bottlenecks: this._identifyBottlenecks(summary)
    };

    this._setCachedData('performance', data);
    return data;
  }

  /**
   * Get errors data
   *
   * @private
   * @returns {Object} Errors data
   */
  _getErrorsData() {
    const cached = this._getCachedData('errors');
    if (cached) return cached;

    let errors = this.debugger.errorLog;

    if (this.selectedSession) {
      errors = errors.filter(error => error.sessionId === this.selectedSession);
    }

    const data = {
      errors: errors.slice(-this.options.maxDisplayEntries),
      errorsByType: this._groupErrorsByType(errors),
      errorTrends: this._analyzeErrorTrends(errors),
      criticalErrors: errors.filter(error => error.level >= DEBUG_LEVEL.ERROR)
    };

    this._setCachedData('errors', data);
    return data;
  }

  /**
   * Get flow data
   *
   * @private
   * @returns {Object} Flow data
   */
  _getFlowData() {
    const cached = this._getCachedData('flow');
    if (cached) return cached;

    const filters = {
      sessionId: this.selectedSession,
      limit: this.options.maxDisplayEntries
    };

    const allEvents = this.debugger.getTraceEntries(filters);
    const flowSequence = this._buildFlowSequence(allEvents);

    const data = {
      sequence: flowSequence,
      flowDiagram: this._generateFlowDiagram(flowSequence),
      criticalPath: this._identifyCriticalPath(flowSequence)
    };

    this._setCachedData('flow', data);
    return data;
  }

  /**
   * Get overview visualization
   *
   * @private
   * @returns {Object} Overview visualization data
   */
  _getOverviewVisualization() {
    const data = this._getOverviewData();

    return {
      type: 'overview',
      title: 'Protocol Overview',
      sections: [
        {
          title: 'System Statistics',
          type: 'stats',
          data: data.stats
        },
        {
          title: 'Active Sessions',
          type: 'sessions',
          data: { count: data.activeSessions, total: data.totalSessions }
        },
        {
          title: 'System Health',
          type: 'health',
          data: data.systemHealth
        },
        {
          title: 'Recent Errors',
          type: 'errors',
          data: data.recentErrors
        }
      ]
    };
  }

  /**
   * Get messages visualization
   *
   * @private
   * @returns {Object} Messages visualization data
   */
  _getMessagesVisualization() {
    const data = this._getMessagesData();

    return {
      type: 'messages',
      title: 'Protocol Messages',
      data: data.messages,
      summary: {
        sent: data.totalSent,
        received: data.totalReceived,
        activity: data.recentActivity
      }
    };
  }

  /**
   * Render visualization
   *
   * @private
   * @param {Object} visualizationData - Visualization data
   * @returns {string} HTML representation
   */
  _renderVisualization(visualizationData) {
    switch (visualizationData.type) {
      case 'overview':
        return this._renderOverview(visualizationData);

      case 'messages':
        return this._renderMessages(visualizationData);

      case 'states':
        return this._renderStates(visualizationData);

      case 'performance':
        return this._renderPerformance(visualizationData);

      case 'errors':
        return this._renderErrors(visualizationData);

      case 'flow':
        return this._renderFlow(visualizationData);

      default:
        return '<div class="error">Unknown visualization type</div>';
    }
  }

  /**
   * Render overview
   *
   * @private
   * @param {Object} data - Overview data
   * @returns {string} HTML
   */
  _renderOverview(data) {
    let html = `<div class="overview-view">`;

    for (const section of data.sections) {
      html += `
        <div class="overview-section">
          <h3>${section.title}</h3>
          <div class="section-content">
      `;

      switch (section.type) {
        case 'stats':
          html += this._renderStatsTable(section.data);
          break;

        case 'sessions':
          html += `
            <div class="session-summary">
              <span class="metric">Active: ${section.data.count}</span>
              <span class="metric">Total: ${section.data.total}</span>
            </div>
          `;
          break;

        case 'health':
          html += this._renderHealthIndicator(section.data);
          break;

        case 'errors':
          html += this._renderErrorList(section.data);
          break;
      }

      html += `
          </div>
        </div>
      `;
    }

    html += `</div>`;
    return html;
  }

  /**
   * Render stats table
   *
   * @private
   * @param {Object} stats - Statistics data
   * @returns {string} HTML
   */
  _renderStatsTable(stats) {
    return `
      <table class="stats-table">
        <tr><td>Total Events</td><td>${stats.totalEvents}</td></tr>
        <tr><td>Messages Sent</td><td>${stats.messagesSent}</td></tr>
        <tr><td>Messages Received</td><td>${stats.messagesReceived}</td></tr>
        <tr><td>State Changes</td><td>${stats.stateChanges}</td></tr>
        <tr><td>Errors Logged</td><td>${stats.errorsLogged}</td></tr>
        <tr><td>Performance Events</td><td>${stats.performanceEvents}</td></tr>
      </table>
    `;
  }

  /**
   * Calculate system health
   *
   * @private
   * @param {Object} stats - System statistics
   * @returns {Object} Health data
   */
  _calculateSystemHealth(stats) {
    const errorRate = stats.totalEvents > 0 ? stats.errorsLogged / stats.totalEvents : 0;
    const health = errorRate < 0.01 ? 'excellent' :
                   errorRate < 0.05 ? 'good' :
                   errorRate < 0.1 ? 'fair' : 'poor';

    return {
      status: health,
      errorRate: errorRate,
      score: Math.max(0, 100 - (errorRate * 1000))
    };
  }

  /**
   * Analyze recent activity
   *
   * @private
   * @param {Array} events - Recent events
   * @returns {Object} Activity analysis
   */
  _analyzeRecentActivity(events) {
    const now = Date.now();
    const recentEvents = events.filter(e => now - e.timestamp < 60000); // Last minute

    return {
      recentCount: recentEvents.length,
      rate: recentEvents.length / 60, // Events per second
      trend: this._calculateTrend(events)
    };
  }

  /**
   * Calculate trend
   *
   * @private
   * @param {Array} events - Events array
   * @returns {string} Trend direction
   */
  _calculateTrend(events) {
    if (events.length < 10) return 'stable';

    const recent = events.slice(-5);
    const previous = events.slice(-10, -5);

    const recentAvg = recent.length / 5;
    const previousAvg = previous.length / 5;

    if (recentAvg > previousAvg * 1.2) return 'increasing';
    if (recentAvg < previousAvg * 0.8) return 'decreasing';
    return 'stable';
  }

  /**
   * Cache management methods
   *
   * @private
   */
  _getCachedData(key) {
    const cached = this.cachedData.get(key);
    if (cached && Date.now() - cached.timestamp < 5000) { // 5 second cache
      return cached.data;
    }
    return null;
  }

  _setCachedData(key, data) {
    this.cachedData.set(key, {
      data: data,
      timestamp: Date.now()
    });
  }

  _clearCache() {
    this.cachedData.clear();
  }

  _clearCacheForEvent(eventType) {
    // Clear relevant caches based on event type
    switch (eventType) {
      case PROTOCOL_EVENT.MESSAGE_SENT:
      case PROTOCOL_EVENT.MESSAGE_RECEIVED:
        this.cachedData.delete('messages');
        this.cachedData.delete('overview');
        break;

      case PROTOCOL_EVENT.STATE_CHANGE:
        this.cachedData.delete('states');
        this.cachedData.delete('overview');
        break;

      case PROTOCOL_EVENT.PERFORMANCE:
        this.cachedData.delete('performance');
        break;

      case PROTOCOL_EVENT.ERROR:
        this.cachedData.delete('errors');
        this.cachedData.delete('overview');
        break;
    }
  }

  /**
   * Emit update event
   *
   * @private
   * @param {string} type - Update type
   * @param {Object} data - Update data
   */
  _emitUpdate(type, data) {
    for (const listener of this.updateListeners) {
      try {
        listener({ type, data, timestamp: Date.now() });
      } catch (error) {
        console.error('Error in inspector update listener:', error);
      }
    }
  }

  /**
   * Download report
   *
   * @private
   * @param {Object} report - Report data
   */
  _downloadReport(report) {
    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `webottr-inspector-report-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  /**
   * Analyze recent activity
   *
   * @private
   * @param {Array} messageEvents - Message events
   * @returns {Object} Recent activity analysis
   */
  _analyzeRecentActivity(messageEvents) {
    const now = Date.now();
    const recentEvents = messageEvents.filter(event =>
      now - event.timestamp < 300000 // Last 5 minutes
    );

    const sentCount = recentEvents.filter(event => event.type === 'MESSAGE_SENT').length;
    const receivedCount = recentEvents.filter(event => event.type === 'MESSAGE_RECEIVED').length;

    return {
      recentCount: recentEvents.length,
      sentCount: sentCount,
      receivedCount: receivedCount,
      rate: recentEvents.length / 5, // Messages per minute
      trend: recentEvents.length > messageEvents.length * 0.3 ? 'active' : 'quiet'
    };
  }

  /**
   * Analyze state transitions
   *
   * @private
   * @param {Array} stateHistory - State history
   * @returns {Object} State transition analysis
   */
  _analyzeStateTransitions(stateHistory) {
    const transitions = {};
    const components = new Set();

    for (const state of stateHistory) {
      components.add(state.component);
      const key = `${state.component}:${state.oldState}->${state.newState}`;
      transitions[key] = (transitions[key] || 0) + 1;
    }

    return {
      totalTransitions: stateHistory.length,
      uniqueTransitions: Object.keys(transitions).length,
      components: Array.from(components),
      transitionCounts: transitions
    };
  }

  /**
   * Analyze performance trends
   *
   * @private
   * @param {Array} events - Performance events
   * @returns {Object} Performance trends
   */
  _analyzePerformanceTrends(events) {
    if (events.length === 0) return { trend: 'stable', variance: 0 };

    const durations = events.map(e => e.data.duration);
    const avg = durations.reduce((sum, d) => sum + d, 0) / durations.length;
    const variance = durations.reduce((sum, d) => sum + Math.pow(d - avg, 2), 0) / durations.length;

    return {
      trend: variance > avg * 0.5 ? 'variable' : 'stable',
      variance: variance,
      average: avg
    };
  }

  /**
   * Identify bottlenecks
   *
   * @private
   * @param {Object} summary - Performance summary
   * @returns {Array} Bottlenecks
   */
  _identifyBottlenecks(summary) {
    const bottlenecks = [];

    for (const [operation, metrics] of Object.entries(summary.operations || {})) {
      if (metrics.avgDuration > 1000) { // > 1 second
        bottlenecks.push({
          operation: operation,
          avgDuration: metrics.avgDuration,
          severity: metrics.avgDuration > 5000 ? 'critical' : 'warning'
        });
      }
    }

    return bottlenecks;
  }

  /**
   * Group errors by type
   *
   * @private
   * @param {Array} errors - Error list
   * @returns {Object} Grouped errors
   */
  _groupErrorsByType(errors) {
    const groups = {};

    for (const error of errors) {
      const type = error.name || 'Unknown';
      if (!groups[type]) {
        groups[type] = [];
      }
      groups[type].push(error);
    }

    return groups;
  }

  /**
   * Analyze error trends
   *
   * @private
   * @param {Array} errors - Error list
   * @returns {Object} Error trends
   */
  _analyzeErrorTrends(errors) {
    const now = Date.now();
    const recentErrors = errors.filter(e => now - e.timestamp < 3600000); // Last hour

    return {
      recentCount: recentErrors.length,
      totalCount: errors.length,
      rate: recentErrors.length / 60, // Errors per minute
      trend: recentErrors.length > errors.length * 0.5 ? 'increasing' : 'stable'
    };
  }

  /**
   * Build flow sequence
   *
   * @private
   * @param {Array} events - All events
   * @returns {Array} Flow sequence
   */
  _buildFlowSequence(events) {
    return events.map((event, index) => ({
      index: index,
      type: event.type,
      timestamp: event.timestamp,
      data: event.data
    }));
  }

  /**
   * Generate flow diagram
   *
   * @private
   * @param {Array} sequence - Flow sequence
   * @returns {string} Flow diagram
   */
  _generateFlowDiagram(sequence) {
    let diagram = 'Flow Diagram:\n';

    for (const step of sequence) {
      diagram += `${step.index + 1}. ${step.type} (${new Date(step.timestamp).toISOString()})\n`;
    }

    return diagram;
  }

  /**
   * Identify critical path
   *
   * @private
   * @param {Array} sequence - Flow sequence
   * @returns {Array} Critical path
   */
  _identifyCriticalPath(sequence) {
    // Simple implementation - return all events as critical path
    return sequence.filter(step =>
      step.type === 'MESSAGE_SENT' ||
      step.type === 'MESSAGE_RECEIVED' ||
      step.type === 'STATE_CHANGE'
    );
  }

  /**
   * Render health indicator
   *
   * @private
   * @param {Object} health - Health data
   * @returns {string} HTML
   */
  _renderHealthIndicator(health) {
    const statusClass = health.status === 'excellent' ? 'success' :
                       health.status === 'good' ? 'warning' : 'error';

    return `
      <div class="health-indicator ${statusClass}">
        <span class="status">${health.status.toUpperCase()}</span>
        <span class="score">Score: ${Math.round(health.score)}</span>
        <span class="error-rate">Error Rate: ${(health.errorRate * 100).toFixed(2)}%</span>
      </div>
    `;
  }

  /**
   * Render error list
   *
   * @private
   * @param {Array} errors - Error list
   * @returns {string} HTML
   */
  _renderErrorList(errors) {
    if (errors.length === 0) {
      return '<div class="no-errors">No recent errors</div>';
    }

    let html = '<div class="error-list">';

    for (const error of errors.slice(0, 5)) {
      html += `
        <div class="error-item">
          <span class="error-name">${error.name}</span>
          <span class="error-message">${error.message}</span>
          <span class="error-time">${new Date(error.timestamp).toLocaleTimeString()}</span>
        </div>
      `;
    }

    html += '</div>';
    return html;
  }

  /**
   * Update session selector
   *
   * @private
   */
  _updateSessionSelector() {
    if (!this.container) return;

    const sessionSelector = this.container.querySelector('.session-selector');
    if (!sessionSelector) return;

    // Clear existing options except "All Sessions"
    const options = sessionSelector.querySelectorAll('option:not([value=""])');
    options.forEach(option => option.remove());

    // Add active sessions
    for (const session of this.debugger.activeSessions.values()) {
      const option = document.createElement('option');
      option.value = session.id;
      option.textContent = `Session: ${session.id}`;
      sessionSelector.appendChild(option);
    }

    // Set selected session
    if (this.selectedSession) {
      sessionSelector.value = this.selectedSession;
    }
  }

  /**
   * Capture visualization
   *
   * @private
   * @returns {string} Visualization data
   */
  _captureVisualization() {
    if (!this.container) return '';

    return this.container.innerHTML;
  }

  /**
   * Cleanup resources
   */
  destroy() {
    this.stop();
    this.updateListeners.clear();
    this.cachedData.clear();
  }
}
