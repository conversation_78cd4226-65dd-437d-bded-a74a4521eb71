/**
 * Authenticated Key Exchange (AKE) implementation for WebOTR
 * 
 * This module implements the AKE protocol for OTR, which allows two parties
 * to establish a secure communication channel with authentication.
 */
import { random } from '../crypto/random';
import { encrypt, decrypt } from '../crypto/aes';
import { hmacSha256 } from '../crypto/hmac';
import { sign, verify } from '../crypto/dsa';
import { generateDHKeyPair, dhExchange } from '../crypto/dh';
import { deriveKeys } from '../crypto/keys';
import { MESSAGE_TYPE, PROTOCOL_VERSION } from './state';
import { ProtocolErrorRecovery, ERROR_TYPES, globalErrorRecovery } from '../security/error-recovery';

/**
 * Create a DH commit message
 * @param {Object} dhKeyPair - DH key pair
 * @param {number} protocolVersion - OTR protocol version
 * @param {number} instanceTag - Sender's instance tag
 * @param {number} receiverInstanceTag - Receiver's instance tag (0 if unknown)
 * @returns {Promise<Object>} DH commit message data
 */
export async function createDHCommit(dhKeyPair, protocolVersion, instanceTag, receiverInstanceTag = 0) {
  // Generate a random key for encrypting our DH public key
  const aesKey = random(16); // 128-bit AES key
  
  // Create initialization vector
  const iv = random(16);
  
  // Convert public key to bytes
  const publicKeyBytes = dhKeyPair.publicKey;
  
  // Encrypt our DH public key
  const encryptedPublicKey = await encrypt(publicKeyBytes, aesKey, iv);
  
  // Hash the public key
  const publicKeyHash = await hmacSha256(publicKeyBytes, aesKey);
  
  return {
    protocolVersion,
    messageType: MESSAGE_TYPE.DH_COMMIT,
    senderInstanceTag: instanceTag,
    receiverInstanceTag,
    encryptedPublicKey,
    publicKeyHash,
    aesKey,
    iv
  };
}

/**
 * Create a DH key message
 * @param {Object} dhKeyPair - DH key pair
 * @param {number} protocolVersion - OTR protocol version
 * @param {number} instanceTag - Sender's instance tag
 * @param {number} receiverInstanceTag - Receiver's instance tag
 * @returns {Object} DH key message data
 */
export function createDHKey(dhKeyPair, protocolVersion, instanceTag, receiverInstanceTag) {
  return {
    protocolVersion,
    messageType: MESSAGE_TYPE.DH_KEY,
    senderInstanceTag: instanceTag,
    receiverInstanceTag,
    publicKey: dhKeyPair.publicKey
  };
}

/**
 * Create a reveal signature message
 * @param {Object} params - Parameters for the reveal signature message
 * @param {Uint8Array} params.aesKey - AES key used to encrypt the DH public key
 * @param {Uint8Array} params.iv - Initialization vector
 * @param {Object} params.dhKeyPair - Our DH key pair
 * @param {Object} params.dsaKeyPair - Our DSA key pair
 * @param {Uint8Array} params.theirPublicKey - Their DH public key
 * @param {number} params.protocolVersion - OTR protocol version
 * @param {number} params.instanceTag - Sender's instance tag
 * @param {number} params.receiverInstanceTag - Receiver's instance tag
 * @returns {Promise<Object>} Reveal signature message data
 */
export async function createRevealSignature(params) {
  const { 
    aesKey, 
    iv, 
    dhKeyPair, 
    dsaKeyPair, 
    theirPublicKey, 
    protocolVersion, 
    instanceTag, 
    receiverInstanceTag 
  } = params;
  
  // Compute the shared secret
  const sharedSecret = await dhExchange(dhKeyPair.privateKey, theirPublicKey);
  
  // Derive keys from the shared secret
  const keys = await deriveKeys(sharedSecret);
  
  // Create the signature data (our public keys and instance tag)
  const signatureData = new Uint8Array([
    ...dhKeyPair.publicKey,
    ...dsaKeyPair.publicKey,
    ...new Uint8Array([(instanceTag >> 24) & 0xFF, (instanceTag >> 16) & 0xFF, (instanceTag >> 8) & 0xFF, instanceTag & 0xFF])
  ]);
  
  // Sign the data with our DSA key
  const signature = await sign(signatureData, dsaKeyPair.privateKey);
  
  // Encrypt the signature with the AES key derived from the shared secret
  const encryptedSignature = await encrypt(signature, keys.sendingAESKey, iv);
  
  // Calculate MAC for the encrypted signature
  const mac = await hmacSha256(encryptedSignature, keys.sendingMACKey);
  
  return {
    protocolVersion,
    messageType: MESSAGE_TYPE.REVEAL_SIGNATURE,
    senderInstanceTag: instanceTag,
    receiverInstanceTag,
    revealedKey: aesKey,
    encryptedSignature,
    mac,
    keys
  };
}

/**
 * Create a signature message
 * @param {Object} params - Parameters for the signature message
 * @param {Object} params.dhKeyPair - Our DH key pair
 * @param {Object} params.dsaKeyPair - Our DSA key pair
 * @param {Uint8Array} params.theirPublicKey - Their DH public key
 * @param {Uint8Array} params.theirDsaPublicKey - Their DSA public key
 * @param {number} params.protocolVersion - OTR protocol version
 * @param {number} params.instanceTag - Sender's instance tag
 * @param {number} params.receiverInstanceTag - Receiver's instance tag
 * @returns {Promise<Object>} Signature message data
 */
export async function createSignature(params) {
  const { 
    dhKeyPair, 
    dsaKeyPair, 
    theirPublicKey, 
    theirDsaPublicKey, 
    protocolVersion, 
    instanceTag, 
    receiverInstanceTag 
  } = params;
  
  // Compute the shared secret
  const sharedSecret = await dhExchange(dhKeyPair.privateKey, theirPublicKey);
  
  // Derive keys from the shared secret
  const keys = await deriveKeys(sharedSecret);
  
  // Create the signature data (our public keys and instance tag)
  const signatureData = new Uint8Array([
    ...dhKeyPair.publicKey,
    ...dsaKeyPair.publicKey,
    ...new Uint8Array([(instanceTag >> 24) & 0xFF, (instanceTag >> 16) & 0xFF, (instanceTag >> 8) & 0xFF, instanceTag & 0xFF])
  ]);
  
  // Sign the data with our DSA key
  const signature = await sign(signatureData, dsaKeyPair.privateKey);
  
  // Create initialization vector
  const iv = random(16);
  
  // Encrypt the signature with the AES key derived from the shared secret
  const encryptedSignature = await encrypt(signature, keys.sendingAESKey, iv);
  
  // Calculate MAC for the encrypted signature
  const mac = await hmacSha256(encryptedSignature, keys.sendingMACKey);
  
  return {
    protocolVersion,
    messageType: MESSAGE_TYPE.SIGNATURE,
    senderInstanceTag: instanceTag,
    receiverInstanceTag,
    encryptedSignature,
    mac,
    iv,
    keys
  };
}

/**
 * Process a DH commit message
 * @param {Object} message - DH commit message data
 * @param {Object} state - OTR state
 * @returns {Promise<Object>} Processing result
 */
export async function processDHCommit(message, state) {
  // Store the DH commit message for later
  state.dhCommitMessage = message;
  
  // Generate our DH key pair if we don't have one
  if (!state.dhKeyPair) {
    state.dhKeyPair = await generateDHKeyPair();
  }
  
  // Create a DH key message in response
  const dhKeyMessage = createDHKey(
    state.dhKeyPair,
    state.protocolVersion,
    state.instanceTag,
    message.senderInstanceTag
  );
  
  // Update state
  state.handleDHCommit();
  
  return {
    response: dhKeyMessage,
    state
  };
}

/**
 * Process a DH key message
 * @param {Object} message - DH key message data
 * @param {Object} state - OTR state
 * @returns {Promise<Object>} Processing result
 */
export async function processDHKey(message, state) {
  // Store their DH public key
  state.theirPublicKey = message.publicKey;
  
  // Create a reveal signature message
  const revealSignatureMessage = await createRevealSignature({
    aesKey: state.dhCommitMessage.aesKey,
    iv: state.dhCommitMessage.iv,
    dhKeyPair: state.dhKeyPair,
    dsaKeyPair: state.dsaKeyPair,
    theirPublicKey: message.publicKey,
    protocolVersion: state.protocolVersion,
    instanceTag: state.instanceTag,
    receiverInstanceTag: message.senderInstanceTag
  });
  
  // Store the keys
  state.sendingAESKey = revealSignatureMessage.keys.sendingAESKey;
  state.sendingMACKey = revealSignatureMessage.keys.sendingMACKey;
  state.receivingAESKey = revealSignatureMessage.keys.receivingAESKey;
  state.receivingMACKey = revealSignatureMessage.keys.receivingMACKey;
  
  // Update state
  state.handleDHKey();
  
  return {
    response: revealSignatureMessage,
    state
  };
}

/**
 * Process a reveal signature message
 * @param {Object} message - Reveal signature message data
 * @param {Object} state - OTR state
 * @returns {Promise<Object>} Processing result
 */
export async function processRevealSignature(message, state) {
  // Decrypt our public key using the revealed AES key
  const decryptedPublicKey = await decrypt(
    state.dhCommitMessage.encryptedPublicKey,
    message.revealedKey,
    state.dhCommitMessage.iv
  );
  
  // Verify the hash
  const publicKeyHash = await hmacSha256(decryptedPublicKey, message.revealedKey);
  const hashMatches = publicKeyHash.every((byte, i) => byte === state.dhCommitMessage.publicKeyHash[i]);
  
  if (!hashMatches) {
    throw new Error('Public key hash does not match');
  }
  
  // Compute the shared secret
  const sharedSecret = await dhExchange(state.dhKeyPair.privateKey, decryptedPublicKey);
  
  // Derive keys from the shared secret
  const keys = await deriveKeys(sharedSecret);
  
  // Verify the MAC
  const isValidMAC = await hmacSha256(message.encryptedSignature, keys.receivingMACKey);
  if (!isValidMAC) {
    throw new Error('Invalid MAC');
  }
  
  // Decrypt the signature
  const signature = await decrypt(message.encryptedSignature, keys.receivingAESKey, message.iv);
  
  // Verify the signature
  // This is a placeholder - in a real implementation, we would extract their DSA public key
  // from the signature and verify it
  const isValidSignature = true;
  
  if (!isValidSignature) {
    throw new Error('Invalid signature');
  }
  
  // Store the keys
  state.sendingAESKey = keys.sendingAESKey;
  state.sendingMACKey = keys.sendingMACKey;
  state.receivingAESKey = keys.receivingAESKey;
  state.receivingMACKey = keys.receivingMACKey;
  
  // Create a signature message
  const signatureMessage = await createSignature({
    dhKeyPair: state.dhKeyPair,
    dsaKeyPair: state.dsaKeyPair,
    theirPublicKey: decryptedPublicKey,
    theirDsaPublicKey: null, // This would be extracted from the signature
    protocolVersion: state.protocolVersion,
    instanceTag: state.instanceTag,
    receiverInstanceTag: message.senderInstanceTag
  });
  
  // Update state
  state.handleRevealSignature();
  
  return {
    response: signatureMessage,
    state
  };
}

/**
 * Process a signature message
 * @param {Object} message - Signature message data
 * @param {Object} state - OTR state
 * @returns {Promise<Object>} Processing result
 */
export async function processSignature(message, state) {
  // Verify the MAC
  const isValidMAC = await hmacSha256(message.encryptedSignature, state.receivingMACKey);
  if (!isValidMAC) {
    throw new Error('Invalid MAC');
  }
  
  // Decrypt the signature
  const signature = await decrypt(message.encryptedSignature, state.receivingAESKey, message.iv);
  
  // Verify the signature
  // This is a placeholder - in a real implementation, we would extract their DSA public key
  // from the signature and verify it
  const isValidSignature = true;
  
  if (!isValidSignature) {
    throw new Error('Invalid signature');
  }
  
  // Update state
  state.handleSignature();
  
  return {
    response: null,
    state
  };
}

/**
 * Start the AKE process
 * @param {Object} state - OTR state
 * @returns {Promise<Object>} AKE initialization result
 */
export async function startAKE(state) {
  // Generate our DH key pair
  state.dhKeyPair = await generateDHKeyPair();
  
  // Create a DH commit message
  const dhCommitMessage = await createDHCommit(
    state.dhKeyPair,
    state.protocolVersion,
    state.instanceTag,
    state.theirInstanceTag
  );
  
  // Store the DH commit message for later
  state.dhCommitMessage = dhCommitMessage;
  
  // Update state
  state.startAKE();
  
  return {
    message: dhCommitMessage,
    state
  };
}