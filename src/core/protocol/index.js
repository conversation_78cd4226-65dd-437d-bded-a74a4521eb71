/**
 * OTR protocol module
 */
import { OtrState, STATE, MESSAGE_TYPE, PROTOCOL_VERSION } from './state';
import { SMPHand<PERSON>, SMP_STATE, SMP_RESULT, SMP_MESSAGE_TYPE, SMPState } from './smp';
import {
  parseMessage,
  createQueryMessage,
  createDataMessage,
  createErrorMessage,
  encryptMessage,
  decryptMessage
} from './message';
import {
  createDHCommit,
  createDHKey,
  createRevealSignature,
  createSignature,
  processDHCommit,
  processDHKey,
  processRevealSignature,
  processSignature,
  startAKE
} from './ake';
import { VersionNegotiation, ProtocolVersionFactory } from './version-negotiation';

export {
  OtrState,
  STATE,
  MESSAGE_TYPE,
  PROTOCOL_VERSION,
  SMPHandler as SMP,
  SMP_STATE,
  SMP_RESULT,
  SMP_MESSAGE_TYPE,
  SMPState,
  parseMessage,
  createQueryMessage,
  createDataMessage,
  createErrorMessage,
  encryptMessage,
  decryptMessage,
  createDHCommit,
  createDHK<PERSON>,
  createRevealSignature,
  createSignature,
  processDHCommit,
  processDHKey,
  processRevealSignature,
  processSignature,
  startAKE,
  VersionNegotiation,
  ProtocolVersionFactory
};
