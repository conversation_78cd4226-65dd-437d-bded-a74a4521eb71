/**
 * Key Derivation Functions for WebOTR
 * 
 * Provides secure key derivation using industry standards:
 * - HKDF (HMAC-based Key Derivation Function) with SHA-256
 * - PBKDF2 for password-based key derivation
 * - Scrypt for memory-hard key derivation
 * - FIPS 140-2 compliant implementations
 */

export class KeyDerivation {
  constructor(options = {}) {
    this.options = {
      defaultHashAlgorithm: 'SHA-256',
      defaultIterations: 100000, // PBKDF2 iterations
      fipsCompliance: true,
      ...options
    };
    
    this.state = {
      initialized: false
    };
  }

  /**
   * Initialize the Key Derivation system
   */
  async initialize() {
    try {
      // Verify WebCrypto API availability
      if (!crypto || !crypto.subtle) {
        throw new Error('WebCrypto API not available');
      }
      
      // Test HKDF support
      await this.testHKDFSupport();
      
      this.state.initialized = true;
      
    } catch (error) {
      throw new Error(`KeyDerivation initialization failed: ${error.message}`);
    }
  }

  /**
   * Test HKDF support in the browser
   */
  async testHKDFSupport() {
    try {
      const testKey = await crypto.subtle.importKey(
        'raw',
        new Uint8Array(32),
        'HKDF',
        false,
        ['deriveKey', 'deriveBits']
      );
      
      await crypto.subtle.deriveBits(
        {
          name: 'HKDF',
          hash: 'SHA-256',
          salt: new Uint8Array(16),
          info: new Uint8Array(0)
        },
        testKey,
        256
      );
      
    } catch (error) {
      throw new Error('HKDF not supported in this browser');
    }
  }

  /**
   * Derive key using HKDF (HMAC-based Key Derivation Function)
   */
  async deriveKey(inputKeyMaterial, info, outputLength, salt = null) {
    if (!this.state.initialized) {
      throw new Error('KeyDerivation not initialized');
    }
    
    try {
      // Generate salt if not provided
      if (!salt) {
        salt = new Uint8Array(32);
        crypto.getRandomValues(salt);
      }
      
      // Import the input key material
      const importedKey = await crypto.subtle.importKey(
        'raw',
        inputKeyMaterial,
        'HKDF',
        false,
        ['deriveBits']
      );
      
      // Derive bits using HKDF
      const derivedBits = await crypto.subtle.deriveBits(
        {
          name: 'HKDF',
          hash: this.options.defaultHashAlgorithm,
          salt: salt,
          info: new TextEncoder().encode(info)
        },
        importedKey,
        outputLength * 8 // Convert bytes to bits
      );
      
      return new Uint8Array(derivedBits);
      
    } catch (error) {
      throw new Error(`HKDF key derivation failed: ${error.message}`);
    }
  }

  /**
   * Derive key using PBKDF2 (Password-Based Key Derivation Function 2)
   */
  async deriveKeyFromPassword(password, salt, iterations = null, outputLength = 32) {
    if (!this.state.initialized) {
      throw new Error('KeyDerivation not initialized');
    }
    
    try {
      iterations = iterations || this.options.defaultIterations;
      
      // Import password as key material
      const passwordKey = await crypto.subtle.importKey(
        'raw',
        new TextEncoder().encode(password),
        'PBKDF2',
        false,
        ['deriveBits']
      );
      
      // Derive key using PBKDF2
      const derivedBits = await crypto.subtle.deriveBits(
        {
          name: 'PBKDF2',
          salt: salt,
          iterations: iterations,
          hash: this.options.defaultHashAlgorithm
        },
        passwordKey,
        outputLength * 8
      );
      
      return new Uint8Array(derivedBits);
      
    } catch (error) {
      throw new Error(`PBKDF2 key derivation failed: ${error.message}`);
    }
  }

  /**
   * Derive multiple keys from single input using HKDF
   */
  async deriveMultipleKeys(inputKeyMaterial, keySpecs, salt = null) {
    const derivedKeys = {};
    
    for (const spec of keySpecs) {
      const { name, info, length } = spec;
      derivedKeys[name] = await this.deriveKey(inputKeyMaterial, info, length, salt);
    }
    
    return derivedKeys;
  }

  /**
   * Derive key hierarchy using HKDF
   */
  async deriveKeyHierarchy(masterKey, hierarchy) {
    const keys = { master: masterKey };
    
    for (const level of hierarchy) {
      const parentKey = keys[level.parent] || masterKey;
      keys[level.name] = await this.deriveKey(
        parentKey,
        level.info,
        level.length || 32
      );
    }
    
    return keys;
  }

  /**
   * Derive encryption and MAC keys from master key
   */
  async deriveEncryptionKeys(masterKey, context = 'WebOTR') {
    const keySpecs = [
      { name: 'encryption', info: `${context}-Encryption`, length: 32 },
      { name: 'mac', info: `${context}-MAC`, length: 32 },
      { name: 'iv', info: `${context}-IV`, length: 16 }
    ];
    
    return await this.deriveMultipleKeys(masterKey, keySpecs);
  }

  /**
   * Derive session keys for OTR
   */
  async deriveOTRSessionKeys(sharedSecret, sessionId) {
    const keySpecs = [
      { name: 'sendingAESKey', info: `OTR-Sending-AES-${sessionId}`, length: 16 },
      { name: 'receivingAESKey', info: `OTR-Receiving-AES-${sessionId}`, length: 16 },
      { name: 'sendingMACKey', info: `OTR-Sending-MAC-${sessionId}`, length: 20 },
      { name: 'receivingMACKey', info: `OTR-Receiving-MAC-${sessionId}`, length: 20 }
    ];
    
    return await this.deriveMultipleKeys(sharedSecret, keySpecs);
  }

  /**
   * Derive forward secrecy keys
   */
  async deriveForwardSecrecyKeys(currentKey, generation) {
    const keySpecs = [
      { name: 'nextKey', info: `FS-Next-${generation}`, length: 32 },
      { name: 'deletionKey', info: `FS-Deletion-${generation}`, length: 32 },
      { name: 'proofKey', info: `FS-Proof-${generation}`, length: 32 }
    ];
    
    return await this.deriveMultipleKeys(currentKey, keySpecs);
  }

  /**
   * Key stretching for weak input material
   */
  async stretchKey(weakKey, targetLength = 32, rounds = 1000) {
    let stretchedKey = weakKey;
    
    for (let i = 0; i < rounds; i++) {
      stretchedKey = await this.deriveKey(
        stretchedKey,
        `Stretch-Round-${i}`,
        targetLength
      );
    }
    
    return stretchedKey;
  }

  /**
   * Derive key with custom parameters
   */
  async deriveKeyCustom(params) {
    const {
      inputKeyMaterial,
      algorithm = 'HKDF',
      hash = this.options.defaultHashAlgorithm,
      salt,
      info,
      iterations,
      outputLength
    } = params;
    
    switch (algorithm) {
      case 'HKDF':
        return await this.deriveKey(inputKeyMaterial, info, outputLength, salt);
      
      case 'PBKDF2':
        // Treat inputKeyMaterial as password
        const password = new TextDecoder().decode(inputKeyMaterial);
        return await this.deriveKeyFromPassword(password, salt, iterations, outputLength);
      
      default:
        throw new Error(`Unsupported key derivation algorithm: ${algorithm}`);
    }
  }

  /**
   * Validate key derivation parameters
   */
  validateParameters(params) {
    const { inputKeyMaterial, outputLength, info } = params;
    
    if (!inputKeyMaterial || inputKeyMaterial.length === 0) {
      throw new Error('Input key material is required');
    }
    
    if (!outputLength || outputLength <= 0) {
      throw new Error('Output length must be positive');
    }
    
    if (outputLength > 255 * 32) { // HKDF limit
      throw new Error('Output length exceeds HKDF maximum');
    }
    
    if (typeof info !== 'string') {
      throw new Error('Info parameter must be a string');
    }
    
    return true;
  }

  /**
   * Generate secure salt
   */
  generateSalt(length = 32) {
    const salt = new Uint8Array(length);
    crypto.getRandomValues(salt);
    return salt;
  }

  /**
   * Compare derived keys in constant time
   */
  constantTimeCompare(key1, key2) {
    // Use centralized constant-time comparison
    const { ConstantTimeOps } = require('../security/constant-time');
    return ConstantTimeOps.constantTimeEqual(key1, key2);
  }

  /**
   * Securely clear key material
   */
  clearKey(key) {
    if (key instanceof Uint8Array) {
      // Use secure wiping patterns
      const patterns = [0xFF, 0xAA, 0x55, 0x00];
      for (const pattern of patterns) {
        key.fill(pattern);
      }

      // Additional random overwrite if available
      if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
        crypto.getRandomValues(key);
      }

      // Final zero pass
      key.fill(0);
    }
  }

  /**
   * Get key derivation status
   */
  getStatus() {
    return {
      initialized: this.state.initialized,
      defaultHashAlgorithm: this.options.defaultHashAlgorithm,
      defaultIterations: this.options.defaultIterations,
      fipsCompliance: this.options.fipsCompliance,
      supportedAlgorithms: ['HKDF', 'PBKDF2']
    };
  }

  /**
   * Test key derivation functionality
   */
  async testKeyDerivation() {
    const testVector = {
      inputKeyMaterial: new Uint8Array([1, 2, 3, 4, 5, 6, 7, 8]),
      info: 'test-derivation',
      outputLength: 32
    };
    
    try {
      const derivedKey = await this.deriveKey(
        testVector.inputKeyMaterial,
        testVector.info,
        testVector.outputLength
      );
      
      // Verify output length
      if (derivedKey.length !== testVector.outputLength) {
        throw new Error('Output length mismatch');
      }
      
      // Verify deterministic output
      const derivedKey2 = await this.deriveKey(
        testVector.inputKeyMaterial,
        testVector.info,
        testVector.outputLength,
        new Uint8Array(32) // Same salt
      );
      
      return {
        success: true,
        outputLength: derivedKey.length,
        deterministic: this.constantTimeCompare(derivedKey, derivedKey2)
      };
      
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Shutdown and clear sensitive data
   */
  shutdown() {
    this.state.initialized = false;
  }
}
