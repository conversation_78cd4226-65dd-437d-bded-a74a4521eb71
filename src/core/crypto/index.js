/**
 * Core cryptographic functions for WebOTR
 */
// Import basic crypto operations from individual modules
import { generateDHKeyPair } from './dh.js';
import { generateDSAKeyPair, verifySignature, createSignature } from './dsa.js';
import { encrypt, decrypt } from './aes.js';
import { hmacSha256 } from './hmac.js';
import { random } from './random.js';

// Import SMP-related functions
import {
  generateRandomExponent,
  computeG1,
  computeG2,
  computeG3,
  createZKP,
  verifyZKP,
  hashForSMP,
  MODP_GROUP
} from './smp.js';

// Import security functions
import { ConstantTimeOps } from '../security/constant-time.js';
import { CryptoValidation, SecurityValidationError } from '../security/validation.js';
import { SecureMemory, SecureMemoryPool, globalSecureMemoryPool } from '../security/secure-memory.js';
import { ProtocolErrorRecovery, ERROR_TYPES, RECOVERY_STRATEGIES, SecurityEvent, globalErrorRecovery } from '../security/error-recovery.js';

/**
 * Generate OTR keys
 * @returns {Promise<Object>} Generated keys
 */
export async function generateKeys() {
  // Generate DSA keys for authentication
  const dsaKeys = await generateDSAKeyPair();
  
  // Generate DH keys for key exchange
  const dhKeys = await generateDHKeyPair();
  
  return {
    dsa: dsaKeys,
    dh: dhKeys
  };
}

/**
 * Derive keys from shared secret using HKDF
 * @param {Uint8Array} sharedSecret - Shared secret from DH exchange
 * @param {Uint8Array} salt - Optional salt (defaults to zero bytes)
 * @param {Uint8Array} info - Optional context info
 * @returns {Promise<Object>} Derived keys
 */
export async function deriveKeys(sharedSecret, salt = null, info = null) {
  // Use proper HKDF for key derivation
  if (!salt) {
    salt = new Uint8Array(32); // Zero salt if not provided
  }

  if (!info) {
    info = new TextEncoder().encode('WebOTR-v1-key-derivation');
  }

  // HKDF Extract: PRK = HMAC-SHA256(salt, IKM)
  const prk = await hmacSha256(salt, sharedSecret);

  // HKDF Expand: derive multiple keys
  const sendingAESKey = await hmacSha256(prk, new Uint8Array([...info, 0x01]));
  const receivingAESKey = await hmacSha256(prk, new Uint8Array([...info, 0x02]));
  const sendingMACKey = await hmacSha256(prk, new Uint8Array([...info, 0x03]));
  const receivingMACKey = await hmacSha256(prk, new Uint8Array([...info, 0x04]));

  return {
    sendingAESKey: sendingAESKey.slice(0, 32), // Use 256 bits for AES key
    receivingAESKey: receivingAESKey.slice(0, 32),
    sendingMACKey: sendingMACKey, // Use all 256 bits for MAC key
    receivingMACKey: receivingMACKey,
    // Legacy compatibility
    aesKey: sendingAESKey.slice(0, 16), // 128 bits for backward compatibility
    macKey: sendingMACKey
  };
}

// Global instance tag registry to ensure uniqueness
const usedInstanceTags = new Set();

/**
 * Generate an instance tag
 * @returns {number} Instance tag
 */
export function generateInstanceTag() {
  let tag;
  let attempts = 0;
  const maxAttempts = 100;

  do {
    // Generate a random 32-bit number with highest bit not 0
    // (as per OTRv3 spec section 5.1)
    const bytes = random(4);
    tag = bytes[0] | (bytes[1] << 8) | (bytes[2] << 16) | (bytes[3] << 24);

    // Ensure highest 8 bits are non-zero by setting at least one of them
    if ((tag & 0xFF000000) === 0) {
      tag |= 0x01000000;
    }

    // Ensure it's a positive number (JavaScript quirk)
    tag = tag >>> 0;

    // Ensure it's greater than 0x00000100 as per OTR spec
    if (tag <= 0x00000100) {
      tag = 0x00000100 + (tag % 0xFFFFFEFF);
    }

    attempts++;
  } while (usedInstanceTags.has(tag) && attempts < maxAttempts);

  if (attempts >= maxAttempts) {
    // Fallback: use timestamp + random to ensure uniqueness
    const timestamp = Date.now() & 0xFFFFFF; // Use lower 24 bits of timestamp
    const randomByte = random(1)[0];
    tag = (timestamp << 8) | randomByte;
    tag = Math.max(tag, 0x00000101); // Ensure minimum value
  }

  usedInstanceTags.add(tag);

  // Clean up old tags periodically (keep last 1000)
  if (usedInstanceTags.size > 1000) {
    const tagsArray = Array.from(usedInstanceTags);
    const toRemove = tagsArray.slice(0, tagsArray.length - 1000);
    toRemove.forEach(oldTag => usedInstanceTags.delete(oldTag));
  }

  return tag;
}

/**
 * Securely clear sensitive data from memory
 * @param {Uint8Array|Array} data - Data to clear
 */
export function secureClear(data) {
  if (data && data.fill) {
    // Use secure wiping patterns
    const patterns = [0xFF, 0xAA, 0x55, 0x00];
    for (const pattern of patterns) {
      data.fill(pattern);
    }

    // Additional random overwrite if available
    if (typeof crypto !== 'undefined' && crypto.getRandomValues && data instanceof Uint8Array) {
      crypto.getRandomValues(data);
    }

    // Final zero pass
    data.fill(0);
  }
}

/**
 * Constant-time comparison to prevent timing attacks
 * @param {Uint8Array} a - First array
 * @param {Uint8Array} b - Second array
 * @returns {boolean} True if arrays are equal
 * @deprecated Use ConstantTimeOps.constantTimeEqual instead
 */
export function constantTimeEqual(a, b) {
  // Delegate to the new comprehensive implementation
  return ConstantTimeOps.constantTimeEqual(a, b);
}

// Export all the required functions
export {
  // From basic crypto
  verifySignature,
  createSignature,
  encrypt,
  decrypt,
  hmacSha256,
  random,

  // SMP functions
  generateRandomExponent,
  computeG1,
  computeG2,
  computeG3,
  createZKP,
  verifyZKP,
  hashForSMP,
  MODP_GROUP,

  // Security functions
  ConstantTimeOps,
  CryptoValidation,
  SecurityValidationError,
  SecureMemory,
  SecureMemoryPool,
  globalSecureMemoryPool,
  ProtocolErrorRecovery,
  ERROR_TYPES,
  RECOVERY_STRATEGIES,
  SecurityEvent,
  globalErrorRecovery
};