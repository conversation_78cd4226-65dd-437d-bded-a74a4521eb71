/**
 * Enhanced Error Recovery Module
 * 
 * This module provides robust protocol error handling, state recovery mechanisms,
 * and graceful degradation for protocol violations based on libOTR patterns.
 * 
 * Implements comprehensive error recovery strategies to maintain security
 * properties even when facing protocol violations or unexpected conditions.
 */

import { ConstantTimeOps } from './constant-time.js';
import { CryptoValidation, SecurityValidationError } from './validation.js';
import { SecureMemory } from './secure-memory.js';

/**
 * Error types for protocol violations and security issues
 */
export const ERROR_TYPES = {
  // Protocol errors
  PROTOCOL_VIOLATION: 'PROTOCOL_VIOLATION',
  INVALID_MESSAGE_TYPE: 'INVALID_MESSAGE_TYPE',
  INVALID_MESSAGE_FORMAT: 'INVALID_MESSAGE_FORMAT',
  SEQUENCE_ERROR: 'SEQUENCE_ERROR',
  
  // Cryptographic errors
  INVALID_SIGNATURE: 'INVALID_SIGNATURE',
  INVALID_MAC: 'INVALID_MAC',
  INVALID_DH_KEY: 'INVALID_DH_KEY',
  INVALID_SMP_ELEMENT: 'INVALID_SMP_ELEMENT',
  
  // State errors
  INVALID_STATE_TRANSITION: 'INVALID_STATE_TRANSITION',
  COMPETING_DH_COMMIT: 'COMPETING_DH_COMMIT',
  SESSION_EXPIRED: 'SESSION_EXPIRED',
  
  // Security errors
  REPLAY_ATTACK: 'REPLAY_ATTACK',
  TIMING_ATTACK: 'TIMING_ATTACK',
  COUNTER_REGRESSION: 'COUNTER_REGRESSION',
  
  // Network errors
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  CONNECTION_LOST: 'CONNECTION_LOST'
};

/**
 * Recovery strategies for different error types
 */
export const RECOVERY_STRATEGIES = {
  IGNORE: 'IGNORE',                    // Ignore the error and continue
  RETRY: 'RETRY',                      // Retry the operation
  RESET_STATE: 'RESET_STATE',          // Reset protocol state
  GRACEFUL_DEGRADATION: 'GRACEFUL_DEGRADATION', // Degrade to plaintext
  ABORT_SESSION: 'ABORT_SESSION',      // Abort the session
  RESTART_PROTOCOL: 'RESTART_PROTOCOL' // Restart the protocol
};

/**
 * Security event for logging and monitoring
 */
export class SecurityEvent {
  constructor(type, severity, message, context = {}) {
    this.type = type;
    this.severity = severity; // 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL'
    this.message = message;
    this.context = context;
    this.timestamp = Date.now();
    this.id = this._generateEventId();
  }

  _generateEventId() {
    return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  toJSON() {
    return {
      id: this.id,
      type: this.type,
      severity: this.severity,
      message: this.message,
      context: this.context,
      timestamp: this.timestamp
    };
  }
}

/**
 * Enhanced error recovery system
 */
export class ProtocolErrorRecovery {
  constructor(options = {}) {
    this.options = {
      maxRetries: options.maxRetries || 3,
      retryDelay: options.retryDelay || 1000,
      enableLogging: options.enableLogging !== false,
      securityEventHandler: options.securityEventHandler || null,
      ...options
    };

    this.retryCount = new Map();
    this.securityEvents = [];
    this.recoveryStats = {
      totalErrors: 0,
      recoveredErrors: 0,
      failedRecoveries: 0,
      securityEvents: 0
    };
  }

  /**
   * Handle AKE (Authenticated Key Exchange) errors
   * 
   * @param {Error} error - The error that occurred
   * @param {Object} context - Protocol context
   * @returns {Object} Recovery action
   */
  handleAKEError(error, context) {
    this.recoveryStats.totalErrors++;

    try {
      switch (error.type || this._classifyError(error)) {
        case ERROR_TYPES.COMPETING_DH_COMMIT:
          return this._resolveCommitConflict(context, error.data);
          
        case ERROR_TYPES.INVALID_SIGNATURE:
          return this._handleSignatureFailure(context, error);
          
        case ERROR_TYPES.INVALID_DH_KEY:
          return this._handleInvalidDHKey(context, error);
          
        case ERROR_TYPES.PROTOCOL_VIOLATION:
          return this._handleProtocolViolation(context, error);
          
        case ERROR_TYPES.SEQUENCE_ERROR:
          return this._handleSequenceError(context, error);
          
        default:
          return this._handleUnknownError(context, error);
      }
    } catch (recoveryError) {
      this.recoveryStats.failedRecoveries++;
      return this._gracefulDegradation(context, recoveryError);
    }
  }

  /**
   * Resolve competing DH commit conflict (libOTR pattern)
   * 
   * @private
   * @param {Object} context - Protocol context
   * @param {Object} incomingCommit - Incoming DH commit data
   * @returns {Object} Recovery action
   */
  _resolveCommitConflict(context, incomingCommit) {
    this._logSecurityEvent(
      ERROR_TYPES.COMPETING_DH_COMMIT,
      'MEDIUM',
      'Competing DH commits detected, resolving conflict',
      { contextId: context.id }
    );

    try {
      // Compare hash values to determine winner (libOTR pattern)
      const ourHash = context.auth?.hashgx;
      const theirHash = incomingCommit?.hashgx;

      if (!ourHash || !theirHash) {
        throw new Error('Missing hash values for commit comparison');
      }

      // Use constant-time comparison for security
      const comparison = ConstantTimeOps.constantTimeCompare(ourHash, theirHash);

      if (comparison > 0) {
        // Our commit wins, ignore incoming
        this.recoveryStats.recoveredErrors++;
        return {
          strategy: RECOVERY_STRATEGIES.IGNORE,
          action: 'ignore_incoming',
          retransmit: true,
          message: 'Our DH commit wins, ignoring incoming commit'
        };
      } else {
        // Their commit wins, restart with their parameters
        this._clearAuthState(context);
        this.recoveryStats.recoveredErrors++;
        return {
          strategy: RECOVERY_STRATEGIES.RESTART_PROTOCOL,
          action: 'restart_with_incoming',
          useIncoming: true,
          message: 'Their DH commit wins, restarting with incoming parameters'
        };
      }
    } catch (error) {
      return this._gracefulDegradation(context, error);
    }
  }

  /**
   * Handle signature verification failure
   * 
   * @private
   * @param {Object} context - Protocol context
   * @param {Error} error - Signature error
   * @returns {Object} Recovery action
   */
  _handleSignatureFailure(context, error) {
    this._logSecurityEvent(
      ERROR_TYPES.INVALID_SIGNATURE,
      'HIGH',
      'Signature verification failed',
      { contextId: context.id, error: error.message }
    );

    // Signature failures are serious security issues
    // Reset to secure state and require re-authentication
    this._clearAuthState(context);
    
    return {
      strategy: RECOVERY_STRATEGIES.RESET_STATE,
      action: 'reset_to_plaintext',
      requireReauth: true,
      message: 'Signature verification failed, resetting to secure state'
    };
  }

  /**
   * Handle invalid DH key
   * 
   * @private
   * @param {Object} context - Protocol context
   * @param {Error} error - DH key error
   * @returns {Object} Recovery action
   */
  _handleInvalidDHKey(context, error) {
    this._logSecurityEvent(
      ERROR_TYPES.INVALID_DH_KEY,
      'HIGH',
      'Invalid DH public key detected',
      { contextId: context.id, error: error.message }
    );

    // Invalid DH keys could indicate an attack
    // Reset state and require new key exchange
    this._clearAuthState(context);
    
    return {
      strategy: RECOVERY_STRATEGIES.RESTART_PROTOCOL,
      action: 'restart_key_exchange',
      message: 'Invalid DH key detected, restarting key exchange'
    };
  }

  /**
   * Handle protocol violations
   * 
   * @private
   * @param {Object} context - Protocol context
   * @param {Error} error - Protocol error
   * @returns {Object} Recovery action
   */
  _handleProtocolViolation(context, error) {
    this._logSecurityEvent(
      ERROR_TYPES.PROTOCOL_VIOLATION,
      'MEDIUM',
      'Protocol violation detected',
      { contextId: context.id, error: error.message }
    );

    const retryKey = `${context.id}_protocol_violation`;
    const retries = this.retryCount.get(retryKey) || 0;

    if (retries < this.options.maxRetries) {
      // Try to recover by resending last valid message
      this.retryCount.set(retryKey, retries + 1);
      
      return {
        strategy: RECOVERY_STRATEGIES.RETRY,
        action: 'resend_last_message',
        delay: this.options.retryDelay * (retries + 1),
        message: `Protocol violation, retrying (attempt ${retries + 1})`
      };
    } else {
      // Too many retries, reset state
      this.retryCount.delete(retryKey);
      return this._gracefulDegradation(context, error);
    }
  }

  /**
   * Handle sequence errors (message ordering issues)
   * 
   * @private
   * @param {Object} context - Protocol context
   * @param {Error} error - Sequence error
   * @returns {Object} Recovery action
   */
  _handleSequenceError(context, error) {
    this._logSecurityEvent(
      ERROR_TYPES.SEQUENCE_ERROR,
      'MEDIUM',
      'Message sequence error detected',
      { contextId: context.id, error: error.message }
    );

    // Check if this might be a replay attack
    if (this._isLikelyReplayAttack(context, error)) {
      return this._handleReplayAttack(context, error);
    }

    // Otherwise, try to resynchronize
    return {
      strategy: RECOVERY_STRATEGIES.RESET_STATE,
      action: 'resynchronize',
      message: 'Message sequence error, attempting resynchronization'
    };
  }

  /**
   * Handle unknown errors
   * 
   * @private
   * @param {Object} context - Protocol context
   * @param {Error} error - Unknown error
   * @returns {Object} Recovery action
   */
  _handleUnknownError(context, error) {
    this._logSecurityEvent(
      'UNKNOWN_ERROR',
      'MEDIUM',
      'Unknown error occurred',
      { contextId: context.id, error: error.message, stack: error.stack }
    );

    return this._gracefulDegradation(context, error);
  }

  /**
   * Handle potential replay attacks
   * 
   * @private
   * @param {Object} context - Protocol context
   * @param {Error} error - Replay error
   * @returns {Object} Recovery action
   */
  _handleReplayAttack(context, error) {
    this._logSecurityEvent(
      ERROR_TYPES.REPLAY_ATTACK,
      'CRITICAL',
      'Potential replay attack detected',
      { contextId: context.id, error: error.message }
    );

    // Replay attacks are critical security issues
    this._clearAuthState(context);
    
    return {
      strategy: RECOVERY_STRATEGIES.ABORT_SESSION,
      action: 'abort_session',
      reason: 'replay_attack_detected',
      message: 'Potential replay attack detected, aborting session'
    };
  }

  /**
   * Graceful degradation to plaintext mode
   * 
   * @private
   * @param {Object} context - Protocol context
   * @param {Error} error - Error that triggered degradation
   * @returns {Object} Recovery action
   */
  _gracefulDegradation(context, error) {
    this._logSecurityEvent(
      'GRACEFUL_DEGRADATION',
      'HIGH',
      'Graceful degradation to plaintext mode',
      { contextId: context.id, error: error.message }
    );

    this._clearAuthState(context);
    
    return {
      strategy: RECOVERY_STRATEGIES.GRACEFUL_DEGRADATION,
      action: 'degrade_to_plaintext',
      message: 'Error recovery failed, degrading to plaintext mode'
    };
  }

  /**
   * Clear authentication state securely
   * 
   * @private
   * @param {Object} context - Protocol context
   */
  _clearAuthState(context) {
    if (context.auth) {
      // Securely clear sensitive authentication data
      const sensitiveFields = ['privateKey', 'sharedSecret', 'sessionKeys', 'hashgx'];
      
      for (const field of sensitiveFields) {
        if (context.auth[field]) {
          if (context.auth[field] instanceof Uint8Array) {
            // Use secure wiping for byte arrays
            const patterns = [0xFF, 0xAA, 0x55, 0x00];
            for (const pattern of patterns) {
              context.auth[field].fill(pattern);
            }
            context.auth[field].fill(0);
          }
          context.auth[field] = null;
        }
      }
      
      // Reset auth state
      context.auth.state = 'PLAINTEXT';
    }
  }

  /**
   * Check if error pattern suggests replay attack
   * 
   * @private
   * @param {Object} context - Protocol context
   * @param {Error} error - Error to analyze
   * @returns {boolean} True if likely replay attack
   */
  _isLikelyReplayAttack(context, error) {
    // Simple heuristics for replay detection
    // In a real implementation, this would be more sophisticated
    
    if (error.message && error.message.includes('counter regression')) {
      return true;
    }
    
    if (error.message && error.message.includes('duplicate message')) {
      return true;
    }
    
    // Check for rapid repeated errors
    const recentErrors = this.securityEvents
      .filter(event => event.timestamp > Date.now() - 5000) // Last 5 seconds
      .filter(event => event.context.contextId === context.id);
    
    return recentErrors.length > 5; // More than 5 errors in 5 seconds
  }

  /**
   * Classify error type based on error message and properties
   * 
   * @private
   * @param {Error} error - Error to classify
   * @returns {string} Error type
   */
  _classifyError(error) {
    const message = error.message?.toLowerCase() || '';
    
    if (message.includes('signature')) return ERROR_TYPES.INVALID_SIGNATURE;
    if (message.includes('mac')) return ERROR_TYPES.INVALID_MAC;
    if (message.includes('dh') && message.includes('key')) return ERROR_TYPES.INVALID_DH_KEY;
    if (message.includes('sequence')) return ERROR_TYPES.SEQUENCE_ERROR;
    if (message.includes('protocol')) return ERROR_TYPES.PROTOCOL_VIOLATION;
    if (message.includes('replay')) return ERROR_TYPES.REPLAY_ATTACK;
    if (message.includes('timeout')) return ERROR_TYPES.TIMEOUT_ERROR;
    
    return 'UNKNOWN_ERROR';
  }

  /**
   * Log security event
   * 
   * @private
   * @param {string} type - Event type
   * @param {string} severity - Event severity
   * @param {string} message - Event message
   * @param {Object} context - Event context
   */
  _logSecurityEvent(type, severity, message, context = {}) {
    const event = new SecurityEvent(type, severity, message, context);
    
    this.securityEvents.push(event);
    this.recoveryStats.securityEvents++;
    
    // Keep only recent events (last 1000)
    if (this.securityEvents.length > 1000) {
      this.securityEvents.shift();
    }
    
    // Call external handler if provided
    if (this.options.securityEventHandler) {
      try {
        this.options.securityEventHandler(event);
      } catch (error) {
        console.error('Error in security event handler:', error);
      }
    }
    
    // Log to console if enabled
    if (this.options.enableLogging) {
      const logLevel = severity === 'CRITICAL' ? 'error' : 
                      severity === 'HIGH' ? 'warn' : 'info';
      console[logLevel](`[SecurityEvent] ${type}: ${message}`, context);
    }
  }

  /**
   * Get recovery statistics
   * 
   * @returns {Object} Recovery statistics
   */
  getStats() {
    return {
      ...this.recoveryStats,
      recoveryRate: this.recoveryStats.totalErrors > 0 ? 
        this.recoveryStats.recoveredErrors / this.recoveryStats.totalErrors : 0,
      recentEvents: this.securityEvents.slice(-10) // Last 10 events
    };
  }

  /**
   * Reset recovery statistics
   */
  resetStats() {
    this.recoveryStats = {
      totalErrors: 0,
      recoveredErrors: 0,
      failedRecoveries: 0,
      securityEvents: 0
    };
    this.securityEvents = [];
    this.retryCount.clear();
  }
}

// Export singleton instance for global use
export const globalErrorRecovery = new ProtocolErrorRecovery();
