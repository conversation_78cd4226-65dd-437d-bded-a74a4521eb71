/**
 * Secure Memory Management Module
 * 
 * This module provides secure memory allocation, wiping, and lifecycle management
 * for sensitive cryptographic data to prevent memory-based attacks and data leakage.
 * 
 * Based on libOTR's secure memory patterns adapted for JavaScript/browser environments.
 */

/**
 * Secure memory buffer with automatic wiping and lifecycle management
 */
export class SecureMemory {
  /**
   * Create a new secure memory buffer
   * 
   * @param {number} size - Size of the buffer in bytes
   * @param {Object} options - Configuration options
   */
  constructor(size, options = {}) {
    if (typeof size !== 'number' || size <= 0) {
      throw new Error('Size must be a positive number');
    }

    this.size = size;
    this.options = {
      autoWipe: options.autoWipe !== false, // Default to true
      wipePatterns: options.wipePatterns || [0xFF, 0xAA, 0x55, 0x00],
      trackAccess: options.trackAccess || false,
      ...options
    };

    // Create the underlying buffer
    this.buffer = new ArrayBuffer(size);
    this.view = new Uint8Array(this.buffer);
    this.isDestroyed = false;
    this.accessCount = 0;
    this.createdAt = Date.now();
    this.lastAccessed = this.createdAt;

    // Register for cleanup
    SecureMemory.registry.add(this);

    // Track access if enabled
    if (this.options.trackAccess) {
      this._trackAccess('created');
    }
  }

  /**
   * Get a view of the secure memory
   * 
   * @returns {Uint8Array} View of the memory buffer
   */
  getView() {
    this._checkDestroyed();
    this._trackAccess('read');
    return this.view;
  }

  /**
   * Write data to secure memory
   * 
   * @param {Uint8Array|Array} data - Data to write
   * @param {number} offset - Offset to start writing (default: 0)
   */
  write(data, offset = 0) {
    this._checkDestroyed();
    
    if (offset + data.length > this.size) {
      throw new Error('Data exceeds buffer size');
    }

    this.view.set(data, offset);
    this._trackAccess('write');
  }

  /**
   * Read data from secure memory
   * 
   * @param {number} length - Number of bytes to read (default: all)
   * @param {number} offset - Offset to start reading (default: 0)
   * @returns {Uint8Array} Copy of the data
   */
  read(length = this.size, offset = 0) {
    this._checkDestroyed();
    
    if (offset + length > this.size) {
      throw new Error('Read exceeds buffer size');
    }

    const result = new Uint8Array(length);
    result.set(this.view.subarray(offset, offset + length));
    this._trackAccess('read');
    return result;
  }

  /**
   * Securely wipe the memory buffer
   * 
   * Uses multiple passes with different patterns to ensure data is overwritten.
   */
  secureWipe() {
    if (this.isDestroyed) {
      return; // Already wiped
    }

    // Multi-pass secure wiping
    for (const pattern of this.options.wipePatterns) {
      this.view.fill(pattern);
    }

    // Additional random overwrite
    if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
      crypto.getRandomValues(this.view);
    }

    // Final zero pass
    this.view.fill(0);

    this._trackAccess('wiped');
  }

  /**
   * Destroy the secure memory buffer
   * 
   * Performs secure wiping and releases all references.
   */
  destroy() {
    if (this.isDestroyed) {
      return;
    }

    this.secureWipe();
    this.isDestroyed = true;
    this.buffer = null;
    this.view = null;

    // Remove from registry
    SecureMemory.registry.delete(this);

    this._trackAccess('destroyed');
  }

  /**
   * Get memory statistics
   * 
   * @returns {Object} Memory usage statistics
   */
  getStats() {
    return {
      size: this.size,
      isDestroyed: this.isDestroyed,
      accessCount: this.accessCount,
      createdAt: this.createdAt,
      lastAccessed: this.lastAccessed,
      age: Date.now() - this.createdAt
    };
  }

  /**
   * Check if memory has been destroyed
   * 
   * @private
   */
  _checkDestroyed() {
    if (this.isDestroyed) {
      throw new Error('Secure memory has been destroyed');
    }
  }

  /**
   * Track memory access for debugging/monitoring
   * 
   * @private
   * @param {string} operation - Type of operation
   */
  _trackAccess(operation) {
    if (this.options.trackAccess) {
      this.accessCount++;
      this.lastAccessed = Date.now();
      
      if (SecureMemory.accessLogger) {
        SecureMemory.accessLogger({
          operation,
          size: this.size,
          accessCount: this.accessCount,
          timestamp: this.lastAccessed
        });
      }
    }
  }

  /**
   * Global registry of all secure memory instances
   */
  static registry = new Set();

  /**
   * Optional access logger for monitoring
   */
  static accessLogger = null;

  /**
   * Set access logger for monitoring memory usage
   * 
   * @param {Function} logger - Logger function
   */
  static setAccessLogger(logger) {
    this.accessLogger = logger;
  }

  /**
   * Clean up all secure memory instances
   */
  static cleanupAll() {
    for (const memory of this.registry) {
      memory.destroy();
    }
  }

  /**
   * Get global memory statistics
   * 
   * @returns {Object} Global memory statistics
   */
  static getGlobalStats() {
    const instances = Array.from(this.registry);
    const totalSize = instances.reduce((sum, mem) => sum + mem.size, 0);
    const activeInstances = instances.filter(mem => !mem.isDestroyed).length;
    
    return {
      totalInstances: instances.length,
      activeInstances,
      totalSize,
      averageSize: instances.length > 0 ? totalSize / instances.length : 0,
      oldestInstance: instances.length > 0 ? Math.min(...instances.map(mem => mem.createdAt)) : null
    };
  }
}

/**
 * Secure memory pool for efficient allocation and reuse
 */
export class SecureMemoryPool {
  /**
   * Create a new secure memory pool
   * 
   * @param {Object} options - Pool configuration
   */
  constructor(options = {}) {
    this.options = {
      maxPoolSize: options.maxPoolSize || 100,
      commonSizes: options.commonSizes || [32, 64, 128, 256, 512, 1024, 2048],
      maxAge: options.maxAge || 300000, // 5 minutes
      ...options
    };

    this.pools = new Map(); // Size -> Array of buffers
    this.stats = {
      allocations: 0,
      deallocations: 0,
      poolHits: 0,
      poolMisses: 0
    };

    // Initialize pools for common sizes
    for (const size of this.options.commonSizes) {
      this.pools.set(size, []);
    }
  }

  /**
   * Allocate secure memory from the pool
   * 
   * @param {number} size - Size of memory to allocate
   * @returns {SecureMemory} Secure memory instance
   */
  allocate(size) {
    this.stats.allocations++;
    
    const poolSize = this._findPoolSize(size);
    const pool = this.getPool(poolSize);

    // Try to reuse from pool
    if (pool.length > 0) {
      const memory = pool.pop();
      this.stats.poolHits++;
      
      // Reset the memory
      memory.secureWipe();
      memory.isDestroyed = false;
      memory.accessCount = 0;
      memory.createdAt = Date.now();
      memory.lastAccessed = memory.createdAt;
      
      // Re-register
      SecureMemory.registry.add(memory);
      
      return memory;
    }

    // Allocate new memory
    this.stats.poolMisses++;
    return new SecureMemory(poolSize);
  }

  /**
   * Return secure memory to the pool
   * 
   * @param {SecureMemory} memory - Memory to return to pool
   */
  deallocate(memory) {
    if (!memory || memory.isDestroyed) {
      return;
    }

    this.stats.deallocations++;
    
    const pool = this.getPool(memory.size);
    
    // Check if pool has space and memory is not too old
    const age = Date.now() - memory.createdAt;
    if (pool.length < this.options.maxPoolSize && age < this.options.maxAge) {
      // Secure wipe before returning to pool
      memory.secureWipe();
      memory.isDestroyed = true;
      
      // Remove from registry
      SecureMemory.registry.delete(memory);
      
      // Add to pool
      pool.push(memory);
    } else {
      // Destroy if pool is full or memory is too old
      memory.destroy();
    }
  }

  /**
   * Get pool for a specific size
   * 
   * @param {number} size - Memory size
   * @returns {Array} Pool array
   */
  getPool(size) {
    if (!this.pools.has(size)) {
      this.pools.set(size, []);
    }
    return this.pools.get(size);
  }

  /**
   * Find appropriate pool size for requested size
   * 
   * @private
   * @param {number} requestedSize - Requested memory size
   * @returns {number} Pool size to use
   */
  _findPoolSize(requestedSize) {
    // Find the smallest common size that fits the request
    for (const size of this.options.commonSizes) {
      if (size >= requestedSize) {
        return size;
      }
    }
    
    // If no common size fits, use the requested size
    return requestedSize;
  }

  /**
   * Clean up expired memory from pools
   */
  cleanup() {
    const now = Date.now();
    
    for (const [size, pool] of this.pools) {
      const validMemory = pool.filter(memory => {
        const age = now - memory.createdAt;
        if (age > this.options.maxAge) {
          memory.destroy();
          return false;
        }
        return true;
      });
      
      this.pools.set(size, validMemory);
    }
  }

  /**
   * Get pool statistics
   * 
   * @returns {Object} Pool statistics
   */
  getStats() {
    const poolSizes = {};
    for (const [size, pool] of this.pools) {
      poolSizes[size] = pool.length;
    }

    return {
      ...this.stats,
      hitRate: this.stats.allocations > 0 ? this.stats.poolHits / this.stats.allocations : 0,
      poolSizes,
      totalPooledMemory: Array.from(this.pools.values()).reduce((sum, pool) => sum + pool.length, 0)
    };
  }

  /**
   * Destroy all pooled memory
   */
  destroy() {
    for (const pool of this.pools.values()) {
      for (const memory of pool) {
        memory.destroy();
      }
    }
    this.pools.clear();
  }
}

// Global secure memory pool instance
export const globalSecureMemoryPool = new SecureMemoryPool();

// Cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    SecureMemory.cleanupAll();
    globalSecureMemoryPool.destroy();
  });
}

// Periodic cleanup
if (typeof setInterval !== 'undefined') {
  setInterval(() => {
    globalSecureMemoryPool.cleanup();
  }, 60000); // Every minute
}
