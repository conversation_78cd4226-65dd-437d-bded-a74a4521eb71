/**
 * Test suite for OTR Version Negotiation
 * 
 * Comprehensive tests for version negotiation, query message handling,
 * protocol selection, and security validation.
 */

import { VersionNegotiation, ProtocolVersionFactory } from '../../src/core/protocol/version-negotiation.js';
import { SecurityValidationError } from '../../src/core/security/validation.js';

describe('VersionNegotiation', () => {
  describe('negotiateVersion', () => {
    test('should negotiate highest common version', () => {
      const theirVersions = [2, 3];
      const result = VersionNegotiation.negotiateVersion(theirVersions);
      
      expect(result.negotiationSuccess).toBe(true);
      expect(result.version).toBe(3);
      expect(result.commonVersions).toEqual([2, 3]);
      expect(result.securityLevel).toBe('high');
    });

    test('should negotiate v2 when only v2 is common', () => {
      const theirVersions = [2];
      const ourPolicy = {
        ...VersionNegotiation.DEFAULT_POLICY,
        [VersionNegotiation.POLICIES.ALLOW_V3]: false
      };
      
      const result = VersionNegotiation.negotiateVersion(theirVersions, ourPolicy);
      
      expect(result.negotiationSuccess).toBe(true);
      expect(result.version).toBe(2);
      expect(result.securityLevel).toBe('medium');
    });

    test('should fail when no common versions exist', () => {
      const theirVersions = [1]; // Unsupported version
      const result = VersionNegotiation.negotiateVersion(theirVersions);
      
      expect(result.negotiationSuccess).toBe(false);
      expect(result.version).toBeNull();
      expect(result.error).toContain('No compatible OTR versions');
    });

    test('should respect policy restrictions', () => {
      const theirVersions = [2, 3];
      const ourPolicy = {
        ...VersionNegotiation.DEFAULT_POLICY,
        [VersionNegotiation.POLICIES.ALLOW_V2]: false
      };
      
      const result = VersionNegotiation.negotiateVersion(theirVersions, ourPolicy);
      
      expect(result.negotiationSuccess).toBe(true);
      expect(result.version).toBe(3);
    });

    test('should handle invalid input gracefully', () => {
      const result = VersionNegotiation.negotiateVersion([]);
      
      expect(result.negotiationSuccess).toBe(false);
      expect(result.error).toContain('Invalid version list');
    });

    test('should include correct capabilities for negotiated version', () => {
      const theirVersions = [3];
      const result = VersionNegotiation.negotiateVersion(theirVersions);
      
      expect(result.capabilities).toEqual({
        instanceTags: true,
        extraSymmetricKey: true,
        smpV1: true,
        fragmentationV2: true,
        smpV2: true
      });
    });
  });

  describe('createQueryMessage', () => {
    test('should create v23 query for both versions enabled', () => {
      const account = '<EMAIL>';
      const result = VersionNegotiation.createQueryMessage(account);
      
      expect(result.message).toBe('?OTRv23? <EMAIL>');
      expect(result.versions).toEqual([2, 3]);
      expect(result.account).toBe(account);
    });

    test('should create v3-only query when v2 disabled', () => {
      const account = '<EMAIL>';
      const policy = {
        ...VersionNegotiation.DEFAULT_POLICY,
        [VersionNegotiation.POLICIES.ALLOW_V2]: false
      };
      
      const result = VersionNegotiation.createQueryMessage(account, policy);
      
      expect(result.message).toBe('?OTRv3? <EMAIL>');
      expect(result.versions).toEqual([3]);
    });

    test('should create v2-only query when v3 disabled', () => {
      const account = '<EMAIL>';
      const policy = {
        ...VersionNegotiation.DEFAULT_POLICY,
        [VersionNegotiation.POLICIES.ALLOW_V3]: false
      };
      
      const result = VersionNegotiation.createQueryMessage(account, policy);
      
      expect(result.message).toBe('?OTRv2? <EMAIL>');
      expect(result.versions).toEqual([2]);
    });

    test('should reject invalid account', () => {
      expect(() => VersionNegotiation.createQueryMessage(''))
        .toThrow(SecurityValidationError);
      expect(() => VersionNegotiation.createQueryMessage(null))
        .toThrow(SecurityValidationError);
    });

    test('should fail when no versions enabled', () => {
      const policy = {
        ...VersionNegotiation.DEFAULT_POLICY,
        [VersionNegotiation.POLICIES.ALLOW_V2]: false,
        [VersionNegotiation.POLICIES.ALLOW_V3]: false
      };
      
      expect(() => VersionNegotiation.createQueryMessage('<EMAIL>', policy))
        .toThrow(SecurityValidationError);
    });
  });

  describe('parseVersions', () => {
    test('should parse v23 query message', () => {
      const message = '?OTRv23? <EMAIL>';
      const result = VersionNegotiation.parseVersions(message);
      
      expect(result.parseSuccess).toBe(true);
      expect(result.versions).toEqual([2, 3]);
      expect(result.account).toBe('<EMAIL>');
    });

    test('should parse v3-only query message', () => {
      const message = '?OTRv3? <EMAIL>';
      const result = VersionNegotiation.parseVersions(message);
      
      expect(result.parseSuccess).toBe(true);
      expect(result.versions).toEqual([3]);
      expect(result.account).toBe('<EMAIL>');
    });

    test('should parse v2-only query message', () => {
      const message = '?OTRv2? <EMAIL>';
      const result = VersionNegotiation.parseVersions(message);
      
      expect(result.parseSuccess).toBe(true);
      expect(result.versions).toEqual([2]);
      expect(result.account).toBe('<EMAIL>');
    });

    test('should parse generic query message', () => {
      const message = '?OTR? <EMAIL>';
      const result = VersionNegotiation.parseVersions(message);
      
      expect(result.parseSuccess).toBe(true);
      expect(result.versions).toEqual([2, 3]);
      expect(result.account).toBe('<EMAIL>');
    });

    test('should handle query without account', () => {
      const message = '?OTRv3?';
      const result = VersionNegotiation.parseVersions(message);
      
      expect(result.parseSuccess).toBe(true);
      expect(result.versions).toEqual([3]);
      expect(result.account).toBeNull();
    });

    test('should handle unrecognized query format', () => {
      const message = 'invalid query';
      const result = VersionNegotiation.parseVersions(message);
      
      expect(result.parseSuccess).toBe(false);
      expect(result.versions).toEqual([]);
      expect(result.error).toContain('Unrecognized query format');
    });

    test('should handle invalid input types', () => {
      const result = VersionNegotiation.parseVersions(null);
      
      expect(result.parseSuccess).toBe(false);
      expect(result.error).toContain('Invalid message format');
    });
  });

  describe('validateVersionCompatibility', () => {
    test('should validate v3 supports all features', () => {
      const features = ['instanceTags', 'extraSymmetricKey', 'smpV1'];
      const result = VersionNegotiation.validateVersionCompatibility(3, features);
      
      expect(result).toBe(true);
    });

    test('should validate v2 lacks some features', () => {
      const features = ['instanceTags']; // Not supported in v2
      const result = VersionNegotiation.validateVersionCompatibility(2, features);
      
      expect(result).toBe(false);
    });

    test('should validate v2 supports basic features', () => {
      const features = ['smpV1', 'fragmentationV2'];
      const result = VersionNegotiation.validateVersionCompatibility(2, features);
      
      expect(result).toBe(true);
    });

    test('should reject unsupported versions', () => {
      expect(() => VersionNegotiation.validateVersionCompatibility(1, []))
        .toThrow(SecurityValidationError);
      expect(() => VersionNegotiation.validateVersionCompatibility(4, []))
        .toThrow(SecurityValidationError);
    });

    test('should handle empty feature list', () => {
      const result = VersionNegotiation.validateVersionCompatibility(2, []);
      expect(result).toBe(true);
    });
  });

  describe('checkVersionDowngrade', () => {
    test('should detect no downgrade when using highest version', () => {
      const result = VersionNegotiation.checkVersionDowngrade(3, [2, 3], VersionNegotiation.DEFAULT_POLICY);
      
      expect(result.isDowngrade).toBe(false);
      expect(result.isAllowed).toBe(true);
      expect(result.securityImpact).toBe('none');
    });

    test('should detect allowed downgrade', () => {
      const policy = {
        ...VersionNegotiation.DEFAULT_POLICY,
        [VersionNegotiation.POLICIES.ALLOW_V2]: true
      };
      
      const result = VersionNegotiation.checkVersionDowngrade(2, [2, 3], policy);
      
      expect(result.isDowngrade).toBe(true);
      expect(result.isAllowed).toBe(true);
      expect(result.fromVersion).toBe(3);
      expect(result.toVersion).toBe(2);
      expect(result.securityImpact).toBe('medium');
    });

    test('should reject disallowed downgrade', () => {
      const policy = {
        ...VersionNegotiation.DEFAULT_POLICY,
        [VersionNegotiation.POLICIES.ALLOW_V2]: false
      };
      
      expect(() => VersionNegotiation.checkVersionDowngrade(2, [2, 3], policy))
        .toThrow(SecurityValidationError);
    });

    test('should reject downgrade when encryption required', () => {
      const policy = {
        ...VersionNegotiation.DEFAULT_POLICY,
        [VersionNegotiation.POLICIES.REQUIRE_ENCRYPTION]: true
      };
      
      expect(() => VersionNegotiation.checkVersionDowngrade(1, [1, 3], policy))
        .toThrow(SecurityValidationError);
    });
  });

  describe('version capabilities', () => {
    test('should have correct v2 capabilities', () => {
      const capabilities = VersionNegotiation.VERSION_CAPABILITIES[2];
      
      expect(capabilities.instanceTags).toBe(false);
      expect(capabilities.extraSymmetricKey).toBe(false);
      expect(capabilities.smpV1).toBe(true);
      expect(capabilities.fragmentationV2).toBe(true);
    });

    test('should have correct v3 capabilities', () => {
      const capabilities = VersionNegotiation.VERSION_CAPABILITIES[3];
      
      expect(capabilities.instanceTags).toBe(true);
      expect(capabilities.extraSymmetricKey).toBe(true);
      expect(capabilities.smpV1).toBe(true);
      expect(capabilities.fragmentationV2).toBe(true);
      expect(capabilities.smpV2).toBe(true);
    });
  });

  describe('policy handling', () => {
    test('should use default policy when none provided', () => {
      const theirVersions = [2, 3];
      const result = VersionNegotiation.negotiateVersion(theirVersions);
      
      expect(result.negotiationSuccess).toBe(true);
      expect(result.version).toBe(3); // Should prefer v3 by default
    });

    test('should respect custom policy settings', () => {
      const customPolicy = {
        [VersionNegotiation.POLICIES.REQUIRE_ENCRYPTION]: true,
        [VersionNegotiation.POLICIES.ALLOW_V2]: false,
        [VersionNegotiation.POLICIES.ALLOW_V3]: true,
        [VersionNegotiation.POLICIES.PREFER_V3]: true
      };
      
      const theirVersions = [2, 3];
      const result = VersionNegotiation.negotiateVersion(theirVersions, customPolicy);
      
      expect(result.negotiationSuccess).toBe(true);
      expect(result.version).toBe(3);
    });
  });
});

describe('ProtocolVersionFactory', () => {
  describe('createProtocolHandler', () => {
    test('should create v2 protocol handler', () => {
      const handler = ProtocolVersionFactory.createProtocolHandler(2);
      
      expect(handler.version).toBe(2);
      expect(handler.capabilities).toEqual(VersionNegotiation.VERSION_CAPABILITIES[2]);
    });

    test('should create v3 protocol handler', () => {
      const handler = ProtocolVersionFactory.createProtocolHandler(3);
      
      expect(handler.version).toBe(3);
      expect(handler.capabilities).toEqual(VersionNegotiation.VERSION_CAPABILITIES[3]);
    });

    test('should reject unsupported versions', () => {
      expect(() => ProtocolVersionFactory.createProtocolHandler(1))
        .toThrow(SecurityValidationError);
      expect(() => ProtocolVersionFactory.createProtocolHandler(4))
        .toThrow(SecurityValidationError);
    });

    test('should accept options parameter', () => {
      const options = { debug: true };
      const handler = ProtocolVersionFactory.createProtocolHandler(3, options);
      
      expect(handler.version).toBe(3);
    });
  });
});

describe('Integration Tests', () => {
  test('should handle complete version negotiation workflow', () => {
    // Step 1: Create query message
    const account = '<EMAIL>';
    const queryResult = VersionNegotiation.createQueryMessage(account);
    
    expect(queryResult.message).toBe('?OTRv23? <EMAIL>');
    
    // Step 2: Parse query message (simulating remote party)
    const parseResult = VersionNegotiation.parseVersions(queryResult.message);
    
    expect(parseResult.parseSuccess).toBe(true);
    expect(parseResult.versions).toEqual([2, 3]);
    
    // Step 3: Negotiate version
    const negotiationResult = VersionNegotiation.negotiateVersion(parseResult.versions);
    
    expect(negotiationResult.negotiationSuccess).toBe(true);
    expect(negotiationResult.version).toBe(3);
    
    // Step 4: Validate compatibility
    const isCompatible = VersionNegotiation.validateVersionCompatibility(
      negotiationResult.version,
      ['instanceTags', 'smpV1']
    );
    
    expect(isCompatible).toBe(true);
    
    // Step 5: Check for downgrade attacks
    const downgradeResult = VersionNegotiation.checkVersionDowngrade(
      negotiationResult.version,
      parseResult.versions,
      VersionNegotiation.DEFAULT_POLICY
    );
    
    expect(downgradeResult.isDowngrade).toBe(false);
    expect(downgradeResult.isAllowed).toBe(true);
  });

  test('should handle version negotiation with policy restrictions', () => {
    const restrictivePolicy = {
      [VersionNegotiation.POLICIES.REQUIRE_ENCRYPTION]: true,
      [VersionNegotiation.POLICIES.ALLOW_V2]: false,
      [VersionNegotiation.POLICIES.ALLOW_V3]: true,
      [VersionNegotiation.POLICIES.PREFER_V3]: true
    };
    
    // Create query with restrictive policy
    const queryResult = VersionNegotiation.createQueryMessage('<EMAIL>', restrictivePolicy);
    expect(queryResult.message).toBe('?OTRv3? <EMAIL>');
    
    // Negotiate with mixed versions
    const negotiationResult = VersionNegotiation.negotiateVersion([2, 3], restrictivePolicy);
    expect(negotiationResult.version).toBe(3);
    
    // Verify downgrade protection
    expect(() => VersionNegotiation.checkVersionDowngrade(2, [2, 3], restrictivePolicy))
      .toThrow(SecurityValidationError);
  });
});
