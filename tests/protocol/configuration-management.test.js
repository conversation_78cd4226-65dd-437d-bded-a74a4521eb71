/**
 * Test suite for Configuration Management System
 * 
 * Comprehensive tests for Policy Manager, Configuration Store,
 * and Configuration Validator integration and functionality.
 */

import { 
  PolicyManager, 
  POLICY_CATEGORY, 
  VALIDATION_LEVEL, 
  ACCESS_LEVEL 
} from '../../src/core/protocol/policy-manager.js';

import { 
  ConfigurationStore, 
  STORAGE_BACKEND, 
  CONFIG_LAYER, 
  ENCRYPTION_MODE 
} from '../../src/core/protocol/configuration-store.js';

import { 
  ConfigurationValidator, 
  VALIDATION_SEVERITY, 
  VALIDATION_RULE_TYPE 
} from '../../src/core/protocol/configuration-validator.js';

describe('Configuration Management System', () => {
  let policyManager;
  let configStore;
  let configValidator;

  beforeEach(() => {
    policyManager = new PolicyManager({
      currentUser: 'test-user',
      userRoles: ['user', 'admin'],
      validationLevel: VALIDATION_LEVEL.STRICT
    });

    configStore = new ConfigurationStore({
      backend: STORAGE_BACKEND.MEMORY,
      encryptionMode: ENCRYPTION_MODE.NONE // Disable for testing
    });

    configValidator = new ConfigurationValidator({
      strictMode: true,
      enablePerformanceValidation: true,
      enableSecurityValidation: true
    });
  });

  afterEach(() => {
    policyManager.destroy();
    configStore.destroy();
  });

  describe('Policy Manager Integration', () => {
    test('should integrate with configuration store', async () => {
      // Set policies through policy manager
      policyManager.setPolicy('security.requireEncryption', true);
      policyManager.setPolicy('protocol.maxBufferSize', 150);

      // Get effective policy configuration
      const effectivePolicy = policyManager.getEffectivePolicy();

      // Store configuration in configuration store
      await configStore.setConfig(CONFIG_LAYER.USER, 'security', effectivePolicy.security);
      await configStore.setConfig(CONFIG_LAYER.USER, 'protocol', effectivePolicy.protocol);

      // Retrieve and verify
      const storedSecurity = await configStore.getConfig('security');
      const storedProtocol = await configStore.getConfig('protocol');

      expect(storedSecurity.requireEncryption).toBe(true);
      expect(storedProtocol.maxBufferSize).toBe(150);
    });

    test('should validate policies through configuration validator', () => {
      // Test valid policy
      const validResult = configValidator.validateValue(
        'security', 
        'requireEncryption', 
        true
      );
      expect(validResult.valid).toBe(true);

      // Test invalid policy
      const invalidResult = configValidator.validateValue(
        'security', 
        'sessionTimeoutMs', 
        10000 // Below minimum
      );
      expect(invalidResult.valid).toBe(false);
      expect(invalidResult.issues[0].severity).toBe(VALIDATION_SEVERITY.ERROR);
    });

    test('should handle policy change validation', () => {
      const validation = policyManager.validatePolicyChange(
        'security.requireEncryption',
        true,
        false
      );

      expect(validation.isValid).toBe(true);
      expect(validation.securityImpact).toBe('high');
      expect(validation.warnings.length).toBeGreaterThan(0);
    });
  });

  describe('Configuration Store', () => {
    test('should handle layered configuration', async () => {
      // Set configuration in different layers
      await configStore.setConfig(CONFIG_LAYER.SYSTEM, 'timeout', 300000);
      await configStore.setConfig(CONFIG_LAYER.USER, 'timeout', 600000);
      await configStore.setConfig(CONFIG_LAYER.RUNTIME, 'timeout', 900000);

      // Should resolve to highest priority layer (runtime)
      const resolvedValue = await configStore.getConfig('timeout');
      expect(resolvedValue).toBe(900000);

      // Remove runtime layer
      await configStore.removeConfig(CONFIG_LAYER.RUNTIME, 'timeout');

      // Should now resolve to user layer
      const newResolvedValue = await configStore.getConfig('timeout');
      expect(newResolvedValue).toBe(600000);
    });

    test('should handle configuration export and import', async () => {
      // Set up test configuration
      await configStore.setConfig(CONFIG_LAYER.USER, 'security.requireEncryption', true);
      await configStore.setConfig(CONFIG_LAYER.USER, 'protocol.maxBufferSize', 200);

      // Export configuration
      const exportedConfig = await configStore.exportConfig({
        includeLayers: [CONFIG_LAYER.USER],
        includeMetadata: true,
        encrypt: false
      });

      expect(exportedConfig.layers[CONFIG_LAYER.USER]).toBeDefined();
      expect(exportedConfig.layers[CONFIG_LAYER.USER].config['security.requireEncryption']).toBe(true);

      // Clear store and import
      await configStore.clearLayer(CONFIG_LAYER.USER);
      const importResult = await configStore.importConfig(exportedConfig);

      expect(importResult).toBe(true);

      // Verify imported data
      const importedValue = await configStore.getConfig('security.requireEncryption');
      expect(importedValue).toBe(true);
    });

    test('should handle configuration caching', async () => {
      // Set configuration
      await configStore.setConfig(CONFIG_LAYER.USER, 'testKey', 'testValue');

      // First access should miss cache
      const stats1 = configStore.getStats();
      const value1 = await configStore.getConfig('testKey');
      const stats2 = configStore.getStats();

      expect(value1).toBe('testValue');
      expect(stats2.reads).toBe(stats1.reads + 1);

      // Second access should hit cache (if caching is enabled)
      const value2 = await configStore.getConfig('testKey', { useCache: true });
      expect(value2).toBe('testValue');
    });
  });

  describe('Configuration Validator', () => {
    test('should validate individual configuration values', () => {
      // Valid values
      expect(configValidator.validateValue('security', 'requireEncryption', true).valid).toBe(true);
      expect(configValidator.validateValue('protocol', 'maxBufferSize', 100).valid).toBe(true);
      expect(configValidator.validateValue('logging', 'logLevel', 'info').valid).toBe(true);

      // Invalid values
      expect(configValidator.validateValue('security', 'sessionTimeoutMs', 'invalid').valid).toBe(false);
      expect(configValidator.validateValue('protocol', 'maxBufferSize', -1).valid).toBe(false);
      expect(configValidator.validateValue('logging', 'logLevel', 'invalid').valid).toBe(false);
    });

    test('should validate complete configuration', () => {
      const config = {
        security: {
          requireEncryption: true,
          allowV2: true,
          allowV3: true,
          sessionTimeoutMs: 300000
        },
        protocol: {
          maxBufferSize: 100,
          maxGapSize: 10,
          replayWindowSize: 64
        },
        performance: {
          enableOptimizations: true,
          maxConcurrentSessions: 10
        }
      };

      const result = configValidator.validateConfiguration(config);

      expect(result.valid).toBe(true);
      expect(result.summary.totalFields).toBeGreaterThan(0);
      expect(result.summary.errorFields).toBe(0);
    });

    test('should detect configuration dependency violations', () => {
      const config = {
        security: {
          allowV2: false,
          allowV3: false // Both versions disabled - should fail dependency check
        },
        protocol: {
          maxBufferSize: 20,
          maxGapSize: 15 // Gap size larger than half buffer size - should fail
        }
      };

      const result = configValidator.validateConfiguration(config);

      expect(result.valid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    test('should analyze performance impact', () => {
      const config = {
        performance: {
          maxConcurrentSessions: 50, // High session count
          memoryPoolSize: 50 * 1024 * 1024 // 50MB
        },
        protocol: {
          maxBufferSize: 500 // Large buffer
        },
        logging: {
          enableDetailedLogging: true // Performance impact
        }
      };

      const result = configValidator.validateConfiguration(config, {
        includePerformanceAnalysis: true
      });

      expect(result.performanceAnalysis).toBeDefined();
      expect(result.performanceAnalysis.overallImpact).toBe('high');
      expect(result.performanceAnalysis.recommendations.length).toBeGreaterThan(0);
    });

    test('should analyze security impact', () => {
      const config = {
        security: {
          requireEncryption: false // Security risk
        },
        logging: {
          enableDetailedLogging: true // Potential information disclosure
        }
      };

      const result = configValidator.validateConfiguration(config, {
        includeSecurityAnalysis: true
      });

      expect(result.securityAnalysis).toBeDefined();
      expect(result.securityAnalysis.overallSecurity).toBe('none');
      expect(result.securityAnalysis.risks.length).toBeGreaterThan(0);
    });

    test('should validate configuration changes', () => {
      const currentConfig = {
        security: {
          requireEncryption: true,
          sessionTimeoutMs: 300000
        }
      };

      const newConfig = {
        security: {
          requireEncryption: false, // Security change
          sessionTimeoutMs: 600000  // Performance change
        }
      };

      const changeAnalysis = configValidator.validateConfigurationChange(
        currentConfig, 
        newConfig
      );

      expect(changeAnalysis.changes.length).toBe(2);
      expect(changeAnalysis.impacts.security.length).toBeGreaterThan(0);
    });

    test('should handle custom validation rules', () => {
      // Register custom validator
      configValidator.registerCustomValidator('powerOfTwo', (value) => {
        return {
          valid: (value & (value - 1)) === 0,
          message: 'Value must be a power of 2'
        };
      });

      // Test with custom schema
      configValidator.registerSchema('custom', {
        testField: {
          type: 'number',
          validation: {
            custom: configValidator.customValidators.get('powerOfTwo')
          }
        }
      });

      // Valid power of 2
      expect(configValidator.validateValue('custom', 'testField', 64).valid).toBe(true);

      // Invalid (not power of 2)
      expect(configValidator.validateValue('custom', 'testField', 63).valid).toBe(false);
    });
  });

  describe('Integrated Configuration Management', () => {
    test('should provide end-to-end configuration management', async () => {
      // 1. Set policies through policy manager
      policyManager.setPolicy('security.requireEncryption', true);
      policyManager.setPolicy('protocol.maxBufferSize', 150);

      // 2. Get effective configuration
      const effectiveConfig = policyManager.getEffectivePolicy();

      // 3. Validate configuration
      const validationResult = configValidator.validateConfiguration(effectiveConfig);
      expect(validationResult.valid).toBe(true);

      // 4. Store validated configuration
      for (const [category, config] of Object.entries(effectiveConfig)) {
        await configStore.setConfig(CONFIG_LAYER.USER, category, config);
      }

      // 5. Retrieve and verify stored configuration
      const completeConfig = await configStore.getCompleteConfig({
        includeMetadata: true,
        layerInfo: true
      });

      expect(completeConfig.config.security.requireEncryption).toBe(true);
      expect(completeConfig.config.protocol.maxBufferSize).toBe(150);
      expect(completeConfig.layers['security.requireEncryption']).toBe(CONFIG_LAYER.USER);
    });

    test('should handle configuration updates with validation', async () => {
      // Initial configuration
      await configStore.setConfig(CONFIG_LAYER.USER, 'security.sessionTimeoutMs', 300000);

      // Attempt invalid update
      const invalidValidation = configValidator.validateValue(
        'security', 
        'sessionTimeoutMs', 
        10000 // Below minimum
      );

      expect(invalidValidation.valid).toBe(false);

      // Valid update
      const validValidation = configValidator.validateValue(
        'security', 
        'sessionTimeoutMs', 
        600000
      );

      expect(validValidation.valid).toBe(true);

      // Apply valid update
      await configStore.setConfig(CONFIG_LAYER.USER, 'security.sessionTimeoutMs', 600000);

      const updatedValue = await configStore.getConfig('security.sessionTimeoutMs');
      expect(updatedValue).toBe(600000);
    });

    test('should provide comprehensive statistics', () => {
      // Get statistics from all components
      const policyStats = policyManager.getStats();
      const storeStats = configStore.getStats();
      const validatorStats = configValidator.getStats();

      expect(policyStats).toHaveProperty('totalPolicies');
      expect(storeStats).toHaveProperty('layers');
      expect(validatorStats).toHaveProperty('validationsPerformed');

      // Verify statistics are being tracked
      expect(typeof policyStats.totalPolicies).toBe('number');
      expect(typeof storeStats.layers).toBe('number');
      expect(typeof validatorStats.validationsPerformed).toBe('number');
    });
  });

  describe('Error Handling and Recovery', () => {
    test('should handle storage backend failures gracefully', async () => {
      // Create store with failing backend
      const failingStore = new ConfigurationStore({
        backend: STORAGE_BACKEND.CUSTOM,
        customBackend: {
          initialize: async () => { throw new Error('Backend failure'); },
          getItem: async () => { throw new Error('Read failure'); },
          setItem: async () => { throw new Error('Write failure'); }
        }
      });

      // Should handle failures gracefully
      const result = await failingStore.setConfig(CONFIG_LAYER.USER, 'test', 'value');
      expect(result).toBe(false);

      failingStore.destroy();
    });

    test('should handle validation errors gracefully', () => {
      // Test with malformed schema
      configValidator.registerSchema('malformed', {
        testField: {
          validation: {
            custom: () => { throw new Error('Validation error'); }
          }
        }
      });

      const result = configValidator.validateValue('malformed', 'testField', 'value');
      expect(result.valid).toBe(false);
      expect(result.issues[0].message).toContain('Validation error');
    });

    test('should handle policy manager errors gracefully', () => {
      // Test with invalid policy key
      const result = policyManager.setPolicy('invalid-key', 'value');
      expect(result).toBe(false);

      // Test with access denied
      const restrictedPM = new PolicyManager({
        currentUser: 'user',
        userRoles: [], // No roles
        enableAccessControl: true
      });

      const restrictedResult = restrictedPM.setPolicy('security.requireEncryption', false);
      expect(restrictedResult).toBe(false);

      restrictedPM.destroy();
    });
  });
});
