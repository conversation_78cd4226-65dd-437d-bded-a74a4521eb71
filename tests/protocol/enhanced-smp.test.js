/**
 * Test suite for Enhanced SMP Features
 * 
 * Comprehensive tests for enhanced SMP functionality including
 * state persistence, session management, abort handling, and debugging.
 */

import { 
  EnhancedSMP, 
  SMP_ABORT_REASON, 
  ENHANCED_SMP_STATE, 
  ENHANCED_SMP_CONFIG 
} from '../../src/core/protocol/enhanced-smp.js';
import { SMP_MESSAGE_TYPE, SMP_RESULT } from '../../src/core/protocol/smp.js';
import { SecurityValidationError } from '../../src/core/security/validation.js';

describe('EnhancedSMP', () => {
  let enhancedSMP;

  beforeEach(() => {
    enhancedSMP = new EnhancedSMP({
      testing: true,
      sessionTimeoutMs: 5000,
      enableStatePersistence: true,
      enableDetailedLogging: false
    });
  });

  afterEach(() => {
    enhancedSMP.destroy();
  });

  describe('constructor', () => {
    test('should initialize with default enhanced configuration', () => {
      const smp = new EnhancedSMP();
      expect(smp.config.sessionTimeoutMs).toBe(ENHANCED_SMP_CONFIG.sessionTimeoutMs);
      expect(smp.config.enableStatePersistence).toBe(true);
      expect(smp.enhancedState).toBe(ENHANCED_SMP_STATE.IDLE);
      smp.destroy();
    });

    test('should accept custom configuration', () => {
      const config = {
        sessionTimeoutMs: 10000,
        enableStatePersistence: false,
        maxRetryAttempts: 5
      };
      const smp = new EnhancedSMP(config);
      expect(smp.config.sessionTimeoutMs).toBe(10000);
      expect(smp.config.enableStatePersistence).toBe(false);
      expect(smp.config.maxRetryAttempts).toBe(5);
      smp.destroy();
    });

    test('should initialize enhanced statistics', () => {
      expect(enhancedSMP.enhancedStats.sessionsStarted).toBe(0);
      expect(enhancedSMP.enhancedStats.sessionsCompleted).toBe(0);
      expect(enhancedSMP.enhancedStats.sessionsFailed).toBe(0);
    });
  });

  describe('initiateEnhancedSMP', () => {
    test('should initiate enhanced SMP with session information', async () => {
      const secret = 'test-secret';
      const options = { question: 'What is the answer?' };
      
      const result = await enhancedSMP.initiateEnhancedSMP(secret, options);
      
      expect(result.type).toBe(SMP_MESSAGE_TYPE.SMP1);
      expect(result.sessionId).toBeDefined();
      expect(result.timestamp).toBeDefined();
      expect(result.version).toBe('1.0');
      expect(result.capabilities).toBeDefined();
      expect(enhancedSMP.enhancedState).toBe(ENHANCED_SMP_STATE.INITIATING);
      expect(enhancedSMP.enhancedStats.sessionsStarted).toBe(1);
    });

    test('should reject invalid secret', async () => {
      await expect(enhancedSMP.initiateEnhancedSMP(null))
        .rejects.toThrow(SecurityValidationError);
      await expect(enhancedSMP.initiateEnhancedSMP(''))
        .rejects.toThrow(SecurityValidationError);
    });

    test('should prevent multiple concurrent sessions', async () => {
      await enhancedSMP.initiateEnhancedSMP('secret1');
      
      await expect(enhancedSMP.initiateEnhancedSMP('secret2'))
        .rejects.toThrow(SecurityValidationError);
    });

    test('should store secret securely', async () => {
      const secret = 'test-secret';
      await enhancedSMP.initiateEnhancedSMP(secret);
      
      expect(enhancedSMP.secureStorage.size).toBe(1);
      expect(enhancedSMP.secureStorage.has(enhancedSMP.sessionId)).toBe(true);
    });

    test('should set session timeout', async () => {
      await enhancedSMP.initiateEnhancedSMP('test-secret');
      expect(enhancedSMP.sessionTimer).toBeDefined();
    });
  });

  describe('respondToEnhancedSMP', () => {
    test('should respond to enhanced SMP with validation', async () => {
      const secret = 'test-secret';
      
      const result = await enhancedSMP.respondToEnhancedSMP(secret);
      
      expect(result.type).toBe(SMP_MESSAGE_TYPE.SMP2);
      expect(result.sessionId).toBeDefined();
      expect(result.timestamp).toBeDefined();
      expect(result.responseTime).toBeDefined();
      expect(enhancedSMP.enhancedState).toBe(ENHANCED_SMP_STATE.RESPONDING);
    });

    test('should reject invalid secret', async () => {
      await expect(enhancedSMP.respondToEnhancedSMP(null))
        .rejects.toThrow(SecurityValidationError);
    });

    test('should reject response in invalid state', async () => {
      enhancedSMP.enhancedState = ENHANCED_SMP_STATE.IN_PROGRESS;
      
      await expect(enhancedSMP.respondToEnhancedSMP('secret'))
        .rejects.toThrow(SecurityValidationError);
    });
  });

  describe('handleAbort', () => {
    test('should handle abort with reason', () => {
      const reason = SMP_ABORT_REASON.USER_ABORT;
      const context = { userAction: 'cancel' };
      
      const result = enhancedSMP.handleAbort(reason, context);
      
      expect(result.type).toBe(SMP_MESSAGE_TYPE.SMP_ABORT);
      expect(result.reason).toBe(reason);
      expect(result.sessionId).toBeDefined();
      expect(result.timestamp).toBeDefined();
      expect(result.context).toEqual(context);
      expect(enhancedSMP.enhancedState).toBe(ENHANCED_SMP_STATE.ABORTED);
      expect(enhancedSMP.enhancedStats.sessionsAborted).toBe(1);
    });

    test('should use default reason for invalid reason', () => {
      const result = enhancedSMP.handleAbort('invalid_reason');
      expect(result.reason).toBe(SMP_ABORT_REASON.USER_ABORT);
    });

    test('should sanitize sensitive context', () => {
      const context = {
        userAction: 'cancel',
        secret: 'sensitive-data',
        privateKey: 'private-key-data'
      };
      
      const result = enhancedSMP.handleAbort(SMP_ABORT_REASON.USER_ABORT, context);
      
      expect(result.context.userAction).toBe('cancel');
      expect(result.context.secret).toBeUndefined();
      expect(result.context.privateKey).toBeUndefined();
    });

    test('should clear session timeout on abort', () => {
      enhancedSMP.sessionTimer = setTimeout(() => {}, 1000);
      enhancedSMP.handleAbort();
      expect(enhancedSMP.sessionTimer).toBeNull();
    });
  });

  describe('state persistence', () => {
    test('should persist state when enabled', async () => {
      await enhancedSMP.initiateEnhancedSMP('test-secret');
      
      const persistedState = await enhancedSMP.persistState();
      
      expect(persistedState).toBeDefined();
      expect(persistedState.encrypted).toBeDefined();
      expect(persistedState.timestamp).toBeDefined();
    });

    test('should not persist state when disabled', async () => {
      enhancedSMP.config.enableStatePersistence = false;
      await enhancedSMP.initiateEnhancedSMP('test-secret');
      
      const persistedState = await enhancedSMP.persistState();
      
      expect(persistedState).toBeNull();
    });

    test('should resume from persisted state', async () => {
      // Create and persist state
      await enhancedSMP.initiateEnhancedSMP('test-secret');
      const persistedState = await enhancedSMP.persistState();
      const originalSessionId = enhancedSMP.sessionId;
      
      // Reset and resume
      enhancedSMP.reset();
      const resumed = await enhancedSMP.resumeFromState(persistedState);
      
      expect(resumed).toBe(true);
      expect(enhancedSMP.sessionId).toBe(originalSessionId);
    });

    test('should reject invalid persisted state', async () => {
      const invalidState = { invalid: 'data' };
      
      const resumed = await enhancedSMP.resumeFromState(invalidState);
      
      expect(resumed).toBe(false);
    });

    test('should reject expired state', async () => {
      // Create old state
      const oldState = {
        encrypted: btoa(JSON.stringify({
          sessionId: 'old-session',
          enhancedState: ENHANCED_SMP_STATE.IN_PROGRESS,
          timestamp: Date.now() - 400000 // 6+ minutes old
        })),
        timestamp: Date.now() - 400000
      };
      
      const resumed = await enhancedSMP.resumeFromState(oldState);
      
      expect(resumed).toBe(false);
    });
  });

  describe('session management', () => {
    test('should pause active session', async () => {
      await enhancedSMP.initiateEnhancedSMP('test-secret');
      enhancedSMP.enhancedState = ENHANCED_SMP_STATE.IN_PROGRESS;
      
      const result = enhancedSMP.pauseSession('user_request');
      
      expect(result.success).toBe(true);
      expect(enhancedSMP.enhancedState).toBe(ENHANCED_SMP_STATE.PAUSED);
      expect(enhancedSMP.sessionTimer).toBeNull();
    });

    test('should not pause session in invalid state', () => {
      enhancedSMP.enhancedState = ENHANCED_SMP_STATE.IDLE;
      
      const result = enhancedSMP.pauseSession();
      
      expect(result.success).toBe(false);
    });

    test('should resume paused session', async () => {
      await enhancedSMP.initiateEnhancedSMP('test-secret');
      enhancedSMP.enhancedState = ENHANCED_SMP_STATE.PAUSED;
      enhancedSMP.lastActivity = Date.now();
      
      const result = enhancedSMP.resumeSession();
      
      expect(result.success).toBe(true);
      expect(enhancedSMP.enhancedState).toBe(ENHANCED_SMP_STATE.IN_PROGRESS);
      expect(enhancedSMP.sessionTimer).toBeDefined();
    });

    test('should not resume session in invalid state', () => {
      enhancedSMP.enhancedState = ENHANCED_SMP_STATE.IDLE;
      
      const result = enhancedSMP.resumeSession();
      
      expect(result.success).toBe(false);
    });
  });

  describe('state validation', () => {
    test('should validate SMP state correctly', () => {
      enhancedSMP.enhancedState = ENHANCED_SMP_STATE.IDLE;
      
      expect(enhancedSMP.validateSMPState(ENHANCED_SMP_STATE.IDLE)).toBe(true);
      expect(enhancedSMP.validateSMPState(ENHANCED_SMP_STATE.INITIATING)).toBe(true);
      expect(enhancedSMP.validateSMPState(ENHANCED_SMP_STATE.COMPLETED)).toBe(false);
    });

    test('should validate state transitions', () => {
      enhancedSMP.enhancedState = ENHANCED_SMP_STATE.IN_PROGRESS;
      
      expect(enhancedSMP.validateSMPState(ENHANCED_SMP_STATE.PAUSED)).toBe(true);
      expect(enhancedSMP.validateSMPState(ENHANCED_SMP_STATE.COMPLETING)).toBe(true);
      expect(enhancedSMP.validateSMPState(ENHANCED_SMP_STATE.IDLE)).toBe(false);
    });
  });

  describe('detailed state and diagnostics', () => {
    test('should provide detailed state information', async () => {
      await enhancedSMP.initiateEnhancedSMP('test-secret');
      
      const state = enhancedSMP.getDetailedState();
      
      expect(state.baseState).toBeDefined();
      expect(state.enhancedState).toBe(ENHANCED_SMP_STATE.INITIATING);
      expect(state.sessionId).toBeDefined();
      expect(state.startTime).toBeDefined();
      expect(state.config).toBeDefined();
      expect(state.stats).toBeDefined();
      expect(state.isValid).toBe(true);
    });

    test('should generate comprehensive diagnostics', async () => {
      await enhancedSMP.initiateEnhancedSMP('test-secret');
      
      const diagnostics = enhancedSMP.generateDiagnostics();
      
      expect(diagnostics.performance).toBeDefined();
      expect(diagnostics.security).toBeDefined();
      expect(diagnostics.resources).toBeDefined();
      expect(diagnostics.recommendations).toBeDefined();
      expect(Array.isArray(diagnostics.recommendations)).toBe(true);
    });

    test('should track session capabilities', async () => {
      await enhancedSMP.initiateEnhancedSMP('test-secret');
      
      const state = enhancedSMP.getDetailedState();
      
      expect(state.canAbort).toBe(true);
      expect(state.canPause).toBe(false); // Not in IN_PROGRESS state
      expect(state.canResume).toBe(false);
    });
  });

  describe('security features', () => {
    test('should store secrets securely', async () => {
      const secret = 'sensitive-secret';
      await enhancedSMP.initiateEnhancedSMP(secret);
      
      expect(enhancedSMP.secureStorage.size).toBe(1);
      
      // Verify secret is not stored in plain text
      const sessionId = enhancedSMP.sessionId;
      const secureMemory = enhancedSMP.secureStorage.get(sessionId);
      expect(secureMemory).toBeDefined();
      expect(secureMemory.constructor.name).toBe('SecureMemory');
    });

    test('should clean up secure storage on destroy', async () => {
      await enhancedSMP.initiateEnhancedSMP('test-secret');
      expect(enhancedSMP.secureStorage.size).toBe(1);
      
      enhancedSMP.destroy();
      
      expect(enhancedSMP.secureStorage.size).toBe(0);
    });

    test('should handle security validation errors', async () => {
      enhancedSMP.config.enableSecurityValidation = true;
      
      // This should increment security violations
      try {
        await enhancedSMP.initiateEnhancedSMP(null);
      } catch (error) {
        // Expected error
      }
      
      expect(enhancedSMP.enhancedStats.securityViolations).toBeGreaterThan(0);
    });
  });

  describe('timeout handling', () => {
    test('should handle session timeout', async () => {
      const shortTimeoutSMP = new EnhancedSMP({
        testing: true,
        sessionTimeoutMs: 100
      });
      
      await shortTimeoutSMP.initiateEnhancedSMP('test-secret');
      
      // Wait for timeout
      await new Promise(resolve => setTimeout(resolve, 150));
      
      expect(shortTimeoutSMP.enhancedState).toBe(ENHANCED_SMP_STATE.ABORTED);
      expect(shortTimeoutSMP.abortReason).toBe(SMP_ABORT_REASON.TIMEOUT);
      
      shortTimeoutSMP.destroy();
    });

    test('should clear timeout on manual abort', async () => {
      await enhancedSMP.initiateEnhancedSMP('test-secret');
      const timer = enhancedSMP.sessionTimer;
      
      enhancedSMP.handleAbort();
      
      expect(enhancedSMP.sessionTimer).toBeNull();
    });
  });

  describe('statistics tracking', () => {
    test('should track session statistics', async () => {
      // Start session
      await enhancedSMP.initiateEnhancedSMP('test-secret');
      expect(enhancedSMP.enhancedStats.sessionsStarted).toBe(1);
      
      // Abort session
      enhancedSMP.handleAbort();
      expect(enhancedSMP.enhancedStats.sessionsAborted).toBe(1);
    });

    test('should calculate success rate', async () => {
      enhancedSMP.enhancedStats.sessionsCompleted = 8;
      enhancedSMP.enhancedStats.sessionsFailed = 2;
      
      const diagnostics = enhancedSMP.generateDiagnostics();
      
      expect(diagnostics.performance.successRate).toBe(80);
    });

    test('should generate recommendations based on statistics', () => {
      enhancedSMP.enhancedStats.sessionsCompleted = 1;
      enhancedSMP.enhancedStats.sessionsFailed = 9;
      enhancedSMP.enhancedStats.securityViolations = 5;
      
      const diagnostics = enhancedSMP.generateDiagnostics();
      
      expect(diagnostics.recommendations.length).toBeGreaterThan(0);
      expect(diagnostics.recommendations.some(r => r.includes('failures'))).toBe(true);
      expect(diagnostics.recommendations.some(r => r.includes('Security violations'))).toBe(true);
    });
  });

  describe('integration scenarios', () => {
    test('should handle complete enhanced SMP workflow', async () => {
      // Initiate
      const smp1 = await enhancedSMP.initiateEnhancedSMP('shared-secret', {
        question: 'What is our secret?'
      });
      expect(smp1.sessionId).toBeDefined();
      
      // Persist state
      const persistedState = await enhancedSMP.persistState();
      expect(persistedState).toBeDefined();
      
      // Pause session
      enhancedSMP.enhancedState = ENHANCED_SMP_STATE.IN_PROGRESS;
      const pauseResult = enhancedSMP.pauseSession('user_request');
      expect(pauseResult.success).toBe(true);
      
      // Resume session
      const resumeResult = enhancedSMP.resumeSession();
      expect(resumeResult.success).toBe(true);
      
      // Get diagnostics
      const diagnostics = enhancedSMP.generateDiagnostics();
      expect(diagnostics.sessionId).toBe(smp1.sessionId);
      
      // Abort
      const abortResult = enhancedSMP.handleAbort(SMP_ABORT_REASON.USER_ABORT);
      expect(abortResult.sessionId).toBe(smp1.sessionId);
    });

    test('should handle error recovery', async () => {
      // Simulate error during initiation
      const originalMethod = enhancedSMP._generateSessionId;
      enhancedSMP._generateSessionId = () => {
        throw new Error('Session ID generation failed');
      };
      
      await expect(enhancedSMP.initiateEnhancedSMP('test-secret'))
        .rejects.toThrow('Session ID generation failed');
      
      expect(enhancedSMP.enhancedStats.securityViolations).toBeGreaterThan(0);
      
      // Restore method
      enhancedSMP._generateSessionId = originalMethod;
    });
  });
});
