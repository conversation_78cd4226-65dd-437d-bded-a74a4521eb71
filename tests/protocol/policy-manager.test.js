/**
 * Test suite for Policy Manager
 * 
 * Comprehensive tests for configuration management, policy validation,
 * access control, and contextual policy application.
 */

import { 
  PolicyManager, 
  POLICY_CATEGORY, 
  VALIDATION_LEVEL, 
  ACCESS_LEVEL,
  DEFAULT_POLICY_SCHEMAS 
} from '../../src/core/protocol/policy-manager.js';
import { SecurityValidationError } from '../../src/core/security/validation.js';

describe('PolicyManager', () => {
  let policyManager;

  beforeEach(() => {
    policyManager = new PolicyManager({
      currentUser: 'test-user',
      userRoles: ['user', 'admin'],
      validationLevel: VALIDATION_LEVEL.STRICT,
      enableAccessControl: true,
      enableAuditLogging: true
    });
  });

  afterEach(() => {
    policyManager.destroy();
  });

  describe('constructor', () => {
    test('should initialize with default configuration', () => {
      const pm = new PolicyManager();
      expect(pm.options.validationLevel).toBe(VALIDATION_LEVEL.STRICT);
      expect(pm.options.enableAccessControl).toBe(true);
      expect(pm.currentUser).toBe('anonymous');
      pm.destroy();
    });

    test('should accept custom configuration', () => {
      const options = {
        currentUser: 'admin',
        userRoles: ['admin'],
        validationLevel: VALIDATION_LEVEL.ENTERPRISE,
        enableAccessControl: false
      };
      const pm = new PolicyManager(options);
      expect(pm.currentUser).toBe('admin');
      expect(pm.userRoles.has('admin')).toBe(true);
      expect(pm.options.validationLevel).toBe(VALIDATION_LEVEL.ENTERPRISE);
      expect(pm.options.enableAccessControl).toBe(false);
      pm.destroy();
    });

    test('should initialize default schemas', () => {
      expect(policyManager.policySchemas.size).toBeGreaterThan(0);
      expect(policyManager.policySchemas.has(POLICY_CATEGORY.SECURITY)).toBe(true);
      expect(policyManager.policySchemas.has(POLICY_CATEGORY.PROTOCOL)).toBe(true);
    });
  });

  describe('setPolicy', () => {
    test('should set valid policy', () => {
      const result = policyManager.setPolicy('security.requireEncryption', true);
      expect(result).toBe(true);
      expect(policyManager.getPolicy('security.requireEncryption')).toBe(true);
    });

    test('should reject invalid policy key format', () => {
      const result = policyManager.setPolicy('invalid-key', true);
      expect(result).toBe(false);
    });

    test('should validate policy values', () => {
      // Valid value
      expect(policyManager.setPolicy('security.sessionTimeoutMs', 60000)).toBe(true);
      
      // Invalid type
      expect(policyManager.setPolicy('security.sessionTimeoutMs', 'invalid')).toBe(false);
      
      // Out of range
      expect(policyManager.setPolicy('security.sessionTimeoutMs', 10000)).toBe(false); // Below minimum
    });

    test('should enforce access control', () => {
      const restrictedPM = new PolicyManager({
        currentUser: 'user',
        userRoles: ['user'], // No admin role
        enableAccessControl: true
      });
      
      // Should fail for restricted policy
      const result = restrictedPM.setPolicy('security.enableSecurityValidation', false);
      expect(result).toBe(false);
      expect(restrictedPM.stats.accessDenials).toBe(1);
      
      restrictedPM.destroy();
    });

    test('should track policy history', () => {
      policyManager.setPolicy('security.requireEncryption', true);
      policyManager.setPolicy('security.requireEncryption', false);
      
      const history = policyManager.policyHistory.get('security.requireEncryption');
      expect(history).toBeDefined();
      expect(history.length).toBe(2);
      expect(history[1].newValue).toBe(false);
    });

    test('should audit policy changes', () => {
      const initialAuditSize = policyManager.auditLog.length;
      policyManager.setPolicy('security.requireEncryption', true);
      
      expect(policyManager.auditLog.length).toBe(initialAuditSize + 1);
      const lastAudit = policyManager.auditLog[policyManager.auditLog.length - 1];
      expect(lastAudit.action).toBe('SET');
      expect(lastAudit.key).toBe('security.requireEncryption');
    });
  });

  describe('getPolicy', () => {
    test('should get existing policy', () => {
      policyManager.setPolicy('security.requireEncryption', false);
      expect(policyManager.getPolicy('security.requireEncryption')).toBe(false);
    });

    test('should return default value for unset policy', () => {
      const defaultValue = policyManager.getPolicy('security.requireEncryption');
      expect(defaultValue).toBe(true); // Default from schema
    });

    test('should respect access control', () => {
      const restrictedPM = new PolicyManager({
        currentUser: 'user',
        userRoles: [], // No roles
        enableAccessControl: true
      });
      
      restrictedPM.setPolicy('security.enableSecurityValidation', true, { system: true });
      const value = restrictedPM.getPolicy('security.enableSecurityValidation');
      expect(value).toBeUndefined(); // Access denied
      
      restrictedPM.destroy();
    });

    test('should handle contextual policies', () => {
      // Set contextual override
      policyManager.contextualPolicies.set('environment.production', {
        'security.sessionTimeoutMs': 600000
      });
      
      const context = { environment: { production: true } };
      const value = policyManager.getPolicy('security.sessionTimeoutMs', context);
      expect(value).toBe(600000);
      expect(policyManager.stats.contextualOverrides).toBe(1);
    });
  });

  describe('getEffectivePolicy', () => {
    test('should return complete policy configuration', () => {
      policyManager.setPolicy('security.requireEncryption', false);
      policyManager.setPolicy('protocol.maxBufferSize', 50);
      
      const effective = policyManager.getEffectivePolicy();
      
      expect(effective.security).toBeDefined();
      expect(effective.protocol).toBeDefined();
      expect(effective.security.requireEncryption).toBe(false);
      expect(effective.protocol.maxBufferSize).toBe(50);
    });

    test('should apply contextual overrides', () => {
      policyManager.contextualPolicies.set('user.admin', {
        'security.sessionTimeoutMs': 1800000
      });
      
      const context = { user: { admin: true } };
      const effective = policyManager.getEffectivePolicy(context);
      
      expect(effective.security.sessionTimeoutMs).toBe(1800000);
    });
  });

  describe('validatePolicy', () => {
    test('should validate correct policy values', () => {
      expect(policyManager.validatePolicy('security.requireEncryption', true)).toBe(true);
      expect(policyManager.validatePolicy('security.sessionTimeoutMs', 300000)).toBe(true);
      expect(policyManager.validatePolicy('logging.logLevel', 'info')).toBe(true);
    });

    test('should reject invalid policy values', () => {
      expect(policyManager.validatePolicy('security.requireEncryption', 'invalid')).toBe(false);
      expect(policyManager.validatePolicy('security.sessionTimeoutMs', 10000)).toBe(false); // Below min
      expect(policyManager.validatePolicy('logging.logLevel', 'invalid')).toBe(false); // Not in enum
    });

    test('should use validation cache', () => {
      // First validation
      policyManager.validatePolicy('security.requireEncryption', true);
      expect(policyManager.validationCache.size).toBe(1);
      
      // Second validation should use cache
      policyManager.validatePolicy('security.requireEncryption', true);
      expect(policyManager.validationCache.size).toBe(1);
    });

    test('should respect validation level', () => {
      const noValidationPM = new PolicyManager({
        validationLevel: VALIDATION_LEVEL.NONE
      });
      
      // Should allow any value when validation is disabled
      expect(noValidationPM.validatePolicy('security.requireEncryption', 'invalid')).toBe(true);
      
      noValidationPM.destroy();
    });
  });

  describe('validatePolicyChange', () => {
    test('should validate policy changes', () => {
      const validation = policyManager.validatePolicyChange(
        'security.requireEncryption',
        true,
        false
      );
      
      expect(validation.isValid).toBe(true);
      expect(validation.securityImpact).toBe('high');
      expect(validation.warnings.length).toBeGreaterThan(0);
    });

    test('should detect invalid changes', () => {
      const validation = policyManager.validatePolicyChange(
        'security.sessionTimeoutMs',
        300000,
        'invalid'
      );
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });

    test('should perform enterprise validation', () => {
      const enterprisePM = new PolicyManager({
        validationLevel: VALIDATION_LEVEL.ENTERPRISE,
        userRoles: ['user'] // Not admin
      });
      
      const validation = enterprisePM.validatePolicyChange(
        'security.requireEncryption',
        true,
        false
      );
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors.some(e => e.includes('admin privileges'))).toBe(true);
      
      enterprisePM.destroy();
    });
  });

  describe('contextual policies', () => {
    test('should apply contextual policies', () => {
      const basePolicies = { 'security.sessionTimeoutMs': 300000 };
      policyManager.contextualPolicies.set('environment.test', {
        'security.sessionTimeoutMs': 60000
      });
      
      const context = { environment: { test: true } };
      const applied = policyManager.applyContextualPolicies(context, basePolicies);
      
      expect(applied['security.sessionTimeoutMs']).toBe(60000);
    });

    test('should inherit policies from parent context', () => {
      policyManager.setPolicy('security.requireEncryption', true);
      policyManager.setPolicy('protocol.maxBufferSize', 100);
      
      const parentContext = {};
      const childContext = {};
      
      const inherited = policyManager.inheritPolicies(parentContext, childContext);
      
      expect(inherited.security.requireEncryption).toBe(true);
      expect(inherited.protocol.maxBufferSize).toBe(100);
    });
  });

  describe('policy updates', () => {
    test('should update policy immediately', () => {
      const result = policyManager.updatePolicy('security.requireEncryption', false, true);
      expect(result).toBe(true);
      expect(policyManager.getPolicy('security.requireEncryption')).toBe(false);
    });

    test('should queue policy updates', () => {
      const result = policyManager.updatePolicy('security.requireEncryption', false, false);
      expect(result).toBe(true);
      
      // Should be applied asynchronously
      setTimeout(() => {
        expect(policyManager.getPolicy('security.requireEncryption')).toBe(false);
      }, 10);
    });

    test('should reload policies from source', async () => {
      const source = {
        'security.requireEncryption': false,
        'protocol.maxBufferSize': 50
      };
      
      const result = await policyManager.reloadPolicies(source);
      expect(result).toBe(true);
      expect(policyManager.getPolicy('security.requireEncryption')).toBe(false);
      expect(policyManager.getPolicy('protocol.maxBufferSize')).toBe(50);
    });
  });

  describe('change listeners', () => {
    test('should notify change listeners', () => {
      let notified = false;
      let changeEvent = null;
      
      const listener = (event) => {
        notified = true;
        changeEvent = event;
      };
      
      policyManager.addChangeListener(listener);
      policyManager.setPolicy('security.requireEncryption', false);
      
      expect(notified).toBe(true);
      expect(changeEvent.key).toBe('security.requireEncryption');
      expect(changeEvent.newValue).toBe(false);
      
      policyManager.removeChangeListener(listener);
    });

    test('should handle listener errors gracefully', () => {
      const errorListener = () => {
        throw new Error('Listener error');
      };
      
      policyManager.addChangeListener(errorListener);
      
      // Should not throw
      expect(() => {
        policyManager.setPolicy('security.requireEncryption', false);
      }).not.toThrow();
      
      policyManager.removeChangeListener(errorListener);
    });
  });

  describe('import/export', () => {
    test('should export policies', () => {
      policyManager.setPolicy('security.requireEncryption', false);
      policyManager.setPolicy('protocol.maxBufferSize', 50);
      
      const exported = policyManager.exportPolicies();
      
      expect(exported.version).toBe('1.0');
      expect(exported.policies['security.requireEncryption']).toBeDefined();
      expect(exported.policies['protocol.maxBufferSize']).toBeDefined();
      expect(exported.metadata.totalPolicies).toBe(2);
    });

    test('should import policies', () => {
      const data = {
        policies: {
          'security.requireEncryption': { value: false },
          'protocol.maxBufferSize': { value: 75 }
        }
      };
      
      const result = policyManager.importPolicies(data);
      expect(result).toBe(true);
      expect(policyManager.getPolicy('security.requireEncryption')).toBe(false);
      expect(policyManager.getPolicy('protocol.maxBufferSize')).toBe(75);
    });

    test('should handle invalid import data', () => {
      const result = policyManager.importPolicies({ invalid: 'data' });
      expect(result).toBe(false);
    });
  });

  describe('reset and cleanup', () => {
    test('should reset policies to defaults', () => {
      policyManager.setPolicy('security.requireEncryption', false);
      policyManager.setPolicy('protocol.maxBufferSize', 50);
      
      policyManager.resetToDefaults([POLICY_CATEGORY.SECURITY]);
      
      expect(policyManager.getPolicy('security.requireEncryption')).toBe(true); // Default
      expect(policyManager.getPolicy('protocol.maxBufferSize')).toBe(50); // Unchanged
    });

    test('should get statistics', () => {
      policyManager.setPolicy('security.requireEncryption', false);
      policyManager.getPolicy('security.requireEncryption');
      
      const stats = policyManager.getStats();
      expect(stats.totalPolicies).toBe(1);
      expect(stats.policiesSet).toBe(1);
      expect(stats.policiesRead).toBe(1);
    });

    test('should perform cleanup', () => {
      // Fill validation cache
      for (let i = 0; i < 1500; i++) {
        policyManager.validationCache.set(`key${i}`, true);
      }
      
      expect(policyManager.validationCache.size).toBe(1500);
      
      // Trigger cleanup
      policyManager._performCleanup();
      
      expect(policyManager.validationCache.size).toBe(0);
    });
  });

  describe('error handling', () => {
    test('should handle validation errors gracefully', () => {
      // Mock validation to throw error
      const originalValidate = policyManager._validatePolicyValue;
      policyManager._validatePolicyValue = () => {
        throw new Error('Validation error');
      };
      
      const result = policyManager.setPolicy('security.requireEncryption', true);
      expect(result).toBe(false);
      
      // Restore original method
      policyManager._validatePolicyValue = originalValidate;
    });

    test('should log errors in audit log', () => {
      const initialAuditSize = policyManager.auditLog.length;
      
      // Force an error
      policyManager._handleError(new Error('Test error'), 'test', { key: 'test' });
      
      expect(policyManager.auditLog.length).toBe(initialAuditSize + 1);
      const lastAudit = policyManager.auditLog[policyManager.auditLog.length - 1];
      expect(lastAudit.action).toBe('ERROR');
    });
  });
});
