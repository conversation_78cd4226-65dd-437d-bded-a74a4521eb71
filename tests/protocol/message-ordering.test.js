/**
 * Test suite for Enhanced Message Ordering
 * 
 * Comprehensive tests for message ordering, replay protection,
 * out-of-order handling, and sequence validation.
 */

import { MessageOrdering } from '../../src/core/protocol/message-ordering.js';
import { SecurityValidationError } from '../../src/core/security/validation.js';

describe('MessageOrdering', () => {
  let messageOrdering;

  beforeEach(() => {
    messageOrdering = new MessageOrdering({
      maxBufferSize: 10,
      maxGapSize: 5,
      replayWindowSize: 8,
      gapTimeoutMs: 1000,
      cleanupIntervalMs: 5000
    });
  });

  afterEach(() => {
    messageOrdering.destroy();
  });

  describe('constructor', () => {
    test('should initialize with default configuration', () => {
      const mo = new MessageOrdering();
      expect(mo.config.maxBufferSize).toBe(100);
      expect(mo.config.maxGapSize).toBe(10);
      expect(mo.nextExpectedSequence).toBe(0);
      expect(mo.nextSendSequence).toBe(0);
      mo.destroy();
    });

    test('should accept custom configuration', () => {
      const config = { maxBufferSize: 50, maxGapSize: 3 };
      const mo = new MessageOrdering(config);
      expect(mo.config.maxBufferSize).toBe(50);
      expect(mo.config.maxGapSize).toBe(3);
      mo.destroy();
    });

    test('should initialize statistics', () => {
      const stats = messageOrdering.getStats();
      expect(stats.messagesProcessed).toBe(0);
      expect(stats.duplicatesDetected).toBe(0);
      expect(stats.gapsDetected).toBe(0);
    });
  });

  describe('validateIncomingMessage', () => {
    test('should process in-order message correctly', () => {
      const message = { sequence: 0, data: 'test' };
      const result = messageOrdering.validateIncomingMessage(message);
      
      expect(result.action).toBe('PROCESS');
      expect(result.state).toBe(MessageOrdering.MESSAGE_STATE.PROCESSED);
      expect(result.sequence).toBe(0);
      expect(messageOrdering.nextExpectedSequence).toBe(1);
    });

    test('should buffer out-of-order message', () => {
      const message = { sequence: 2, data: 'test' };
      const result = messageOrdering.validateIncomingMessage(message);
      
      expect(result.action).toBe('BUFFER');
      expect(result.state).toBe(MessageOrdering.MESSAGE_STATE.PENDING);
      expect(result.gap).toBe(2);
      expect(messageOrdering.pendingMessages.size).toBe(1);
    });

    test('should reject duplicate messages', () => {
      const message1 = { sequence: 0, data: 'test1' };
      const message2 = { sequence: 0, data: 'test2' };
      
      messageOrdering.validateIncomingMessage(message1);
      const result = messageOrdering.validateIncomingMessage(message2);
      
      expect(result.action).toBe('REJECT');
      expect(result.reason).toBe('REPLAY_DETECTED');
      expect(result.state).toBe(MessageOrdering.MESSAGE_STATE.DUPLICATE);
    });

    test('should reject messages with invalid structure', () => {
      const invalidMessage = { data: 'test' }; // Missing sequence
      const result = messageOrdering.validateIncomingMessage(invalidMessage);
      
      expect(result.action).toBe('ERROR');
      expect(result.reason).toContain('Invalid message structure');
    });

    test('should reject sequence numbers out of range', () => {
      const message = { sequence: -1, data: 'test' };
      const result = messageOrdering.validateIncomingMessage(message);
      
      expect(result.action).toBe('ERROR');
      expect(result.reason).toContain('Sequence number out of range');
    });

    test('should handle very large gaps', () => {
      const message = { sequence: 10, data: 'test' }; // Gap of 10 > maxGapSize (5)
      const result = messageOrdering.validateIncomingMessage(message);
      
      expect(result.action).toBe('ERROR');
      expect(result.reason).toContain('Message gap too large');
    });
  });

  describe('handleOutOfOrder', () => {
    test('should buffer out-of-order message', () => {
      const message = { sequence: 3, data: 'test' };
      const result = messageOrdering.handleOutOfOrder(message, 3);
      
      expect(result.action).toBe('BUFFERED');
      expect(result.pendingCount).toBe(1);
      expect(messageOrdering.pendingMessages.has(3)).toBe(true);
    });

    test('should detect duplicate buffered messages', () => {
      const message1 = { sequence: 3, data: 'test1' };
      const message2 = { sequence: 3, data: 'test2' };
      
      messageOrdering.handleOutOfOrder(message1, 3);
      const result = messageOrdering.handleOutOfOrder(message2, 3);
      
      expect(result.action).toBe('DUPLICATE');
      expect(result.reason).toBe('Already buffered');
    });

    test('should enforce buffer size limits', () => {
      // Fill buffer to capacity
      for (let i = 10; i < 20; i++) {
        messageOrdering.handleOutOfOrder({ sequence: i, data: `test${i}` }, i);
      }
      
      expect(messageOrdering.pendingMessages.size).toBe(10);
      
      // Add one more - should evict oldest
      messageOrdering.handleOutOfOrder({ sequence: 25, data: 'test25' }, 25);
      expect(messageOrdering.pendingMessages.size).toBe(10);
      expect(messageOrdering.pendingMessages.has(10)).toBe(false); // Oldest evicted
    });

    test('should return ready messages when gaps are filled', () => {
      // Buffer messages 2 and 3
      messageOrdering.handleOutOfOrder({ sequence: 2, data: 'test2' }, 2);
      messageOrdering.handleOutOfOrder({ sequence: 3, data: 'test3' }, 3);
      
      // Process message 0 and 1 to make 2 and 3 ready
      messageOrdering.validateIncomingMessage({ sequence: 0, data: 'test0' });
      const result = messageOrdering.validateIncomingMessage({ sequence: 1, data: 'test1' });
      
      expect(result.readyMessages).toHaveLength(2);
      expect(result.readyMessages[0].sequence).toBe(2);
      expect(result.readyMessages[1].sequence).toBe(3);
      expect(messageOrdering.nextExpectedSequence).toBe(4);
    });
  });

  describe('detectMessageGaps', () => {
    test('should detect no gap for consecutive messages', () => {
      const result = messageOrdering.detectMessageGaps(1, 1);
      expect(result.hasGap).toBe(false);
      expect(result.gapSize).toBe(0);
    });

    test('should detect gap in sequence', () => {
      const result = messageOrdering.detectMessageGaps(5, 2);
      expect(result.hasGap).toBe(true);
      expect(result.gapSize).toBe(3);
      expect(result.missingSequences).toEqual([2, 3, 4]);
    });

    test('should reject gaps that are too large', () => {
      expect(() => messageOrdering.detectMessageGaps(10, 2))
        .toThrow(SecurityValidationError);
    });

    test('should track detected gaps', () => {
      messageOrdering.detectMessageGaps(3, 1);
      expect(messageOrdering.detectedGaps.size).toBe(2); // Sequences 1 and 2
      expect(messageOrdering.stats.gapsDetected).toBe(2);
    });
  });

  describe('getNextSendSequence', () => {
    test('should return sequential numbers', () => {
      expect(messageOrdering.getNextSendSequence()).toBe(0);
      expect(messageOrdering.getNextSendSequence()).toBe(1);
      expect(messageOrdering.getNextSendSequence()).toBe(2);
    });

    test('should wrap around at maximum', () => {
      messageOrdering.nextSendSequence = messageOrdering.config.maxSequenceNumber;
      expect(messageOrdering.getNextSendSequence()).toBe(messageOrdering.config.maxSequenceNumber);
      expect(messageOrdering.getNextSendSequence()).toBe(0);
    });
  });

  describe('validateReplayProtection', () => {
    test('should allow new messages', () => {
      const message = { sequence: 0 };
      expect(messageOrdering.validateReplayProtection(message)).toBe(true);
    });

    test('should reject duplicate messages', () => {
      const message = { sequence: 0 };
      messageOrdering.validateIncomingMessage(message);
      expect(messageOrdering.validateReplayProtection(message)).toBe(false);
    });

    test('should reject very old messages', () => {
      // Process several messages to advance window
      for (let i = 0; i < 10; i++) {
        messageOrdering.validateIncomingMessage({ sequence: i, data: `test${i}` });
      }
      
      // Try to replay a very old message
      const oldMessage = { sequence: 0 };
      expect(messageOrdering.validateReplayProtection(oldMessage)).toBe(false);
    });

    test('should use custom window size', () => {
      const message = { sequence: 0 };
      messageOrdering.validateIncomingMessage(message);
      expect(messageOrdering.validateReplayProtection(message, 1)).toBe(false);
    });
  });

  describe('updateSequenceTracking', () => {
    test('should update tracking for in-order message', () => {
      messageOrdering.updateSequenceTracking(0);
      expect(messageOrdering.lastProcessedSequence).toBe(0);
      expect(messageOrdering.nextExpectedSequence).toBe(1);
    });

    test('should advance through buffered messages', () => {
      // Buffer messages 1 and 2
      messageOrdering.pendingMessages.set(1, { message: { sequence: 1 }, timestamp: Date.now() });
      messageOrdering.pendingMessages.set(2, { message: { sequence: 2 }, timestamp: Date.now() });
      
      // Process message 0
      messageOrdering.updateSequenceTracking(0);
      expect(messageOrdering.nextExpectedSequence).toBe(3); // Should advance through buffered messages
    });

    test('should update replay window', () => {
      messageOrdering.updateSequenceTracking(0);
      expect(messageOrdering.replayWindow.has(0)).toBe(true);
    });

    test('should clean up processed messages from buffers', () => {
      messageOrdering.pendingMessages.set(0, { message: { sequence: 0 }, timestamp: Date.now() });
      messageOrdering.detectedGaps.set(0, { timestamp: Date.now() });
      
      messageOrdering.updateSequenceTracking(0);
      expect(messageOrdering.pendingMessages.has(0)).toBe(false);
      expect(messageOrdering.detectedGaps.has(0)).toBe(false);
    });
  });

  describe('statistics', () => {
    test('should track message processing statistics', () => {
      messageOrdering.validateIncomingMessage({ sequence: 0, data: 'test' });
      
      const stats = messageOrdering.getStats();
      expect(stats.messagesProcessed).toBe(1);
      expect(stats.nextExpectedSequence).toBe(1);
      expect(stats.lastProcessedSequence).toBe(0);
    });

    test('should track duplicate detection', () => {
      const message = { sequence: 0, data: 'test' };
      messageOrdering.validateIncomingMessage(message);
      messageOrdering.validateIncomingMessage(message); // Duplicate
      
      const stats = messageOrdering.getStats();
      expect(stats.duplicatesDetected).toBe(1);
    });

    test('should track gap detection', () => {
      messageOrdering.detectMessageGaps(3, 1);
      
      const stats = messageOrdering.getStats();
      expect(stats.gapsDetected).toBe(2);
    });

    test('should track message reordering', () => {
      // Buffer out-of-order message
      messageOrdering.validateIncomingMessage({ sequence: 2, data: 'test2' });
      
      // Process earlier messages to trigger reordering
      messageOrdering.validateIncomingMessage({ sequence: 0, data: 'test0' });
      messageOrdering.validateIncomingMessage({ sequence: 1, data: 'test1' });
      
      const stats = messageOrdering.getStats();
      expect(stats.messagesReordered).toBe(1);
    });
  });

  describe('reset', () => {
    test('should reset all state', () => {
      // Set up some state
      messageOrdering.validateIncomingMessage({ sequence: 0, data: 'test' });
      messageOrdering.handleOutOfOrder({ sequence: 2, data: 'test2' }, 2);
      messageOrdering.detectMessageGaps(5, 3);
      
      // Reset
      messageOrdering.reset();
      
      expect(messageOrdering.nextExpectedSequence).toBe(0);
      expect(messageOrdering.lastProcessedSequence).toBe(-1);
      expect(messageOrdering.nextSendSequence).toBe(0);
      expect(messageOrdering.pendingMessages.size).toBe(0);
      expect(messageOrdering.replayWindow.size).toBe(0);
      expect(messageOrdering.detectedGaps.size).toBe(0);
      
      const stats = messageOrdering.getStats();
      expect(stats.messagesProcessed).toBe(0);
      expect(stats.duplicatesDetected).toBe(0);
    });
  });

  describe('cleanup', () => {
    test('should clean up expired messages', async () => {
      // Create message ordering with short timeout
      const mo = new MessageOrdering({ gapTimeoutMs: 100, cleanupIntervalMs: 50 });
      
      // Buffer a message
      mo.handleOutOfOrder({ sequence: 5, data: 'test' }, 5);
      expect(mo.pendingMessages.size).toBe(1);
      
      // Wait for cleanup
      await new Promise(resolve => setTimeout(resolve, 200));
      
      expect(mo.pendingMessages.size).toBe(0);
      expect(mo.stats.timeouts).toBeGreaterThan(0);
      
      mo.destroy();
    });

    test('should clean up expired gaps', async () => {
      // Create message ordering with short timeout
      const mo = new MessageOrdering({ gapTimeoutMs: 100, cleanupIntervalMs: 50 });
      
      // Detect gaps
      mo.detectMessageGaps(5, 2);
      expect(mo.detectedGaps.size).toBe(3);
      
      // Wait for cleanup
      await new Promise(resolve => setTimeout(resolve, 200));
      
      expect(mo.detectedGaps.size).toBe(0);
      
      mo.destroy();
    });
  });

  describe('integration scenarios', () => {
    test('should handle complex out-of-order scenario', () => {
      // Receive messages: 0, 2, 4, 1, 3
      const results = [];
      
      results.push(messageOrdering.validateIncomingMessage({ sequence: 0, data: 'msg0' }));
      expect(results[0].action).toBe('PROCESS');
      
      results.push(messageOrdering.validateIncomingMessage({ sequence: 2, data: 'msg2' }));
      expect(results[1].action).toBe('BUFFER');
      
      results.push(messageOrdering.validateIncomingMessage({ sequence: 4, data: 'msg4' }));
      expect(results[2].action).toBe('BUFFER');
      
      results.push(messageOrdering.validateIncomingMessage({ sequence: 1, data: 'msg1' }));
      expect(results[3].action).toBe('PROCESS');
      expect(results[3].readyMessages).toHaveLength(1); // Message 2 becomes ready
      
      results.push(messageOrdering.validateIncomingMessage({ sequence: 3, data: 'msg3' }));
      expect(results[4].action).toBe('PROCESS');
      expect(results[4].readyMessages).toHaveLength(1); // Message 4 becomes ready
      
      expect(messageOrdering.nextExpectedSequence).toBe(5);
      expect(messageOrdering.pendingMessages.size).toBe(0);
    });

    test('should handle replay attack during reordering', () => {
      // Process some messages
      messageOrdering.validateIncomingMessage({ sequence: 0, data: 'msg0' });
      messageOrdering.validateIncomingMessage({ sequence: 2, data: 'msg2' });
      
      // Try to replay message 0
      const result = messageOrdering.validateIncomingMessage({ sequence: 0, data: 'replay' });
      expect(result.action).toBe('REJECT');
      expect(result.reason).toBe('REPLAY_DETECTED');
    });

    test('should handle buffer overflow gracefully', () => {
      // Fill buffer beyond capacity
      for (let i = 10; i < 25; i++) {
        messageOrdering.handleOutOfOrder({ sequence: i, data: `msg${i}` }, i);
      }
      
      expect(messageOrdering.pendingMessages.size).toBe(10); // Should not exceed maxBufferSize
      expect(messageOrdering.stats.timeouts).toBeGreaterThan(0); // Should have evicted messages
    });
  });
});
