/**
 * Test suite for Protocol Debugging Tools
 * 
 * Comprehensive tests for Protocol Debugger, Inspector, and Tracer
 * components including functionality, integration, and performance.
 */

import { 
  ProtocolDebugger, 
  DEBUG_LEVEL, 
  PROTOCOL_EVENT, 
  DEBUG_FORMAT 
} from '../../src/core/protocol/protocol-debugger.js';

import { 
  ProtocolInspector, 
  INSPECTOR_VIEW, 
  VISUALIZATION_MODE 
} from '../../src/core/protocol/protocol-inspector.js';

import { 
  ProtocolTracer, 
  TRACE_EVENT, 
  FLOW_ANALYSIS, 
  TRACE_FORMAT 
} from '../../src/core/protocol/protocol-tracer.js';

describe('Protocol Debugging Tools', () => {
  let debugger;
  let inspector;
  let tracer;

  beforeEach(() => {
    debugger = new ProtocolDebugger({
      enabled: true,
      level: DEBUG_LEVEL.TRACE,
      maxTraceEntries: 1000,
      enablePerformanceMonitoring: true,
      enableStateTracking: true,
      enableMessageCapture: true,
      enableErrorTracking: true
    });

    inspector = new ProtocolInspector(debugger, {
      enableInteractiveMode: true,
      enableStateManipulation: true, // Enable for testing
      enableVisualization: true
    });

    tracer = new ProtocolTracer(debugger, {
      enableFlowTracking: true,
      enableTimingAnalysis: true,
      enableDependencyTracking: true,
      autoGenerateDiagrams: true
    });
  });

  afterEach(() => {
    debugger.destroy();
    inspector.destroy();
    tracer.destroy();
  });

  describe('Protocol Debugger', () => {
    test('should initialize with correct options', () => {
      expect(debugger.options.enabled).toBe(true);
      expect(debugger.options.level).toBe(DEBUG_LEVEL.TRACE);
      expect(debugger.options.maxTraceEntries).toBe(1000);
    });

    test('should start and end debugging sessions', () => {
      const sessionId = 'test-session-1';
      const sessionInfo = { user: 'test-user', protocol: 'OTRv3' };

      debugger.startSession(sessionId, sessionInfo);

      expect(debugger.activeSessions.has(sessionId)).toBe(true);
      expect(debugger.stats.sessionsTracked).toBe(1);

      const session = debugger.activeSessions.get(sessionId);
      expect(session.id).toBe(sessionId);
      expect(session.info).toEqual(sessionInfo);

      debugger.endSession(sessionId, { status: 'completed' });

      expect(debugger.activeSessions.has(sessionId)).toBe(false);
    });

    test('should log protocol messages', () => {
      const sessionId = 'test-session-2';
      debugger.startSession(sessionId);

      const message = {
        type: 'DH_COMMIT',
        data: 'encrypted_data',
        timestamp: Date.now()
      };

      debugger.logMessage('sent', message, sessionId, { size: 256 });

      expect(debugger.stats.messagesSent).toBe(1);
      expect(debugger.traceEntries.length).toBeGreaterThan(0);

      const messageEntry = debugger.traceEntries.find(
        entry => entry.type === PROTOCOL_EVENT.MESSAGE_SENT
      );
      expect(messageEntry).toBeDefined();
      expect(messageEntry.data.sessionId).toBe(sessionId);
      expect(messageEntry.data.direction).toBe('sent');
    });

    test('should track state changes', () => {
      const sessionId = 'test-session-3';
      debugger.startSession(sessionId);

      debugger.logStateChange(
        sessionId,
        'AKE',
        'AWAITING_DHKEY',
        'AWAITING_REVEALSIG',
        { reason: 'DH_KEY_received' }
      );

      expect(debugger.stats.stateChanges).toBe(1);
      expect(debugger.stateHistory.length).toBe(1);

      const stateEntry = debugger.stateHistory[0];
      expect(stateEntry.sessionId).toBe(sessionId);
      expect(stateEntry.component).toBe('AKE');
      expect(stateEntry.oldState).toBe('AWAITING_DHKEY');
      expect(stateEntry.newState).toBe('AWAITING_REVEALSIG');
    });

    test('should monitor performance', () => {
      const sessionId = 'test-session-4';
      debugger.startSession(sessionId);

      const timerId = debugger.startTimer('encryption', sessionId);
      expect(timerId).toBeDefined();
      expect(debugger.performanceTimers.has(timerId)).toBe(true);

      // Simulate some work
      setTimeout(() => {
        debugger.endTimer(timerId, { algorithm: 'AES-256' });
        
        expect(debugger.performanceTimers.has(timerId)).toBe(false);
        expect(debugger.stats.performanceEvents).toBe(1);
        expect(debugger.performanceMetrics.has('encryption')).toBe(true);
      }, 10);
    });

    test('should log and track errors', () => {
      const sessionId = 'test-session-5';
      debugger.startSession(sessionId);

      const error = new Error('Invalid DH public key');
      error.name = 'ValidationError';

      debugger.logError(error, sessionId, { 
        component: 'AKE',
        phase: 'key_validation' 
      });

      expect(debugger.stats.errorsLogged).toBe(1);
      expect(debugger.errorLog.length).toBe(1);

      const errorEntry = debugger.errorLog[0];
      expect(errorEntry.message).toBe('Invalid DH public key');
      expect(errorEntry.name).toBe('ValidationError');
      expect(errorEntry.sessionId).toBe(sessionId);
    });

    test('should export debug data', () => {
      const sessionId = 'test-session-6';
      debugger.startSession(sessionId);

      // Generate some test data
      debugger.logMessage('sent', { type: 'TEST' }, sessionId);
      debugger.logStateChange(sessionId, 'TEST', 'A', 'B');
      debugger.logPerformance('test_op', 100, sessionId);

      const exportData = debugger.exportDebugData({
        includeTraceEntries: true,
        includeStateHistory: true,
        includePerformanceMetrics: true,
        sessionId: sessionId
      });

      expect(exportData.timestamp).toBeDefined();
      expect(exportData.statistics).toEqual(debugger.stats);
      expect(exportData.traceEntries.length).toBeGreaterThan(0);
      expect(exportData.stateHistory.length).toBeGreaterThan(0);
      expect(exportData.performanceMetrics).toBeDefined();
    });

    test('should handle event listeners', () => {
      let eventReceived = null;
      
      const listenerId = debugger.addEventListener(PROTOCOL_EVENT.MESSAGE_SENT, (entry) => {
        eventReceived = entry;
      });

      expect(listenerId).toBeDefined();

      const sessionId = 'test-session-7';
      debugger.startSession(sessionId);
      debugger.logMessage('sent', { type: 'TEST' }, sessionId);

      expect(eventReceived).toBeDefined();
      expect(eventReceived.type).toBe(PROTOCOL_EVENT.MESSAGE_SENT);

      debugger.removeEventListener(PROTOCOL_EVENT.MESSAGE_SENT, listenerId);
    });

    test('should filter trace entries', () => {
      const sessionId1 = 'test-session-8a';
      const sessionId2 = 'test-session-8b';

      debugger.startSession(sessionId1);
      debugger.startSession(sessionId2);

      debugger.logMessage('sent', { type: 'TEST1' }, sessionId1);
      debugger.logMessage('sent', { type: 'TEST2' }, sessionId2);

      const allEntries = debugger.getTraceEntries();
      expect(allEntries.length).toBeGreaterThan(0);

      const session1Entries = debugger.getTraceEntries({ sessionId: sessionId1 });
      const session2Entries = debugger.getTraceEntries({ sessionId: sessionId2 });

      expect(session1Entries.length).toBeGreaterThan(0);
      expect(session2Entries.length).toBeGreaterThan(0);
      expect(session1Entries.length + session2Entries.length).toBeLessThanOrEqual(allEntries.length);
    });
  });

  describe('Protocol Inspector', () => {
    test('should initialize with debugger', () => {
      expect(inspector.debugger).toBe(debugger);
      expect(inspector.currentView).toBe(INSPECTOR_VIEW.OVERVIEW);
      expect(inspector.selectedSession).toBeNull();
    });

    test('should change views', () => {
      inspector.setView(INSPECTOR_VIEW.MESSAGES);
      expect(inspector.currentView).toBe(INSPECTOR_VIEW.MESSAGES);

      inspector.setView(INSPECTOR_VIEW.PERFORMANCE);
      expect(inspector.currentView).toBe(INSPECTOR_VIEW.PERFORMANCE);
    });

    test('should select sessions', () => {
      const sessionId = 'test-session-9';
      debugger.startSession(sessionId);

      inspector.setSelectedSession(sessionId);
      expect(inspector.selectedSession).toBe(sessionId);
    });

    test('should get inspector data', () => {
      const sessionId = 'test-session-10';
      debugger.startSession(sessionId);
      debugger.logMessage('sent', { type: 'TEST' }, sessionId);

      const data = inspector.getInspectorData();

      expect(data.overview).toBeDefined();
      expect(data.sessions).toBeDefined();
      expect(data.messages).toBeDefined();
      expect(data.states).toBeDefined();
      expect(data.performance).toBeDefined();
      expect(data.errors).toBeDefined();
    });

    test('should export inspector report', () => {
      const sessionId = 'test-session-11';
      debugger.startSession(sessionId);

      const report = inspector.exportReport({
        includeVisualization: false,
        includeRawData: true
      });

      expect(report.timestamp).toBeDefined();
      expect(report.inspector).toBeDefined();
      expect(report.data).toBeDefined();
      expect(report.debuggerStats).toBeDefined();
      expect(report.rawData).toBeDefined();
    });

    test('should manipulate state in testing mode', () => {
      const sessionId = 'test-session-12';
      debugger.startSession(sessionId);

      inspector.manipulateState(sessionId, 'AKE', 'TEST_STATE', { 
        reason: 'testing' 
      });

      // Should have logged a state change
      expect(debugger.stats.stateChanges).toBe(1);
      
      const stateEntry = debugger.stateHistory[0];
      expect(stateEntry.sessionId).toBe(sessionId);
      expect(stateEntry.component).toBe('AKE');
      expect(stateEntry.newState).toBe('TEST_STATE');
      expect(stateEntry.context.manipulated).toBe(true);
    });

    test('should inject test messages', () => {
      const sessionId = 'test-session-13';
      debugger.startSession(sessionId);

      const testMessage = {
        type: 'TEST_MESSAGE',
        data: 'test_data'
      };

      inspector.injectMessage(sessionId, testMessage, 'received');

      // Should have logged a message
      expect(debugger.stats.messagesReceived).toBe(1);
    });

    test('should handle update listeners', () => {
      let updateReceived = null;

      const unsubscribe = inspector.addUpdateListener((update) => {
        updateReceived = update;
      });

      expect(typeof unsubscribe).toBe('function');

      // Trigger an update
      inspector.manipulateState('test-session', 'TEST', 'STATE1', 'STATE2');

      expect(updateReceived).toBeDefined();
      expect(updateReceived.type).toBe('state_manipulated');

      unsubscribe();
    });
  });

  describe('Protocol Tracer', () => {
    test('should initialize with debugger', () => {
      expect(tracer.debugger).toBe(debugger);
      expect(tracer.options.enableFlowTracking).toBe(true);
      expect(tracer.options.enableTimingAnalysis).toBe(true);
    });

    test('should track flow lifecycle', () => {
      const flowId = 'test-flow-1';
      const flowType = 'AKE';

      tracer.startFlow(flowId, flowType, { initiator: 'alice' });

      expect(tracer.activeFlows.has(flowId)).toBe(true);
      expect(tracer.stats.flowsTracked).toBe(1);

      const flow = tracer.activeFlows.get(flowId);
      expect(flow.id).toBe(flowId);
      expect(flow.type).toBe(flowType);
      expect(flow.state).toBe('active');

      tracer.endFlow(flowId, { status: 'success' });

      expect(tracer.activeFlows.has(flowId)).toBe(false);
      expect(tracer.completedFlows.length).toBe(1);

      const completedFlow = tracer.completedFlows[0];
      expect(completedFlow.id).toBe(flowId);
      expect(completedFlow.state).toBe('completed');
      expect(completedFlow.duration).toBeDefined();
    });

    test('should add timing points', () => {
      const flowId = 'test-flow-2';
      tracer.startFlow(flowId, 'AKE');

      tracer.addTimingPoint(flowId, 'dh_commit_sent', { size: 256 });
      tracer.addTimingPoint(flowId, 'dh_key_received', { size: 128 });

      expect(tracer.stats.timingPointsRecorded).toBe(2);
      expect(tracer.timingPoints.has(flowId)).toBe(true);

      const timingPoints = tracer.timingPoints.get(flowId);
      expect(timingPoints.length).toBe(2);
      expect(timingPoints[0].name).toBe('dh_commit_sent');
      expect(timingPoints[1].name).toBe('dh_key_received');
    });

    test('should trace messages in flow', () => {
      const flowId = 'test-flow-3';
      tracer.startFlow(flowId, 'AKE');

      const message = {
        type: 'DH_COMMIT',
        data: 'encrypted_data'
      };

      tracer.traceMessage(flowId, 'sent', message, { size: 256 });

      const flow = tracer.activeFlows.get(flowId);
      expect(flow.events.length).toBe(1);

      const messageEvent = flow.events[0];
      expect(messageEvent.type).toBe('message');
      expect(messageEvent.direction).toBe('sent');
      expect(messageEvent.message.type).toBe('DH_COMMIT');
    });

    test('should trace state transitions', () => {
      const flowId = 'test-flow-4';
      tracer.startFlow(flowId, 'AKE');

      tracer.traceStateTransition(
        flowId,
        'AKE',
        'AWAITING_DHKEY',
        'AWAITING_REVEALSIG',
        { trigger: 'DH_KEY_received' }
      );

      const flow = tracer.activeFlows.get(flowId);
      expect(flow.events.length).toBe(1);

      const stateEvent = flow.events[0];
      expect(stateEvent.type).toBe('state_transition');
      expect(stateEvent.component).toBe('AKE');
      expect(stateEvent.fromState).toBe('AWAITING_DHKEY');
      expect(stateEvent.toState).toBe('AWAITING_REVEALSIG');
    });

    test('should track dependencies', () => {
      const flowId1 = 'test-flow-5a';
      const flowId2 = 'test-flow-5b';

      tracer.startFlow(flowId1, 'AKE');
      tracer.startFlow(flowId2, 'SMP');

      tracer.addDependency(flowId2, flowId1, 'requires');

      expect(tracer.stats.dependenciesTracked).toBe(1);
      expect(tracer.dependencies.has(flowId2)).toBe(true);

      const dependencies = tracer.dependencies.get(flowId2);
      expect(dependencies.length).toBe(1);
      expect(dependencies[0].dependency).toBe(flowId1);
      expect(dependencies[0].type).toBe('requires');
    });

    test('should analyze flow performance', () => {
      const flowId = 'test-flow-6';
      tracer.startFlow(flowId, 'AKE');

      // Add some timing points
      tracer.addTimingPoint(flowId, 'start');
      setTimeout(() => {
        tracer.addTimingPoint(flowId, 'middle');
      }, 10);
      setTimeout(() => {
        tracer.addTimingPoint(flowId, 'end');
        tracer.endFlow(flowId);

        const analysis = tracer.analyzeFlowPerformance(flowId);

        expect(analysis).toBeDefined();
        expect(analysis.flowId).toBe(flowId);
        expect(analysis.totalDuration).toBeDefined();
        expect(analysis.phases).toBeDefined();
        expect(analysis.efficiency).toBeDefined();
      }, 20);
    });

    test('should generate sequence diagrams', () => {
      const flowId = 'test-flow-7';
      tracer.startFlow(flowId, 'AKE');

      // Add some events
      tracer.traceMessage(flowId, 'sent', { type: 'DH_COMMIT' });
      tracer.traceMessage(flowId, 'received', { type: 'DH_KEY' });
      tracer.traceStateTransition(flowId, 'AKE', 'STATE1', 'STATE2');

      tracer.endFlow(flowId);

      const diagram = tracer.generateSequenceDiagram(flowId, TRACE_FORMAT.MERMAID);

      expect(diagram).toBeDefined();
      expect(diagram.flowId).toBe(flowId);
      expect(diagram.format).toBe(TRACE_FORMAT.MERMAID);
      expect(diagram.data).toContain('sequenceDiagram');
      expect(tracer.stats.diagramsGenerated).toBeGreaterThan(0);
    });

    test('should generate flow charts', () => {
      const flowId = 'test-flow-8';
      tracer.startFlow(flowId, 'AKE');

      // Add some events
      tracer.traceMessage(flowId, 'sent', { type: 'DH_COMMIT' });
      tracer.traceStateTransition(flowId, 'AKE', 'STATE1', 'STATE2');

      tracer.endFlow(flowId);

      const chart = tracer.generateFlowChart(flowId, TRACE_FORMAT.MERMAID);

      expect(chart).toBeDefined();
      expect(chart.flowId).toBe(flowId);
      expect(chart.format).toBe(TRACE_FORMAT.MERMAID);
      expect(chart.data).toContain('flowchart');
    });

    test('should export trace data', () => {
      const flowId = 'test-flow-9';
      tracer.startFlow(flowId, 'AKE');
      tracer.addTimingPoint(flowId, 'test_point');
      tracer.endFlow(flowId);

      const exportData = tracer.exportTraceData({
        includeActiveFlows: true,
        includeCompletedFlows: true,
        includeTimingData: true,
        includeDependencies: true,
        includeDiagrams: true
      });

      expect(exportData.timestamp).toBeDefined();
      expect(exportData.statistics).toEqual(tracer.stats);
      expect(exportData.completedFlows).toBeDefined();
      expect(exportData.timingPoints).toBeDefined();
    });

    test('should provide comprehensive statistics', () => {
      const flowId = 'test-flow-10';
      tracer.startFlow(flowId, 'AKE');
      tracer.endFlow(flowId);

      const stats = tracer.getStats();

      expect(stats.flowsTracked).toBe(1);
      expect(stats.completedFlowsCount).toBe(1);
      expect(stats.totalFlowsCount).toBe(1);
      expect(stats.averageFlowDuration).toBeDefined();
    });
  });

  describe('Integration Tests', () => {
    test('should integrate debugger with inspector', () => {
      const sessionId = 'integration-test-1';
      
      // Start session and generate some data
      debugger.startSession(sessionId);
      debugger.logMessage('sent', { type: 'TEST' }, sessionId);
      debugger.logStateChange(sessionId, 'TEST', 'A', 'B');

      // Inspector should see the data
      inspector.setSelectedSession(sessionId);
      const data = inspector.getInspectorData();

      expect(data.sessions.active.length).toBe(1);
      expect(data.messages.messages.length).toBeGreaterThan(0);
      expect(data.states.history.length).toBeGreaterThan(0);
    });

    test('should integrate debugger with tracer', () => {
      const sessionId = 'integration-test-2';
      const flowId = sessionId; // Use same ID for simplicity

      // Start debugging session and flow
      debugger.startSession(sessionId);
      tracer.startFlow(flowId, 'AKE');

      // Log some events
      debugger.logMessage('sent', { type: 'DH_COMMIT' }, sessionId);
      debugger.logStateChange(sessionId, 'AKE', 'STATE1', 'STATE2');

      // Tracer should automatically capture events
      const flow = tracer.activeFlows.get(flowId);
      expect(flow).toBeDefined();
      
      // End flow and session
      tracer.endFlow(flowId);
      debugger.endSession(sessionId);

      // Verify integration
      expect(tracer.completedFlows.length).toBe(1);
      expect(debugger.activeSessions.size).toBe(0);
    });

    test('should handle complex debugging scenario', () => {
      const sessionId = 'complex-test';
      const flowId = sessionId;

      // Start comprehensive debugging
      debugger.startSession(sessionId, { protocol: 'OTRv3', user: 'alice' });
      tracer.startFlow(flowId, 'FULL_AKE');
      inspector.setSelectedSession(sessionId);

      // Simulate AKE flow
      const akeSteps = [
        { message: { type: 'DH_COMMIT' }, direction: 'sent', state: 'AWAITING_DHKEY' },
        { message: { type: 'DH_KEY' }, direction: 'received', state: 'AWAITING_REVEALSIG' },
        { message: { type: 'REVEAL_SIGNATURE' }, direction: 'sent', state: 'AWAITING_SIGNATURE' },
        { message: { type: 'SIGNATURE' }, direction: 'received', state: 'MSGSTATE_ENCRYPTED' }
      ];

      akeSteps.forEach((step, index) => {
        const timerId = debugger.startTimer(`step_${index}`, sessionId);
        
        debugger.logMessage(step.direction, step.message, sessionId);
        tracer.traceMessage(flowId, step.direction, step.message);
        
        if (index > 0) {
          debugger.logStateChange(sessionId, 'AKE', akeSteps[index-1].state, step.state);
          tracer.traceStateTransition(flowId, 'AKE', akeSteps[index-1].state, step.state);
        }
        
        tracer.addTimingPoint(flowId, `step_${index}_complete`);
        debugger.endTimer(timerId);
      });

      // Complete the flow
      tracer.endFlow(flowId, { status: 'success', finalState: 'MSGSTATE_ENCRYPTED' });
      debugger.endSession(sessionId, { status: 'completed' });

      // Verify comprehensive data collection
      expect(debugger.stats.messagesSent).toBe(2);
      expect(debugger.stats.messagesReceived).toBe(2);
      expect(debugger.stats.stateChanges).toBe(3);
      expect(debugger.stats.performanceEvents).toBe(4);

      expect(tracer.completedFlows.length).toBe(1);
      expect(tracer.stats.timingPointsRecorded).toBe(4);

      const inspectorData = inspector.getInspectorData();
      expect(inspectorData.overview.stats.totalEvents).toBeGreaterThan(10);

      // Generate diagrams
      const sequenceDiagram = tracer.generateSequenceDiagram(flowId);
      const flowChart = tracer.generateFlowChart(flowId);

      expect(sequenceDiagram).toBeDefined();
      expect(flowChart).toBeDefined();

      // Export comprehensive report
      const report = inspector.exportReport({ includeRawData: true });
      expect(report.data).toBeDefined();
      expect(report.rawData).toBeDefined();
    });
  });
});
