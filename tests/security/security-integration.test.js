/**
 * Security Integration Test Suite
 * 
 * This comprehensive test suite validates the integration of all security
 * enhancements including constant-time operations, input validation,
 * secure memory management, and error recovery.
 */

import { 
  ConstantTimeOps,
  CryptoValidation,
  SecurityValidationError,
  SecureMemory,
  SecureMemoryPool,
  ProtocolErrorRecovery,
  ERROR_TYPES,
  globalErrorRecovery
} from '../../src/core/crypto/index.js';

import { BigInteger } from 'jsbn';

describe('Security Integration Tests', () => {
  describe('End-to-End Security Validation', () => {
    test('should validate DH key exchange with secure memory and constant-time operations', async () => {
      // Create secure memory for DH private key
      const privateKeyMemory = new SecureMemory(32);
      const publicKeyMemory = new SecureMemory(256);
      
      try {
        // Generate test DH keys
        const privateKey = new Uint8Array(32);
        crypto.getRandomValues(privateKey);
        privateKeyMemory.write(privateKey);
        
        // Create a valid DH public key
        const validPublicKey = new BigInteger('12345678901234567890ABCDEF', 16);
        
        // Validate using the comprehensive validation framework
        expect(() => CryptoValidation.validateDHPublicKey(validPublicKey)).not.toThrow();
        
        // Test constant-time comparison of keys
        const key1 = privateKeyMemory.read();
        const key2 = new Uint8Array(key1); // Copy
        const key3 = new Uint8Array(32);
        crypto.getRandomValues(key3);
        
        expect(ConstantTimeOps.constantTimeEqual(key1, key2)).toBe(true);
        expect(ConstantTimeOps.constantTimeEqual(key1, key3)).toBe(false);
        
      } finally {
        // Secure cleanup
        privateKeyMemory.destroy();
        publicKeyMemory.destroy();
      }
    });

    test('should handle protocol errors with secure state recovery', () => {
      const mockContext = {
        id: 'test-context',
        auth: {
          state: 'AWAITING_DHKEY',
          privateKey: new Uint8Array([1, 2, 3, 4]),
          sharedSecret: new Uint8Array([5, 6, 7, 8])
        }
      };

      // Create an invalid signature error
      const error = new Error('Invalid signature detected');
      error.type = ERROR_TYPES.INVALID_SIGNATURE;

      // Handle the error with recovery system
      const recovery = globalErrorRecovery.handleAKEError(error, mockContext);

      // Verify recovery strategy
      expect(recovery.strategy).toBe('RESET_STATE');
      expect(recovery.requireReauth).toBe(true);

      // Verify sensitive data was cleared
      expect(mockContext.auth.privateKey).toBeNull();
      expect(mockContext.auth.sharedSecret).toBeNull();
      expect(mockContext.auth.state).toBe('PLAINTEXT');
    });

    test('should validate SMP protocol with comprehensive security checks', () => {
      // Test SMP group element validation
      const validElement = new BigInteger('123456789ABCDEF', 16);
      const invalidElement = new BigInteger('1'); // Too small
      
      expect(() => CryptoValidation.validateSMPGroupElement(validElement)).not.toThrow();
      expect(() => CryptoValidation.validateSMPGroupElement(invalidElement))
        .toThrow(SecurityValidationError);

      // Test ZK proof structure validation
      const validProof = {
        c: new BigInteger('12345', 16),
        d: new BigInteger('67890', 16)
      };
      
      const invalidProof = {
        c: new BigInteger('1'), // Invalid element
        d: new BigInteger('67890', 16)
      };

      expect(() => CryptoValidation.validateZKProofStructure(validProof)).not.toThrow();
      expect(() => CryptoValidation.validateZKProofStructure(invalidProof))
        .toThrow(SecurityValidationError);
    });
  });

  describe('Memory Security Integration', () => {
    test('should manage cryptographic keys securely throughout lifecycle', () => {
      const pool = new SecureMemoryPool({ maxPoolSize: 5 });
      
      try {
        // Allocate memory for session keys
        const encKeyMemory = pool.allocate(32);
        const macKeyMemory = pool.allocate(32);
        const dhKeyMemory = pool.allocate(256);

        // Write test keys
        const encKey = new Uint8Array(32);
        const macKey = new Uint8Array(32);
        const dhKey = new Uint8Array(256);
        
        crypto.getRandomValues(encKey);
        crypto.getRandomValues(macKey);
        crypto.getRandomValues(dhKey);

        encKeyMemory.write(encKey);
        macKeyMemory.write(macKey);
        dhKeyMemory.write(dhKey);

        // Verify keys are stored correctly
        expect(encKeyMemory.read()).toEqual(encKey);
        expect(macKeyMemory.read()).toEqual(macKey);
        expect(dhKeyMemory.read()).toEqual(dhKey);

        // Return to pool (should be securely wiped)
        pool.deallocate(encKeyMemory);
        pool.deallocate(macKeyMemory);
        pool.deallocate(dhKeyMemory);

        // Verify pool statistics
        const stats = pool.getStats();
        expect(stats.deallocations).toBe(3);
        expect(stats.totalPooledMemory).toBeGreaterThan(0);

      } finally {
        pool.destroy();
      }
    });

    test('should handle memory pressure and cleanup correctly', () => {
      const memories = [];
      
      try {
        // Allocate many secure memory instances
        for (let i = 0; i < 50; i++) {
          const memory = new SecureMemory(64);
          const data = new Uint8Array(64);
          crypto.getRandomValues(data);
          memory.write(data);
          memories.push(memory);
        }

        // Check global statistics
        const stats = SecureMemory.getGlobalStats();
        expect(stats.activeInstances).toBeGreaterThanOrEqual(50);
        expect(stats.totalSize).toBeGreaterThanOrEqual(50 * 64);

        // Cleanup half
        for (let i = 0; i < 25; i++) {
          memories[i].destroy();
        }

        // Verify cleanup
        const newStats = SecureMemory.getGlobalStats();
        expect(newStats.activeInstances).toBeLessThan(stats.activeInstances);

      } finally {
        // Cleanup remaining
        for (const memory of memories) {
          if (!memory.isDestroyed) {
            memory.destroy();
          }
        }
      }
    });
  });

  describe('Timing Attack Resistance', () => {
    test('should demonstrate consistent timing for MAC verification', () => {
      const iterations = 1000;
      const macSize = 32;

      // Create test MACs
      const validMac = new Uint8Array(macSize);
      crypto.getRandomValues(validMac);
      
      const invalidMac = new Uint8Array(validMac);
      invalidMac[macSize - 1] ^= 1; // Change last byte

      // Measure timing for valid MAC
      const startValid = performance.now();
      for (let i = 0; i < iterations; i++) {
        ConstantTimeOps.constantTimeEqual(validMac, validMac);
      }
      const timeValid = performance.now() - startValid;

      // Measure timing for invalid MAC
      const startInvalid = performance.now();
      for (let i = 0; i < iterations; i++) {
        ConstantTimeOps.constantTimeEqual(validMac, invalidMac);
      }
      const timeInvalid = performance.now() - startInvalid;

      // Timing difference should be minimal
      const timingRatio = Math.abs(timeValid - timeInvalid) / Math.max(timeValid, timeInvalid);
      expect(timingRatio).toBeLessThan(0.3); // Allow 30% variance for JavaScript timing
    });

    test('should use constant-time operations for sensitive comparisons', () => {
      // Test various data sizes
      const sizes = [16, 32, 64, 128, 256];
      
      for (const size of sizes) {
        const data1 = new Uint8Array(size);
        const data2 = new Uint8Array(size);
        const data3 = new Uint8Array(size);
        
        crypto.getRandomValues(data1);
        data2.set(data1); // Copy
        crypto.getRandomValues(data3); // Different

        // Test equality
        expect(ConstantTimeOps.constantTimeEqual(data1, data2)).toBe(true);
        expect(ConstantTimeOps.constantTimeEqual(data1, data3)).toBe(false);

        // Test comparison
        expect(ConstantTimeOps.constantTimeCompare(data1, data2)).toBe(0);
        expect(ConstantTimeOps.constantTimeCompare(data1, data3)).not.toBe(0);
      }
    });
  });

  describe('Input Validation Security', () => {
    test('should reject all forms of invalid cryptographic parameters', () => {
      // Test boundary conditions for DH keys
      const dhTests = [
        { key: new BigInteger('0'), shouldFail: true, reason: 'zero key' },
        { key: new BigInteger('1'), shouldFail: true, reason: 'identity key' },
        { key: new BigInteger('2'), shouldFail: true, reason: 'generator key' },
        { key: CryptoValidation.DH_MODULUS, shouldFail: true, reason: 'modulus key' },
        { key: CryptoValidation.DH_MODULUS.subtract(new BigInteger('1')), shouldFail: true, reason: 'p-1 key' },
        { key: new BigInteger('12345678901234567890', 16), shouldFail: false, reason: 'valid key' }
      ];

      for (const test of dhTests) {
        if (test.shouldFail) {
          expect(() => CryptoValidation.validateDHPublicKey(test.key))
            .toThrow(SecurityValidationError);
        } else {
          expect(() => CryptoValidation.validateDHPublicKey(test.key))
            .not.toThrow();
        }
      }
    });

    test('should validate protocol messages comprehensively', () => {
      // Test valid message
      const validMessage = {
        type: 'SMP1',
        instanceFrom: 0x12345678,
        instanceTo: 0x87654321,
        data: new Uint8Array([1, 2, 3, 4])
      };

      expect(() => CryptoValidation.validateProtocolMessage(validMessage, 'SMP1'))
        .not.toThrow();

      // Test invalid messages
      const invalidMessages = [
        null,
        'not an object',
        { type: 'SMP1' }, // Missing instance tags
        { type: 'SMP2', instanceFrom: 0x100 }, // Invalid instance tag
        { type: 'SMP1', instanceFrom: 0x12345678, instanceTo: -1 } // Negative instance tag
      ];

      for (const invalidMsg of invalidMessages) {
        expect(() => CryptoValidation.validateProtocolMessage(invalidMsg))
          .toThrow(SecurityValidationError);
      }
    });

    test('should validate message counters for replay protection', () => {
      // Test valid counter progression
      expect(() => CryptoValidation.validateMessageCounter(2, 1)).not.toThrow();
      expect(() => CryptoValidation.validateMessageCounter(100, 50)).not.toThrow();

      // Test invalid counters
      const invalidTests = [
        { newCounter: 1, lastCounter: 2, reason: 'regression' },
        { newCounter: 5, lastCounter: 5, reason: 'duplicate' },
        { newCounter: -1, lastCounter: 0, reason: 'negative' },
        { newCounter: 2000000, lastCounter: 1, reason: 'large jump' }
      ];

      for (const test of invalidTests) {
        expect(() => CryptoValidation.validateMessageCounter(test.newCounter, test.lastCounter))
          .toThrow(SecurityValidationError);
      }
    });
  });

  describe('Error Recovery Integration', () => {
    test('should maintain security properties during error recovery', () => {
      const recovery = new ProtocolErrorRecovery({ enableLogging: false });
      const sensitiveContext = {
        id: 'sensitive-context',
        auth: {
          state: 'ENCRYPTED',
          sessionKeys: new Uint8Array([1, 2, 3, 4, 5, 6, 7, 8]),
          privateKey: new Uint8Array([9, 10, 11, 12, 13, 14, 15, 16]),
          sharedSecret: new Uint8Array([17, 18, 19, 20, 21, 22, 23, 24])
        }
      };

      // Simulate a critical security error
      const criticalError = new Error('Potential replay attack');
      criticalError.type = ERROR_TYPES.REPLAY_ATTACK;

      const recoveryResult = recovery.handleAKEError(criticalError, sensitiveContext);

      // Verify security response
      expect(recoveryResult.strategy).toBe('ABORT_SESSION');
      expect(recoveryResult.reason).toBe('replay_attack_detected');

      // Verify sensitive data was cleared
      expect(sensitiveContext.auth.sessionKeys).toBeNull();
      expect(sensitiveContext.auth.privateKey).toBeNull();
      expect(sensitiveContext.auth.sharedSecret).toBeNull();
    });

    test('should track security events for monitoring', () => {
      const events = [];
      const recovery = new ProtocolErrorRecovery({
        securityEventHandler: (event) => events.push(event),
        enableLogging: false
      });

      const context = { id: 'test-context', auth: { state: 'PLAINTEXT' } };

      // Generate various security events
      const errors = [
        { message: 'Invalid signature', type: ERROR_TYPES.INVALID_SIGNATURE },
        { message: 'Invalid MAC', type: ERROR_TYPES.INVALID_MAC },
        { message: 'Protocol violation', type: ERROR_TYPES.PROTOCOL_VIOLATION }
      ];

      for (const errorData of errors) {
        const error = new Error(errorData.message);
        error.type = errorData.type;
        recovery.handleAKEError(error, context);
      }

      // Verify events were logged
      expect(events.length).toBeGreaterThanOrEqual(3);
      expect(events.some(e => e.type === ERROR_TYPES.INVALID_SIGNATURE)).toBe(true);
      expect(events.some(e => e.type === ERROR_TYPES.INVALID_MAC)).toBe(true);
      expect(events.some(e => e.type === ERROR_TYPES.PROTOCOL_VIOLATION)).toBe(true);

      // Verify event structure
      for (const event of events) {
        expect(event.id).toMatch(/^evt_/);
        expect(event.timestamp).toBeGreaterThan(0);
        expect(event.severity).toBeDefined();
        expect(event.message).toBeDefined();
      }
    });
  });

  describe('Performance and Security Balance', () => {
    test('should maintain acceptable performance with security enhancements', () => {
      const iterations = 100;

      // Test constant-time operations performance
      const data1 = new Uint8Array(256);
      const data2 = new Uint8Array(256);
      crypto.getRandomValues(data1);
      crypto.getRandomValues(data2);

      const start = performance.now();
      for (let i = 0; i < iterations; i++) {
        ConstantTimeOps.constantTimeEqual(data1, data2);
      }
      const duration = performance.now() - start;

      // Should complete within reasonable time (adjust threshold as needed)
      expect(duration).toBeLessThan(100); // 100ms for 100 iterations

      // Test validation performance
      const validationStart = performance.now();
      for (let i = 0; i < iterations; i++) {
        const key = new BigInteger('123456789ABCDEF', 16);
        CryptoValidation.validateSMPGroupElement(key);
      }
      const validationDuration = performance.now() - validationStart;

      expect(validationDuration).toBeLessThan(200); // 200ms for 100 validations
    });

    test('should provide security metrics and monitoring', () => {
      // Test global error recovery statistics
      const stats = globalErrorRecovery.getStats();
      expect(stats).toHaveProperty('totalErrors');
      expect(stats).toHaveProperty('recoveredErrors');
      expect(stats).toHaveProperty('recoveryRate');
      expect(stats).toHaveProperty('recentEvents');

      // Test secure memory global statistics
      const memStats = SecureMemory.getGlobalStats();
      expect(memStats).toHaveProperty('totalInstances');
      expect(memStats).toHaveProperty('activeInstances');
      expect(memStats).toHaveProperty('totalSize');
      expect(memStats).toHaveProperty('averageSize');
    });
  });
});
