/**
 * Test suite for enhanced error recovery system
 * 
 * These tests verify that the error recovery system correctly handles
 * protocol violations, security issues, and provides appropriate
 * recovery strategies while maintaining security properties.
 */

import { 
  ProtocolErrorRecovery, 
  ERROR_TYPES, 
  RECOVERY_STRATEGIES,
  SecurityEvent,
  globalErrorRecovery 
} from '../../src/core/security/error-recovery.js';

describe('ProtocolErrorRecovery', () => {
  let recovery;
  let mockContext;

  beforeEach(() => {
    recovery = new ProtocolErrorRecovery({
      maxRetries: 3,
      retryDelay: 100,
      enableLogging: false
    });

    mockContext = {
      id: 'test-context-123',
      auth: {
        state: 'AWAITING_DHKEY',
        hashgx: new Uint8Array([1, 2, 3, 4]),
        privateKey: new Uint8Array([5, 6, 7, 8]),
        sharedSecret: new Uint8Array([9, 10, 11, 12])
      }
    };
  });

  afterEach(() => {
    recovery.resetStats();
  });

  describe('handleAKEError', () => {
    test('should handle competing DH commit conflicts', () => {
      const error = new Error('Competing DH commits');
      error.type = ERROR_TYPES.COMPETING_DH_COMMIT;
      error.data = {
        hashgx: new Uint8Array([5, 6, 7, 8]) // Different hash
      };

      const result = recovery.handleAKEError(error, mockContext);

      expect(result.strategy).toBe(RECOVERY_STRATEGIES.RESTART_PROTOCOL);
      expect(result.action).toBe('restart_with_incoming');
      expect(result.useIncoming).toBe(true);
    });

    test('should handle competing DH commit where our commit wins', () => {
      const error = new Error('Competing DH commits');
      error.type = ERROR_TYPES.COMPETING_DH_COMMIT;
      error.data = {
        hashgx: new Uint8Array([0, 1, 2, 3]) // Smaller hash
      };

      const result = recovery.handleAKEError(error, mockContext);

      expect(result.strategy).toBe(RECOVERY_STRATEGIES.IGNORE);
      expect(result.action).toBe('ignore_incoming');
      expect(result.retransmit).toBe(true);
    });

    test('should handle signature verification failures', () => {
      const error = new Error('Invalid signature');
      error.type = ERROR_TYPES.INVALID_SIGNATURE;

      const result = recovery.handleAKEError(error, mockContext);

      expect(result.strategy).toBe(RECOVERY_STRATEGIES.RESET_STATE);
      expect(result.action).toBe('reset_to_plaintext');
      expect(result.requireReauth).toBe(true);
    });

    test('should handle invalid DH keys', () => {
      const error = new Error('Invalid DH key');
      error.type = ERROR_TYPES.INVALID_DH_KEY;

      const result = recovery.handleAKEError(error, mockContext);

      expect(result.strategy).toBe(RECOVERY_STRATEGIES.RESTART_PROTOCOL);
      expect(result.action).toBe('restart_key_exchange');
    });

    test('should handle protocol violations with retries', () => {
      const error = new Error('Protocol violation');
      error.type = ERROR_TYPES.PROTOCOL_VIOLATION;

      const result = recovery.handleAKEError(error, mockContext);

      expect(result.strategy).toBe(RECOVERY_STRATEGIES.RETRY);
      expect(result.action).toBe('resend_last_message');
      expect(result.delay).toBeGreaterThan(0);
    });

    test('should gracefully degrade after max retries', () => {
      const error = new Error('Protocol violation');
      error.type = ERROR_TYPES.PROTOCOL_VIOLATION;

      // Exhaust retries
      for (let i = 0; i < 3; i++) {
        recovery.handleAKEError(error, mockContext);
      }

      // Next attempt should gracefully degrade
      const result = recovery.handleAKEError(error, mockContext);

      expect(result.strategy).toBe(RECOVERY_STRATEGIES.GRACEFUL_DEGRADATION);
      expect(result.action).toBe('degrade_to_plaintext');
    });

    test('should handle replay attacks', () => {
      const error = new Error('Counter regression detected');
      error.type = ERROR_TYPES.SEQUENCE_ERROR;

      // Mock multiple recent errors to trigger replay detection
      for (let i = 0; i < 6; i++) {
        recovery._logSecurityEvent('TEST_ERROR', 'MEDIUM', 'Test error', { contextId: mockContext.id });
      }

      const result = recovery.handleAKEError(error, mockContext);

      expect(result.strategy).toBe(RECOVERY_STRATEGIES.ABORT_SESSION);
      expect(result.action).toBe('abort_session');
      expect(result.reason).toBe('replay_attack_detected');
    });

    test('should handle unknown errors with graceful degradation', () => {
      const error = new Error('Unknown error');
      // No error type set

      const result = recovery.handleAKEError(error, mockContext);

      expect(result.strategy).toBe(RECOVERY_STRATEGIES.GRACEFUL_DEGRADATION);
      expect(result.action).toBe('degrade_to_plaintext');
    });
  });

  describe('security event logging', () => {
    test('should log security events', () => {
      const error = new Error('Test error');
      error.type = ERROR_TYPES.INVALID_SIGNATURE;

      recovery.handleAKEError(error, mockContext);

      const stats = recovery.getStats();
      expect(stats.securityEvents).toBeGreaterThan(0);
      expect(stats.recentEvents.length).toBeGreaterThan(0);
    });

    test('should call external security event handler', () => {
      const eventHandler = jest.fn();
      const recoveryWithHandler = new ProtocolErrorRecovery({
        securityEventHandler: eventHandler,
        enableLogging: false
      });

      const error = new Error('Test error');
      error.type = ERROR_TYPES.INVALID_SIGNATURE;

      recoveryWithHandler.handleAKEError(error, mockContext);

      expect(eventHandler).toHaveBeenCalled();
      expect(eventHandler.mock.calls[0][0]).toBeInstanceOf(SecurityEvent);
    });

    test('should limit security event history', () => {
      // Generate many events
      for (let i = 0; i < 1100; i++) {
        recovery._logSecurityEvent('TEST_EVENT', 'LOW', `Event ${i}`, {});
      }

      expect(recovery.securityEvents.length).toBeLessThanOrEqual(1000);
    });
  });

  describe('state clearing', () => {
    test('should securely clear authentication state', () => {
      const error = new Error('Test error');
      error.type = ERROR_TYPES.INVALID_SIGNATURE;

      recovery.handleAKEError(error, mockContext);

      // Check that sensitive fields are cleared
      expect(mockContext.auth.privateKey).toBeNull();
      expect(mockContext.auth.sharedSecret).toBeNull();
      expect(mockContext.auth.hashgx).toBeNull();
      expect(mockContext.auth.state).toBe('PLAINTEXT');
    });

    test('should handle missing auth state gracefully', () => {
      const contextWithoutAuth = { id: 'test-context' };
      const error = new Error('Test error');
      error.type = ERROR_TYPES.INVALID_SIGNATURE;

      expect(() => {
        recovery.handleAKEError(error, contextWithoutAuth);
      }).not.toThrow();
    });
  });

  describe('statistics tracking', () => {
    test('should track recovery statistics', () => {
      const error1 = new Error('Error 1');
      error1.type = ERROR_TYPES.INVALID_SIGNATURE;

      const error2 = new Error('Error 2');
      error2.type = ERROR_TYPES.PROTOCOL_VIOLATION;

      recovery.handleAKEError(error1, mockContext);
      recovery.handleAKEError(error2, mockContext);

      const stats = recovery.getStats();
      expect(stats.totalErrors).toBe(2);
      expect(stats.recoveredErrors).toBeGreaterThan(0);
      expect(stats.recoveryRate).toBeGreaterThan(0);
    });

    test('should reset statistics', () => {
      const error = new Error('Test error');
      error.type = ERROR_TYPES.INVALID_SIGNATURE;

      recovery.handleAKEError(error, mockContext);
      recovery.resetStats();

      const stats = recovery.getStats();
      expect(stats.totalErrors).toBe(0);
      expect(stats.recoveredErrors).toBe(0);
      expect(stats.securityEvents).toBe(0);
    });
  });

  describe('error classification', () => {
    test('should classify errors based on message content', () => {
      const signatureError = new Error('signature verification failed');
      const macError = new Error('MAC validation failed');
      const dhError = new Error('DH key validation failed');
      const sequenceError = new Error('sequence number invalid');

      expect(recovery._classifyError(signatureError)).toBe(ERROR_TYPES.INVALID_SIGNATURE);
      expect(recovery._classifyError(macError)).toBe(ERROR_TYPES.INVALID_MAC);
      expect(recovery._classifyError(dhError)).toBe(ERROR_TYPES.INVALID_DH_KEY);
      expect(recovery._classifyError(sequenceError)).toBe(ERROR_TYPES.SEQUENCE_ERROR);
    });

    test('should handle unknown error types', () => {
      const unknownError = new Error('Something went wrong');
      expect(recovery._classifyError(unknownError)).toBe('UNKNOWN_ERROR');
    });
  });
});

describe('SecurityEvent', () => {
  test('should create security event with all properties', () => {
    const event = new SecurityEvent(
      ERROR_TYPES.INVALID_SIGNATURE,
      'HIGH',
      'Test security event',
      { contextId: 'test-123' }
    );

    expect(event.type).toBe(ERROR_TYPES.INVALID_SIGNATURE);
    expect(event.severity).toBe('HIGH');
    expect(event.message).toBe('Test security event');
    expect(event.context.contextId).toBe('test-123');
    expect(event.timestamp).toBeGreaterThan(0);
    expect(event.id).toMatch(/^evt_/);
  });

  test('should serialize to JSON correctly', () => {
    const event = new SecurityEvent(
      ERROR_TYPES.PROTOCOL_VIOLATION,
      'MEDIUM',
      'Test event'
    );

    const json = event.toJSON();
    expect(json.type).toBe(ERROR_TYPES.PROTOCOL_VIOLATION);
    expect(json.severity).toBe('MEDIUM');
    expect(json.message).toBe('Test event');
    expect(json.timestamp).toBe(event.timestamp);
    expect(json.id).toBe(event.id);
  });
});

describe('Global error recovery', () => {
  test('should provide global error recovery instance', () => {
    expect(globalErrorRecovery).toBeInstanceOf(ProtocolErrorRecovery);
  });

  test('should handle errors through global instance', () => {
    const error = new Error('Global test error');
    error.type = ERROR_TYPES.PROTOCOL_VIOLATION;

    const result = globalErrorRecovery.handleAKEError(error, mockContext);
    expect(result).toBeDefined();
    expect(result.strategy).toBeDefined();
  });
});
