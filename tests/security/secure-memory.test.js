/**
 * Test suite for secure memory management
 * 
 * These tests verify that secure memory allocation, wiping, and lifecycle
 * management work correctly to protect sensitive cryptographic data.
 */

import { SecureMemory, SecureMemoryPool, globalSecureMemoryPool } from '../../src/core/security/secure-memory.js';

describe('SecureMemory', () => {
  afterEach(() => {
    // Clean up any remaining secure memory instances
    SecureMemory.cleanupAll();
  });

  describe('constructor', () => {
    test('should create secure memory with specified size', () => {
      const memory = new SecureMemory(64);
      expect(memory.size).toBe(64);
      expect(memory.getView()).toHaveLength(64);
      expect(memory.isDestroyed).toBe(false);
      memory.destroy();
    });

    test('should reject invalid sizes', () => {
      expect(() => new SecureMemory(0)).toThrow('Size must be a positive number');
      expect(() => new SecureMemory(-1)).toThrow('Size must be a positive number');
      expect(() => new SecureMemory('invalid')).toThrow('Size must be a positive number');
    });

    test('should register instance in global registry', () => {
      const initialCount = SecureMemory.registry.size;
      const memory = new SecureMemory(32);
      expect(SecureMemory.registry.size).toBe(initialCount + 1);
      expect(SecureMemory.registry.has(memory)).toBe(true);
      memory.destroy();
    });
  });

  describe('read/write operations', () => {
    test('should write and read data correctly', () => {
      const memory = new SecureMemory(16);
      const testData = new Uint8Array([1, 2, 3, 4, 5]);
      
      memory.write(testData);
      const readData = memory.read(5);
      
      expect(readData).toEqual(testData);
      memory.destroy();
    });

    test('should handle offset operations', () => {
      const memory = new SecureMemory(16);
      const testData = new Uint8Array([1, 2, 3, 4]);
      
      memory.write(testData, 4);
      const readData = memory.read(4, 4);
      
      expect(readData).toEqual(testData);
      memory.destroy();
    });

    test('should reject operations that exceed buffer size', () => {
      const memory = new SecureMemory(8);
      const largeData = new Uint8Array(16);
      
      expect(() => memory.write(largeData)).toThrow('Data exceeds buffer size');
      expect(() => memory.read(16)).toThrow('Read exceeds buffer size');
      
      memory.destroy();
    });

    test('should reject operations on destroyed memory', () => {
      const memory = new SecureMemory(16);
      memory.destroy();
      
      expect(() => memory.getView()).toThrow('Secure memory has been destroyed');
      expect(() => memory.write(new Uint8Array([1, 2, 3]))).toThrow('Secure memory has been destroyed');
      expect(() => memory.read()).toThrow('Secure memory has been destroyed');
    });
  });

  describe('secure wiping', () => {
    test('should wipe memory with multiple patterns', () => {
      const memory = new SecureMemory(16);
      const testData = new Uint8Array([1, 2, 3, 4, 5, 6, 7, 8]);
      
      memory.write(testData);
      memory.secureWipe();
      
      // After wiping, all bytes should be zero
      const view = memory.getView();
      for (let i = 0; i < view.length; i++) {
        expect(view[i]).toBe(0);
      }
      
      memory.destroy();
    });

    test('should handle multiple wipe calls safely', () => {
      const memory = new SecureMemory(16);
      
      memory.secureWipe();
      memory.secureWipe(); // Should not throw
      
      memory.destroy();
    });
  });

  describe('destruction', () => {
    test('should destroy memory and remove from registry', () => {
      const memory = new SecureMemory(16);
      const initialCount = SecureMemory.registry.size;
      
      memory.destroy();
      
      expect(memory.isDestroyed).toBe(true);
      expect(SecureMemory.registry.size).toBe(initialCount - 1);
      expect(SecureMemory.registry.has(memory)).toBe(false);
    });

    test('should handle multiple destroy calls safely', () => {
      const memory = new SecureMemory(16);
      
      memory.destroy();
      memory.destroy(); // Should not throw
      
      expect(memory.isDestroyed).toBe(true);
    });
  });

  describe('statistics', () => {
    test('should track memory statistics', () => {
      const memory = new SecureMemory(64, { trackAccess: true });
      
      const initialStats = memory.getStats();
      expect(initialStats.size).toBe(64);
      expect(initialStats.isDestroyed).toBe(false);
      expect(initialStats.accessCount).toBeGreaterThan(0);
      
      memory.write(new Uint8Array([1, 2, 3]));
      memory.read(3);
      
      const finalStats = memory.getStats();
      expect(finalStats.accessCount).toBeGreaterThan(initialStats.accessCount);
      
      memory.destroy();
    });

    test('should provide global statistics', () => {
      const memory1 = new SecureMemory(32);
      const memory2 = new SecureMemory(64);
      
      const stats = SecureMemory.getGlobalStats();
      expect(stats.totalInstances).toBeGreaterThanOrEqual(2);
      expect(stats.activeInstances).toBeGreaterThanOrEqual(2);
      expect(stats.totalSize).toBeGreaterThanOrEqual(96);
      
      memory1.destroy();
      memory2.destroy();
    });
  });

  describe('access logging', () => {
    test('should log access when logger is set', () => {
      const logs = [];
      SecureMemory.setAccessLogger((log) => logs.push(log));
      
      const memory = new SecureMemory(32, { trackAccess: true });
      memory.write(new Uint8Array([1, 2, 3]));
      memory.read(3);
      memory.destroy();
      
      expect(logs.length).toBeGreaterThan(0);
      expect(logs.some(log => log.operation === 'created')).toBe(true);
      expect(logs.some(log => log.operation === 'write')).toBe(true);
      expect(logs.some(log => log.operation === 'read')).toBe(true);
      
      // Reset logger
      SecureMemory.setAccessLogger(null);
    });
  });
});

describe('SecureMemoryPool', () => {
  let pool;

  beforeEach(() => {
    pool = new SecureMemoryPool({
      maxPoolSize: 5,
      commonSizes: [32, 64, 128]
    });
  });

  afterEach(() => {
    pool.destroy();
    SecureMemory.cleanupAll();
  });

  describe('allocation and deallocation', () => {
    test('should allocate memory from pool', () => {
      const memory = pool.allocate(32);
      expect(memory).toBeInstanceOf(SecureMemory);
      expect(memory.size).toBe(32);
      memory.destroy();
    });

    test('should reuse memory from pool', () => {
      const memory1 = pool.allocate(64);
      pool.deallocate(memory1);
      
      const memory2 = pool.allocate(64);
      expect(memory2.size).toBe(64);
      
      const stats = pool.getStats();
      expect(stats.poolHits).toBeGreaterThan(0);
      
      memory2.destroy();
    });

    test('should handle size matching', () => {
      // Request smaller size than common size
      const memory = pool.allocate(30);
      expect(memory.size).toBe(32); // Should use next larger common size
      memory.destroy();
    });

    test('should handle non-common sizes', () => {
      const memory = pool.allocate(100);
      expect(memory.size).toBe(128); // Should use next larger common size
      memory.destroy();
    });
  });

  describe('pool management', () => {
    test('should limit pool size', () => {
      const memories = [];
      
      // Allocate more than pool size
      for (let i = 0; i < 10; i++) {
        memories.push(pool.allocate(32));
      }
      
      // Deallocate all
      for (const memory of memories) {
        pool.deallocate(memory);
      }
      
      const pool32 = pool.getPool(32);
      expect(pool32.length).toBeLessThanOrEqual(5); // Should respect maxPoolSize
    });

    test('should clean up expired memory', () => {
      const memory = pool.allocate(32);
      
      // Manually set old creation time
      memory.createdAt = Date.now() - 400000; // Older than maxAge
      
      pool.deallocate(memory);
      pool.cleanup();
      
      const pool32 = pool.getPool(32);
      expect(pool32.length).toBe(0); // Should be cleaned up
    });
  });

  describe('statistics', () => {
    test('should track pool statistics', () => {
      const memory1 = pool.allocate(32);
      const memory2 = pool.allocate(32);
      
      pool.deallocate(memory1);
      
      const memory3 = pool.allocate(32); // Should be pool hit
      
      const stats = pool.getStats();
      expect(stats.allocations).toBe(3);
      expect(stats.deallocations).toBe(1);
      expect(stats.poolHits).toBe(1);
      expect(stats.poolMisses).toBe(2);
      expect(stats.hitRate).toBeCloseTo(1/3);
      
      memory2.destroy();
      memory3.destroy();
    });
  });
});

describe('Global cleanup', () => {
  test('should clean up all secure memory on cleanup', () => {
    const memory1 = new SecureMemory(32);
    const memory2 = new SecureMemory(64);
    
    const initialCount = SecureMemory.registry.size;
    expect(initialCount).toBeGreaterThanOrEqual(2);
    
    SecureMemory.cleanupAll();
    
    expect(SecureMemory.registry.size).toBe(0);
    expect(memory1.isDestroyed).toBe(true);
    expect(memory2.isDestroyed).toBe(true);
  });
});
